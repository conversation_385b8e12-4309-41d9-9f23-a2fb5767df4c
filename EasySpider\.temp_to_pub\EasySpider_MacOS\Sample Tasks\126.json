{"id": 126, "name": "Dynamic Iframe", "url": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://www.ceic.ac.cn/history", "links": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://www.ceic.ac.cn/history", "create_time": "7/4/2023, 10:55:54 PM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://www.ceic.ac.cn/history", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://www.ceic.ac.cn/history", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://www.ceic.ac.cn/history"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "SuitableforAnyoneintheWorkplace"}, {"id": 1, "name": "参数2_文本", "desc": "", "type": "string", "exampleValue": "Designandexecutewebscrapingtasksvisually,justlikeusingExcel,regardlessofcodingexperience."}, {"id": 2, "name": "自定义参数_0", "desc": "", "type": "string", "exampleValue": "自定义字段"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 7, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 10, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://www.ceic.ac.cn/history", "links": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://www.ceic.ac.cn/history", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": -1, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[1]/div", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[1]", "//div[contains(., 'EasySpider')]", "//DIV[@class='move-title']", "/html/body/div[last()-8]/div/div[last()-2]/div[last()-4]"]}}, {"id": -1, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "EasySpider: Visual Web Crawler"}], "unique_index": "yb5bjxhcgbaljodxlcm", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 3, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[3]/div[1]/div[110]", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[3]/div[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='func-item']", "/html/body/div[last()-6]/div/div[last()-2]"]}}, {"id": 4, "index": 5, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/div[2]", "allXPaths": ["/div[2]", "//div[contains(., 'Suitable f')]", "//DIV[@class='func-title']", "/html/body/div[last()-6]/div/div[last()-2]/div[last()-1]"], "exampleValues": [{"num": 0, "value": "SuitableforAnyoneintheWorkplace"}], "unique_index": "/div[2]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/div[3]", "allXPaths": ["/div[3]", "//div[contains(., 'Design and')]", "//DIV[@class='func-desc']", "/html/body/div[last()-6]/div/div[last()-2]/div"], "exampleValues": [{"num": 0, "value": "Designandexecutewebscrapingtasksvisually,justlikeusingExcel,regardlessofcodingexperience."}], "unique_index": "/div[3]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 6, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": false, "name": "参数1_文本", "desc": "", "relativeXPath": "/html/body/div[3]/div[1]/div", "allXPaths": ["/div[2]", "//div[contains(., 'Suitable f')]", "//DIV[@class='func-title']", "/html/body/div[last()-6]/div/div[last()-2]/div[last()-1]"], "exampleValues": [{"num": 0, "value": "SuitableforAnyoneintheWorkplace"}], "unique_index": "/div[2]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/div[3]", "allXPaths": ["/div[3]", "//div[contains(., 'Design and')]", "//DIV[@class='func-desc']", "/html/body/div[last()-6]/div/div[last()-2]/div"], "exampleValues": [{"num": 0, "value": "Designandexecutewebscrapingtasksvisually,justlikeusingExcel,regardlessofcodingexperience."}], "unique_index": "/div[3]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 2, "index": 7, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "自定义参数_0", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[3]/div[1]/div", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "iframe": true}]}}]}