{"id": 313, "name": "Anna’s Archive 下载链接采集(Don't Check)", "url": "https://annas-archive.org/", "links": "https://annas-archive.org/", "create_time": "2023-12-30 09:26:58", "update_time": "2023-12-30 22:47:12", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "dataWriteMode": 1, "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": true, "browser": "chrome", "removeDuplicate": 0, "desc": "https://annas-archive.org/", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://annas-archive.org/", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://annas-archive.org/"}, {"id": 1, "name": "loopText_1", "nodeId": 4, "nodeName": "循环 - 文本列表", "desc": "要输入的文本/网址,多行以\\n分开", "type": "text", "exampleValue": "978-3-030-89032-2\tpdf <enter>\n978-3-030-61595-6\tpdf <enter>\n978-3-030-64701-8\tpdf <enter>\n978-3-030-54946-6\tpdf <enter>\n978-3-030-56402-5\tpdf <enter>\n978-3-030-59088-8\tpdf <enter>\n978-3-030-59562-3\tpdf <enter>\n978-3-030-50284-3\tpdf <enter>\n978-3-030-43781-7\tpdf <enter>\n978-3-030-43901-9\tpdf <enter>\n978-3-030-46209-3\tpdf <enter>\n978-3-030-47295-5\tpdf <enter>\n978-3-030-40475-8\tpdf <enter>\n978-3-030-41556-3\tpdf <enter>\n978-3-030-40183-2\tpdf <enter>\n978-3-030-27153-4\tpdf <enter>\n978-3-030-29016-0\tpdf <enter>\n978-3-030-21792-1\tpdf <enter>\n978-3-030-26646-2\tpdf <enter>\n978-981-32-9741-8\tpdf <enter>\n978-1-4939-9644-5\tpdf <enter>\n978-3-030-13751-9\tpdf <enter>\n978-3-030-02781-0\tpdf <enter>\n978-3-030-14977-2\tpdf <enter>\n978-3-662-48993-2\tpdf <enter>\n978-3-030-03296-8\tpdf <enter>\n978-3-319-96876-6\tpdf <enter>\n978-3-319-98271-7\tpdf <enter>\n978-981-13-1657-9\tpdf <enter>\n", "value": "978-3-030-89032-2\tpdf <enter>\n978-3-030-61595-6\tpdf <enter>\n978-3-030-64701-8\tpdf <enter>\n978-3-030-54946-6\tpdf <enter>\n978-3-030-56402-5\tpdf <enter>\n978-3-030-59088-8\tpdf <enter>\n978-3-030-59562-3\tpdf <enter>\n978-3-030-50284-3\tpdf <enter>\n978-3-030-43781-7\tpdf <enter>\n978-3-030-43901-9\tpdf <enter>\n978-3-030-46209-3\tpdf <enter>\n978-3-030-47295-5\tpdf <enter>\n978-3-030-40475-8\tpdf <enter>\n978-3-030-41556-3\tpdf <enter>\n978-3-030-40183-2\tpdf <enter>\n978-3-030-27153-4\tpdf <enter>\n978-3-030-29016-0\tpdf <enter>\n978-3-030-21792-1\tpdf <enter>\n978-3-030-26646-2\tpdf <enter>\n978-981-32-9741-8\tpdf <enter>\n978-1-4939-9644-5\tpdf <enter>\n978-3-030-13751-9\tpdf <enter>\n978-3-030-02781-0\tpdf <enter>\n978-3-030-14977-2\tpdf <enter>\n978-3-662-48993-2\tpdf <enter>\n978-3-030-03296-8\tpdf <enter>\n978-3-319-96876-6\tpdf <enter>\n978-3-319-98271-7\tpdf <enter>\n978-981-13-1657-9\tpdf <enter>\n"}], "outputParameters": [{"id": 0, "name": "参数1_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://download.library.lol/main/951000/074a7a50c193bb2765622e48f1714e4d/%28Universitext%29%20Claudio%20Procesi%20%28auth.%29%20-%20Lie%20Groups_%20An%20Approach%20through%20Invariants%20and%20Representations-Springer-Verlag%20New%20York%20%282007%29.pdf"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://annas-archive.org/", "links": "https://annas-archive.org/", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"header-search\")]/input[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "value": "978-3-030-89032-2", "index": 0, "allXPaths": ["/html/body/div[1]/div[2]/div[4]/form[1]/input[1]", "//input[contains(., '')]", "//INPUT[@class='js-slash-focus rounded']", "//INPUT[@name='q']", "/html/body/div[last()-3]/div/div/form/input"]}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 4, "title": "输入ISBN", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "//*[contains(@class, \"header-search\")]/input[1]", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "value": "978-3-030-89032-2<enter>", "index": 0, "allXPaths": ["/html/body/div[1]/div[2]/div[4]/form[1]/input[1]", "//input[contains(., '')]", "//INPUT[@class='js-slash-focus rounded']", "//INPUT[@name='q']", "/html/body/div[last()-3]/div/div/form/input"]}}, {"id": 2, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环 - 文本列表", "sequence": [3, 30, 21], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 3, "pathList": "", "textList": "978-3-030-89032-2\tpdf <enter>\n978-3-030-61595-6\tpdf <enter>\n978-3-030-64701-8\tpdf <enter>\n978-3-030-54946-6\tpdf <enter>\n978-3-030-56402-5\tpdf <enter>\n978-3-030-59088-8\tpdf <enter>\n978-3-030-59562-3\tpdf <enter>\n978-3-030-50284-3\tpdf <enter>\n978-3-030-43781-7\tpdf <enter>\n978-3-030-43901-9\tpdf <enter>\n978-3-030-46209-3\tpdf <enter>\n978-3-030-47295-5\tpdf <enter>\n978-3-030-40475-8\tpdf <enter>\n978-3-030-41556-3\tpdf <enter>\n978-3-030-40183-2\tpdf <enter>\n978-3-030-27153-4\tpdf <enter>\n978-3-030-29016-0\tpdf <enter>\n978-3-030-21792-1\tpdf <enter>\n978-3-030-26646-2\tpdf <enter>\n978-981-32-9741-8\tpdf <enter>\n978-1-4939-9644-5\tpdf <enter>\n978-3-030-13751-9\tpdf <enter>\n978-3-030-02781-0\tpdf <enter>\n978-3-030-14977-2\tpdf <enter>\n978-3-662-48993-2\tpdf <enter>\n978-3-030-03296-8\tpdf <enter>\n978-3-319-96876-6\tpdf <enter>\n978-3-319-98271-7\tpdf <enter>\n978-981-13-1657-9\tpdf <enter>\n", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0}}, {"id": -1, "index": 5, "parentId": 2, "type": 2, "option": 9, "title": "进入书籍下载页", "sequence": [6, 7], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": -1, "parentId": 5, "index": 6, "type": 3, "option": 10, "title": "第二项", "sequence": [15], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 2, "value": "//*[contains(@class, \"min-w-[0]\")]/div[1]/div[2]/a[1]/div[2]/div[1]", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 5, "index": 7, "type": 3, "option": 10, "title": "第一项", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 2, "value": "//*[contains(@class, \"min-w-[0]\")]/div[1]/div[1]/a[1]/div[2]/div[1]", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 8, "parentId": 5, "type": 1, "option": 8, "title": "循环移动到每个元素", "sequence": [9], "isInLoop": true, "position": 0, "parameters": {"history": 10, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/main[1]/form[1]/div[4]/div[2]/div[1]/div/a[1]/div[2]/div[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ""}}, {"id": -1, "index": 9, "parentId": 7, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 10, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "allXPaths": ""}}, {"id": -1, "index": 10, "parentId": 2, "type": 1, "option": 8, "title": "循环移动到每个元素", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 10, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/main[1]/form[1]/div[4]/div[2]/div[1]/div/a[1]/div[2]/div[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ""}}, {"id": -1, "index": 11, "parentId": 4, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 10, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "allXPaths": ""}}, {"id": -1, "index": 12, "parentId": 7, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0}}, {"id": -1, "index": 13, "parentId": 8, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0}}, {"id": -1, "index": 14, "parentId": 2, "type": 1, "option": 8, "title": "循环点击每个元素", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 10, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/main[1]/form[1]/div[4]/div[2]/div[1]/div/a[1]/div[2]/div[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ""}}, {"id": -1, "index": 15, "parentId": 7, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 10, "tabIndex": -1, "useLoop": true, "xpath": "//*[contains(@class, \"min-w-[0]\")]/div[1]/div[2]/a[1]/div[2]/div[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 1, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ""}}, {"id": -1, "index": 16, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 10, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"min-w-[0]\")]/div[1]/div[1]/a[1]/div[2]/div[1]", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 1, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ""}}, {"id": -1, "index": 17, "parentId": 2, "type": 1, "option": 8, "title": "循环移动到每个元素", "sequence": [18], "isInLoop": true, "position": 2, "parameters": {"history": 12, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 2, "pathList": "//*[contains(@class, \"min-w-[0]\")]/div[1]/div[1]/a[1]/div[2]/div[1]\n//*[contains(@class, \"min-w-[0]\")]/div[1]/div[2]/a[1]/div[2]/div[1]", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ""}}, {"id": -1, "index": 18, "parentId": 5, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 12, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "allXPaths": ""}}, {"id": 8, "index": 19, "parentId": 6, "type": 0, "option": 2, "title": "点击Libgen.rs链接", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 12, "tabIndex": -1, "useLoop": false, "xpath": "//a[contains(text(), 'Libgen.rs')]", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/main[1]/div[3]/div[2]/ul[1]/li[6]/a[1]", "//a[contains(., 'Libgen.rs')]", "//A[@class='js-download-link']", "/html/body/main/div[last()-3]/div[last()-1]/ul/li[last()-8]/a"]}}, {"id": 9, "index": 20, "parentId": 6, "type": 0, "option": 3, "title": "采集书籍链接地址", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 3, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 2, "contentType": 0, "relative": false, "name": "参数1_链接地址", "desc": "", "relativeXPath": "//a[contains(., 'GET')]", "allXPaths": ["/html/body/table[1]/tbody[1]/tr[1]/td[2]/div[1]/h2[1]/a[1]", "//a[contains(., 'GET')]", "/html/body/table/tbody/tr/td[last()-1]/div[last()-2]/h2[last()-1]/a"], "exampleValues": [{"num": 0, "value": "https://download.library.lol/main/951000/074a7a50c193bb2765622e48f1714e4d/%28Universitext%29%20Claudio%20Procesi%20%28auth.%29%20-%20Lie%20Groups_%20An%20Approach%20through%20Invariants%20and%20Representations-Springer-Verlag%20New%20York%20%282007%29.pdf"}], "unique_index": "wlzl6xtxvumlqrb2e5x", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}]}}, {"id": 5, "index": 21, "parentId": 2, "type": 2, "option": 9, "title": "点击Libgen.rs下载链接", "sequence": [22, 23], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": 6, "parentId": 5, "index": 22, "type": 3, "option": 10, "title": "当前页面包含元素", "sequence": [19, 20], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 2, "value": "//a[contains(text(), 'Libgen.rs')]", "code": "", "waitTime": 0}, "position": 0}, {"id": 7, "parentId": 5, "index": 23, "type": 3, "option": 10, "title": "当前页面包含元素", "sequence": [24, 25], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 2, "value": "//a[contains(text(), 'Libgen.li')]", "code": "", "waitTime": 0}, "position": 1}, {"id": 10, "index": 24, "parentId": 7, "type": 0, "option": 2, "title": "点击Libgen.li链接", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 12, "tabIndex": -1, "useLoop": false, "xpath": "//a[contains(text(), 'Libgen.li')]", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/main[1]/div[3]/div[2]/ul[1]/li[6]/a[1]", "//a[contains(., 'Libgen.rs')]", "//A[@class='js-download-link']", "/html/body/main/div[last()-3]/div[last()-1]/ul/li[last()-8]/a"]}}, {"id": 11, "index": 25, "parentId": 7, "type": 0, "option": 2, "title": "点击GET下载", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//a[contains(., 'GET')]", "iframe": false, "wait": 3, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/table[1]/tbody[1]/tr[1]/td[2]/a[1]/h2[1]", "//h2[contains(., 'GET')]", "/html/body/table/tbody/tr[last()-3]/td[last()-1]/a/h2"]}}, {"id": -1, "index": 26, "parentId": 1, "type": 0, "option": 1, "title": "重新打开<PERSON>'s <PERSON><PERSON>网页", "sequence": [], "isInLoop": true, "position": 4, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "about:blank", "links": "https://annas-archive.org/", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 27, "parentId": 2, "type": 0, "option": 5, "title": "关闭GET页", "sequence": [], "isInLoop": true, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 0, "code": "return window.close()", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"index": 28, "id": -1, "parentId": 5, "type": 3, "option": 10, "title": "无条件", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 2}, {"id": -1, "index": 29, "parentId": 2, "type": 0, "option": 5, "title": "关闭书籍详情页", "sequence": [], "isInLoop": true, "position": 4, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 0, "code": "return window.close()", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"id": 4, "index": 30, "parentId": 2, "type": 0, "option": 2, "title": "点击书籍图片", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"w-[72]\")]/img[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 1, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/main[1]/form[1]/div[4]/div[2]/div[1]/div[1]/a[1]/div[1]/div[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='relative inline-block']", "/html/body/main/form/div/div/div/div/a/div[last()-1]/div/img"]}}, {"id": -1, "index": 31, "parentId": 2, "type": 0, "option": 5, "title": "刷新主页面", "sequence": [], "isInLoop": true, "position": 4, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 0, "code": "return location.reload()", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}]}