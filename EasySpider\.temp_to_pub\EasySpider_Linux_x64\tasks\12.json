{"id": 12, "name": "Electronics, Cars, Fashion, Collectibles & More | eBay", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "create_time": "5/25/2023, 10:18:55 PM", "version": "0.3.1", "containJudge": true, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "Open Page", "value": "https://www.ebay.com", "desc": "List of URLs to be collected, separated by \\n for multiple lines", "type": "string", "exampleValue": "https://www.ebay.com"}], "outputParameters": [{"id": 0, "name": "para1_text", "desc": "", "type": "string", "exampleValue": "Home"}, {"id": 1, "name": "para1_page_title", "desc": "", "type": "string", "exampleValue": "Electronics, Cars, Fashion, Collectibles & More | eBay"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "Loop", "sequence": [4, 7], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[6]/div[1]/ul[1]/li", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": "1", "pathList": "", "textList": "", "code": "python D:/test.py", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": "1", "breakCode": "return window.innerHeight > 500", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[6]/div[1]/ul[1]/li[1]", "//li[contains(., 'Home')]", "//LI[@class='hl-cat-nav__active']"]}}, {"id": 7, "index": 3, "parentId": 5, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "para1_text", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "Home"}, {"num": 1, "value": "Saved"}, {"num": 2, "value": "MotorsExpand: MotorsParts & AccessoriesAll Parts & AccessoriesCar & Truck PartsMotorcycle PartsAutomotive Tools & SuppliesApparel & MerchandiseMotors DealsMy GarageVehiclesCars & TrucksClassicsMotorcyclesPowersportsRVs & CampersTrailers & Other VehiclesBoats"}, {"num": 3, "value": "ElectronicsExpand: ElectronicsTop CategoriesComputers, Tablets & Network HardwareCameras & PhotoCell Phones & SmartphonesCell Phone Cases, Covers & SkinsTV, Video & Home Audio ElectronicsVehicle Electronics & GPSHeadphonesSurveillance & Smart Home ElectronicsPopular TopicseBay RefurbishedVideo GamesNintendo Toys to LifeApple iPhonePC Desktops & All-In-One ComputersComputer Graphics CardsTablets & eReadersLaptops & Netbooks"}, {"num": 4, "value": "CollectiblesExpand: CollectiblesTop CategoriesSports Memorabilia, Fan Shop & Sports CardsSports Trading CardsCoins & Paper MoneyAntiquesBullionArtCollectible Card GamesComicsPopular TopicsArt PaintingsMorgan DollarsFunko Pop!DisneyanaMusic MemorabiliaBaseball CardsNFTsStamps"}, {"num": 5, "value": "Home & GardenExpand: Home & GardenTop CategoriesYard, Garden & Outdoor Living ItemsHome ImprovementSmall Kitchen AppliancesLamps, Lighting & Ceiling FansHome DécorPower ToolsFurnitureHousehold & Cleaning SuppliesAdditional Categories eBay RefurbishedSurveillance & Smart HomeVacuum CleanersKitchenAid Countertop MixersOutdoor EntertainingBeddingMattressesHome & Garden Deals"}, {"num": 6, "value": "FashionExpand: FashionTop CategoriesWomen's ClothingWomen's ShoesWomen’s AccessoriesMen's ClothingMen's ShoesMen's AccessoriesGirls' ClothingBoys' ClothingAdditional CategoriesDesigner HandbagsCollectible SneakersWomen's DressesWomen's Coats, Jackets & VestsWomen’s Intimates & SleepwearMen's ShirtsMen's Coats & JacketsFashion Deals"}, {"num": 7, "value": "ToysExpand: ToysTop CategoriesAction FiguresDolls & Teddy BearsDiecast & Toy VehiclesBuilding ToysCollectible Card GamesModel Railroads & TrainsRC Model Vehicles, Toys & Control LineToy Models & KitsPopular TopicsLEGO Sets & PacksWarhammer 40KBoard & Roleplaying GamesPreschool Toys & Pretend PlayNERF Dart Guns & Soft DartsReborn Dolls PlaysetsMarvel Legends Action FiguresToys Deals"}, {"num": 8, "value": "Sporting GoodsExpand: Sporting GoodsTop CategoriesHunting EquipmentCycling EquipmentFishing Equipment & SuppliesGolf ClubsTeam SportsFitness, Running & Yoga EquipmentCamping & Hiking EquipmentWater SportsPopular TopicsBilliard CuesCamping MealsBikesElectric BikesGPS & Running WatchesDumbbellsShimano Fishing ReelsScotty Cameron Golf Clubs"}, {"num": 9, "value": "Business & IndustrialExpand: Business & IndustrialTop CategoriesHeavy EquipmentPersonal Protective EquipmentHealthcareCNC, Metalworking & ManufacturingOfficeTest, Measurement & Inspection EquipmentRestaurant & Food ServiceDentalAdditional CategoriesIndustrial Automation & Motion ControlShipping & PackagingElectrical Equipment & SuppliesFood Trucks & Concession TrailersHeavy Equipment AttachmentsHeavy Equipment Parts & AccessoriesLight Industrial Equipment & ToolsHVAC & Refrigeration"}, {"num": 10, "value": "Jewelry & WatchesExpand: Jewelry & WatchesTop CategoriesLuxury WatchesWristwatchesAll Watches, Parts, AccessoriesFashion JewelryFine JewelryVintage & Antique JewelryLoose Diamonds & GemstonesAll JewelryTop BrandsRolexOMEGABreitlingTAG HeuerPatek PhilippeCartierVan Cleef & ArpelsTiffany & Co."}, {"num": 11, "value": "eBay Live"}, {"num": 12, "value": "RefurbishedExpand: RefurbishedShop eBay Refurbished ElectronicsCell PhonesDesktop ComputersHome AudioLaptopsMonitorsPortable Audio & HeadphonesSmart WatchesTabletsShop eBay Refurbished HomeCeiling FansIndoor air quality & FansOutdoor Power EquipmentPower ToolsSmall Kitchen AppliancesSporting GoodsSurveillance & Smart HomeVacuums"}, {"num": 13, "value": "More Expand: MoreMotorsElectronicsCollectiblesHome & GardenFashionToysSporting GoodsBusiness & IndustrialJewelry & WatcheseBay LiveRefurbished"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 3, "index": 4, "parentId": 2, "type": 2, "option": 9, "title": "If Condition", "sequence": [5, 6], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": 5, "parentId": 3, "index": 5, "type": 3, "option": 10, "title": "Condition", "sequence": [3], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "7", "value": "", "code": "return arguments[0].innerText.indexOf(\"i\") >=0", "waitTime": 0}, "position": 0}, {"id": 6, "parentId": 3, "index": 6, "type": 3, "option": 10, "title": "Condition", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 4, "index": 7, "parentId": 2, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 6, "relative": false, "name": "para1_page_title", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[6]/div[3]/div[1]/a[1]/div[2]", "allXPaths": ["/html/body/div[6]/div[3]/div[1]/a[1]/div[2]", "//div[contains(., 'These Deal')]", "//DIV[@class='hl-full-bleed-banner__wrapper']"], "exampleValues": [{"num": 0, "value": "Electronics, Cars, Fashion, Collectibles & More | eBay"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}