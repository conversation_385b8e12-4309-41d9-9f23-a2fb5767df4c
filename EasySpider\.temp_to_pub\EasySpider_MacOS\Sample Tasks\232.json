{"id": 232, "name": "多层嵌套iframe示例 - 主页面", "url": "https://easyspider.cn/test_pages/nested_iframe.html", "links": "https://easyspider.cn/test_pages/nested_iframe.html", "create_time": "12/8/2023, 5:12:02 AM", "update_time": "12/8/2023, 6:30:22 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "mysql", "saveName": "地震", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://easyspider.cn/test_pages/nested_iframe.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://easyspider.cn/test_pages/nested_iframe.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://easyspider.cn/test_pages/nested_iframe.html"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "这是第二层嵌套页面的内容"}, {"id": 1, "name": "参数2_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "这是第二层嵌套页面"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 10], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://easyspider.cn/test_pages/nested_iframe.html", "links": "https://easyspider.cn/test_pages/nested_iframe.html", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h3[1]", "allXPaths": ["/html/body/h3[1]", "//h3[contains(., '这是第二层嵌套页面')]", "/html/body/h3"], "exampleValues": [{"num": 0, "value": "这是第二层嵌套页面"}], "unique_index": "0k9uhqb7mlnhlpvp1hwz", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 3, "parentId": 2, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [4], "isInLoop": true, "position": 2, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/p", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/p[1]", "//p[contains(., '这是第二层嵌套页面的')]", "/html/body/p[last()-3]"]}}, {"id": -1, "index": 4, "parentId": 5, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数2_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "这是第二层嵌套页面的内容"}], "unique_index": "6kosmxdf35llpvp1k8k", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": -1, "index": 5, "parentId": 0, "type": 0, "option": 2, "title": "点击这是第一层嵌...", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/h2[1]", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/h2[1]", "//h2[contains(., '这是第一层嵌套页面')]", "/html/body/h2"]}}, {"id": -1, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": -1, "index": 7, "parentId": 0, "type": 1, "option": 8, "title": "循环点击单个元素", "sequence": [8, 9, 3], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/h2[1]", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 2, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/h2[1]", "//h2[contains(., '这是第一层嵌套页面')]", "/html/body/h2"]}}, {"id": -1, "index": 8, "parentId": 2, "type": 0, "option": 2, "title": "点击这是第一层嵌...", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/h2[1]", "//h2[contains(., '这是第一层嵌套页面')]", "/html/body/h2"]}}, {"id": -1, "index": 9, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": false, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h2[1]", "allXPaths": ["/html/body/h2[1]", "//h2[contains(., '这是第一层嵌套页面')]", "/html/body/h2"], "exampleValues": [{"num": 0, "value": "这是第一层嵌套页面"}], "unique_index": "r2h4mcoueblpvp38gj", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 2, "index": 10, "parentId": 0, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [11, 12, 13], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/p", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/p[1]", "//p[contains(., '这是第二层嵌套页面的')]", "/html/body/p[last()-3]"]}}, {"id": 3, "index": 11, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": true, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "这是第二层嵌套页面的内容"}], "unique_index": "8siuxtx3k2glpvqory3", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 4, "index": 12, "parentId": 2, "type": 0, "option": 2, "title": "点击这是第二层嵌...", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/h1[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/h3[1]", "//h3[contains(., '这是第二层嵌套页面')]", "/html/body/h3"]}}, {"id": 5, "index": 13, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数2_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h1[1]", "allXPaths": ["/html/body/h3[1]", "//h3[contains(., '这是第二层嵌套页面')]", "/html/body/h3"], "exampleValues": [{"num": 0, "value": "这是第二层嵌套页面"}], "unique_index": "p8lt44b21hblpvqoy5e", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}