To open the EasySpider, please open your terminal, and then type:
./easy-spider.sh
Then EasySpider will be opened, and don't close the terminal when running EasySpider.

Official Site: https://www.easyspider.net

Welcome to promote this software to other friends and star our Github Repository!

This version is for Ubuntu 20.04, Debian, Deepin x64 and above.

The software's open-source code repository on GitHub: https://github.com/NaiboWang/EasySpider

Official documentation can be found at: https://github.com/NaiboWang/EasySpider/wiki

Video Tutorial: https://youtube.com/playlist?list=PL0kEFEkWrT7mt9MUlEBV2DTo1QsaanUTp

Tasks can be imported from other machines by simply placing the .json files from the "tasks" folder of those machines into the "tasks" folder of this directory. Similarly, execution instance files can be imported by copying the .json files from the "execution_instances" folder. Note that only files named with a number greater than 0 are supported in both folders.
