{"id": 42, "name": "ebay截图", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.ebay.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.ebay.com"}], "outputParameters": [{"id": 0, "name": "参数1_背景图片地址", "desc": "", "type": "string", "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "scrollType": 0, "scrollCount": 0}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "params": [{"nodeType": 0, "contentType": 7, "relative": false, "name": "参数1_背景图片地址", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": ["/html/body", "//body[contains(., '')]", "//BODY[@class='desktop gh-flex']"], "exampleValues": [{"num": 0, "value": ""}], "default": ""}]}}]}