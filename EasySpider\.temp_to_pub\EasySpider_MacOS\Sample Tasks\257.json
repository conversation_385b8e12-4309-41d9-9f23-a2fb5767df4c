{"id": 257, "name": "百度一下，你就知道", "url": "https://www.baidu.com", "links": "https://www.baidu.com", "create_time": "12/12/2023, 5:59:29 AM", "update_time": "12/12/2023, 5:59:29 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": true, "desc": "https://www.baidu.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.baidu.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.baidu.com"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 6], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.baidu.com", "links": "https://www.baidu.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击“同志加兄弟...", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"hotsearch-content-wrapper\"]/li[1]/a[1]/span[2]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[2]/div[1]/div[5]/div[1]/div[1]/div[3]/ul[1]/li[1]/a[1]/span[2]", "//span[contains(., '“同志加兄弟”：中越')]", "//SPAN[@class='title-content-title']", "/html/body/div[last()-5]/div[last()-3]/div[last()-3]/div/div/div/ul/li[last()-5]/a/span"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 2, "option": 9, "title": "判断条件", "sequence": [4, 5], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": 5, "parentId": 3, "index": 4, "type": 3, "option": 10, "title": "条件分支1", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 1, "value": "hao123", "code": "", "waitTime": 0}, "position": 0}, {"id": 6, "parentId": 3, "index": 5, "type": 3, "option": 10, "title": "条件分支2", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 1, "value": "元首", "code": "", "waitTime": 0}, "position": 1}, {"id": 4, "index": 6, "parentId": 0, "type": 0, "option": 2, "title": "点击\n换一...", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"head_wrapper\"]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[2]/div[1]/div[5]", "//div[contains(., '')]", "id(\"head_wrapper\")", "//DIV[@class='head_wrapper s-isindex-wrap nologin']", "/html/body/div[last()-6]/div[last()-3]/div[last()-3]"]}}]}