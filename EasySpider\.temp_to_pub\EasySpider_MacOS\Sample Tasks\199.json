{"id": 199, "name": "天际青年关注-天际青年关注UP主-哔哩哔哩视频", "url": "https://space.bilibili.com/291929894/fans/follow", "links": "https://space.bilibili.com/291929894/fans/follow", "create_time": "7/23/2023, 11:49:47 PM", "update_time": "7/23/2023, 11:49:47 PM", "version": "0.5.0", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "https://space.bilibili.com/291929894/fans/follow", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://space.bilibili.com/291929894/fans/follow", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://space.bilibili.com/291929894/fans/follow"}, {"id": 1, "name": "loopTimes_循环_1", "nodeId": 2, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数1_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n    \n      \n\n\n      \n      \n      \n\n      \n    \n  "}, {"id": 1, "name": "参数2_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//space.bilibili.com/28389168"}, {"id": 2, "name": "参数3_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//i0.hdslb.com/bfs/face/44d29ba46572d71e633eaa88270ab4ea78f4709e.jpg@96w_96h_1c_1s_!web-avatar-space-list.avif"}, {"id": 3, "name": "参数4_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "AI血色玫瑰"}, {"id": 4, "name": "参数5_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//space.bilibili.com/28389168/"}, {"id": 5, "name": "参数6_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "荧厨与百合"}, {"id": 6, "name": "参数7_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "已关注"}, {"id": 7, "name": "参数8_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "设置分组"}, {"id": 8, "name": "参数9_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "取消关注"}, {"id": 9, "name": "参数10_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "发消息"}, {"id": 10, "name": "参数11_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//message.bilibili.com/#whisper/mid28389168"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://space.bilibili.com/291929894/fans/follow", "links": "https://space.bilibili.com/291929894/fans/follow", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4, 3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"be-pager-next\")]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/ul[2]/li[6]", "//li[contains(., '下一页')]", "//LI[@class='be-pager-next']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul/li"]}}, {"id": 4, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/ul[2]/li[6]", "//li[contains(., '下一页')]", "//LI[@class='be-pager-next']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul/li"], "loopType": 0}}, {"id": 3, "index": 4, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[4]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/ul[1]/li", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/ul[1]/li[1]", "//li[contains(., '')]", "//LI[@class='list-item clearfix']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]"]}}, {"id": 5, "index": 5, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/div[2]/a[1]", "allXPaths": ["/div[2]/a[1]", "//a[contains(., '')]", "//A[@class='up-cover-components']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div[last()-1]/a"], "exampleValues": [{"num": 0, "value": "\n    \n      \n\n\n      \n      \n      \n\n      \n    \n  "}], "unique_index": "/div[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/div[2]/a[1]", "allXPaths": ["/div[2]/a[1]", "//a[contains(., '')]", "//A[@class='up-cover-components']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div[last()-1]/a"], "exampleValues": [{"num": 0, "value": "//space.bilibili.com/28389168"}], "unique_index": "/div[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数3_图片地址", "desc": "", "relativeXPath": "/div[2]/a[1]/div[1]/img[1]", "allXPaths": ["/div[2]/a[1]/div[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='bili-avatar-img bili-avatar-face bili-avatar-img-radius']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div[last()-1]/a/div/img"], "exampleValues": [{"num": 0, "value": "//i0.hdslb.com/bfs/face/44d29ba46572d71e633eaa88270ab4ea78f4709e.jpg@96w_96h_1c_1s_!web-avatar-space-list.avif"}], "unique_index": "/div[2]/a[1]/div[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数4_链接文本", "desc": "", "relativeXPath": "/div[3]/a[1]", "allXPaths": ["/div[3]/a[1]", "//a[contains(., 'AI血色玫瑰')]", "//A[@class='title']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div/a"], "exampleValues": [{"num": 0, "value": "AI血色玫瑰"}], "unique_index": "/div[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数5_链接地址", "desc": "", "relativeXPath": "/div[3]/a[1]", "allXPaths": ["/div[3]/a[1]", "//a[contains(., 'AI血色玫瑰')]", "//A[@class='title']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div/a"], "exampleValues": [{"num": 0, "value": "//space.bilibili.com/28389168/"}], "unique_index": "/div[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数6_文本", "desc": "", "relativeXPath": "/div[3]/p[1]", "allXPaths": ["/div[3]/p[1]", "//p[contains(., '')]", "//P[@class='desc']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div/p"], "exampleValues": [{"num": 0, "value": "荧厨与百合"}], "unique_index": "/div[3]/p[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数7_文本", "desc": "", "relativeXPath": "/div[3]/div[1]/div[1]/span[1]", "allXPaths": ["/div[3]/div[1]/div[1]/span[1]", "//span[contains(., '已关注')]", "//SPAN[@class='fans-action-text']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div/div/div[last()-1]/span"], "exampleValues": [{"num": 0, "value": "已关注"}], "unique_index": "/div[3]/div[1]/div[1]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数8_文本", "desc": "", "relativeXPath": "/div[3]/div[1]/div[1]/ul[1]/li[1]", "allXPaths": ["/div[3]/div[1]/div[1]/ul[1]/li[1]", "//li[contains(., '')]", "//LI[@class='be-dropdown-item']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div/div/div[last()-1]/ul/li[last()-1]"], "exampleValues": [{"num": 0, "value": "设置分组"}], "unique_index": "/div[3]/div[1]/div[1]/ul[1]/li[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/div[3]/div[1]/div[1]/ul[1]/li[2]", "allXPaths": ["/div[3]/div[1]/div[1]/ul[1]/li[2]", "//li[contains(., '')]", "//LI[@class='be-dropdown-item']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div/div/div[last()-1]/ul/li"], "exampleValues": [{"num": 0, "value": "取消关注"}], "unique_index": "/div[3]/div[1]/div[1]/ul[1]/li[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数10_链接文本", "desc": "", "relativeXPath": "/div[3]/div[1]/div[2]/ul[1]/li[1]/a[1]", "allXPaths": ["/div[3]/div[1]/div[2]/ul[1]/li[1]/a[1]", "//a[contains(., '发消息')]", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div/div/div/ul/li/a"], "exampleValues": [{"num": 0, "value": "发消息"}], "unique_index": "/div[3]/div[1]/div[2]/ul[1]/li[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数11_链接地址", "desc": "", "relativeXPath": "/div[3]/div[1]/div[2]/ul[1]/li[1]/a[1]", "allXPaths": ["/div[3]/div[1]/div[2]/ul[1]/li[1]/a[1]", "//a[contains(., '发消息')]", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div/div/div/ul/li/a"], "exampleValues": [{"num": 0, "value": "//message.bilibili.com/#whisper/mid28389168"}], "unique_index": "/div[3]/div[1]/div[2]/ul[1]/li[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}