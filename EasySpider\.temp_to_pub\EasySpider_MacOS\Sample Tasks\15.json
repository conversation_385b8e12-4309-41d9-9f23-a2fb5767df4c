{"id": 15, "name": "拼多多国际", "url": "https://mobile.yangkeduo.com/login.html", "links": "https://mobile.yangkeduo.com/login.html", "containJudge": false, "desc": "https://mobile.yangkeduo.com/login.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 2, "nodeName": "Open Page", "value": "https://mobile.yangkeduo.com/login.html", "desc": "List of URLs to be collected, separated by \\n for multiple lines", "type": "string", "exampleValue": "https://mobile.yangkeduo.com/login.html"}, {"id": 1, "name": "inputText_1", "nodeName": "Input Text", "nodeId": 4, "desc": "The text to be entered, such as 'computer' at eBay search box", "type": "string", "exampleValue": "15300412144", "value": "15300412144"}, {"id": 2, "name": "loopText_2", "nodeId": 6, "nodeName": "Loop", "desc": "Text/URL to be entered, multiple lines should be separated by \\n", "type": "string", "exampleValue": "https://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=山崎12年威士忌 700ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利蓝带干邑 1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利干邑XO 1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利干邑VSOP\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利干邑名仕  1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=卡慕干邑XO 1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=百龄坛17年威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=格兰菲迪23年单一麦芽威士忌700ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=格兰菲迪珍藏桶单一纯麦威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=帝王苏格兰调和威士忌15年\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=艾柏迪12年单一麦芽威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利蓝带干邑特醇1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=大摩12年单一麦芽威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=高原骑士雄鹰之翼16年\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=格兰花格10年单一麦芽苏格兰威士忌\n", "value": "https://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=山崎12年威士忌 700ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利蓝带干邑 1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利干邑XO 1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利干邑VSOP\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利干邑名仕  1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=卡慕干邑XO 1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=百龄坛17年威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=格兰菲迪23年单一麦芽威士忌700ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=格兰菲迪珍藏桶单一纯麦威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=帝王苏格兰调和威士忌15年\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=艾柏迪12年单一麦芽威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利蓝带干邑特醇1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=大摩12年单一麦芽威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=高原骑士雄鹰之翼16年\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=格兰花格10年单一麦芽苏格兰威士忌\n"}], "outputParameters": [{"id": 0, "name": "拼多多商品名称", "desc": "", "type": "string", "exampleValue": "格兰昆奇12年700ml单一麦芽入门级苏格兰进口威士忌帝亚吉欧洋酒退货包运费"}, {"id": 1, "name": "拼多多销量", "desc": "", "type": "string", "exampleValue": "已拼1108件"}, {"id": 2, "name": "拼多多补贴数", "desc": "", "type": "string", "exampleValue": "官方补贴15元"}, {"id": 3, "name": "拼多多价格", "desc": "", "type": "string", "exampleValue": "200"}, {"id": 4, "name": "拼多多满减", "desc": "", "type": "string", "exampleValue": "满20减12件9.9折"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [2, 3, 4, 5, 6], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": -1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "url": "https://mobile.yangkeduo.com/login.html", "links": "https://mobile.yangkeduo.com/login.html", "scrollType": 0, "scrollCount": 0}}, {"id": 1, "index": 2, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "url": "https://mobile.yangkeduo.com/login.html", "links": "https://mobile.yangkeduo.com/login.html", "scrollType": 0, "scrollCount": 0}}, {"id": 2, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "<PERSON>lick Element", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"first\"]/div[2]/div[1]", "wait": 0, "scrollType": 0, "scrollCount": 0, "params": []}}, {"id": 3, "index": 4, "parentId": 0, "type": 0, "option": 4, "title": "Input Text", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"phone-number\"]", "wait": 3, "value": "15300412144"}}, {"id": 4, "index": 5, "parentId": 0, "type": 0, "option": 2, "title": "<PERSON>lick Element", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"captcha-btn\"]", "wait": 60, "scrollType": 0, "scrollCount": 0, "params": []}}, {"id": 5, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "Loop", "sequence": [7, 8], "isInLoop": false, "position": 4, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "scrollType": 0, "scrollCount": 0, "loopType": "4", "pathList": "", "textList": "https://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=山崎12年威士忌 700ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利蓝带干邑 1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利干邑XO 1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利干邑VSOP\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利干邑名仕  1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=卡慕干邑XO 1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=百龄坛17年威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=格兰菲迪23年单一麦芽威士忌700ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=格兰菲迪珍藏桶单一纯麦威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=帝王苏格兰调和威士忌15年\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=艾柏迪12年单一麦芽威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=马爹利蓝带干邑特醇1000ml\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=大摩12年单一麦芽威士忌\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=高原骑士雄鹰之翼16年\nhttps://mobile.yangkeduo.com/search_result.html?sort_type=_sales&search_key=格兰花格10年单一麦芽苏格兰威士忌\n", "exitCount": 0, "historyWait": 2}}, {"id": 6, "index": 7, "parentId": 5, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"useLoop": true, "xpath": "", "wait": 10, "url": "https://mobile.yangkeduo.com/login.html", "links": "https://mobile.yangkeduo.com/login.html", "scrollType": 0, "scrollCount": 0}}, {"id": 7, "index": 8, "parentId": 5, "type": 1, "option": 8, "title": "Loop", "sequence": [9, 10], "isInLoop": true, "position": 1, "parameters": {"history": 6, "tabIndex": -1, "useLoop": false, "xpath": "(//*[@id=\"main\"]/div/div/div/div/div/div/div/div/div/div/div/div[1])[position()<22]", "wait": 0, "scrollType": 0, "scrollCount": 0, "loopType": 1, "pathList": "", "textList": "", "exitCount": 0, "historyWait": 2}}, {"id": 8, "index": 9, "parentId": 7, "type": 0, "option": 2, "title": "<PERSON>lick Element", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 6, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[2]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[2]/div[1]", "wait": 5, "scrollType": 0, "scrollCount": 0, "params": [], "loopType": 1}}, {"id": 9, "index": 10, "parentId": 7, "type": 0, "option": 3, "title": "Extract Data", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "拼多多商品名称", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[2]/div[3]/div[1]", "exampleValues": [{"num": 0, "value": "格兰昆奇12年700ml单一麦芽入门级苏格兰进口威士忌帝亚吉欧洋酒退货包运费"}], "default": ""}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "拼多多销量", "desc": "", "relativeXPath": "//div[contains(text(),'已拼')] | //span[contains(text(),'已拼')]", "exampleValues": [{"num": 0, "value": "已拼1108件"}], "default": ""}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "拼多多补贴数", "desc": "", "relativeXPath": "//div[contains(text(),'官方补贴')]", "exampleValues": [{"num": 0, "value": "官方补贴15元"}], "default": ""}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "拼多多价格", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/span[1]/span[2]/span[1]", "exampleValues": [{"num": 0, "value": "200"}], "default": ""}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "拼多多满减", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[2]/div[2]/div[1]", "exampleValues": [{"num": 0, "value": "满20减12件9.9折"}]}]}}]}