{"id": 1, "name": "中国知网", "url": "https://www.cnki.net", "links": "https://navi.cnki.net/knavi/journals/GGYY/detail?uniplatform=NZKPT", "create_time": "7/6/2023, 5:39:11 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "csv", "containJudge": false, "desc": "https://www.cnki.net", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 13, "nodeName": "打开网页", "value": "https://navi.cnki.net/knavi/journals/GGYY/detail?uniplatform=NZKPT", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://navi.cnki.net/knavi/journals/GGYY/detail?uniplatform=NZKPT"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [13, 64], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": -1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.cnki.net", "links": "https://www.cnki.net", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"Ecp_top_login_show\"]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/a[1]/i[1]", "//i[contains(., '登录')]", "id(\"Ecp_top_login_show\")"]}}, {"id": -1, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"login-alert-btn4\")]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[5]/div[1]/div[1]/div[2]/div[1]/div[2]/div[6]/a[3]", "//a[contains(., '校外访问>>')]", "//A[@class='login-alert-btn4']"]}}, {"id": -1, "index": 4, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"o\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "海南大学", "allXPaths": ["/html/body/div[2]/div[1]/div[2]/input[1]", "//input[contains(., '')]", "id(\"o\")"]}}, {"id": -1, "index": 5, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"auto_onmouseover\")]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[1]/div[2]/div[1]/div[1]", "//div[contains(., '海南大学')]", "//DIV[@class='auto_onmouseover']"]}}, {"id": -1, "index": 6, "parentId": 0, "type": 0, "option": 12, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "index": 7, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"submit_button\")]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[1]/div[2]/div[2]", "//div[contains(., '')]", "//DIV[@class='submit_button']"]}}, {"id": -1, "index": 8, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"scanLogin\"]/img[1]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[2]/div[2]/div[1]/div[2]/p[3]/img[1]", "//img[contains(., '')]"]}}, {"id": -1, "index": 9, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"txt_SearchText\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "政府", "allXPaths": ["/html/body/div[2]/div[2]/div[1]/div[1]/input[1]", "//input[contains(., '')]", "id(\"txt_SearchText\")", "//INPUT[@class='search-input']", "//INPUT[@name='txt_SearchText']"]}}, {"id": -1, "index": 10, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"txt_1_value1\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "中国工业经济", "allXPaths": ["/html/body/div[1]/div[5]/div[1]/div[1]/div[2]/input[1]", "//input[contains(., '')]", "id(\"txt_1_value1\")", "//INPUT[@class='rekeyword']", "//INPUT[@name='txt_1_value1']"]}}, {"id": -1, "index": 11, "parentId": 0, "type": 0, "option": 12, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "index": 12, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"submit_button\")]", "wait": 1, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[1]/div[2]/div[2]", "//div[contains(., '')]", "//DIV[@class='submit_button']"]}}, {"id": 1, "index": 13, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 5, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "about:blank", "links": "https://navi.cnki.net/knavi/journals/GGYY/detail?uniplatform=NZKPT", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": "", "waitType": "0"}}, {"id": -1, "index": 14, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"naviSearch\"]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[2]/div[1]/div[2]/a[2]", "//a[contains(., '出版物检索')]", "id(\"naviSearch\")"]}}, {"id": -1, "index": 15, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"txt_1_value1\"]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "中国工业经济", "allXPaths": ["/html/body/div[1]/div[5]/div[1]/div[1]/div[2]/input[1]", "//input[contains(., '')]", "id(\"txt_1_value1\")", "//INPUT[@class='rekeyword']", "//INPUT[@name='txt_1_value1']"]}}, {"id": -1, "index": 16, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"btnSearch\"]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[5]/div[1]/div[1]/div[2]/input[2]", "//input[contains(., '')]", "id(\"btnSearch\")", "//INPUT[@class='researchbtn']"]}}, {"id": -1, "index": 17, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"searchResult\"]/dl[1]/dd[1]/div[2]/h1[1]/a[1]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[1]/div[2]/div[2]/div[1]/dl[1]/dd[1]/div[2]/h1[1]/a[1]", "//a[contains(., '')]"]}}, {"id": -1, "index": 18, "parentId": 0, "type": 1, "option": 8, "title": "循环点击-年份", "sequence": [19], "isInLoop": false, "position": 10, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"2023_Year_Issue\"]/dt[1]/em[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[3]/div[1]/div[1]/div[1]/div[1]/dl[1]/dt[1]/em[1]", "//em[contains(., '2023')]"]}}, {"id": -1, "index": 19, "parentId": 11, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"2023_Year_Issue\"]/dt[1]/em[1]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[3]/div[1]/div[1]/div[1]/div[1]/dl[1]/dt[1]/em[1]", "//em[contains(., '2023')]"], "loopType": 0}}, {"id": -1, "index": 20, "parentId": 11, "type": 1, "option": 8, "title": "循环点击-每期", "sequence": [21], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"yq202305\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 1, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[3]/div[1]/div[1]/div[1]/div[1]/dl[1]/dd[1]/a[1]", "//a[contains(., 'No.05')]", "id(\"yq202305\")", "//A[@class='current']"]}}, {"id": -1, "index": 21, "parentId": 14, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"yq202305\"]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[3]/div[1]/div[1]/div[1]/div[1]/dl[1]/dd[1]/a[1]", "//a[contains(., 'No.05')]", "id(\"yq202305\")", "//A[@class='current']"]}}, {"id": -1, "index": 22, "parentId": 13, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "relativeXPath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]", "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]", "//div[contains(., '国')]"], "exampleValues": [{"num": 0, "value": "\n        国民经济\n        \n        \n            共同富裕与重塑中国经济循环——政治经济学的理论逻辑与经验证据\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            乔晓楠;李欣;蒲佩芝;\n            5-23\n        \n        \n        \n            全球化与通货膨胀\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            谭小芬;王欣康;杨雅涵;\n            24-42\n        \n    "}], "unique_index": "jzmddzwwf8gljphah4h", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数2_文本", "desc": "", "relativeXPath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[2]", "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[2]", "//div[contains(., '产')]"], "exampleValues": [{"num": 0, "value": "\n        产业经济\n        \n        \n            本地化创新能力、区域创新高地与产业地理梯度演化路径\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            郑江淮;师磊;\n            43-60\n        \n        \n        \n            数字化转型、出口增长与低加成率陷阱\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            戴翔;马皓巍;\n            61-79\n        \n        \n        \n            数字经济的碳减排效应：理论分析与经验证据\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            杨刚强;王海森;范恒山;岳子洋;\n            80-98\n        \n        \n        \n            地方政府环境规制与经济高质量发展\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            贾俊雪;罗理恒;顾嘉;\n            99-117\n        \n    "}], "unique_index": "0g4wnng3aj9ljphah4h", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数3_文本", "desc": "", "relativeXPath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[3]", "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[3]", "//div[contains(., '工')]"], "exampleValues": [{"num": 0, "value": "\n        工商管理\n        \n        \n            数字化转型、产业链供应链韧性与企业生产率\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            陶锋;王欣然;徐扬;朱盼;\n            118-136\n        \n        \n        \n            基于专利数据的人工智能就业效应研究——来自中关村企业的微观证据\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            尹志锋;曹爱家;郭家宝;郭冬梅;\n            137-154\n        \n        \n        \n            贸易摩擦下企业出口韧性提升：数字化转型的作用\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            张鹏杨;刘维刚;唐宜红;\n            155-173\n        \n    "}], "unique_index": "k4smnlhu26ljphah4h", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数4_文本", "desc": "", "relativeXPath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]", "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]", "//div[contains(., '')]"], "exampleValues": [{"num": 0, "value": "\n    \n        国民经济\n        \n        \n            共同富裕与重塑中国经济循环——政治经济学的理论逻辑与经验证据\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            乔晓楠;李欣;蒲佩芝;\n            5-23\n        \n        \n        \n            全球化与通货膨胀\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            谭小芬;王欣康;杨雅涵;\n            24-42\n        \n    \n    \n        产业经济\n        \n        \n            本地化创新能力、区域创新高地与产业地理梯度演化路径\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            郑江淮;师磊;\n            43-60\n        \n        \n        \n            数字化转型、出口增长与低加成率陷阱\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            戴翔;马皓巍;\n            61-79\n        \n        \n        \n            数字经济的碳减排效应：理论分析与经验证据\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            杨刚强;王海森;范恒山;岳子洋;\n            80-98\n        \n        \n        \n            地方政府环境规制与经济高质量发展\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            贾俊雪;罗理恒;顾嘉;\n            99-117\n        \n    \n    \n        工商管理\n        \n        \n            数字化转型、产业链供应链韧性与企业生产率\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            陶锋;王欣然;徐扬;朱盼;\n            118-136\n        \n        \n        \n            基于专利数据的人工智能就业效应研究——来自中关村企业的微观证据\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            尹志锋;曹爱家;郭家宝;郭冬梅;\n            137-154\n        \n        \n        \n            贸易摩擦下企业出口韧性提升：数字化转型的作用\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            张鹏杨;刘维刚;唐宜红;\n            155-173\n        \n    \n    \n        案例研究\n        \n        \n            AI能力如何助推企业实现价值共创——基于企业与客户间互动的探索性案例研究\n                        免费\n\n                \n\n            \n\n            增强出版\n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            李树文;罗瑾琏;张志菲;\n            174-192\n        \n    \n    \n        \n        \n        \n            光华管理学院\n                        免费\n\n                \n\n            \n\n            \n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            \n            2\n        \n        \n        \n            《中國工業經濟》征稿启事\n                        免费\n\n                \n\n            \n\n            \n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            \n            193\n        \n        \n        \n            2023《中國工業經濟》订阅\n                        免费\n\n                \n\n            \n\n            \n            \n\n            \n\n            \n\n            \n\n            \n\n        \n            \n                \n\n                \n                    \n                    \n                        \n                    \n                \n\n                \n                    \n                        \n                        \n                    \n                \n\n            \n            \n            194\n        \n    \n\n    \n"}], "unique_index": "pi21sly4dmrljphah4h", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 23, "parentId": 13, "type": 1, "option": 8, "title": "循环", "sequence": [26, 24, 25], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"CataLogContent\"]/div[1]/div[1]/dt[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]/dt[1]", "//dt[contains(., '国民经济')]", "//DT[@class='tit']"]}}, {"id": -1, "index": 24, "parentId": 15, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"CataLogContent\"]/div[1]/div[1]/dt[1]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]/dt[1]", "//dt[contains(., '国民经济')]", "//DT[@class='tit']"]}}, {"id": -1, "index": 25, "parentId": 15, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "", "waitTime": 0, "recordASField": 0}}, {"id": -1, "index": 26, "parentId": 15, "type": 2, "option": 9, "title": "判断条件", "sequence": [27, 28], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 16, "index": 27, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 16, "index": 28, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 29, "parentId": 19, "type": 2, "option": 9, "title": "条件分支", "sequence": [30, 31], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 21, "index": 30, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 21, "index": 31, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 32, "parentId": 19, "type": 0, "option": 12, "title": "条件分支", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "index": 33, "parentId": 13, "type": 2, "option": 9, "title": "判断条件", "sequence": [34, 35], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 16, "index": 34, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 16, "index": 35, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 36, "parentId": 16, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]/dt[1]", "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]/dt[1]", "//dt[contains(., '国民经济')]", "//DT[@class='tit']"], "exampleValues": [{"num": 0, "value": "国民经济"}], "unique_index": "emdw0gr1l7rljphnok8", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 37, "parentId": 13, "type": 0, "option": 12, "title": "条件分支", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "index": 38, "parentId": 13, "type": 0, "option": 12, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "index": 39, "parentId": 13, "type": 1, "option": 8, "title": "循环点击-每个主题-判断是否为空", "sequence": [44], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div/dt[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": "0", "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 40, "parentId": 15, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div/dt[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": "", "loopType": 1}}, {"id": -1, "index": 41, "parentId": 15, "type": 2, "option": 9, "title": "判断条件", "sequence": [42, 43], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 17, "index": 42, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "5", "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 17, "index": 43, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 44, "parentId": 16, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "自定义参数_0", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "if (arguments[0] && arguments[0].textContent.trim() === '') {\n    return; // 如果元素没有文字内容，直接跳出循环\n  }", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 45, "parentId": 14, "type": 1, "option": 8, "title": "循环点击-每一论文", "sequence": [46], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"CataLogContent\"]/div[1]/div/dd/span[1]/a", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": "1", "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]/dd[1]/span[1]/a[1]", "//a[contains(., '共同富裕与重塑中国经')]"]}}, {"id": -1, "index": 46, "parentId": 16, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"CataLogContent\"]/div[1]/div[1]/dd[1]/span[1]/a[1]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]/dd[1]/span[1]/a[1]", "//a[contains(., '共同富裕与重塑中国经')]"], "loopType": 0}}, {"id": -1, "index": 47, "parentId": 17, "type": 1, "option": 8, "title": "循环-获取是否为核心期刊", "sequence": [48], "isInLoop": true, "position": 1, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/a", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '北大核心')]", "//A[@class='type']"]}}, {"id": -1, "index": 48, "parentId": 19, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "核心", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "北大核心"}, {"num": 1, "value": "CSSCI"}, {"num": 2, "value": "AMI"}], "unique_index": "6nyf3qm9iqsljpj5thu", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": -1, "index": 49, "parentId": 13, "type": 1, "option": 8, "title": "循环", "sequence": [50, 51], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div/dt[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 50, "parentId": 15, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div/dt[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": "", "loopType": 1}}, {"id": -1, "index": 51, "parentId": 15, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "自定义参数_0", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 52, "parentId": 17, "type": 1, "option": 8, "title": "循环", "sequence": [53], "isInLoop": true, "position": 1, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/a", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '北大核心')]", "//A[@class='type']"]}}, {"id": -1, "index": 53, "parentId": 19, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "核心", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "北大核心"}, {"num": 1, "value": "CSSCI"}, {"num": 2, "value": "AMI"}], "unique_index": "h5gqgj43y4sljpjjw95", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 54, "parentId": 17, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": []}}, {"id": -1, "index": 55, "parentId": 17, "type": 1, "option": 8, "title": "循环", "sequence": [56], "isInLoop": true, "position": 1, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/a", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '北大核心')]", "//A[@class='type']"]}}, {"id": -1, "index": 56, "parentId": 19, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "北大核心"}, {"num": 1, "value": "CSSCI"}, {"num": 2, "value": "AMI"}], "unique_index": "7k82xvtgb9sljpjzzzx", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 57, "parentId": 17, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "核心", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '中国工业经济.')]", "//DIV[@class='top-tip']"], "exampleValues": [{"num": 0, "value": "\n中国工业经济.\n          2023(05)北大核心CSSCIAMI\n"}], "unique_index": "gccht9bap7jljpk1ka7", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "标题", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h1[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h1[1]", "//h1[contains(., '共同富裕与重塑中国经')]"], "exampleValues": [{"num": 0, "value": "共同富裕与重塑中国经济循环——政治经济学的理论逻辑与经验证据\n"}], "unique_index": "cnhrt95zuioljpk29x1", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 3, "relative": false, "name": "被引量", "desc": "", "extractType": 0, "relativeXPath": "/html/body/input[6]", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "作者", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[1]", "//h3[contains(., '乔晓楠1,2xia')]", "id(\"authorpart\")", "//H3[@class='author']"], "exampleValues": [{"num": 0, "value": "\n乔晓楠1,2<PERSON><PERSON><PERSON>_qia<PERSON>@163.com李欣1蒲佩芝1\n"}], "unique_index": "k0l66zzqrcdljpk5bsi", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[2]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[2]", "//h3[contains(., '1. 南开大学经济')]"], "exampleValues": [{"num": 0, "value": "\n1. 南开大学经济学院2. 南开大学中国特色社会主义经济建设协同创新中心\n"}], "unique_index": "tewfehthkdlljpk67e5", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 8, "index": 58, "parentId": 6, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"pdfDown\"]", "wait": 5, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[9]/div[2]/ul[1]/li[4]/a[1]", "//a[contains(., 'PDF下载')]", "id(\"pdfDown\")", "//A[@name='pdfDown']"]}}, {"id": -1, "index": 59, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [60], "isInLoop": false, "position": 11, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"CataLogContent\"]/div[1]/div[1]/dt[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]/dt[1]", "//dt[contains(., '国民经济')]", "//DT[@class='tit']"]}}, {"id": -1, "index": 60, "parentId": 12, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"CataLogContent\"]/div[1]/div[1]/dt[1]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]/dt[1]", "//dt[contains(., '国民经济')]", "//DT[@class='tit']"], "loopType": 0}}, {"id": -1, "index": 61, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 11, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 2, "contentType": 0, "relative": false, "name": "参数1_链接地址", "desc": "", "relativeXPath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]/dd[1]/span[1]/a[1]", "allXPaths": ["/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div[1]/dd[1]/span[1]/a[1]", "//a[contains(., '共同富裕与重塑中国经')]"], "exampleValues": [{"num": 0, "value": "https://kns.cnki.net/kcms2/article/abstract?v=QhvK1uXUvSNqVBSIPpskEFbkDwAIXA7AjdTAYVakrhwhT8Sl8lh11WCsvV9cgnt_EPnh3YP5_RUQm4-ZCaGhZFCqoWCCHd9-DbqPsau4T89aju5FJP0ksg==&uniplatform=NZKPT"}], "unique_index": "10ywcr5hod4eljpqc0fq", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 4, "index": 62, "parentId": 2, "type": 1, "option": 8, "title": "循环点击-每期", "sequence": [63, 69], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[2]/div[3]/div[3]/div[1]/div[1]/div[1]/div[1]/dl[1]/dd[1]/a", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 5, "index": 63, "parentId": 4, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[2]/div[2]/div[3]/div[3]/div[1]/div[1]/div[1]/div[1]/dl[1]/dd[1]/a", "wait": 5, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 1}}, {"id": 2, "index": 64, "parentId": 0, "type": 1, "option": 8, "title": "循环点击，年份", "sequence": [65, 62], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[2]/div[3]/div[3]/div[1]/div[1]/div[1]/div[1]/dl/dt[1]/em[1]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 3, "index": 65, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[2]/div[2]/div[3]/div[3]/div[1]/div[1]/div[1]/div[1]/dl/dt[1]/em[1]", "wait": 5, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 1, "waitType": "0"}}, {"id": -1, "index": 66, "parentId": 15, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "", "waitTime": 0, "recordASField": 0}}, {"id": -1, "index": 67, "parentId": 14, "type": 1, "option": 8, "title": "循环", "sequence": [68], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 2, "pathList": "//*[@id=\"CataLogContent\"]/div[1]/div[1]/dd[1]/span[1]/a[1]\n//*[@id=\"CataLogContent\"]/div[1]/div[1]/dd[2]/span[1]/a[1]\n//*[@id=\"CataLogContent\"]/div[1]/div[2]/dd[1]/span[1]/a[1]", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 68, "parentId": 16, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": true, "xpath": "", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 2}}, {"id": 6, "index": 69, "parentId": 4, "type": 1, "option": 8, "title": "循环", "sequence": [70, 58], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div/dd/span[1]/a[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 7, "index": 70, "parentId": 6, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[2]/div[2]/div[3]/div[4]/div[2]/div[2]/dl[1]/div[1]/div/dd/span[1]/a[1]", "wait": 5, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 1}}, {"id": -1, "index": 71, "parentId": 6, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 1, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "window.close()", "waitTime": 0, "recordASField": 0}}]}