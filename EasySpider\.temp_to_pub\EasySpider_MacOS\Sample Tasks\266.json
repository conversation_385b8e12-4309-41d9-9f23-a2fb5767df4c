{"id": 266, "name": "高级搜索-招标采购信息搜索", "url": "https://www.chinabidding.cn/search/searchgj/zbcg?keywords=12", "links": "https://www.chinabidding.cn/search/searchgj/zbcg?keywords=12", "create_time": "12/12/2023, 6:00:52 PM", "update_time": "12/12/2023, 6:08:57 PM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://www.chinabidding.cn/search/searchgj/zbcg?keywords=12", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.chinabidding.cn/search/searchgj/zbcg?keywords=12", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.chinabidding.cn/search/searchgj/zbcg?keywords=12"}, {"id": 1, "name": "loopTimes_循环点击单个元素_1", "nodeId": 2, "nodeName": "循环点击单个元素", "desc": "循环循环点击单个元素执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数1_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "济源市中医院急诊科急救设备采购项目成交结果公告"}, {"id": 1, "name": "参数2_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/public/bidagency/index.html#/infoDetails?fid=1693858951806977"}, {"id": 2, "name": "参数3_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/public/search/searchadvzbxx/test/images/souc_11.jpg"}, {"id": 3, "name": "参数4_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "收藏"}, {"id": 4, "name": "参数5_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://cdn.chinabidding.cn/public/search/searchadvzbxx/test/images/souc_hua_11.jpg"}, {"id": 5, "name": "参数6_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "已收藏"}, {"id": 6, "name": "参数8_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "河南"}, {"id": 7, "name": "参数9_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "医疗卫生"}, {"id": 8, "name": "参数10_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "2023-12-12"}, {"id": 9, "name": "参数11_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "您搜索的关键词包含“”，搜索结果可能有偏差！如果信息没有搜索到，请换成“”关键词再次搜索。"}, {"id": 10, "name": "参数12_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "数字、字母或特殊字符"}, {"id": 11, "name": "参数13_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "文字"}, {"id": 12, "name": "参数14_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\"\"及其后面的字词均被忽略，因为采购与招标网的查询限制在38个汉字以内。"}, {"id": 13, "name": "参数15_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "集团有"}, {"id": 14, "name": "参数16_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/public/2020/img/searchgj/icon_gb.png"}, {"id": 15, "name": "参数17_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "广告"}, {"id": 16, "name": "参数18_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "360织语即时通讯-安全的企业内部沟通工具"}, {"id": 17, "name": "参数19_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "广告"}, {"id": 18, "name": "参数20_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "全国"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.chinabidding.cn/search/searchgj/zbcg?keywords=12", "links": "https://www.chinabidding.cn/search/searchgj/zbcg?keywords=12", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环点击单个元素", "sequence": [4, 3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"pages\"]/a[4]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[11]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/a[4]", "//a[contains(., '后一页')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/div/a[last()-2]"]}}, {"id": 4, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击后一页", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[11]/table[1]/tbody[1]/tr[1]/td[1]/div[2]/a[4]", "//a[contains(., '后一页')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/div/a[last()-2]"]}}, {"id": 3, "index": 4, "parentId": 2, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[11]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[11]/table[1]/tbody[1]/tr[1]/td[1]/table[1]/tbody[1]/tr[1]", "//tr[contains(., '')]", "id(\"resultPrompt\")", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-31]"]}}, {"id": 5, "index": 5, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/td[2]/a[1]", "allXPaths": ["/td[2]/a[1]", "//a[contains(., '济源市中医院急诊科急')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-29]/td[last()-5]/a"], "exampleValues": [{"num": 0, "value": "济源市中医院急诊科急救设备采购项目成交结果公告"}], "unique_index": "/td[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/td[2]/a[1]", "allXPaths": ["/td[2]/a[1]", "//a[contains(., '济源市中医院急诊科急')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-29]/td[last()-5]/a"], "exampleValues": [{"num": 0, "value": "/public/bidagency/index.html#/infoDetails?fid=1693858951806977"}], "unique_index": "/td[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数3_图片地址", "desc": "", "relativeXPath": "/td[3]/img[1]", "allXPaths": ["/td[3]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-29]/td[last()-4]/img"], "exampleValues": [{"num": 0, "value": "/public/search/searchadvzbxx/test/images/souc_11.jpg"}], "unique_index": "/td[3]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/td[3]/span[1]", "allXPaths": ["/td[3]/span[1]", "//span[contains(., '收藏')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-29]/td[last()-4]/span"], "exampleValues": [{"num": 0, "value": "收藏"}], "unique_index": "/td[3]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数5_图片地址", "desc": "", "relativeXPath": "/td[4]/img[1]", "allXPaths": ["/td[4]/img[1]", "//img[contains(., '')]", "//IMG[@class='active']", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-29]/td[last()-3]/img"], "exampleValues": [{"num": 0, "value": "https://cdn.chinabidding.cn/public/search/searchadvzbxx/test/images/souc_hua_11.jpg"}], "unique_index": "/td[4]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数6_文本", "desc": "", "relativeXPath": "/td[4]/span[1]", "allXPaths": ["/td[4]/span[1]", "//span[contains(., '已收藏')]", "//SPAN[@class='active']", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-29]/td[last()-3]/span"], "exampleValues": [{"num": 0, "value": "已收藏"}], "unique_index": "/td[4]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数8_文本", "desc": "", "relativeXPath": "/td[5]", "allXPaths": ["/td[5]", "//td[contains(., '河南')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-29]/td[last()-2]"], "exampleValues": [{"num": 0, "value": "河南"}], "unique_index": "/td[5]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/td[6]", "allXPaths": ["/td[6]", "//td[contains(., '医疗卫生')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-29]/td[last()-1]"], "exampleValues": [{"num": 0, "value": "医疗卫生"}], "unique_index": "/td[6]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/td[7]", "allXPaths": ["/td[7]", "//td[contains(., '2023-12-1')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-29]/td"], "exampleValues": [{"num": 0, "value": "2023-12-12"}], "unique_index": "/td[7]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数11_文本", "desc": "", "relativeXPath": "/td[1]/div[1]/h4[1]", "allXPaths": ["/td[1]/div[1]/h4[1]", "//h4[contains(., '')]", "//H4[@class='promptText']", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-31]/td/div/h4[last()-1]"], "exampleValues": [{"num": 1, "value": "您搜索的关键词包含“”，搜索结果可能有偏差！如果信息没有搜索到，请换成“”关键词再次搜索。"}], "unique_index": "/td[1]/div[1]/h4[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数12_文本", "desc": "", "relativeXPath": "/td[1]/div[1]/h4[1]/span[1]", "allXPaths": ["/td[1]/div[1]/h4[1]/span[1]", "//span[contains(., '数字、字母或特殊字符')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-31]/td/div/h4[last()-1]/span[last()-1]"], "exampleValues": [{"num": 1, "value": "数字、字母或特殊字符"}], "unique_index": "/td[1]/div[1]/h4[1]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数13_文本", "desc": "", "relativeXPath": "/td[1]/div[1]/h4[1]/span[2]", "allXPaths": ["/td[1]/div[1]/h4[1]/span[2]", "//span[contains(., '文字')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-31]/td/div/h4[last()-1]/span"], "exampleValues": [{"num": 1, "value": "文字"}], "unique_index": "/td[1]/div[1]/h4[1]/span[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数14_文本", "desc": "", "relativeXPath": "/td[1]/div[1]/h4[2]", "allXPaths": ["/td[1]/div[1]/h4[2]", "//h4[contains(., '')]", "//H4[@class='promptText2']", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-31]/td/div/h4"], "exampleValues": [{"num": 1, "value": "\"\"及其后面的字词均被忽略，因为采购与招标网的查询限制在38个汉字以内。"}], "unique_index": "/td[1]/div[1]/h4[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数15_文本", "desc": "", "relativeXPath": "/td[1]/div[1]/h4[2]/span[1]", "allXPaths": ["/td[1]/div[1]/h4[2]/span[1]", "//span[contains(., '集团有')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-31]/td/div/h4/span"], "exampleValues": [{"num": 1, "value": "集团有"}], "unique_index": "/td[1]/div[1]/h4[2]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数16_图片地址", "desc": "", "relativeXPath": "/td[1]/div[1]/div[1]/img[1]", "allXPaths": ["/td[1]/div[1]/div[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-31]/td/div/div/img"], "exampleValues": [{"num": 1, "value": "/public/2020/img/searchgj/icon_gb.png"}], "unique_index": "/td[1]/div[1]/div[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数17_文本", "desc": "", "relativeXPath": "/td[2]/div[1]/span[1]", "allXPaths": ["/td[2]/div[1]/span[1]", "//span[contains(., '广告')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-30]/td[last()-4]/div/span[last()-1]"], "exampleValues": [{"num": 2, "value": "广告"}], "unique_index": "/td[2]/div[1]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数18_文本", "desc": "", "relativeXPath": "/td[2]/div[1]/span[2]", "allXPaths": ["/td[2]/div[1]/span[2]", "//span[contains(., '360织语即时通讯-')]", "//SPAN[@class='advTitle']", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-30]/td[last()-4]/div/span"], "exampleValues": [{"num": 2, "value": "360织语即时通讯-安全的企业内部沟通工具"}], "unique_index": "/td[2]/div[1]/span[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数19_文本", "desc": "", "relativeXPath": "/td[3]", "allXPaths": ["/td[3]", "//td[contains(., '广告')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-30]/td[last()-3]"], "exampleValues": [{"num": 2, "value": "广告"}], "unique_index": "/td[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数20_文本", "desc": "", "relativeXPath": "/td[4]", "allXPaths": ["/td[4]", "//td[contains(., '全国')]", "/html/body/div[last()-10]/div[last()-2]/div/table/tbody/tr/td/table/tbody/tr[last()-30]/td[last()-2]"], "exampleValues": [{"num": 2, "value": "全国"}], "unique_index": "/td[4]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}