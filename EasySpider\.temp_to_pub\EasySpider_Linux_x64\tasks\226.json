{"id": 226, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "12/7/2023, 1:27:42 AM", "update_time": "12/7/2023, 1:27:42 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n  \n  \n  \n    \n      \n        京东首页\n        \t\t\t\t\t\t\t\t\t\t\t海外 \t\t\t\t\t\t\t\t\t\t\t\t\t\t \t\t\t\t\t北京上海天津重庆河北山西河南辽宁吉林黑龙江内蒙古江苏山东安徽浙江福建湖北湖南广东广西江西四川海南贵州云南西藏陕西甘肃青海宁夏新疆港澳台湾钓鱼岛海外 \t\t\t\t \t\t\t\t \t\t\t\t   \t\t\t\t  地区专享版本 \t\t\t\t   \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t中國港澳 \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t中國台灣 \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t京东全球 \t\t\t\t\t   \t\t\t\t\t \t\t\t\t   \t\t\t\t \t\t\t\t \t\t\t\t   \t\t\t\t  Available Sites \t\t\t\t   \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t \t\t\t\t\t\tGlobal Site \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t \t\t\t\t\t\tСайт России \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t \t\t\t\t\t\tSitus Indonesia \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t \t\t\t\t\t\tSitio de España \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t\t \t\t\t\t\t\t   \t\t\t\t\t\t  เว็บไซต์ประเทศไทย \t\t\t\t\t\t \t\t\t\t\t \t\t\t\t   \t\t\t\t \t\t\t\t\t\n      \n      \n        你好，请登录  免费注册\n        \n        \n          \n            我的订单\n          \n        \n        \n        \n          \n            我的京东\n          \n          \n        \n        \n        \n          \n            京东会员\n          \n        \n        \n        \n          \n            企业采购\n          \n        \n        \n        \n          \n            客户服务\n          \n          \n        \n        \n        \n          \n            网站导航\n          \n          \n        \n        \n        \n          \n            手机京东\n          \n        \t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n      网站无障碍\n      \n    \n  \n  \n  \n  \n\n  \n  \n    \n    (function (window) {\n      window.data = window.data || {}\n      window.data['cms_header'] = {\n        setting: [{\"NAME\":\"手机\",\"URL\":\"//\",\"ANCHOR\":\"\",\"property\":\"\",\"CUSTOM1\":\"#050505\",\"CUSTOM2\":\"\",\"CUSTOM3\":\"\"}]\n      }\n    })(window)\n    \n    \n    #search-2014 .text {border-color: #050505;}#search-2014 .button {background: #050505;}  京东  手机   全部分类◇            搜索      >0 我的购物车      \n    \n\n    \n\n    \n      \n    顶通组件占位手机分类热门推荐苹果华为荣耀小米vivoOPPO运营商手机卡营业厅充话费配件充电器数据线手机壳贴膜移动电源创意配件热门分类全部手机5G手机苹果 iPhone 11iPhone XS Max华为Mate40 Pro 4Gnova 8 Pro 无充Nova8SE 乐活版Mate 40 RSNova 9P50 Pro 4G华为智选手机荣耀荣耀V30 PRO荣耀30 Pro荣耀X10 Max荣耀Play4 Pro小米小米10Redmi K30vivoS10 ProX60Y70t iQOO 8 iQOO Neo5  iQOO Z3OPPOReno 6OPPO K9 Find X3品牌中国移动中国联通中国电信精选店铺京东通信北京移动广东移动宽带专区50M300M优选品牌Anker罗马仕品胜倍思绿联亿色毕亚兹斯泰克热卖爆款移动电源原装充电器散热背夹扩展坞苹果数据线氮化镓Type C信号放大器手游周边王者荣耀手机散热器吃鸡神器弯头线游戏手柄王座同屏器新奇好物氮化镓PD快充双向快充创意配件配件频道海量配件低至9.包邮机身存储16GB8GB4GB分辨率全高清FHD+高清HD+屏幕尺寸5.0英寸及以下5.0～5.49英寸5.5～5.99英寸6.0～6.24英寸有新机游戏手机手机营业厅手机好店以旧换新企业购热卖推荐人气新品限时特惠配件专区闪魔 苹果13钢化膜 iphone13Promax手机膜mini全屏无边高清抗指纹保护贴膜 苹果13/13Pro【加强版】2片+神器￥19.800度 联想拯救者Y90钢化膜手机膜全屏防蓝光电竞磨砂防反光游戏保护膜防汗防指纹疏油玻璃贴膜 拯救者Y90（AG悦享版）￥99.00摩斯维 适用华为mate40pro手机壳Mate40 Pro保护套真素皮超薄epro全包防摔马特男女 【买家推荐！石墨黑】真皮质感丨贈全屏膜￥59.00倍思 苹果14/13钢化膜iPhone13/13Pro/14手机膜超瓷晶 防摔防指纹高清全屏覆盖全包边保护前贴膜 2片装￥32.90声阔Soundcore超能小彩蛋 LifeP3主动降噪真无线TWS入耳式蓝牙耳机适用苹果/华为/小米手机蓝30H续航版￥349.00\n  \n  \n\n  \n  多品类齐全，轻松购物快多仓直发，极速配送好正品行货，精致服务省天天低价，畅选无忧购物指南购物流程会员介绍生活旅行/团购常见问题大家电联系客服配送方式上门自提211限时达配送服务查询配送费收取标准海外配送支付方式货到付款在线支付分期付款公司转账售后服务售后政策价格保护退款说明返修/退换货取消订单特色服务夺宝岛DIY装机延保服务京东E卡京东通信京鱼座智能关于我们|联系我们|联系客服|合作招商|商家帮助|营销中心|手机京东|友情链接|销售联盟|京东社区|风险监测|隐私政策|京东公益|English Site|Media & IR京公网安备 11000002000088号|京ICP备11041704号|ICP|互联网药品信息服务资格证编号(京)-经营性-2014-0008|新出发京零 字第大120007号互联网出版许可证编号新出网证(京)字150号|出版物经营许可证|网络文化经营许可证京网文[2020]6112-1201号|违法和不良信息举报电话：4006561155Copyright © 2004 - 2023  京东JD.com 版权所有|消费者维权热线：4006067733经营证照|(京)网械平台备字(2018)第00003号|营业执照|增值电信业务经营许可证Global Site|Сайт России|Situs Indonesia|Sitio de España|เว็บไซต์ประเทศไทย京东旗下网站：京东钱包|京东云可信网站信用评估网络警察提醒你诚信网站中国互联网举报中心网络举报APP下载国家知识产权公共服务网\n  \n\n  \n\n  \n  \n  window.pageConfig = window.pageConfig || {}\n  window.pageConfig.o2JSConfig = {\n    useTplInJs: true,\n    pathRule: function (path) {\n      return '//static.360buyimg.com/mtd/pc/cms' + '/floors/' + path + '.min.js'\n    }\n  }\n  \n  seajs.use(['//static.360buyimg.com/mtd/pc/base/1.0.1/channel.js'])\n  \n  seajs.use(['//wl.jd.com/wl.js'])\n  \n  \n  \n  !(function () {\n    var testObject = {}\n    if (!(Object.setPrototypeOf || testObject.__proto__)) {\n      var nativeGetPrototypeOf = Object.getPrototypeOf\n      Object.getPrototypeOf = function (object) {\n        return object.__proto__ || nativeGetPrototypeOf.call(Object, object)\n      }\n    }\n  })()\n  \n  \n  \n  \n  \n  \n    // nerv-create-class 用 'nervjs'\n    window.nervjs = Nerv\n    // nerv-create-class\n    !(function(t,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"nervjs\")):\"function\"==typeof define&&define.amd?define([\"nervjs\"],e):t.NervCreateClass=e(t.nervjs)})(this,(function(t){\"use strict\";function e(t){return t===undefined||null===t}function n(){}function r(t){return\"function\"==typeof t}function o(t){return t===undefined}function i(t,n){for(var r in n)e(n[r])||(t[r]=n[r]);return t}function a(t){for(var e in t){var n=t[e];\"function\"!=typeof n||n.__bound||1===g[e]||((t[e]=n.bind(t)).__bound=!0)}}function c(t,e){void 0===e&&(e={});for(var n=0,r=t.length;n<r;n++){var o=t[n];o.mixins&&c(o.mixins,e);for(var i in o)o.hasOwnProperty(i)&&\"function\"==typeof o[i]&&(e[i]||(e[i]=[])).push(o[i])}return e}function s(t,e){return function(){for(var n,r=arguments,i=this,a=0,c=t.length;a<c;a++){var s=t[a].apply(i,r);e?n=e(n,s):o(s)||(n=s)}return n}}function u(t,e){if(!o(e)){t||(t={});for(var n in e)if(e.hasOwnProperty(n)){if(t.hasOwnProperty(n))throw new Error(\"Mixins return duplicate key \"+n+\" in their return values\");t[n]=e[n]}}return t}function f(t,e,n){var r=o(e[t])?n:n.concat(e[t]);e[t]=\"getDefaultProps\"===t||\"getInitialState\"===t||\"getChildContext\"===t?s(r,u):s(r)}function p(t,e){for(var n in e)if(e.hasOwnProperty(n)){var o=e[n],i=\"getDefaultProps\"===n?t:t.prototype;r(o[0])?f(n,i,o):i[n]=o}}var h;!(function(t){t[t.Text=1]=\"Text\",t[t.Node=2]=\"Node\",t[t.Composite=4]=\"Composite\",t[t.Void=16]=\"Void\",t[t.Portal=32]=\"Portal\"})(h||(h={}));var l=(function(){var t;if(void 0!==l)t=l;else if(\"undefined\"!=typeof self)t=self;else try{t=Function(\"return this\")()}catch(e){throw new Error(\"global object is unavailable in this environment\")}return t})(),d=\"undefined\"!=typeof window,v={createElement:n,createElementNS:n,createTextNode:n},m=d?document:v;d&&navigator.platform&&/mac/i.test(navigator.platform)&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent),r(m.createAttributeNS);Object.is=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!==t&&e!==e};var y=function(){this.cache=[],this.size=0};y.prototype.set=function(t,e){var n=this,r=this.cache.length;if(!r)return this.cache.push({k:t,v:e}),void(this.size+=1);for(var o=0;o<r;o++){var i=n.cache[o];if(i.k===t)return void(i.v=e)}this.cache.push({k:t,v:e}),this.size+=1},y.prototype.get=function(t){var e=this,n=this.cache.length;if(n)for(var r=0;r<n;r++){var o=e.cache[r];if(o.k===t)return o.v}},y.prototype.has=function(t){var e=this,n=this.cache.length;if(!n)return!1;for(var r=0;r<n;r++)if(e.cache[r].k===t)return!0;return!1},y.prototype[\"delete\"]=function(t){for(var e=this,n=this.cache.length,r=0;r<n;r++)if(e.cache[r].k===t)return e.cache.splice(r,1),e.size-=1,!0;return!1},y.prototype.clear=function(){var t=this,e=this.cache.length;if(this.size=0,e)for(;e;)t.cache.pop(),e--};var g={constructor:1,render:1,shouldComponentUpdate:1,componentWillUpdate:1,componentWillReceiveProps:1,componentDidUpdate:1,componentWillMount:1,componentDidMount:1,componentWillUnmount:1,componentDidUnmount:1,getDerivedStateFromProps:1};return function(e){var n=(function(t){function e(e,n){t.call(this,e,n),a(this),this.getInitialState&&(this.state=this.getInitialState())}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.replaceState=function(t,e){this.setState(t,e)},e.prototype.isMounted=function(){return!this.dom},e})(t.Component);return n.displayName=e.displayName||\"Component\",n.propTypes=e.propTypes,n.mixins=e.mixins&&c(e.mixins),n.getDefaultProps=e.getDefaultProps,i(n.prototype,e),e.statics&&i(n,e.statics),e.mixins&&p(n,c(e.mixins)),n.defaultProps=o(n.getDefaultProps)?undefined:n.getDefaultProps(),n}}));\n    // 节流\n    function throttle (func, wait) {\n      var timeout, context, args, result;\n      var previous = 0;\n\n      var later = function() {\n        previous = +new Date();\n        timeout = null;\n        func.apply(context, args)\n      };\n\n      var throttled = function() {\n        var now = +new Date();\n        var remaining = wait - (now - previous);\n        context = this;\n        args = arguments;\n        if (remaining <= 0 || remaining > wait) {\n          if (timeout) {\n            clearTimeout(timeout);\n            timeout = null;\n          }\n          previous = now;\n          func.apply(context, args);\n        } else if (!timeout) {\n          timeout = setTimeout(later, remaining);\n        }\n      };\n      return throttled;\n    }\n    // Context\n    var LingAtomScrollContext = Nerv.createContext({\n      scrollTop: 0,\n      windowHeight: 0,\n      bodyHeight: 0\n    })\n  \n  \n  \n  \n    \n  \n    \n  \n    \n  \n    \n  \n    \n  \n    \n  \n  \n    function genComponentElement (type, props, children) {\n      \n      return Nerv.createElement(\n        Lc[type] ? Lc[type].component : 'div',\n        props,\n        children\n      )\n      \n    }\n  \n  \n    window.Lc = window.Lc || {}\n    Lc['Page'] = { component: 'div' }\n  \n  \n    function EventEmitter () {\n      var eventEmitterPool = {}\n      var eventEmitter = {\n        on: function (eventName, handler) {\n          eventEmitterPool[eventName] = eventEmitterPool[eventName] || []\n          eventEmitterPool[eventName].push(handler)\n        },\n        trigger: function (eventName) {\n          var handlers = eventEmitterPool[eventName] || []\n          var args = Array.prototype.slice.call(arguments, 1)\n          for (var i = 0; i < handlers.length; i++) {\n            handlers[i].apply(this, args)\n          }\n        },\n        off: function (eventName, handler) {\n          if (!eventEmitterPool[eventName]) return\n          if (!handler) {\n            eventEmitterPool[eventName] = []\n            return\n          }\n          var handlers = eventEmitterPool[eventName] || []\n          for (var i = 0; i < handlers.length; i++) {\n            if (handlers[i] === handler) {\n              eventEmitterPool[eventName].splice(i, 1)\n              break\n            }\n          }\n        }\n      }\n      return eventEmitter\n    }\n    var eventEmitter = EventEmitter()\n\n    !function () {\n      var downloadedBundles = [\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd5a6__5ee34e5afc685b3ecdd0f087.js\",\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd5a7__5ee34e58fc685b3ecdd0f078.js\",\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd5a8__5ee34e5afc685b3ecdd0f08a.js\",\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd5a9__5fa90cbc7e292cb70199df98.js\",\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd594__5ee34e59fc685b3ecdd0f07e.js\",\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd5aa__5ee34e5bfc685b3ecdd0f094.js\"]\n      var body = document.body || document.documentElement\n      var Wrap = function (props) {\n        return props.children\n      }\n      // Lazyload Component\n      var LazyLoadComponent = NervCreateClass({\n        // loading loaded\n        status: '',\n        getInitialState: function () {\n          return {\n            status: 'initial'\n          }\n        },\n        updateOffsetTop: function () {\n          if (this.state.status !== 'initial') return\n          var node = Nerv.findDOMNode(this)\n          var rect = node.getBoundingClientRect()\n          var winHeight = this.props.__context.windowHeight\n          var canLoad = rect.top < winHeight * 1.5 && rect.top + rect.height > -winHeight * 0.5\n          if (canLoad) {\n            this.downloadScript()\n          }\n        },\n        delayLoaded: function () {\n          var that = this\n          setTimeout(function () {\n            that.setState({ status: 'loaded' })\n          }, 500)\n        },\n        downloadScript: function () {\n          var that = this\n          var myItem = this.props.item\n          if (!myItem.reactBundle || downloadedBundles.indexOf(myItem.reactBundle) !== -1) {\n            if (Lc[myItem.type]) {\n              this.setState({ status: 'loading' })\n              this.delayLoaded()\n            } else {\n              eventEmitter.on(myItem.type, function () {\n                that.setState({ status: 'loading' })\n                that.delayLoaded()\n              })\n            }\n            return\n          }\n          myItem.reactBundle = myItem.reactBundle && myItem.reactBundle.replace('storage.jd.com', 'storage.360buyimg.com')\n          downloadedBundles.push(myItem.reactBundle)\n          var element = document.createElement('script')\n          element.src = myItem.reactBundle\n          element.charset = 'UTF-8'\n          body.appendChild(element)\n          element.onload = function () {\n            that.setState({ status: 'loading' })\n            that.delayLoaded()\n            eventEmitter.trigger(myItem.type)\n          }\n        },\n        componentDidMount: function () {\n          this.updateOffsetTop()\n        },\n        componentWillReceiveProps: function (nextProps) {\n          this.updateOffsetTop()\n        },\n        shouldComponentUpdate: function (nextProps, nextState) {\n          return this.state.status !== nextState.status\n        },\n        componentDidCatch: function (error, info) {\n          // console.log(error)\n          // 上报组件渲染错误\n          // @see //talos30011-prelb.o2athena.svc.n.jd.local/api-docs#null%2Fpaths%2F~1report%2Fpost\n          var xhr = new XMLHttpRequest()\n          xhr.open('POST', 'https://atom-log.3.cn/report', true)\n          xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded')\n          xhr.send('platform=h5&url=' + location.href + '&cname=' + this.props.item.type + '&project=' + globalData.projectId)\n        },\n        getWrappedElement: function (extraProps) {\n          if (this.WrappedElement) {\n            return this.WrappedElement\n          }\n          var item = this.props.item\n          var children = item.props.content || this.props.children || null\n          var WrappedElement = genComponentElement(item.type, Object.assign({}, item.props, extraProps), children)\n          this.WrappedElement = WrappedElement\n          return WrappedElement\n        },\n        render: function () {\n          var that = this\n          var status = this.state.status\n          // if (status === 'initial' || status === 'loading') {\n          if (status === 'initial') {\n            return Nerv.createElement(\n              'div',\n              {\n                style: {\n                  padding: '10px',\n                  backgroundClip: 'content-box',\n                  minHeight: '256px',\n                  height: '100%',\n                  backgroundColor: '#e3e4e5'\n                }\n              }\n              // status === 'loading' ?\n              //   Nerv.createElement(\n              //     'div',\n              //     {\n              //       style: {\n              //         width: 0,\n              //         height: 0,\n              //         overflow: 'hidden'\n              //       }\n              //     },\n              //     that.getWrappedElement()\n              //   ) : null\n            )\n          } else {\n            // return that.getWrappedElement()\n            // 缺点是多了一层 div\n            this.cc = this.cc || that.getWrappedElement()\n            return Nerv.createElement(\n              'div',\n              status === 'loading' ? {\n                style: {\n                  padding: '10px',\n                  backgroundClip: 'content-box',\n                  minHeight: '256px',\n                  height: '100%',\n                  backgroundColor: '#e3e4e5'\n                }\n              } : {},\n              this.cc\n            )\n          }\n        }\n      })\n\n      function createTree (tree) {\n        tree = tree.filter(function (item) {\n          return item.isShow === undefined || item.isShow === true\n        })\n\n        return tree.map(function (item) {\n          var hasChildren = item.childrens && item.childrens.length\n          hasChildren && (item.props.children = createTree(item.childrens))\n          if (hasChildren) {\n            return genComponentElement(item.type, item.props, item.props.content)\n          } else {\n            return Nerv.createElement(\n              LingAtomScrollContext.Consumer,\n              {},\n              function (context) {\n                return Nerv.createElement(\n                  LazyLoadComponent,\n                  {\n                    item: item,\n                    __context: context\n                  }\n                )\n              }\n            )\n          }\n        })\n      }\n\n      var App = NervCreateClass({\n        getInitialState: function () {\n          return {\n            scrollTop: 0,\n            windowHeight: window.innerHeight,\n            bodyHeight: document.body.clientHeight\n          }\n        },\n        componentDidMount: function () {\n          var that = this\n          window.onscroll = throttle(function () {\n            var bodyScrollHeight = document.documentElement.scrollTop || document.body.scrollTop\n            that.setState({\n              scrollTop: bodyScrollHeight\n            })\n          }, 200)\n          window.onresize = function () {\n            var windowInnerHeight = window.innerHeight\n            if (windowInnerHeight === that.state.windowHeight) return\n            that.setState({\n              windowHeight: windowInnerHeight\n            })\n          }\n          function onElementHeightChange (elm, callback) {\n            var lastHeight = elm.clientHeight\n            var newHeight\n            (function run () {\n              newHeight = elm.clientHeight\n              if (lastHeight !== newHeight) {\n                callback(newHeight)\n              }\n              lastHeight = newHeight\n              if (elm.onElementHeightChangeTimer) {\n                clearTimeout(elm.onElementHeightChangeTimer)\n              }\n              elm.onElementHeightChangeTimer = setTimeout(run, 200)\n            })()\n          }\n          onElementHeightChange(document.body, function (h) {\n            that.setState({ bodyHeight: h })\n          })\n        },\n        render: function () {\n          return Nerv.createElement(\n            LingAtomScrollContext.Provider,\n            {\n              value: this.state\n            },\n            createTree(window.o2PageConfig.data)\n          )\n        }\n      })\n\n      Nerv.render(\n        Nerv.createElement(App),\n        document.querySelector('#app')\n      )\n    }()\n  \n  \n\n\n\n\n     ✖ ✍操作台（点此拖动，左上角调整大小）  \n              ● 已选中1个元素，您可以:\n                  确认采集    取消选择  Path: /html/body  \n"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环点击每个元素", "sequence": [3, 4], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div/a[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": "", "loopType": 1}}, {"id": 4, "index": 4, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": ["/html/body", "//body[contains(., '')]", "/html/body"], "exampleValues": [{"num": 0, "value": "\n  \n  \n  \n    \n      \n        京东首页\n        \t\t\t\t\t\t\t\t\t\t\t海外 \t\t\t\t\t\t\t\t\t\t\t\t\t\t \t\t\t\t\t北京上海天津重庆河北山西河南辽宁吉林黑龙江内蒙古江苏山东安徽浙江福建湖北湖南广东广西江西四川海南贵州云南西藏陕西甘肃青海宁夏新疆港澳台湾钓鱼岛海外 \t\t\t\t \t\t\t\t \t\t\t\t   \t\t\t\t  地区专享版本 \t\t\t\t   \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t中國港澳 \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t中國台灣 \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t京东全球 \t\t\t\t\t   \t\t\t\t\t \t\t\t\t   \t\t\t\t \t\t\t\t \t\t\t\t   \t\t\t\t  Available Sites \t\t\t\t   \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t \t\t\t\t\t\tGlobal Site \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t \t\t\t\t\t\tСайт России \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t \t\t\t\t\t\tSitus Indonesia \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t   \t\t\t\t\t\t \t\t\t\t\t\tSitio de España \t\t\t\t\t   \t\t\t\t\t \t\t\t\t\t \t\t\t\t\t\t \t\t\t\t\t\t   \t\t\t\t\t\t  เว็บไซต์ประเทศไทย \t\t\t\t\t\t \t\t\t\t\t \t\t\t\t   \t\t\t\t \t\t\t\t\t\n      \n      \n        你好，请登录  免费注册\n        \n        \n          \n            我的订单\n          \n        \n        \n        \n          \n            我的京东\n          \n          \n        \n        \n        \n          \n            京东会员\n          \n        \n        \n        \n          \n            企业采购\n          \n        \n        \n        \n          \n            客户服务\n          \n          \n        \n        \n        \n          \n            网站导航\n          \n          \n        \n        \n        \n          \n            手机京东\n          \n        \t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n      网站无障碍\n      \n    \n  \n  \n  \n  \n\n  \n  \n    \n    (function (window) {\n      window.data = window.data || {}\n      window.data['cms_header'] = {\n        setting: [{\"NAME\":\"手机\",\"URL\":\"//\",\"ANCHOR\":\"\",\"property\":\"\",\"CUSTOM1\":\"#050505\",\"CUSTOM2\":\"\",\"CUSTOM3\":\"\"}]\n      }\n    })(window)\n    \n    \n    #search-2014 .text {border-color: #050505;}#search-2014 .button {background: #050505;}  京东  手机   全部分类◇            搜索      >0 我的购物车      \n    \n\n    \n\n    \n      \n    顶通组件占位手机分类热门推荐苹果华为荣耀小米vivoOPPO运营商手机卡营业厅充话费配件充电器数据线手机壳贴膜移动电源创意配件热门分类全部手机5G手机苹果 iPhone 11iPhone XS Max华为Mate40 Pro 4Gnova 8 Pro 无充Nova8SE 乐活版Mate 40 RSNova 9P50 Pro 4G华为智选手机荣耀荣耀V30 PRO荣耀30 Pro荣耀X10 Max荣耀Play4 Pro小米小米10Redmi K30vivoS10 ProX60Y70t iQOO 8 iQOO Neo5  iQOO Z3OPPOReno 6OPPO K9 Find X3品牌中国移动中国联通中国电信精选店铺京东通信北京移动广东移动宽带专区50M300M优选品牌Anker罗马仕品胜倍思绿联亿色毕亚兹斯泰克热卖爆款移动电源原装充电器散热背夹扩展坞苹果数据线氮化镓Type C信号放大器手游周边王者荣耀手机散热器吃鸡神器弯头线游戏手柄王座同屏器新奇好物氮化镓PD快充双向快充创意配件配件频道海量配件低至9.包邮机身存储16GB8GB4GB分辨率全高清FHD+高清HD+屏幕尺寸5.0英寸及以下5.0～5.49英寸5.5～5.99英寸6.0～6.24英寸有新机游戏手机手机营业厅手机好店以旧换新企业购热卖推荐人气新品限时特惠配件专区闪魔 苹果13钢化膜 iphone13Promax手机膜mini全屏无边高清抗指纹保护贴膜 苹果13/13Pro【加强版】2片+神器￥19.800度 联想拯救者Y90钢化膜手机膜全屏防蓝光电竞磨砂防反光游戏保护膜防汗防指纹疏油玻璃贴膜 拯救者Y90（AG悦享版）￥99.00摩斯维 适用华为mate40pro手机壳Mate40 Pro保护套真素皮超薄epro全包防摔马特男女 【买家推荐！石墨黑】真皮质感丨贈全屏膜￥59.00倍思 苹果14/13钢化膜iPhone13/13Pro/14手机膜超瓷晶 防摔防指纹高清全屏覆盖全包边保护前贴膜 2片装￥32.90声阔Soundcore超能小彩蛋 LifeP3主动降噪真无线TWS入耳式蓝牙耳机适用苹果/华为/小米手机蓝30H续航版￥349.00\n  \n  \n\n  \n  多品类齐全，轻松购物快多仓直发，极速配送好正品行货，精致服务省天天低价，畅选无忧购物指南购物流程会员介绍生活旅行/团购常见问题大家电联系客服配送方式上门自提211限时达配送服务查询配送费收取标准海外配送支付方式货到付款在线支付分期付款公司转账售后服务售后政策价格保护退款说明返修/退换货取消订单特色服务夺宝岛DIY装机延保服务京东E卡京东通信京鱼座智能关于我们|联系我们|联系客服|合作招商|商家帮助|营销中心|手机京东|友情链接|销售联盟|京东社区|风险监测|隐私政策|京东公益|English Site|Media & IR京公网安备 11000002000088号|京ICP备11041704号|ICP|互联网药品信息服务资格证编号(京)-经营性-2014-0008|新出发京零 字第大120007号互联网出版许可证编号新出网证(京)字150号|出版物经营许可证|网络文化经营许可证京网文[2020]6112-1201号|违法和不良信息举报电话：4006561155Copyright © 2004 - 2023  京东JD.com 版权所有|消费者维权热线：4006067733经营证照|(京)网械平台备字(2018)第00003号|营业执照|增值电信业务经营许可证Global Site|Сайт России|Situs Indonesia|Sitio de España|เว็บไซต์ประเทศไทย京东旗下网站：京东钱包|京东云可信网站信用评估网络警察提醒你诚信网站中国互联网举报中心网络举报APP下载国家知识产权公共服务网\n  \n\n  \n\n  \n  \n  window.pageConfig = window.pageConfig || {}\n  window.pageConfig.o2JSConfig = {\n    useTplInJs: true,\n    pathRule: function (path) {\n      return '//static.360buyimg.com/mtd/pc/cms' + '/floors/' + path + '.min.js'\n    }\n  }\n  \n  seajs.use(['//static.360buyimg.com/mtd/pc/base/1.0.1/channel.js'])\n  \n  seajs.use(['//wl.jd.com/wl.js'])\n  \n  \n  \n  !(function () {\n    var testObject = {}\n    if (!(Object.setPrototypeOf || testObject.__proto__)) {\n      var nativeGetPrototypeOf = Object.getPrototypeOf\n      Object.getPrototypeOf = function (object) {\n        return object.__proto__ || nativeGetPrototypeOf.call(Object, object)\n      }\n    }\n  })()\n  \n  \n  \n  \n  \n  \n    // nerv-create-class 用 'nervjs'\n    window.nervjs = Nerv\n    // nerv-create-class\n    !(function(t,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"nervjs\")):\"function\"==typeof define&&define.amd?define([\"nervjs\"],e):t.NervCreateClass=e(t.nervjs)})(this,(function(t){\"use strict\";function e(t){return t===undefined||null===t}function n(){}function r(t){return\"function\"==typeof t}function o(t){return t===undefined}function i(t,n){for(var r in n)e(n[r])||(t[r]=n[r]);return t}function a(t){for(var e in t){var n=t[e];\"function\"!=typeof n||n.__bound||1===g[e]||((t[e]=n.bind(t)).__bound=!0)}}function c(t,e){void 0===e&&(e={});for(var n=0,r=t.length;n<r;n++){var o=t[n];o.mixins&&c(o.mixins,e);for(var i in o)o.hasOwnProperty(i)&&\"function\"==typeof o[i]&&(e[i]||(e[i]=[])).push(o[i])}return e}function s(t,e){return function(){for(var n,r=arguments,i=this,a=0,c=t.length;a<c;a++){var s=t[a].apply(i,r);e?n=e(n,s):o(s)||(n=s)}return n}}function u(t,e){if(!o(e)){t||(t={});for(var n in e)if(e.hasOwnProperty(n)){if(t.hasOwnProperty(n))throw new Error(\"Mixins return duplicate key \"+n+\" in their return values\");t[n]=e[n]}}return t}function f(t,e,n){var r=o(e[t])?n:n.concat(e[t]);e[t]=\"getDefaultProps\"===t||\"getInitialState\"===t||\"getChildContext\"===t?s(r,u):s(r)}function p(t,e){for(var n in e)if(e.hasOwnProperty(n)){var o=e[n],i=\"getDefaultProps\"===n?t:t.prototype;r(o[0])?f(n,i,o):i[n]=o}}var h;!(function(t){t[t.Text=1]=\"Text\",t[t.Node=2]=\"Node\",t[t.Composite=4]=\"Composite\",t[t.Void=16]=\"Void\",t[t.Portal=32]=\"Portal\"})(h||(h={}));var l=(function(){var t;if(void 0!==l)t=l;else if(\"undefined\"!=typeof self)t=self;else try{t=Function(\"return this\")()}catch(e){throw new Error(\"global object is unavailable in this environment\")}return t})(),d=\"undefined\"!=typeof window,v={createElement:n,createElementNS:n,createTextNode:n},m=d?document:v;d&&navigator.platform&&/mac/i.test(navigator.platform)&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent),r(m.createAttributeNS);Object.is=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!==t&&e!==e};var y=function(){this.cache=[],this.size=0};y.prototype.set=function(t,e){var n=this,r=this.cache.length;if(!r)return this.cache.push({k:t,v:e}),void(this.size+=1);for(var o=0;o<r;o++){var i=n.cache[o];if(i.k===t)return void(i.v=e)}this.cache.push({k:t,v:e}),this.size+=1},y.prototype.get=function(t){var e=this,n=this.cache.length;if(n)for(var r=0;r<n;r++){var o=e.cache[r];if(o.k===t)return o.v}},y.prototype.has=function(t){var e=this,n=this.cache.length;if(!n)return!1;for(var r=0;r<n;r++)if(e.cache[r].k===t)return!0;return!1},y.prototype[\"delete\"]=function(t){for(var e=this,n=this.cache.length,r=0;r<n;r++)if(e.cache[r].k===t)return e.cache.splice(r,1),e.size-=1,!0;return!1},y.prototype.clear=function(){var t=this,e=this.cache.length;if(this.size=0,e)for(;e;)t.cache.pop(),e--};var g={constructor:1,render:1,shouldComponentUpdate:1,componentWillUpdate:1,componentWillReceiveProps:1,componentDidUpdate:1,componentWillMount:1,componentDidMount:1,componentWillUnmount:1,componentDidUnmount:1,getDerivedStateFromProps:1};return function(e){var n=(function(t){function e(e,n){t.call(this,e,n),a(this),this.getInitialState&&(this.state=this.getInitialState())}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.replaceState=function(t,e){this.setState(t,e)},e.prototype.isMounted=function(){return!this.dom},e})(t.Component);return n.displayName=e.displayName||\"Component\",n.propTypes=e.propTypes,n.mixins=e.mixins&&c(e.mixins),n.getDefaultProps=e.getDefaultProps,i(n.prototype,e),e.statics&&i(n,e.statics),e.mixins&&p(n,c(e.mixins)),n.defaultProps=o(n.getDefaultProps)?undefined:n.getDefaultProps(),n}}));\n    // 节流\n    function throttle (func, wait) {\n      var timeout, context, args, result;\n      var previous = 0;\n\n      var later = function() {\n        previous = +new Date();\n        timeout = null;\n        func.apply(context, args)\n      };\n\n      var throttled = function() {\n        var now = +new Date();\n        var remaining = wait - (now - previous);\n        context = this;\n        args = arguments;\n        if (remaining <= 0 || remaining > wait) {\n          if (timeout) {\n            clearTimeout(timeout);\n            timeout = null;\n          }\n          previous = now;\n          func.apply(context, args);\n        } else if (!timeout) {\n          timeout = setTimeout(later, remaining);\n        }\n      };\n      return throttled;\n    }\n    // Context\n    var LingAtomScrollContext = Nerv.createContext({\n      scrollTop: 0,\n      windowHeight: 0,\n      bodyHeight: 0\n    })\n  \n  \n  \n  \n    \n  \n    \n  \n    \n  \n    \n  \n    \n  \n    \n  \n  \n    function genComponentElement (type, props, children) {\n      \n      return Nerv.createElement(\n        Lc[type] ? Lc[type].component : 'div',\n        props,\n        children\n      )\n      \n    }\n  \n  \n    window.Lc = window.Lc || {}\n    Lc['Page'] = { component: 'div' }\n  \n  \n    function EventEmitter () {\n      var eventEmitterPool = {}\n      var eventEmitter = {\n        on: function (eventName, handler) {\n          eventEmitterPool[eventName] = eventEmitterPool[eventName] || []\n          eventEmitterPool[eventName].push(handler)\n        },\n        trigger: function (eventName) {\n          var handlers = eventEmitterPool[eventName] || []\n          var args = Array.prototype.slice.call(arguments, 1)\n          for (var i = 0; i < handlers.length; i++) {\n            handlers[i].apply(this, args)\n          }\n        },\n        off: function (eventName, handler) {\n          if (!eventEmitterPool[eventName]) return\n          if (!handler) {\n            eventEmitterPool[eventName] = []\n            return\n          }\n          var handlers = eventEmitterPool[eventName] || []\n          for (var i = 0; i < handlers.length; i++) {\n            if (handlers[i] === handler) {\n              eventEmitterPool[eventName].splice(i, 1)\n              break\n            }\n          }\n        }\n      }\n      return eventEmitter\n    }\n    var eventEmitter = EventEmitter()\n\n    !function () {\n      var downloadedBundles = [\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd5a6__5ee34e5afc685b3ecdd0f087.js\",\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd5a7__5ee34e58fc685b3ecdd0f078.js\",\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd5a8__5ee34e5afc685b3ecdd0f08a.js\",\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd5a9__5fa90cbc7e292cb70199df98.js\",\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd594__5ee34e59fc685b3ecdd0f07e.js\",\"//storage.360buyimg.com/quark-platform/component/standard/5cd436279a6ea5003becd5aa__5ee34e5bfc685b3ecdd0f094.js\"]\n      var body = document.body || document.documentElement\n      var Wrap = function (props) {\n        return props.children\n      }\n      // Lazyload Component\n      var LazyLoadComponent = NervCreateClass({\n        // loading loaded\n        status: '',\n        getInitialState: function () {\n          return {\n            status: 'initial'\n          }\n        },\n        updateOffsetTop: function () {\n          if (this.state.status !== 'initial') return\n          var node = Nerv.findDOMNode(this)\n          var rect = node.getBoundingClientRect()\n          var winHeight = this.props.__context.windowHeight\n          var canLoad = rect.top < winHeight * 1.5 && rect.top + rect.height > -winHeight * 0.5\n          if (canLoad) {\n            this.downloadScript()\n          }\n        },\n        delayLoaded: function () {\n          var that = this\n          setTimeout(function () {\n            that.setState({ status: 'loaded' })\n          }, 500)\n        },\n        downloadScript: function () {\n          var that = this\n          var myItem = this.props.item\n          if (!myItem.reactBundle || downloadedBundles.indexOf(myItem.reactBundle) !== -1) {\n            if (Lc[myItem.type]) {\n              this.setState({ status: 'loading' })\n              this.delayLoaded()\n            } else {\n              eventEmitter.on(myItem.type, function () {\n                that.setState({ status: 'loading' })\n                that.delayLoaded()\n              })\n            }\n            return\n          }\n          myItem.reactBundle = myItem.reactBundle && myItem.reactBundle.replace('storage.jd.com', 'storage.360buyimg.com')\n          downloadedBundles.push(myItem.reactBundle)\n          var element = document.createElement('script')\n          element.src = myItem.reactBundle\n          element.charset = 'UTF-8'\n          body.appendChild(element)\n          element.onload = function () {\n            that.setState({ status: 'loading' })\n            that.delayLoaded()\n            eventEmitter.trigger(myItem.type)\n          }\n        },\n        componentDidMount: function () {\n          this.updateOffsetTop()\n        },\n        componentWillReceiveProps: function (nextProps) {\n          this.updateOffsetTop()\n        },\n        shouldComponentUpdate: function (nextProps, nextState) {\n          return this.state.status !== nextState.status\n        },\n        componentDidCatch: function (error, info) {\n          // console.log(error)\n          // 上报组件渲染错误\n          // @see //talos30011-prelb.o2athena.svc.n.jd.local/api-docs#null%2Fpaths%2F~1report%2Fpost\n          var xhr = new XMLHttpRequest()\n          xhr.open('POST', 'https://atom-log.3.cn/report', true)\n          xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded')\n          xhr.send('platform=h5&url=' + location.href + '&cname=' + this.props.item.type + '&project=' + globalData.projectId)\n        },\n        getWrappedElement: function (extraProps) {\n          if (this.WrappedElement) {\n            return this.WrappedElement\n          }\n          var item = this.props.item\n          var children = item.props.content || this.props.children || null\n          var WrappedElement = genComponentElement(item.type, Object.assign({}, item.props, extraProps), children)\n          this.WrappedElement = WrappedElement\n          return WrappedElement\n        },\n        render: function () {\n          var that = this\n          var status = this.state.status\n          // if (status === 'initial' || status === 'loading') {\n          if (status === 'initial') {\n            return Nerv.createElement(\n              'div',\n              {\n                style: {\n                  padding: '10px',\n                  backgroundClip: 'content-box',\n                  minHeight: '256px',\n                  height: '100%',\n                  backgroundColor: '#e3e4e5'\n                }\n              }\n              // status === 'loading' ?\n              //   Nerv.createElement(\n              //     'div',\n              //     {\n              //       style: {\n              //         width: 0,\n              //         height: 0,\n              //         overflow: 'hidden'\n              //       }\n              //     },\n              //     that.getWrappedElement()\n              //   ) : null\n            )\n          } else {\n            // return that.getWrappedElement()\n            // 缺点是多了一层 div\n            this.cc = this.cc || that.getWrappedElement()\n            return Nerv.createElement(\n              'div',\n              status === 'loading' ? {\n                style: {\n                  padding: '10px',\n                  backgroundClip: 'content-box',\n                  minHeight: '256px',\n                  height: '100%',\n                  backgroundColor: '#e3e4e5'\n                }\n              } : {},\n              this.cc\n            )\n          }\n        }\n      })\n\n      function createTree (tree) {\n        tree = tree.filter(function (item) {\n          return item.isShow === undefined || item.isShow === true\n        })\n\n        return tree.map(function (item) {\n          var hasChildren = item.childrens && item.childrens.length\n          hasChildren && (item.props.children = createTree(item.childrens))\n          if (hasChildren) {\n            return genComponentElement(item.type, item.props, item.props.content)\n          } else {\n            return Nerv.createElement(\n              LingAtomScrollContext.Consumer,\n              {},\n              function (context) {\n                return Nerv.createElement(\n                  LazyLoadComponent,\n                  {\n                    item: item,\n                    __context: context\n                  }\n                )\n              }\n            )\n          }\n        })\n      }\n\n      var App = NervCreateClass({\n        getInitialState: function () {\n          return {\n            scrollTop: 0,\n            windowHeight: window.innerHeight,\n            bodyHeight: document.body.clientHeight\n          }\n        },\n        componentDidMount: function () {\n          var that = this\n          window.onscroll = throttle(function () {\n            var bodyScrollHeight = document.documentElement.scrollTop || document.body.scrollTop\n            that.setState({\n              scrollTop: bodyScrollHeight\n            })\n          }, 200)\n          window.onresize = function () {\n            var windowInnerHeight = window.innerHeight\n            if (windowInnerHeight === that.state.windowHeight) return\n            that.setState({\n              windowHeight: windowInnerHeight\n            })\n          }\n          function onElementHeightChange (elm, callback) {\n            var lastHeight = elm.clientHeight\n            var newHeight\n            (function run () {\n              newHeight = elm.clientHeight\n              if (lastHeight !== newHeight) {\n                callback(newHeight)\n              }\n              lastHeight = newHeight\n              if (elm.onElementHeightChangeTimer) {\n                clearTimeout(elm.onElementHeightChangeTimer)\n              }\n              elm.onElementHeightChangeTimer = setTimeout(run, 200)\n            })()\n          }\n          onElementHeightChange(document.body, function (h) {\n            that.setState({ bodyHeight: h })\n          })\n        },\n        render: function () {\n          return Nerv.createElement(\n            LingAtomScrollContext.Provider,\n            {\n              value: this.state\n            },\n            createTree(window.o2PageConfig.data)\n          )\n        }\n      })\n\n      Nerv.render(\n        Nerv.createElement(App),\n        document.querySelector('#app')\n      )\n    }()\n  \n  \n\n\n\n\n     ✖ ✍操作台（点此拖动，左上角调整大小）  \n              ● 已选中1个元素，您可以:\n                  确认采集    取消选择  Path: /html/body  \n"}], "unique_index": "b8visdn34klpu1l6nl", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}