{"id": 169, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "", "update_time": "7/13/2023, 5:01:52 PM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 6, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "Field[\"自定义操作\"]Field[\"参数2_文本\"]", "value": "Field[\"自定义操作\"]Field[\"参数2_文本\"]"}, {"id": 2, "name": "loopText_2", "nodeId": 7, "nodeName": "循环", "desc": "要输入的文本/网址,多行以\\n分开", "type": "text", "exampleValue": "A~TField[\"自定义操作\"]T~C", "value": "A~TField[\"自定义操作\"]T~C"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "HI～欢迎来到京东!"}, {"id": 1, "name": "参数2_文本", "desc": "", "type": "text", "recordASField": 0, "exampleValue": "/手机/数码"}, {"id": 2, "name": "自定义参数_1", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}, {"id": 3, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]", "//div[contains(., 'HI～欢迎来到京东!')]", "//DIV[@class='welcome']", "/html/body/div[last()-6]/div/div[last()-4]/div/div/div/div[last()-6]/div/div"], "exampleValues": [{"num": 0, "value": "HI～欢迎来到京东!"}], "unique_index": "oper73sn6qlk0vw7y2", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 3, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4, 5, 7], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '/手机/数码')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG ']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]"]}}, {"id": 4, "index": 4, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": true, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数2_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "/手机/数码"}], "unique_index": "v4f6995ttqslk0vw9ri", "iframe": false, "default": "", "paraType": "text", "recordASField": 0, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 7, "relative": false, "name": "自定义参数_1", "desc": "", "extractType": 0, "relativeXPath": "//body", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}], "loopType": 1}}, {"id": 5, "index": 5, "parentId": 3, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": 0, "code": "return window.innerHeight", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 7, "index": 6, "parentId": 6, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 1, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "Field[\"自定义操作\"]Field[\"参数2_文本\"]", "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": 6, "index": 7, "parentId": 3, "type": 1, "option": 8, "title": "循环", "sequence": [6, 8, 12, 13, 14, 15], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "A~B~C", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": "3", "pathList": "", "textList": "A~TField[\"自定义操作\"]T~C", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 8, "index": 8, "parentId": 6, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 1, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "", "index": 0, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": -1, "index": 9, "parentId": 6, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "", "index": 0, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": -1, "index": 10, "parentId": 6, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "", "index": 0, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": -1, "index": 11, "parentId": 6, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "", "index": 0, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": 9, "index": 12, "parentId": 6, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 1, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "", "index": 1, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": 10, "index": 13, "parentId": 6, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 3, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 1, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "", "index": 2, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": 11, "index": 14, "parentId": 6, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 4, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 1, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "", "index": 3, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": 12, "index": 15, "parentId": 6, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 5, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 1, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "", "index": 4, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}]}