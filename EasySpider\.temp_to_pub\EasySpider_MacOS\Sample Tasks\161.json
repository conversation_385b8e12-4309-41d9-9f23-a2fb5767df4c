{"id": 161, "name": "Error 404 (Not Found)!!1", "url": "https://www.google.com/q=easyspider", "links": "https://www.google.com/search?q=easyspider", "create_time": "7/12/2023, 3:44:48 AM", "update_time": "7/12/2023, 3:44:48 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "containJudge": false, "desc": "https://www.google.com/q=easyspider", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.google.com/search?q=easyspider", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.google.com/search?q=easyspider"}, {"id": 1, "name": "loopTimes_循环_1", "nodeId": 2, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "{\"AUI_TNR_V2_180836\":\"C\",\"AUI_ACCORDION_A11Y_ROLE_354025\":\"T1\",\"AUI_PRELOAD_261698\":\"C\",\"AUI_TEMPLATE_WEBLAB_CACHE_333406\":\"C\",\"AUI_72554\":\"C\",\"AUI_KILLSWITCH_CSA_LOGGER_372963\":\"C\",\"AUI_REL_NOREFERRER_NOOPENER_309527\":\"C\",\"AUI_PCI_RISK_BANNER_210084\":\"C\"}typeof uex === 'function' && uex('ld', 'portal-bb', {wb: 1})\n\n\n\n\n!function(){function n(n,t){var r=i(n);return t&&(r=r(\"instance\",t)),r}var r=[],c=0,i=function(t){return function(){var n=c++;return r.push([t,[].slice.call(arguments,0),n,{time:Date.now()}]),i(n)}};n._s=r,this.csa=n}();;\ncsa('Config', {\"ContentInteractionsSummary.FlushInterval\":5000,\"AddMissingPluginsToEnd\":1});\nif (window.csa) {\n    csa(\"Config\", {\n        'Application': 'Retail:Prod:www.amazon.com',\n        'Events.Namespace': 'csa',\n        'ObfuscatedMarketplaceId': 'ATVPDKIKX0DER',\n        'Events.SushiEndpoint': 'https://unagi.amazon.com/1/events/com.amazon.csm.csa.prod',\n        'CacheDetection.RequestID': \"K1JQ9VEP43RRWKW775V5\",\n        'CacheDetection.Callback': window.ue && ue.reset,\n        'LCP.elementDedup': 1\n    });\n\n    csa(\"Events\")(\"setEntity\", {\n        page: {requestId: \"K1JQ9VEP43RRWKW775V5\", meaningful: \"interactive\"},\n        session: {id: \"134-1570085-9450222\"}\n    });\n}\n!function(i){var r,e,o=\"splice\",u=i.csa,f={},c={},a=i.csa._s,s=0,l=0,g=-1,h={},d={},v={},n=Object.keys,p=function(){};function t(n,t){return u(n,t)}function m(n,t){var i=c[n]||{};O(i,t),c[n]=i,l++,S(U,0)}function w(n,t,i){var r=!0;return t=D(t),i&&i.buffered&&(r=(v[n]||[]).every(function(n){return!1!==t(n)})),r?(h[n]||(h[n]=[]),h[n].push(t),function(){!function(n,t){var i=h[n];i&&i[o](i.indexOf(t),1)}(n,t)}):p}function b(n,t){if(t=D(t),n in d)return t(d[n]),p;return w(n,function(n){return t(n),!1})}function E(n,t){if(u(\"Errors\")(\"logError\",n),f.DEBUG)throw t||n}function y(){return Math.abs(4294967295*Math.random()|0).toString(36)}function D(n,t){return function(){try{return n.apply(this,arguments)}catch(n){E(n.message||n,n)}}}function S(n,t){return i.setTimeout(D(n),t)}function U(){for(var n=0;n<a.length;){var t=a[n],i=t[0]in c;if(!i&&!e)return void(s=f.AddMissingPluginsToEnd?a.length:t.length);i?(a[o](s=n,1),I(t)):n++}g=l}function I(n){var arguments,t=c[n[0]],i=(arguments=n[1])[0];if(!t||!t[i])return E(\"Undefined function: \"+t+\"/\"+i);r=n[3],c[n[2]]=t[i].apply(t,arguments.slice(1))||{},r=0}function M(){e=1,U()}function O(t,i){n(i).forEach(function(n){t[n]=i[n]})}b(\"$beforeunload\",M),m(\"Config\",{instance:function(n){O(f,n)}}),u.plugin=D(function(n){n(t)}),t.config=f,t.register=m,t.on=w,t.once=b,t.blank=p,t.emit=function(n,t,i){for(var r=h[n]||[],e=0;e<r.length;)!1===r[e](t)?r[o](e,1):e++;d[n]=t||{},i&&i.buffered&&(v[n]||(v[n]=[]),100<=v[n].length&&v[n].shift(),v[n].push(t||{}))},t.UUID=function(){return[y(),y(),y(),y()].join(\"-\")},t.time=function(n){var t=r?new Date(r.time):new Date;return\"ISO\"===n?t.toISOString():t.getTime()},t.error=E,t.warn=function(n,t){if(u(\"Errors\")(\"logWarn\",n),f.DEBUG)throw t||n},t.exec=D,t.timeout=S,t.interval=function(n,t){return i.setInterval(D(n),t)},(t.global=i).csa._s.push=function(n){n[0]in c&&(!a.length||e)?(I(n),a.length&&g!==l&&U()):a[o](s++,0,n)},U(),S(function(){S(M,f.SkipMissingPluginsTimeout||5e3)},1)}(\"undefined\"!=typeof window?window:global);csa.plugin(function(o){var f=\"addEventListener\",e=\"requestAnimationFrame\",t=o.exec,r=o.global,u=o.on;o.raf=function(n){if(r[e])return r[e](t(n))},o.on=function(n,e,t,r){if(n&&\"function\"==typeof n[f]){var i=o.exec(t);return n[f](e,i,r),function(){n.removeEventListener(e,i,r)}}return\"string\"==typeof n?u(n,e,t,r):o.blank}});csa.plugin(function(o){var t,n,r={},e=\"localStorage\",c=\"sessionStorage\",a=\"local\",i=\"session\",u=o.exec;function s(e,t){var n;try{r[t]=!!(n=o.global[e]),n=n||{}}catch(e){r[t]=!(n={})}return n}function f(){t=t||s(e,a),n=n||s(c,i)}function l(e){return e&&e[i]?n:t}o.store=u(function(e,t,n){f();var o=l(n);return e?t?void(o[e]=t):o[e]:Object.keys(o)}),o.storageSupport=u(function(){return f(),r}),o.deleteStored=u(function(e,t){f();var n=l(t);if(\"function\"==typeof e)for(var o in n)n.hasOwnProperty(o)&&e(o,n[o])&&delete n[o];else delete n[e]})});csa.plugin(function(n){n.types={ovl:function(n){var r=[];if(n)for(var i in n)n.hasOwnProperty(i)&&r.push(n[i]);return r}}});csa.plugin(function(c){function e(n){return function(e){c(\"Metrics\",{producerId:\"csa\",dimensions:{message:e}})(\"recordMetric\",n,1)}}function n(r){var t,o,l=c(\"Events\",{producerId:r.producerId}),u=[\"name\",\"type\",\"csm\",\"adb\"],i={url:\"pageURL\",file:\"f\",line:\"l\",column:\"c\"};this.log=function(e){if(!function(e){if(!e)return!0;for(var n in e)return!1;return!0}(e)){var n=r.logOptions||{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}};l(\"log\",function(n){return t=c.UUID(),o={messageId:t,schemaId:r.schemaId||\"<ns>.Error.5\",errorMessage:n.m||null,attribution:n.attribution||null,logLevel:\"FATAL\",url:null,file:null,line:null,column:null,stack:n.s||[],context:n.cinfo||{},metadata:{}},n.logLevel&&(o.logLevel=\"\"+n.logLevel),u.forEach(function(e){n[e]&&(o.metadata[e]=n[e])}),\"INFO\"===n.logLevel||Object.keys(i).forEach(function(e){\"number\"!=typeof n[i[e]]&&\"string\"!=typeof n[i[e]]||(o[e]=\"\"+n[i[e]])}),o}(e),n)}}}c.register(\"Errors\",{instance:function(e){return new n(e||{})},logError:e(\"jsError\"),logWarn:e(\"jsWarn\")})});csa.plugin(function(o){var r,e,n,t,a,i=\"function\",u=\"willDisappear\",f=\"$app.\",p=\"$document.\",c=\"focus\",s=\"blur\",d=\"active\",l=\"resign\",$=o.global,b=o.exec,m=o.config[\"Transport.AnonymizeRequests\"]||!1,g=o(\"Events\"),h=$.location,v=$.document||{},y=$.P||{},P=(($.performance||{}).navigation||{}).type,w=o.on,k=o.emit,E=v.hidden,T={};h&&v&&(w($,\"beforeunload\",D),w($,\"pagehide\",D),w(v,\"visibilitychange\",R(p,function(){return v.visibilityState||\"unknown\"})),w(v,c,R(p+c)),w(v,s,R(p+s)),y.when&&y.when(\"mash\").execute(function(e){e&&(w(e,\"appPause\",R(f+\"pause\")),w(e,\"appResume\",R(f+\"resume\")),R(f+\"deviceready\")(),$.cordova&&$.cordova.platformId&&R(f+cordova.platformId)(),w(v,d,R(f+d)),w(v,l,R(f+l)))}),e=$.app||{},n=b(function(){k(f+\"willDisappear\"),D()}),a=typeof(t=e[u])==i,e[u]=b(function(){n(),a&&t()}),$.app||($.app=e),\"complete\"===v.readyState?A():w($,\"load\",A),E?S():x(),o.on(\"$app.blur\",S),o.on(\"$app.focus\",x),o.on(\"$document.blur\",S),o.on(\"$document.focus\",x),o.on(\"$document.hidden\",S),o.on(\"$document.visible\",x),o.register(\"SPA\",{newPage:I}),I({transitionType:{0:\"hard\",1:\"refresh\",2:\"back-button\"}[P]||\"unknown\"}));function I(n,e){var t=!!r,a=(e=e||{}).keepPageAttributes;t&&(k(\"$beforePageTransition\"),k(\"$pageTransition\")),t&&!a&&g(\"removeEntity\",\"page\"),r=o.UUID(),a?T.id=r:T={schemaId:\"<ns>.PageEntity.1\",id:r,url:m?h.href.split(\"?\")[0]:h.href,server:h.hostname,path:h.pathname,referrer:m?v.referrer.split(\"?\")[0]:v.referrer,title:v.title},Object.keys(n||{}).forEach(function(e){T[e]=n[e]}),g(\"setEntity\",{page:T}),k(\"$pageChange\",T,{buffered:1}),t&&k(\"$afterPageTransition\")}function A(){k(\"$load\"),k(\"$ready\"),k(\"$afterload\")}function D(){k(\"$ready\"),k(\"$beforeunload\"),k(\"$unload\"),k(\"$afterunload\")}function S(){E||(k(\"$visible\",!1,{buffered:1}),E=!0)}function x(){E&&(k(\"$visible\",!0,{buffered:1}),E=!1)}function R(n,t){return b(function(){var e=typeof t==i?n+t():n;k(e)})}});csa.plugin(function(c){var e=\"Events\",t=\"UNKNOWN\",s=\"id\",a=\"all\",n=\"messageId\",i=\"timestamp\",u=\"producerId\",o=\"application\",r=\"obfuscatedMarketplaceId\",f=\"entities\",d=\"schemaId\",l=\"version\",p=\"attributes\",v=\"<ns>\",g=\"session\",h=c.config,m=(c.global.location||{}).host,y=h[e+\".Namespace\"]||\"csa_other\",I=h.Application||\"Other\"+(m?\":\"+m:\"\"),b=h[\"Transport.AnonymizeRequests\"]||!1,O=c(\"Transport\"),E={},U=function(e,t){Object.keys(e).forEach(t)};function A(n,i,o){U(i,function(e){var t=o===a||(o||{})[e];e in n||(n[e]={version:1,id:i[e][s]||c.UUID()}),N(n[e],i[e],t)})}function N(t,n,i){U(n,function(e){!function(e,t,n){return\"string\"!=typeof t&&e!==l?c.error(\"Attribute is not of type string: \"+e):!0===n||1===n||(e===s||!!~(n||[]).indexOf(e))}(e,n[e],i)||(t[e]=n[e])})}function S(o,e,r){U(e,function(e){var t=o[e];if(t[d]){var n={},i={};n[s]=t[s],n[u]=t[u]||r,n[d]=t[d],n[l]=t[l]++,n[p]=i,k(n),N(i,t,1),w(i),O(\"log\",n)}})}function k(e){e[i]=function(e){return\"number\"==typeof e&&(e=new Date(e).toISOString()),e||c.time(\"ISO\")}(e[i]),e[n]=e[n]||c.UUID(),e[o]=I,e[r]=h.ObfuscatedMarketplaceId||t,e[d]=e[d].replace(v,y)}function w(e){delete e[l],delete e[d],delete e[u]}function D(o){var r={};this.log=function(e,t){var n={},i=(t||{}).ent;return e?\"string\"!=typeof e[d]?c.error(\"A valid schema id is required for the event\"):(k(e),A(n,E,i),A(n,r,i),A(n,e[f]||{},i),U(n,function(e){w(n[e])}),e[u]=o[u],e[f]=n,void O(\"log\",e,t)):c.error(\"The event cannot be undefined\")},this.setEntity=function(e){b&&delete e[g],A(r,e,a),S(r,e,o[u])}}h[\"KillSwitch.\"+e]||c.register(e,{setEntity:function(e){b&&delete e[g],A(E,e,a),S(E,e,\"csa\")},removeEntity:function(e){delete E[e]},instance:function(e){return new D(e)}})});csa.plugin(function(s){var c,g=\"Transport\",l=\"post\",f=\"preflight\",r=\"csa.cajun.\",i=\"store\",a=\"deleteStored\",u=\"sendBeacon\",t=\"__merge\",e=\"messageId\",n=\".FlushInterval\",o=0,d=s.config[g+\".BufferSize\"]||2e3,h=s.config[g+\".RetryDelay\"]||1500,p=s.config[g+\".AnonymizeRequests\"]||!1,v={},y=0,m=[],E=s.global,R=E.document,b=s.timeout,k=E.Object.keys,w=s.config[g+n]||5e3,I=w,O=s.config[g+n+\".BackoffFactor\"]||1,S=s.config[g+n+\".BackoffLimit\"]||3e4,B=0;function T(n){if(864e5<s.time()-+new Date(n.timestamp))return s.warn(\"Event is too old: \"+n);y<d&&(n[e]in v||(v[n[e]]=n,y++),\"function\"==typeof n[t]&&n[t](v[n[e]]),!B&&o&&(B=b(q,function(){var n=I;return I=Math.min(n*O,S),n}())))}function q(){m.forEach(function(e){var o=[];k(v).forEach(function(n){var t=v[n];e.accepts(t)&&o.push(t)}),o.length&&(e.chunks?e.chunks(o).forEach(function(n){D(e,n)}):D(e,o))}),v={},B=0}function D(t,e){function o(){s[a](r+n)}var n=s.UUID();s[i](r+n,JSON.stringify(e)),[function(n,t,e){var o=E.navigator||{},r=E.cordova||{};if(p)return 0;if(!o[u]||!n[l])return 0;n[f]&&r&&\"ios\"===r.platformId&&!c&&((new Image).src=n[f]().url,c=1);var i=n[l](t);if(!i.type&&o[u](i.url,i.body))return e(),1},function(n,t,e){if(!n[l])return 0;var o=n[l](t),r=o.url,i=o.body,c=o.type,f=new XMLHttpRequest,a=0;function u(n,t,e){f.open(\"POST\",n),f.withCredentials=!p,e&&f.setRequestHeader(\"Content-Type\",e),f.send(t)}return f.onload=function(){f.status<299?e():s.config[g+\".XHRRetries\"]&&a<3&&b(function(){u(r,i,c)},++a*h)},u(r,i,c),1}].some(function(n){try{return n(t,e,o)}catch(n){}})}k&&(s.once(\"$afterload\",function(){o=1,function(e){(s[i]()||[]).forEach(function(n){if(!n.indexOf(r))try{var t=s[i](n);s[a](n),JSON.parse(t).forEach(e)}catch(n){s.error(n)}})}(T),s.on(R,\"visibilitychange\",q,!1),q()}),s.once(\"$afterunload\",function(){o=1,q()}),s.on(\"$afterPageTransition\",function(){y=0,I=w}),s.register(g,{log:T,register:function(n){m.push(n)}}))});csa.plugin(function(n){var r=n.config[\"Events.SushiEndpoint\"];n(\"Transport\")(\"register\",{accepts:function(n){return n.schemaId},post:function(n){var t=n.map(function(n){return{data:n}});return{url:r,body:JSON.stringify({events:t})}},preflight:function(){var n,t=/\\/\\/(.*?)\\//.exec(r);return t&&t[1]&&(n=\"https://\"+t[1]+\"/ping\"),{url:n}},chunks:function(n){for(var t=[];500<n.length;)t.push(n.splice(0,500));return t.push(n),t}})});csa.plugin(function(n){var t,a,o,r,e=n.config,i=\"PageViews\",d=e[i+\".ImpressionMinimumTime\"]||1e3,s=\"hidden\",c=\"innerHeight\",g=\"innerWidth\",l=\"renderedTo\",f=l+\"Viewed\",m=l+\"Meaningful\",u=l+\"Impressed\",p=1,v=2,h=3,w=4,y=5,P=\"loaded\",I=7,T=8,b=n.global,E=n.on,V=n(\"Events\",{producerId:\"csa\"}),$=b.document,M={},S={},H=y;function K(e){if(!M[I]){var i;if(M[e]=n.time(),e!==h&&e!==P||(t=t||M[e]),t&&H===w)a=a||M[e],(i={})[m]=t-o,i[f]=a-o,R(\"PageView.4\",i),r=r||n.timeout(j,d);if(e!==y&&e!==p&&e!==v||(clearTimeout(r),r=0),e!==p&&e!==v||R(\"PageRender.3\",{transitionType:e===p?\"hard\":\"soft\"}),e===I)(i={})[m]=t-o,i[f]=a-o,i[u]=M[e]-o,R(\"PageImpressed.2\",i)}}function R(e,i){S[e]||(i.schemaId=\"<ns>.\"+e,V(\"log\",i,{ent:\"all\"}),S[e]=1)}function W(){0===b[c]&&0===b[g]?(H=T,n(\"Events\")(\"setEntity\",{page:{viewport:\"hidden-iframe\"}})):H=$[s]?y:w,K(H)}function j(){K(I),r=0}function k(){var e=o?v:p;M={},S={},a=t=0,o=n.time(),K(e),W()}function q(){var e=$.readyState;\"interactive\"===e&&K(h),\"complete\"===e&&K(P)}e[\"KillSwitch.\"+i]||($&&void 0!==$[s]?(k(),E($,\"visibilitychange\",W,!1),E($,\"readystatechange\",q,!1),E(\"$afterPageTransition\",k),E(\"$timing:loaded\",q),n.once(\"$load\",q)):n.warn(\"Page visibility not supported\"))});csa.plugin(function(c){var s=c.config[\"Interactions.ParentChainLength\"]||35,e=\"click\",r=\"touches\",f=\"timeStamp\",o=\"length\",u=\"pageX\",g=\"pageY\",p=\"pageXOffset\",h=\"pageYOffset\",m=250,v=5,d=200,l=.5,t={capture:!0,passive:!0},X=c.global,Y=c.emit,n=c.on,x=X.Math.abs,a=(X.document||{}).documentElement||{},y={x:0,y:0,t:0,sX:0,sY:0},N={x:0,y:0,t:0,sX:0,sY:0};function b(t){if(t.id)return\"//*[@id='\"+t.id+\"']\";var e=function(t){var e,n=1;for(e=t.previousSibling;e;e=e.previousSibling)e.nodeName===t.nodeName&&(n+=1);return n}(t),n=t.nodeName;return 1!==e&&(n+=\"[\"+e+\"]\"),t.parentNode&&(n=b(t.parentNode)+\"/\"+n),n}function I(t,e,n){var a=c(\"Content\",{target:n}),i={schemaId:\"<ns>.ContentInteraction.1\",interaction:t,interactionData:e,messageId:c.UUID()};if(n){var r=b(n);r&&(i.attribution=r);var o=function(t){for(var e=t,n=e.tagName,a=!1,i=t?t.href:null,r=0;r<s;r++){if(!e||!e.parentElement){a=!0;break}n=(e=e.parentElement).tagName+\"/\"+n,i=i||e.href}return a||(n=\".../\"+n),{pc:n,hr:i}}(n);o.pc&&(i.interactionData.parentChain=o.pc),o.hr&&(i.interactionData.href=o.hr)}a(\"log\",i),Y(\"$content.interaction\",i)}function i(t){I(e,{interactionX:\"\"+t.pageX,interactionY:\"\"+t.pageY},t.target)}function C(t){if(t&&t[r]&&1===t[r][o]){var e=t[r][0];N=y={e:t.target,x:e[u],y:e[g],t:t[f],sX:X[p],sY:X[h]}}}function D(t){if(t&&t[r]&&1===t[r][o]&&y&&N){var e=t[r][0],n=t[f],a=n-N.t,i={e:t.target,x:e[u],y:e[g],t:n,sX:X[p],sY:X[h]};N=i,d<=a&&(y=i)}}function E(t){if(t){var e=x(y.x-N.x),n=x(y.y-N.y),a=x(y.sX-N.sX),i=x(y.sY-N.sY),r=t[f]-y.t;if(m<1e3*e/r&&v<e||m<1e3*n/r&&v<n){var o=n<e;o&&a&&e*l<=a||!o&&i&&n*l<=i||I((o?\"horizontal\":\"vertical\")+\"-swipe\",{interactionX:\"\"+y.x,interactionY:\"\"+y.y,endX:\"\"+N.x,endY:\"\"+N.y},y.e)}}}n(a,e,i,t),n(a,\"touchstart\",C,t),n(a,\"touchmove\",D,t),n(a,\"touchend\",E,t)});csa.plugin(function(r){var a,o,t,e=\"MutationObserver\",c=\"observe\",n=\"disconnect\",s=\"mutObs\",f=\"_csa_flt\",l=\"_csa_llt\",b=\"_csa_mr\",d=\"_csa_mi\",m=\"lastChild\",p=\"length\",_={childList:!0,subtree:!0},g=10,h=4,u=r.global,i=u.document,v=i.body||i.documentElement,y=Date.now,O=[],k=[],w=[],L=0,B=0,I=0,M=1,Y=[],$=[],x=0,A=r.blank,C={buffered:1},D=0;function E(e){r.global.ue_csa_ss_tag||r.emit(\"$csmTag:\"+e,0,C)}y&&u[e]?(E(s+\"Yes\"),L=0,o=new u[e](N),(t=new u[e](F))[c](v,{attributes:!0,subtree:!0,attributeFilter:[\"src\"],attributeOldValue:!0}),A=r.on(u,\"scroll\",S,{passive:!0}),r.once(\"$ready\",V),M&&T(),r.register(\"SpeedIndexBuffers\",{getBuffers:function(e){e&&(V(),S(),e(L,Y,O,k,w),o&&o[n](),t&&t[n](),A())},registerListener:function(e){a=e},replayModuleIsLive:function(){r.raf(V)}})):E(s+\"No\");function F(e){O.push({t:y(),m:e})}function N(e){k.push({t:y(),m:e}),D||E(s+\"Active\"),D=I=1,a&&a()}function S(){I&&(w.push({t:y(),y:B}),B=u.pageYOffset,I=0)}function T(){for(var e=v,t=y(),n=[],s=[],u=0,i=0;e;)e[f]?++u:(e[f]=t,n.push(e),i=1),s[p]<h&&s.push(e),e[d]=x,e[l]=t,e=e[m];i&&(u<$[p]&&function(e){for(var t=e,n=$[p];t<n;t++){var s=$[t];if(s){if(s[b])break;if(s[d]<x){s[b]=1,o[c](s,_);break}}}}(u),$=s,Y.push({t:t,m:n}),++x,I=i,a&&a()),M&&(i?r.raf(T):r.timeout(T,g))}function V(){M&&(M=0,T(),o[c](v,_))}});\n\nvar ue_csa_ss_tag = false;\ncsa.plugin(function(b){var a=b.global,e=a.uet,f=a.uex,c=a.ue,d=a.Object,g={largestContentfulPaint:\"lcp\",speedIndex:\"si\",atfSpeedIndex:\"atfsi\",visuallyLoaded50:\"vl50\",visuallyLoaded90:\"vl90\",visuallyLoaded100:\"vl100\"},k=\"perfNo perfYes browserQuiteFn browserQuiteUd browserQuiteLd browserQuiteMut mutObsNo mutObsYes mutObsActive startVL endVL\".split(\" \");b&&e&&f&&d.keys&&c&&(d.keys(g).forEach(function(h){b.on(\"$timing:\"+h,function(a){var b=g[h];if(c.isl){var d=\"csa:\"+b;e(b,d,void 0,a);f(\"at\",d)}else e(b,\nvoid 0,void 0,a)})}),a.ue_csa_ss_tag||k.forEach(function(a){b.on(\"$csmTag:\"+a,function(){c.tag&&c.tag(a);c.isl&&f(\"at\",\"csa:\"+a)},{buffered:1})}))});\n\n\nwindow.rx = { 'rid':'K1JQ9VEP43RRWKW775V5', 'sid':'134-1570085-9450222', 'c':{  'rxp':'/rd/uedata', }};\n\nwindow.ue && ue.count && ue.count('CSMLibrarySize', 15975)\n\n\n\n\n!function(n){function e(n,e){return{m:n,a:function(n){return[].slice.call(n)}(e)}}document.createElement(\"header\");var r=function(n){function u(n,r,u){n[u]=function(){a._replay.push(r.concat(e(u,arguments)))}}var a={};return a._sourceName=n,a._replay=[],a.getNow=function(n,e){return e},a.when=function(){var n=[e(\"when\",arguments)],r={};return u(r,n,\"run\"),u(r,n,\"declare\"),u(r,n,\"publish\"),u(r,n,\"build\"),r.depends=n,r.iff=function(){var r=n.concat([e(\"iff\",arguments)]),a={};return u(a,r,\"run\"),u(a,r,\"declare\"),u(a,r,\"publish\"),u(a,r,\"build\"),a},r},u(a,[],\"declare\"),u(a,[],\"build\"),u(a,[],\"publish\"),u(a,[],\"importEvent\"),r._shims.push(a),a};r._shims=[],n.$Nav||(n.$Nav=r(\"rcx-nav\")),n.$Nav.make||(n.$Nav.make=r)}(window)\n$Nav.importEvent('navbarJS-beaconbelt');\n$Nav.declare('img.sprite', {\n  'png32': 'https://m.media-amazon.com/images/G/01/gno/sprites/nav-sprite-global-1x-reorg-privacy._CB587940754_.png',\n  'png32-2x': 'https://m.media-amazon.com/images/G/01/gno/sprites/nav-sprite-global-2x-reorg-privacy._CB587940754_.png'\n});\n$Nav.declare('img.timeline', {\n  'timeline-icon-2x': 'https://m.media-amazon.com/images/G/01/gno/sprites/timeline_sprite_2x._CB443581191_.png'\n});\nwindow._navbarSpriteUrl = 'https://m.media-amazon.com/images/G/01/gno/sprites/nav-sprite-global-1x-reorg-privacy._CB587940754_.png';\n$Nav.declare('img.pixel', 'https://m.media-amazon.com/images/G/01/x-locale/common/transparent-pixel._CB485935036_.gif');\n\n\n\nvar nav_t_after_preload_sprite = + new Date();\n\n(window.AmazonUIPageJS ? AmazonUIPageJS : P).when('navCF').execute(function() {\n  (window.AmazonUIPageJS ? AmazonUIPageJS : P).load.js('https://images-na.ssl-images-amazon.com/images/I/41X+pNHyLAL._RC|71q7BAoqR6L.js,01QvReFeJyL.js,01phmzCOwJL.js,01eOvPdxG7L.js,71f2YMGfmTL.js,41gNKoK0s7L.js,115pV8Rl02L.js,01+pnQJuQ0L.js,21rDHgaooIL.js,41rU9l+NGKL.js,51t-JTxfnwL.js,317BC63dC8L.js,11lEMI5MhIL.js,31c7Fn9h9gL.js,01LEzWzrPZL.js,01AqeWA7PKL.js_.js?AUIClients/NavDesktopUberAsset&FmhEG1Hq#desktop.language-en.us.488400-T1.488413-T1.375680-T1.366740-T1.310484-T1.651011-T1');\n});\n\n\n\n\n\n\n\n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    if(window.navmet===undefined) {\n      window.navmet=[];\n      if (window.performance && window.performance.timing && window.ue_t0) {\n        var t = window.performance.timing;\n        var now = + new Date();\n        window.navmet.basic = {\n          'networkLatency': (t.responseStart - t.fetchStart),\n          'navFirstPaint': (now - t.responseStart),\n          'NavStart': (now - window.ue_t0)\n        };\n        window.navmet.push({key:\"NavFirstPaintStart\",end:+new Date(),begin:window.ue_t0});\n      }\n    }\n    if (window.ue_t0) {\n      window.navmet.push({key:\"NavMainStart\",end:+new Date(),begin:window.ue_t0});\n    }\n\n\n\n\n\nwindow.navmet.tmp=+new Date();\n  \n    // Nav start should be logged at this place only if request is NOT progressively loaded.\n    // For progressive loading case this metric is logged as part of skeleton.\n    // Presence of skeleton signals that request is progressively loaded.\n    if(!document.getElementById(\"navbar-skeleton\")) {\n      window.uet && uet('ns');\n    }\n    window._navbar = (function (o) {\n      o.componentLoaded = o.loading = function(){};\n      o.browsepromos = {};\n      o.issPromos = [];\n      return o;\n    }(window._navbar || {}));\n    window._navbar.declareOnLoad = function () { window.$Nav && $Nav.declare('page.load'); };\n    if (window.addEventListener) {\n      window.addEventListener(\"load\", window._navbar.declareOnLoad, false);\n    } else if (window.attachEvent) {\n      window.attachEvent(\"onload\", window._navbar.declareOnLoad);\n    } else if (window.$Nav) {\n      $Nav.when('page.domReady').run(\"OnloadFallbackSetup\", function () {\n        window._navbar.declareOnLoad();\n      });\n    }\n    window.$Nav && $Nav.declare('logEvent.enabled',\n      'false');\n\n    window.$Nav && $Nav.declare('config.lightningDeals', {});\n  \n\n    \n       #nav-flyout-ewc .nav-flyout-buffer-left { display: none; } #nav-flyout-ewc .nav-flyout-buffer-right { display: none; } div#navSwmHoliday.nav-focus {border: none;margin: 0;}\n    \n    \n      try {\n        if(window.navmet===undefined)window.navmet=[]; if(window.$Nav) { $Nav.when('$', 'config', 'flyout.accountList', 'SignInRedirect', 'dataPanel').run('accountListRedirectFix', function ($, config, flyout, SignInRedirect, dataPanel) { if (!config.accountList) { return; } flyout.getPanel().onData(function (data) { if (SignInRedirect) { var $anchors = $('[data-nav-role=signin]', flyout.elem()); $.each($anchors, function(i, anchorEl) {SignInRedirect.setRedirectUrl($(anchorEl), null, null);});}});}); $Nav.when('$').run('defineIsArray', function(jQuery) { if(jQuery.isArray===undefined) { jQuery.isArray=function(param) { if(param.length===undefined) { return false; } return true; }; } }); $Nav.declare('config.cartFlyoutDisabled', 'true'); $Nav.when('$','$F','config','logEvent','panels','phoneHome','dataPanel','flyouts.renderPromo','flyouts.sloppyTrigger','flyouts.accessibility','util.mouseOut','util.onKey','debug.param').build('flyouts.buildSubPanels',function($,$F,config,logEvent,panels,phoneHome,dataPanel,renderPromo,createSloppyTrigger,a11yHandler,mouseOutUtility,onKey,debugParam){var flyoutDebug=debugParam('navFlyoutClick');return function(flyout,event){var linkKeys=[];$('.nav-item',flyout.elem()).each(function(){var $item=$(this);linkKeys.push({link:$item,panelKey:$item.attr('data-nav-panelkey')});});if(linkKeys.length===0){return;} var visible=false;var $parent=$('<div class=\\'nav-subcats\\'></div>').appendTo(flyout.elem());var panelGroup=flyout.getName()+'SubCats';var hideTimeout=null;var sloppyTrigger=createSloppyTrigger($parent);var showParent=function(){if(hideTimeout){clearTimeout(hideTimeout);hideTimeout=null;} if(visible){return;} var height=$('#nav-flyout-shopAll').height(); $parent.css({'height': height});$parent.animate({width:'show'},{duration:200,complete:function(){$parent.css({overflow:'visible'});}});visible=true;};var hideParentNow=function(){$parent.stop().css({overflow:'hidden',display:'none',width:'auto',height:'auto'});panels.hideAll({group:panelGroup});visible=false;if(hideTimeout){clearTimeout(hideTimeout);hideTimeout=null;}};var hideParent=function(){if(!visible){return;} if(hideTimeout){clearTimeout(hideTimeout);hideTimeout=null;} hideTimeout=setTimeout(hideParentNow,10);};flyout.onHide(function(){sloppyTrigger.disable();hideParentNow();this.elem().hide();});var addPanel=function($link,panelKey){var panel=dataPanel({className:'nav-subcat',dataKey:panelKey,groups:[panelGroup],spinner:false,visible:false});if(!flyoutDebug){var mouseout=mouseOutUtility();mouseout.add(flyout.elem());mouseout.action(function(){panel.hide();});mouseout.enable();} var a11y=a11yHandler({link:$link,onEscape:function(){panel.hide();$link.focus();}});var logPanelInteraction=function(promoID,wlTriggers){var logNow=$F.once().on(function(){var panelEvent=$.extend({},event,{id:promoID});if(config.browsePromos&&!!config.browsePromos[promoID]){panelEvent.bp=1;} logEvent(panelEvent);phoneHome.trigger(wlTriggers);});if(panel.isVisible()&&panel.hasInteracted()){logNow();}else{panel.onInteract(logNow);}};panel.onData(function(data){renderPromo(data.promoID,panel.elem());logPanelInteraction(data.promoID,data.wlTriggers);});panel.onShow(function(){var columnCount=$('.nav-column',panel.elem()).length;panel.elem().addClass('nav-colcount-'+columnCount);showParent();var $subCatLinks=$('.nav-subcat-links > a',panel.elem());var length=$subCatLinks.length;if(length>0){var firstElementLeftPos=$subCatLinks.eq(0).offset().left;for(var i=1;i<length;i++){if(firstElementLeftPos===$subCatLinks.eq(i).offset().left){$subCatLinks.eq(i).addClass('nav_linestart');}} if($('span.nav-title.nav-item',panel.elem()).length===0){var catTitle=$.trim($link.html());catTitle=catTitle.replace(/ref=sa_menu_top/g,'ref=sa_menu');var $subPanelTitle=$('<span class=\\'nav-title nav-item\\'>'+ catTitle+'</span>');panel.elem().prepend($subPanelTitle);}} $link.addClass('nav-active');});panel.onHide(function(){$link.removeClass('nav-active');hideParent();a11y.disable();sloppyTrigger.disable();});panel.onShow(function(){a11y.elems($('a, area',panel.elem()));});sloppyTrigger.register($link,panel);if(flyoutDebug){$link.click(function(){if(panel.isVisible()){panel.hide();}else{panel.show();}});} var panelKeyHandler=onKey($link,function(){if(this.isEnter()||this.isSpace()){panel.show();}},'keydown',false);$link.focus(function(){panelKeyHandler.bind();}).blur(function(){panelKeyHandler.unbind();});panel.elem().appendTo($parent);};var hideParentAndResetTrigger=function(){hideParent();sloppyTrigger.disable();};for(var i=0;i<linkKeys.length;i++){var item=linkKeys[i];if(item.panelKey){addPanel(item.link,item.panelKey);}else{item.link.mouseover(hideParentAndResetTrigger);}}};});};\n      } catch ( err ) {\n        if ( window.$Nav ) {\n          window.$Nav.when('metrics', 'logUeError').run(function(metrics, log) {\n            metrics.increment('NavJS:AboveNavInjection:error');\n            log(err.toString(), {\n              'attribution': 'rcx-nav',\n              'logLevel': 'FATAL'\n            });\n          });\n        }\n      }\n    \n\n  \n    <style type=\"text/css\"><!--\n      #navbar #nav-shop .nav-a:hover {\n        color: #ff9900;\n        text-decoration: underline;\n      }\n      #navbar #nav-search .nav-search-facade,\n      #navbar #nav-tools .nav-icon,\n      #navbar #nav-shop .nav-icon,\n      #navbar #nav-subnav .nav-hasArrow .nav-arrow {\n        display: none;\n      }\n      #navbar #nav-search .nav-search-submit,\n      #navbar #nav-search .nav-search-scope {\n        display: block;\n      }\n      #nav-search .nav-search-scope {\n        padding: 0 5px;\n      }\n      #navbar #nav-search .nav-search-dropdown {\n        position: relative;\n        top: 5px;\n        height: 23px;\n        font-size: 14px;\n        opacity: 1;\n        filter: alpha(opacity = 100);\n      }\n    --></style>\n \nwindow.navmet.push({key:'PreNav',end:+new Date(),begin:window.navmet.tmp});\n\n\n\n\n\n\nSkip to main content\n\nwindow.navmet.tmp=+new Date();\n\n    \n      \n      \n    \nwindow.navmet.push({key:'UpNav',end:+new Date(),begin:window.navmet.tmp});\n\n\nwindow.navmet.main=+new Date();\n\n\n\n\n\n   \n  \n    \n      \n        window.navmet.tmp=+new Date();\n  \n    \n      \n      \n      .us\n    \n  \nwindow.navmet.push({key:'Logo',end:+new Date(),begin:window.navmet.tmp});\n        \n\n    \n        \n            \n            \n                \n                   Deliver to\n                \n                \n                   Singapore\n                \n            \n        \n        \n        \n        \n        \n\n\n\n\n\n      \n          \n            window.navmet.tmp=+new Date();\n\n  \n  \n\n    \n      \n        \n  \n    \n      All\n      \n    \n    Select the department you want to search in\n    \n        All Departments\n        Arts & Crafts\n        Automotive\n        Baby\n        Beauty & Personal Care\n        Books\n        Boys' Fashion\n        Computers\n        Deals\n        Digital Music\n        Electronics\n        Girls' Fashion\n        Health & Household\n        Home & Kitchen\n        Industrial & Scientific\n        Kindle Store\n        Luggage\n        Men's Fashion\n        Movies & TV\n        Music, CDs & Vinyl\n        Pet Supplies\n        Prime Video\n        Software\n        Sports & Outdoors\n        Tools & Home Improvement\n        Toys & Games\n        Video Games\n        Women's Fashion\n    \n  \n\n      \n    \n    \n      \n        Search Amazon\n        \n      \n      \n    \n    \n      \n        \n          \n        \n      \n    \n  \n\nwindow.navmet.push({key:'Search',end:+new Date(),begin:window.navmet.tmp});\n          \n      \n          window.navmet.tmp=+new Date();\n          \n              \n              \n              \n              \n  \n    \n      \n      \n      \n        \n          EN\n        \n      \n    \n  \n\n              \n  \n  Hello, sign in\n  Account & Lists\n  \n\n\n              \n\n  Returns\n  & Orders\n\n\n              \n              \n  \n    \n      0\n      \n    \n    \n      \n        \n      \n      \n        Cart\n        \n      \n    \n  \n\n          \n          window.navmet.push({key:'Tools',end:+new Date(),begin:window.navmet.tmp});\n\n      \n    Sign inNew customer? Start here.Your ListsCreate a List Find a List or RegistryYour AccountAccount Orders Recommendations Browsing History Watchlist Video Purchases & Rentals Kindle Unlimited Content & Devices Subscribe & Save Items Memberships & Subscriptions Music LibrarySign inNew customer? Start here.\n    \n      \n        window.navmet.tmp=+new Date();\n  \n    \n    All\n  \n  \n\n  var hmenu = document.getElementById(\"nav-hamburger-menu\");\n  hmenu.setAttribute(\"href\", \"javascript: void(0)\");\n  window.navHamburgerMetricLogger = function() {\n    if (window.ue && window.ue.count) {\n      var metricName = \"Nav:Hmenu:IconClickActionPending\";\n      window.ue.count(metricName, (ue.count(metricName) || 0) + 1);\n    }\n    window.$Nav && $Nav.declare(\"navHMenuIconClicked\",!0);\n    window.$Nav && $Nav.declare(\"navHMenuIconClickedNotReadyTimeStamp\", Date.now());\n  };\n  hmenu.addEventListener(\"click\", window.navHamburgerMetricLogger);\n  window.$Nav && $Nav.declare('hamburgerMenuIconAvailableOnLoad', false);\n  \nwindow.navmet.push({key:'HamburgerMenuIcon',end:+new Date(),begin:window.navmet.tmp});\n      \n      \n        \n \n \n        \n          \n            window.navmet.tmp=+new Date();\nToday's Deals\n\nCustomer Service\n\nRegistry\n\nGift Cards\n\nSell\n\nDisability Customer Support\nwindow.navmet.push({key:'CrossShop',end:+new Date(),begin:window.navmet.tmp});\n          \n        \n      \n      \n        window.navmet.tmp=+new Date();\n\n  \nwindow.navmet.push({key:'SWM',end:+new Date(),begin:window.navmet.tmp});\n      \n    \n\n    \n\n    \n    \n      window.navmet.tmp=+new Date();\n\n\n  \n    \n      Appstore for Android\n      \n    \n    \n  \n  \n    \n      Amazon Coins\n      \n    \n    \n  \n  \n    \n      Fire Tablet Apps\n      \n    \n  \n  \n    \n      Fire TV Apps\n      \n    \n  \n  \n    \n      Games\n      \n    \n  \n  \n    \n      Your Apps & Subscriptions\n      \n    \n  \n  \n    \n      Help\n      \n    \n  \n\n\n\nwindow.navmet.push({key:'Subnav',end:+new Date(),begin:window.navmet.tmp});\n    \n\n    \n(function() {\n  var viewportWidth = function() {\n    return window.innerWidth ||\n      document.documentElement.clientWidth ||\n      document.body.clientWidth;\n  };\n\n  if (typeof uet === 'function') {  uet('x1', 'ewc', {wb: 1}); }\n\n  window.$Nav && $Nav.declare('config.ewc', (function() {\n    var config = {\"enablePersistent\":true,\"viewportWidthForPersistent\":1400,\"isEWCLogging\":1,\"isEWCStateExpanded\":true,\"EWCStateReason\":\"fixed\",\"isSmallScreenEnabled\":true,\"isFreshCustomer\":false,\"errorContent\":{\"html\":\"<div class='nav-ewc-error'><span class='nav-title'>Oops!</span><p class='nav-paragraph'>There's a problem loading your cart right now.</p><a href='/gp/cart/view.html?ref_=nav_err_ewc_timeout' class='nav-action-button'><span class='nav-action-inner'>Your Cart</span></a></div>\"},\"url\":\"/cart/ewc/compact?hostPageType=MASDetailPage&hostSubPageType=null&hostPageRID=K1JQ9VEP43RRWKW775V5&prerender=0&storeName=mobile-apps\",\"cartCount\":0,\"freshCartCount\":0,\"almCartCount\":0,\"primeWardrobeCartCount\":0,\"isCompactViewEnabled\":true,\"isCompactEWCRendered\":true,\"isWiderCompactEWCRendered\":true,\"EWCBrowserCacheKey\":\"EWC_Cache_134-1570085-9450222__USD_en_US\",\"isContentRepainted\":false,\"clearCache\":false,\"loadFromCacheWithDelay\":0,\"delayRenderingTillATF\":false};\n    var hasAui = window.P && window.P.AUI_BUILD_DATE;\n    var isRTLEnabled = (document.dir === 'rtl');\n    config.pinnable = config.pinnable && hasAui;\n    config.isMigrationTreatment = true;\n\n    config.flyout = (function() {\n      var navbelt = document.getElementById('nav-belt');\n      var navCart = document.getElementById('nav-cart');\n      var ewcFlyout = document.getElementById('nav-flyout-ewc');\n      var persistentClassOnBody = 'nav-ewc-persistent-hover nav-ewc-full-height-persistent-hover';\n      var flyout = {};\n\n      var getDocumentScrollTop = function() {\n        return (document.documentElement && document.documentElement.scrollTop) || document.body.scrollTop;\n      };\n\n      var isWindow = function(obj) {\n        return obj != null && obj === obj.window;\n      };\n\n      var getWindow = function(elem) {\n        return isWindow(elem) ? elem : elem.nodeType === 9 && elem.defaultView;\n      };\n\n      var getOffset = function(elem) {\n        if (elem.getClientRects && !elem.getClientRects().length) {\n          return {top: 0};\n        }\n\n        var rect = elem.getBoundingClientRect\n          ? elem.getBoundingClientRect()\n          : {top: 0};\n\n        if (rect.width || rect.height) {\n          var doc = elem.ownerDocument;\n          var win = getWindow(doc);\n          return {\n            top: rect.top + win.pageYOffset - doc.documentElement.clientTop\n          };\n        }\n        return rect;\n      };\n\n      flyout.align = function() {\n        var newTop = getOffset(navbelt).top - getDocumentScrollTop();\n        ewcFlyout.style.top = (newTop > 0 ? newTop + 'px' : 0);\n      };\n\n      flyout.hide = function() {\n        isRTLEnabled\n          ? (ewcFlyout.style.left = '')\n          : (ewcFlyout.style.right = '');\n      };\n\n      if(typeof config.isCompactEWCRendered === 'undefined') {\n        if (\n          (config.isSmallScreenEnabled && viewportWidth() < 1400) ||\n          (config.isCompactViewEnabled && viewportWidth() >= 1400)\n        ) {\n          config.isCompactEWCRendered = true;\n          config.isEWCStateExpanded = true;\n          config.url = config.url.replace(\"/gp/navcart/sidebar\", \"/cart/ewc/compact\");\n        } else {\n          config.isCompactEWCRendered = false;\n        }\n      }\n\n      var viewportQualifyForPersistent = function () {\n        return (config.isCompactEWCRendered)\n          ? true\n          : viewportWidth() >= 1400;\n      }\n\n      flyout.hasQualifiedViewportForPersistent = viewportQualifyForPersistent;\n\n      var getEWCRightOffset = function() {\n        if (!config.isCompactEWCRendered) {\n          return 0;\n        }\n\n        var $navbelt = document.getElementById('nav-belt');\n        if ($navbelt === undefined || $navbelt === null) {\n          return 0;\n        }\n\n        var EWCCompactViewWidth = (config.isWiderCompactEWCRendered  && viewportWidth() >= 1280) ? 130 : 100;\n        var scrollLeft = (window.pageXOffset !== undefined)\n          ? window.pageXOffset\n          : (document.documentElement || document.body.parentNode || document.body).scrollLeft;\n        var scrollXAxis = Math.abs(scrollLeft);\n        var windowWidth = document.documentElement.clientWidth;\n        var navbeltWidth = $navbelt.offsetWidth;\n        var isPartOfNavbarNotVisible = (navbeltWidth + EWCCompactViewWidth) > windowWidth;\n\n        if (isPartOfNavbarNotVisible) {\n          return 0 - (navbeltWidth - scrollXAxis - windowWidth + EWCCompactViewWidth);\n        } else {\n          return 0;\n        }\n      }\n\n      flyout.getEWCRightOffsetCssProperty = function () {\n        return getEWCRightOffset() + 'px';\n      }\n\n      if (config.isCompactEWCRendered) {\n        persistentClassOnBody = 'nav-ewc-persistent-hover nav-ewc-compact-view';\n        if (config.isWiderCompactEWCRendered) { persistentClassOnBody += ' nav-ewc-wider-compact-view'; }\n      }\n\n      flyout.show = function() {\n        isRTLEnabled\n          ? (ewcFlyout.style.left = flyout.getEWCRightOffsetCssProperty())\n          : (ewcFlyout.style.right = flyout.getEWCRightOffsetCssProperty());\n      };\n\n      var isIOSDevice = function() {\n        return (/iPad|iPhone|iPod/.test(navigator.platform) ||\n                (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1)) &&\n                !window.MSStream;\n      }\n\n      var checkForPersistent = function() {\n        if (!hasAui) {\n          return { result: false, reason: 'noAui' };\n        }\n        if (!config.enablePersistent) {\n          return { result: false, reason: 'config' };\n        }\n        if (!viewportQualifyForPersistent()) {\n          return { result: false, reason: 'viewport' };\n        }\n        if (isIOSDevice()) {\n          return { result: false, reason: 'iOS' };\n        }\n        if (!config.cartCount > 0) {\n          return { result: false, reason: 'emptycart' };\n        }\n        return { result: true };\n      };\n\n      flyout.ableToPersist = function() {\n        return checkForPersistent().result;\n      };\n      var persistentClassRegExp = '(?:^|\\\\s)' + persistentClassOnBody + '(?!\\\\S)';\n      flyout.applyPageLayoutForPersistent = function() {\n        if (!document.documentElement.className.match( new RegExp(persistentClassRegExp) )) {\n          document.documentElement.className += ' ' + persistentClassOnBody;\n        }\n      };\n\n      flyout.unapplyPageLayoutForPersistent = function() {\n        document.documentElement.className = document.documentElement.className.replace( new RegExp(persistentClassRegExp, 'g'), '');\n      };\n\n      flyout.persist = function() {\n        flyout.applyPageLayoutForPersistent();\n        flyout.show();\n        if (config.isCompactEWCRendered) {\n          flyout.align();\n        }\n      };\n\n      flyout.unpersist = function() {\n        flyout.unapplyPageLayoutForPersistent();\n        flyout.hide();\n      };\n      \n      var persistentCheck = checkForPersistent();\n    \n\n      var resizeCallback = function() {\n        \n        if (flyout.ableToPersist()) {\n          flyout.persist();\n        }\n        else {\n          flyout.unpersist();\n        }\n      };\n\n      flyout.bindEvents = function() {\n        if (window.addEventListener) {\n          window.addEventListener('resize', resizeCallback, false);\n          \n          if (config.isCompactEWCRendered) {\n            window.addEventListener('scroll', flyout.align, false);\n          }\n        }\n      };\n\n      flyout.unbindEvents = function() {\n        if (window.removeEventListener) {\n          window.removeEventListener('resize', resizeCallback, false);\n          \n          if (config.isCompactEWCRendered) {\n            window.removeEventListener('scroll', flyout.align, false);\n          }\n        }\n      };\n      \n      var ewcDefaultPersistence = function() {\n      \n        if (persistentCheck.result) {\n          flyout.persist();\n          if (window.ue && ue.tag) {\n            ue.tag('ewc:persist');\n          }\n        } else {\n          if (window.ue && ue.tag) {\n            ue.tag('ewc:unpersist');\n            if (persistentCheck.reason === 'noAui') {\n              ue.tag('ewc:unpersist:noAui');\n            }\n            if (persistentCheck.reason === 'viewport') {\n              ue.tag('ewc:unpersist:viewport');\n            }\n            if (persistentCheck.reason === 'emptycart') {\n              ue.tag('ewc:unpersist:emptycart');\n            }\n            if (persistentCheck.reason === 'iOS') {\n              ue.tag('ewc:unpersist:iOS');\n            }\n          }\n        }\n      };\n      \n      ewcDefaultPersistence();\n      \n      if (window.ue && ue.tag)  {\n        if (flyout.hasQualifiedViewportForPersistent()) {\n          ue.tag('ewc:bview');\n        }\n        else {\n          ue.tag('ewc:sview');\n        }\n      }\n      flyout.bindEvents();\n      flyout.cache = function () {\n    const cache = window.sessionStorage;\n    const CACHE_KEY = \"EWCBrowserCacheKey\";\n    const CACHE_EXPIRY = \"EWCBrowserCacheExpiry\"; \n    const CACHE_VALUE = \"EWCBrowserCacheValue\"; \n    const isSessionStorageValid = function () {\n        return window && cache && cache instanceof Storage;\n    };\n    const isCachePresent = function (key) {\n        return cache.length > 0 && cache.getItem(key);\n    }\n    const isValidType = function (value) {\n        // Prevents accessing empty key-value and internal methods(prototypes) of storage\n        // TODO: Log metrics for invalid access;\n        return value && value.constructor == String;\n    }\n    return {\n        getCache: function (key) {\n            const value = isCachePresent(key);\n            return (isValidType(value)) ? value : null;\n        },\n        setCache: function (key, value) {\n            const oldValue = isCachePresent(key);\n            const cacheExpiryTime = isCachePresent(CACHE_EXPIRY);\n            // Set the expiry when there's no existing cache - to prevent resetting expiry on page navigation\n            if (!cacheExpiryTime) {\n                var currentTime = new Date();\n                cache.setItem(CACHE_EXPIRY, new Date(currentTime.getTime() + 5 * 60000))\n            }\n            // TODO: Log length of old and new cache values when logMetrics is true\n            cache.setItem(key, value);\n        },\n        updateCacheAndEwcContainer: function (cacheKey, newEwcContent) {\n            const $ = $Nav.getNow(\"$\");\n            const $currentEwc = $(\"#ewc-content\");\n            if (!$currentEwc.length) {\n                var $content = $('#nav-flyout-ewc .nav-ewc-content');\n                $content.html(newEwcContent);\n                this.setCache(CACHE_KEY, cacheKey);\n                if (window.ue && window.ue.count) {\n                    var current = window.ue.count(\"ewc-init-cache\") || 0;\n                    window.ue.count(\"ewc-init-cache\", current + 1);\n                }\n            } else {\n                var $newEwcContent = $('<div />');\n                var EWC_CONTENT_BODY_SCROLL_SELECTOR = \".ewc-scroller--selected\";\n                if (newEwcContent) { // 1. Updates EWC container with new HTML \n                    const $newEwcHtml = $newEwcContent.html(newEwcContent).find(\"#ewc-content\");\n                    const offSet = $currentEwc.find(EWC_CONTENT_BODY_SCROLL_SELECTOR).position().top - $currentEwc.find(\".ewc-active-cart--selected\").position().top;\n                    $currentEwc.html($newEwcHtml.html());\n                    $currentEwc.find(EWC_CONTENT_BODY_SCROLL_SELECTOR).scrollTop(offSet);\n                    if (typeof window.uex === 'function') {\n                        window.uex('ld', 'ewc-reflect-new-state', {wb: 1});\n                    }\n                } else {\n                    // 2. Fetches cached response and updates it's html with new state on EWC Update\n                    const cachedEwc = this.getCache(CACHE_VALUE);\n                    $newEwcContent = $newEwcContent[0];\n                    $(cachedEwc).map(function (elementIndex, element) {\n                         $newEwcContent.appendChild((element.id === \"ewc-content\") ? $currentEwc.clone()[0] : element);\n                    });\n                    newEwcContent = $newEwcContent.innerHTML;\n                    if (window.ue && window.ue.count) {\n                        var current = window.ue.count(\"ewc-update-cache\") || 0;\n                        window.ue.count(\"ewc-update-cache\", current + 1);\n                    }\n                }\n                $newEwcContent.remove();\n            }\n            this.setCache(CACHE_VALUE, newEwcContent);\n        },\n        removeCache: function (key) {\n            return cache.removeItem(key);\n        }\n    }\n}\n;\n      return flyout;\n    }());\n     \n     \n     \nconst CACHE_KEY = \"EWCBrowserCacheKey\";\nconst CACHE_VALUE = \"EWCBrowserCacheValue\"; \nconst CACHE_EXPIRY = \"EWCBrowserCacheExpiry\"; \nvar cache = config.flyout.cache();\n\nconst isCacheValid = function () {\n  // Check for page types and tenure of the cache\n  const clearCache = config.clearCache;\n  const cacheExpiryTime = cache.getCache(CACHE_EXPIRY);\n  const isCacheExpired = new Date() > new Date(cacheExpiryTime);\n  const cacheKey = config.EWCBrowserCacheKey;\n  const oldCacheKey = cache.getCache(CACHE_KEY);\n  const isCacheValid = !clearCache && !isCacheExpired && cacheKey == oldCacheKey;\n  if (!isCacheValid && window.ue && window.ue.count) {\n    var current = window.ue.count(\"ewc-cache-invalidated\") || 0;\n    window.ue.count(\"ewc-cache-invalidated\", current + 1);\n  }\n  return isCacheValid;\n}\nfunction loadFromCache() {\n    if (window.uet && typeof window.uet === 'function') {\n        window.uet('bb', 'ewc-loaded-from-cache', {wb: 1});\n    }\n    if (cache) {\n        if (isCacheValid()) {\n            var content = cache.getCache(CACHE_VALUE);\n            if (content) {\n                var $ewcContainer = document.getElementById(\"nav-flyout-ewc\").getElementsByClassName(\"nav-ewc-content\")[0];\n                var $ewcContent = document.getElementById(\"ewc-content\");\n                if ($ewcContainer && !$ewcContent) {\n                    $ewcContainer.innerHTML = content;\n                    // Execute scripts from cache\n                    const ewcJavascript = document.getElementById(\"ewc-content\").parentNode.querySelectorAll(':scope > script');\n                    ewcJavascript.forEach(function (script) {\n                        var scriptTag = document.createElement(\"script\");\n                        scriptTag.innerHTML = script.innerHTML;\n                        document.body.appendChild(scriptTag);\n                    });\n                    if (typeof window.uex === 'function') {\n                        window.uex('ld', 'ewc-loaded-from-cache', {wb: 1});\n                    }\n                } else if (window.ue && window.ue.count && typeof window.ue.count === 'function') {\n                    var currentFailure = window.ue.count(\"ewc-slow-cache\") || 0;\n                    window.ue.count(\"ewc-slow-cache\", currentFailure + 1);\n                }\n            }\n        } else {\n            cache.removeCache(CACHE_VALUE);\n            cache.removeCache(CACHE_KEY);\n            cache.removeCache(CACHE_EXPIRY);\n        }\n    }\n}\nfunction delayBy(delayTime) {\n    if (delayTime) {\n        window.setTimeout(function() {\n            loadFromCache();\n        }, delayTime)\n    } else {\n        loadFromCache();\n    }\n}\nif(config.delayRenderingTillATF) {\n    (window.AmazonUIPageJS ? AmazonUIPageJS : P).when('atf').execute(\"EverywhereCartLoadFromCacheOnAtf\", function () {\n        delayBy(config.loadFromCacheWithDelay);\n    });\n} else {\n    delayBy(config.loadFromCacheWithDelay);\n}\n\n    return config;\n  }()));\n\n  if (typeof uet === 'function') {\n    uet('x2', 'ewc', {wb: 1});\n  }\n\n  if (window.ue && ue.tag) {\n    ue.tag('ewc');\n    ue.tag('ewc:unrec');\n    ue.tag('ewc:cartsize:0');\n\n    if ( window.P && window.P.AUI_BUILD_DATE ) {\n      ue.tag('ewc:aui');\n    } else {\n      ue.tag('ewc:noAui');\n    }\n  }\n}());\n\n   \n\n  \n  \n\n\n\n\nwindow.navmet.push({key:'NavBar',end:+new Date(),begin:window.navmet.main});\n\n\n\n  if (window.ue_t0) {\n    window.navmet.push({key:\"NavMainPaintEnd\",end:+new Date(),begin:window.ue_t0});\n    window.navmet.push({key:\"NavFirstPaintEnd\",end:+new Date(),begin:window.ue_t0});\n  }\n\n\n\n\n    <!--\n    \n    window.$Nav && $Nav.when(\"data\").run(function(data) { data({\"freshTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"title\":\"<style>#nav-flyout-fresh{width:269px;padding:0;}#nav-flyout-fresh .nav-flyout-content{padding:0;}</style><a href='/amazonfresh'><img src='https://images-na.ssl-images-amazon.com/images/G/01/omaha/images/yoda/flyout_72dpi._V270255989_.png' /></a>\"}}}},\"cartTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"button\":{\"text\":\"Your Cart\",\"url\":\"/gp/cart/view.html?ref_=nav_err_cart_timeout\"},\"title\":\"Oops!\",\"paragraph\":\"Unable to retrieve your cart.\"}}}},\"primeTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"title\":\"<a href='/gp/prime'><img src='https://images-na.ssl-images-amazon.com/images/G/01/prime/piv/YourPrimePIV_fallback_CTA._V327346943_.jpg' /></a>\"}}}},\"ewcTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"button\":{\"text\":\"Your Cart\",\"url\":\"/gp/cart/view.html?ref_=nav_err_ewc_timeout\"},\"title\":\"Oops!\",\"paragraph\":\"There's a problem loading your cart right now.\"}}}},\"errorWishlist\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"button\":{\"text\":\"Your Wishlist\",\"url\":\"/gp/registry/wishlist/?ref_=nav_err_wishlist\"},\"title\":\"Oops!\",\"paragraph\":\"Unable to retrieve your wishlist\"}}}},\"emptyWishlist\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"button\":{\"text\":\"Your Wishlist\",\"url\":\"/gp/registry/wishlist/?ref_=nav_err_empty_wishlist\"},\"title\":\"Oops!\",\"paragraph\":\"Your list is empty\"}}}},\"yourAccountContent\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"button\":{\"text\":\"Your Account\",\"url\":\"/gp/css/homepage.html?ref_=nav_err_youraccount\"},\"title\":\"Oops!\",\"paragraph\":\"Unable to retrieve your account\"}}}},\"shopAllTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"paragraph\":\"Unable to retrieve departments, please try again later\"}}}},\"kindleTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"paragraph\":\"Unable to retrieve list, please try again later\"}}}}}); });\nwindow.$Nav && $Nav.when(\"util.templates\").run(\"FlyoutErrorTemplate\", function(templates) {\n      templates.add(\"flyoutError\", \"<# if(error.title) { #><span class='nav-title'><#=error.title #></span><# } #><# if(error.paragraph) { #><p class='nav-paragraph'><#=error.paragraph #></p><# } #><# if(error.button) { #><a href='<#=error.button.url #>' class='nav-action-button' ><span class='nav-action-inner'><#=error.button.text #></span></a><# } #>\");\n    });\n\n    if (typeof uet == 'function') {\n    uet('bb', 'iss-init-pc', {wb: 1});\n  }\n  if (!window.$SearchJS && window.$Nav) {\n    window.$SearchJS = $Nav.make('sx');\n  }\n\n  var opts = {\n    host: \"completion.amazon.com/search/complete\"\n  , marketId: \"1\"\n  , obfuscatedMarketId: \"ATVPDKIKX0DER\"\n  , searchAliases: [\"aps\",\"amazon-custom-products\",\"amazon-devices\",\"amazonbasics\",\"amazonfresh\",\"amazon-pharmacy\",\"wholefoods\",\"allthebestpets\",\"bartelldrugs\",\"bristolfarms\",\"cardenas\",\"familyfare\",\"freshthyme\",\"kegnbottle\",\"missionwinespirits\",\"petfoodexpress\",\"savemart\",\"sousaswineliquors\",\"surdyksliquorcheeseshop\",\"weis\",\"stripbooks\",\"popular\",\"apparel\",\"electronics\",\"sporting\",\"sports-and-fitness\",\"outdoor-recreation\",\"fan-shop\",\"garden\",\"videogames\",\"toys-and-games\",\"jewelry\",\"digital-text\",\"digital-music\",\"prime-digital-music\",\"watches\",\"grocery\",\"hpc\",\"instant-video\",\"handmade\",\"handmade-jewelry\",\"handmade-home-and-kitchen\",\"prime-instant-video\",\"shop-instant-video\",\"baby-products\",\"office-products\",\"software\",\"smart-home\",\"magazines\",\"tools\",\"automotive\",\"misc\",\"industrial\",\"mi\",\"pet-supplies\",\"digital-music-track\",\"digital-music-album\",\"mobile\",\"mobile-apps\",\"movies-tv\",\"music-artist\",\"music-album\",\"music-song\",\"stripbooks-spanish\",\"electronics-accessories\",\"photo\",\"audio-video\",\"computers\",\"furniture\",\"kitchen\",\"audible\",\"audiobooks\",\"beauty\",\"shoes\",\"arts-crafts\",\"appliances\",\"gift-cards\",\"pets\",\"outdoor\",\"lawngarden\",\"collectibles\",\"replacement-parts\",\"financial\",\"fine-art\",\"fashion\",\"fashion-womens\",\"fashion-womens-clothing\",\"fashion-womens-jewelry\",\"fashion-womens-shoes\",\"fashion-womens-watches\",\"fashion-womens-handbags\",\"fashion-mens\",\"fashion-mens-clothing\",\"fashion-mens-jewelry\",\"fashion-mens-shoes\",\"fashion-mens-watches\",\"fashion-girls\",\"fashion-girls-clothing\",\"fashion-girls-jewelry\",\"fashion-girls-shoes\",\"fashion-girls-watches\",\"fashion-boys\",\"fashion-boys-clothing\",\"fashion-boys-jewelry\",\"fashion-boys-shoes\",\"fashion-boys-watches\",\"fashion-baby\",\"fashion-baby-boys\",\"fashion-baby-girls\",\"fashion-luggage\",\"3d-printing\",\"tradein-aps\",\"todays-deals\",\"live-explorations\",\"local-services\",\"vehicles\",\"video-shorts\",\"warehouse-deals\",\"luxury-beauty\",\"banjo-apps\",\"black-friday\",\"cyber-monday\",\"alexa-skills\",\"subscribe-with-amazon\",\"courses\",\"edu-alt-content\",\"amazon-global-store\",\"prime-wardrobe\",\"under-ten-dollars\",\"tempo\",\"specialty-aps-sns\",\"luxury\"]\n  , filterAliases: []\n  , pageType: \"MASDetailPage\"\n  , requestId: \"K1JQ9VEP43RRWKW775V5\"\n  , sessionId: \"134-1570085-9450222\"\n  , language: \"en_US\"\n  , customerId: \"\"\n  , asin: \"\"\n  , b2b: 0\n  , fresh: 0\n  , isJpOrCn: 0\n  , isUseAuiIss: 1\n};\n\nvar issOpts = {\n    fallbackFlag: 1\n  , isDigitalFeaturesEnabled: 0\n  , isWayfindingEnabled: 1\n  , dropdown: \"select.searchSelect\"\n  , departmentText: \"in {department}\"\n  , suggestionText: \"Search suggestions\"\n  , recentSearchesTreatment: \"C\"\n  , authorSuggestionText: \"Explore books by XXAUTHXX\"\n  , translatedStringsMap: {\"sx-recent-searches\":\"Recent searches\",\"sx-your-recent-search\":\"Inspired by your recent search\"}\n  , biaTitleText: \"\"\n  , biaPurchasedText: \"\"\n  , biaViewAllText: \"\"\n  , biaViewAllManageText: \"\"\n  , biaAndText: \"\"\n  , biaManageText: \"\"\n  , biaWeblabTreatment: \"\"\n  , issNavConfig: {}\n  , np: 0\n  , issCorpus: []\n  , cf: 1\n  , removeDeepNodeISS: \"\"\n  , trendingTreatment: \"C\"\n  , useAPIV2: \"\"\n  , opfSwitch: \"\"\n  , isISSDesktopRefactorEnabled: \"1\"\n  , useServiceHighlighting: \"true\"\n  , isInternal: 0\n  , isAPICachingDisabled: true\n  , isBrowseNodeScopingEnabled: false\n  , isStorefrontTemplateEnabled: false\n  , disableAutocompleteOnFocus: \"\"\n};\n\n  if (opts.isUseAuiIss === 1 && window.$Nav) {\n  window.$Nav.when('sx.iss').run('iss-mason-init', function(iss){\n    var issInitObj = buildIssInitObject(opts, issOpts, true);\n    new iss.IssParentCoordinator(issInitObj);\n\n    $SearchJS.declare('canCreateAutocomplete', issInitObj);\n  });\n} else if (window.$SearchJS) {\n  var iss;\n\n  // BEGIN Deprecated globals\n  var issHost = opts.host\n    , issMktid = opts.marketId\n    , issSearchAliases = opts.searchAliases\n    , updateISSCompletion = function() { iss.updateAutoCompletion(); };\n  // END deprecated globals\n\n\n  $SearchJS.when('jQuery', 'search-js-autocomplete-lib').run('autocomplete-init', initializeAutocomplete);\n  $SearchJS.when('canCreateAutocomplete').run('createAutocomplete', createAutocomplete);\n\n} // END conditional for window.$SearchJS\n  function initializeAutocomplete(jQuery) {\n  var issInitObj = buildIssInitObject(opts, issOpts);\n  $SearchJS.declare(\"canCreateAutocomplete\", issInitObj);\n} // END initializeAutocomplete\n  function initSearchCsl(searchCSL, issInitObject) {\n  searchCSL.init(\n    opts.pageType,\n    (window.ue && window.ue.rid) || opts.requestId\n  );\n  $SearchJS.declare(\"canCreateAutocomplete\", issInitObject);\n} // END initSearchCsl\n  function createAutocomplete(issObject) {\n  iss = new AutoComplete(issObject);\n\n  $SearchJS.publish(\"search-js-autocomplete\", iss);\n\n  logMetrics();\n} // END createAutocomplete\n  function buildIssInitObject(opts, issOpts, isNewIss) {\n    var issInitObj = {\n        src: opts.host\n      , sessionId: opts.sessionId\n      , requestId: opts.requestId\n      , mkt: opts.marketId\n      , obfMkt: opts.obfuscatedMarketId\n      , pageType: opts.pageType\n      , language: opts.language\n      , customerId: opts.customerId\n      , fresh: opts.fresh\n      , b2b: opts.b2b\n      , aliases: opts.searchAliases\n      , fb: issOpts.fallbackFlag\n      , isDigitalFeaturesEnabled: issOpts.isDigitalFeaturesEnabled\n      , isWayfindingEnabled: issOpts.isWayfindingEnabled\n      , issPrimeEligible: issOpts.issPrimeEligible\n      , deptText: issOpts.departmentText\n      , sugText: issOpts.suggestionText\n      , filterAliases: opts.filterAliases\n      , biaWidgetUrl: opts.biaWidgetUrl\n      , recentSearchesTreatment: issOpts.recentSearchesTreatment\n      , authorSuggestionText: issOpts.authorSuggestionText\n      , translatedStringsMap: issOpts.translatedStringsMap\n      , biaTitleText: \"\"\n      , biaPurchasedText: \"\"\n      , biaViewAllText: \"\"\n      , biaViewAllManageText: \"\"\n      , biaAndText: \"\"\n      , biaManageText: \"\"\n      , biaWeblabTreatment: \"\"\n      , issNavConfig: issOpts.issNavConfig\n      , cf: issOpts.cf\n      , ime: opts.isJpOrCn\n      , mktid: opts.marketId\n      , qs: opts.isJpOrCn\n      , issCorpus: issOpts.issCorpus\n      , deepNodeISS: {\n          searchAliasAccessor: function($) {\n            return (window.SearchPageAccess && window.SearchPageAccess.searchAlias()) ||\n                   $('select.searchSelect').children().attr('data-root-alias');\n          },\n          searchAliasDisplayNameAccessor: function() {\n            return (window.SearchPageAccess && window.SearchPageAccess.searchAliasDisplayName());\n          }\n        }\n      , removeDeepNodeISS: issOpts.removeDeepNodeISS\n      , trendingTreatment: issOpts.trendingTreatment\n      , useAPIV2: issOpts.useAPIV2\n      , opfSwitch: issOpts.opfSwitch\n      , isISSDesktopRefactorEnabled: issOpts.isISSDesktopRefactorEnabled\n      , useServiceHighlighting: issOpts.useServiceHighlighting\n      , isInternal: issOpts.isInternal\n      , isAPICachingDisabled: issOpts.isAPICachingDisabled\n      , isBrowseNodeScopingEnabled: issOpts.isBrowseNodeScopingEnabled\n      , isStorefrontTemplateEnabled: issOpts.isStorefrontTemplateEnabled\n      , disableAutocompleteOnFocus: issOpts.disableAutocompleteOnFocus\n      , asin: opts.asin\n    };\n  \n    // If we aren't using the new ISS then we need to add these properties\n    \n    if (!isNewIss) {\n      issInitObj.dd = issOpts.dropdown; // The element with id searchDropdownBox doesn't exist in C.\n      issInitObj.imeSpacing = issOpts.imeSpacing;\n      issInitObj.isNavInline = 1;\n      issInitObj.triggerISSOnClick = 0;\n      issInitObj.sc = 1;\n      issInitObj.np = issOpts.np;\n    }\n  \n    return issInitObj;\n  } // END buildIssInitObject\n  function logMetrics() {\n  if (typeof uet == 'function' && typeof uex == 'function') {\n      uet('be', 'iss-init-pc',\n          {\n              wb: 1\n          });\n      uex('ld', 'iss-init-pc',\n          {\n              wb: 1\n          });\n  }\n} // END logMetrics\n  \n    \nwindow.$Nav && $Nav.declare('config.navDeviceType','desktop');\n\nwindow.$Nav && $Nav.declare('config.navDebugHighres',false);\n\nwindow.$Nav && $Nav.declare('config.pageType','MASDetailPage');\nwindow.$Nav && $Nav.declare('config.subPageType','null');\n\nwindow.$Nav && $Nav.declare('config.dynamicMenuUrl','\\x2Fgp\\x2Fnavigation\\x2Fajax\\x2Fdynamic\\x2Dmenu.html');\n\nwindow.$Nav && $Nav.declare('config.dismissNotificationUrl','\\x2Fgp\\x2Fnavigation\\x2Fajax\\x2Fdismissnotification.html');\n\nwindow.$Nav && $Nav.declare('config.enableDynamicMenus',true);\n\nwindow.$Nav && $Nav.declare('config.isInternal',false);\n\nwindow.$Nav && $Nav.declare('config.isBackup',false);\n\nwindow.$Nav && $Nav.declare('config.isRecognized',false);\n\nwindow.$Nav && $Nav.declare('config.transientFlyoutTrigger','\\x23nav\\x2Dtransient\\x2Dflyout\\x2Dtrigger');\n\nwindow.$Nav && $Nav.declare('config.subnavFlyoutUrl','\\x2Fnav\\x2Fajax\\x2FsubnavFlyout');\nwindow.$Nav && $Nav.declare('config.isSubnavFlyoutMigrationEnabled',true);\n\nwindow.$Nav && $Nav.declare('config.recordEvUrl','\\x2Fgp\\x2Fnavigation\\x2Fajax\\x2Frecordevent.html');\nwindow.$Nav && $Nav.declare('config.recordEvInterval',15000);\nwindow.$Nav && $Nav.declare('config.sessionId','134\\x2D1570085\\x2D9450222');\nwindow.$Nav && $Nav.declare('config.requestId','K1JQ9VEP43RRWKW775V5');\n\nwindow.$Nav && $Nav.declare('config.alexaListEnabled',true);\n\nwindow.$Nav && $Nav.declare('config.readyOnATF',false);\n\nwindow.$Nav && $Nav.declare('config.dynamicMenuArgs',{\"rid\":\"K1JQ9VEP43RRWKW775V5\",\"isFullWidthPrime\":0,\"isPrime\":0,\"dynamicRequest\":1,\"weblabs\":\"\",\"isFreshRegionAndCustomer\":\"\",\"primeMenuWidth\":310});\n\nwindow.$Nav && $Nav.declare('config.customerName',false);\n\nwindow.$Nav && $Nav.declare('config.customerCountryCode','SG');\n\nwindow.$Nav && $Nav.declare('config.yourAccountPrimeURL',null);\n\nwindow.$Nav && $Nav.declare('config.yourAccountPrimeHover',true);\n\nwindow.$Nav && $Nav.declare('config.searchBackState',{});\n\nwindow.$Nav && $Nav.declare('nav.inline');\n\n(function (i) {\n  if(window._navbarSpriteUrl) {\n    i.onload = function() {window.uet && uet('ne')};\n    i.src = window._navbarSpriteUrl;\n  }\n}(new Image()));\n\nwindow.$Nav && $Nav.declare('config.autoFocus',false);\n\nwindow.$Nav && $Nav.declare('config.responsiveTouchAgents',[\"ieTouch\"]);\n\nwindow.$Nav && $Nav.declare('config.responsiveGW',false);\n\nwindow.$Nav && $Nav.declare('config.pageHideEnabled',false);\n\nwindow.$Nav && $Nav.declare('config.sslTriggerType','flyoutProximityLarge');\nwindow.$Nav && $Nav.declare('config.sslTriggerRetry',0);\n\nwindow.$Nav && $Nav.declare('config.doubleCart',false);\n\nwindow.$Nav && $Nav.declare('config.signInOverride',true);\n\nwindow.$Nav && $Nav.declare('config.signInTooltip',true);\n\nwindow.$Nav && $Nav.declare('config.isPrimeMember',false);\n\nwindow.$Nav && $Nav.declare('config.packardGlowTooltip',false);\n\nwindow.$Nav && $Nav.declare('config.packardGlowFlyout',false);\n\nwindow.$Nav && $Nav.declare('config.rightMarginAlignEnabled',true);\n\nwindow.$Nav && $Nav.declare('config.flyoutAnimation',false);\n\nwindow.$Nav && $Nav.declare('config.campusActivation','null');\n\nwindow.$Nav && $Nav.declare('config.primeTooltip',false);\n\nwindow.$Nav && $Nav.declare('config.primeDay',false);\n\nwindow.$Nav && $Nav.declare('config.disableBuyItAgain',false);\n\nwindow.$Nav && $Nav.declare('config.enableCrossShopBiaFlyout',false);\n\nwindow.$Nav && $Nav.declare('config.pseudoPrimeFirstBrowse',null);\n\nwindow.$Nav && $Nav.declare('config.sdaYourAccount',false);\n\nwindow.$Nav && $Nav.declare('config.csYourAccount',{\"url\":\"/gp/youraccount/navigation/sidepanel\"});\n\nwindow.$Nav && $Nav.declare('config.cartFlyoutDisabled',true);\n\nwindow.$Nav && $Nav.declare('config.isTabletBrowser',false);\n\nwindow.$Nav && $Nav.declare('config.HmenuProximityArea',[200,200,200,200]);\n\nwindow.$Nav && $Nav.declare('config.HMenuIsProximity',true);\n\nwindow.$Nav && $Nav.declare('config.isPureAjaxALF',false);\n\nwindow.$Nav && $Nav.declare('config.accountListFlyoutRedesign',false);\n\nwindow.$Nav && $Nav.declare('config.navfresh',false);\n\nwindow.$Nav && $Nav.declare('config.isFreshRegion',false);\n\nif (window.ue && ue.tag) { ue.tag('navbar'); };\n\nwindow.$Nav && $Nav.declare('config.blackbelt',true);\n\nwindow.$Nav && $Nav.declare('config.beaconbelt',true);\n\nwindow.$Nav && $Nav.declare('config.accountList',true);\n\nwindow.$Nav && $Nav.declare('config.iPadTablet',false);\n\nwindow.$Nav && $Nav.declare('config.searchapiEndpoint',false);\n\nwindow.$Nav && $Nav.declare('config.timeline',false);\n\nwindow.$Nav && $Nav.declare('config.timelineAsinPriceEnabled',false);\n\nwindow.$Nav && $Nav.declare('config.timelineDeleteEnabled',false);\n\n\n\nwindow.$Nav && $Nav.declare('config.extendedFlyout',false);\n\nwindow.$Nav && $Nav.declare('config.flyoutCloseDelay',600);\n\nwindow.$Nav && $Nav.declare('config.pssFlag',0);\n\nwindow.$Nav && $Nav.declare('config.isPrimeTooltipMigrated',false);\n\nwindow.$Nav && $Nav.declare('config.hashCustomerAndSessionId','cb6f45e5ba7a6bd7ebab810301f387e765ca16d5');\n\nwindow.$Nav && $Nav.declare('config.isExportMode',true);\n\nwindow.$Nav && $Nav.declare('config.languageCode','en_US');\n\nwindow.$Nav && $Nav.declare('config.environmentVFI','AmazonNavigationCards\\x2Fdevelopment\\x40B6135401680\\x2DAL2_x86_64');\n\nwindow.$Nav && $Nav.declare('config.isHMenuBrowserCacheDisable',false);\n\nwindow.$Nav && $Nav.declare('config.signInUrlWithRefTag','https\\x3A\\x2F\\x2Fwww.amazon.com\\x2Fap\\x2Fsignin\\x3Fopenid.pape.max_auth_age\\x3D0\\x26openid.return_to\\x3Dhttps\\x253A\\x252F\\x252Fwww.amazon.com\\x252FSmallthing\\x2DEasy\\x2DSpider\\x2DSolitaire\\x252Fdp\\x252FB00G36GT5A\\x252F\\x253F_encoding\\x253DUTF8\\x2526\\x2AVersion\\x2A\\x253D1\\x2526\\x2Aentries\\x2A\\x253D0\\x2526asin\\x253DB00G36GT5A\\x2526ie\\x253DUTF8\\x2526ref_\\x253DnavSignInUrlRefTagPlaceHolder\\x26openid.identity\\x3Dhttp\\x253A\\x252F\\x252Fspecs.openid.net\\x252Fauth\\x252F2.0\\x252Fidentifier_select\\x26openid.assoc_handle\\x3Dusflex\\x26openid.mode\\x3Dcheckid_setup\\x26openid.claimed_id\\x3Dhttp\\x253A\\x252F\\x252Fspecs.openid.net\\x252Fauth\\x252F2.0\\x252Fidentifier_select\\x26openid.ns\\x3Dhttp\\x253A\\x252F\\x252Fspecs.openid.net\\x252Fauth\\x252F2.0');\n\nwindow.$Nav && $Nav.declare('config.regionalStores',[]);\n\nwindow.$Nav && $Nav.declare('config.isALFRedesignPT2',true);\n\nwindow.$Nav && $Nav.declare('config.isNavALFRegistryGiftList',false);\n\nwindow.$Nav && $Nav.declare('config.marketplaceId','ATVPDKIKX0DER');\n\nwindow.$Nav && $Nav.declare('config.exportTransitionState','none');\n\nwindow.$Nav && $Nav.declare('config.enableAeeXopFlyout',false);\n\nwindow.$Nav && $Nav.declare('config.isPrimeFlyoutMigrationEnabled',false);\n\nwindow.$Nav && $Nav.declare('config.isAjaxMigrated',true);\n\nwindow.$Nav && $Nav.declare('config.isAjaxPaymentNotificationMigrated',false);\n\nif (window.P && typeof window.P.declare === \"function\" && typeof window.P.now === \"function\") {\n  window.P.now('packardGlowIngressJsEnabled').execute(function(glowEnabled) {\n    if (!glowEnabled) {\n      window.P.declare('packardGlowIngressJsEnabled', true);\n    }\n  });\n  window.P.now('packardGlowStoreName').execute(function(storeName) {\n    if (!storeName) {\n      window.P.declare('packardGlowStoreName','mobile\\x2Dapps');\n    }\n  });\n}\n\nwindow.$Nav && $Nav.declare('configComplete');\n\n    -->\n\n\n\n\n\nwindow.navmet.MainEnd = new Date();\n\n    if (window.ue_t0) {\n      window.navmet.push({key:\"NavMainEnd\",end:+new Date(),begin:window.ue_t0});\n    }\n\n\n\n\n    {\"AUI_ACCORDION_A11Y_ROLE_354025\":\"T1\"}\n            \n                        \n                                \n                                    \n                                    \n                                \n                            \n                        \n                            \n                                \n                                \n    \n      \n         \n                  Easy Spider SolitaireEasy Spider Solitaire\n             \n    \n    \n      by Smallthing\n\n\n                                 4.1 out of 5 stars17  customer ratings\n                                \n                                \n   Guidance Suggested\n\n\n                            \n                                \n                                    \n                                    \n\n\n\n\n   \n      \n         \n         \n      \n      \n   \n\n\n\n\n\n                                \n                                    \n                                    \n\n\n\n\n\n    \n\n    \n        \n            \n            \n                \n\n\n\n\n    \n        \n    \n    \n    \n\n\n\n    \n        \n            Price:\n        \n\n        \n            \n                \n                    \n                        Free Download\n                    \n                    \n                \n            \n\n            \n        \n    \n\n    \n\n\n            \n        \n\n        \n    \n\n\n                                    \n   Sold by:Amazon.com Services LLC\n\n                                    \n                                    \n                                    \n        \n            \n                Languages Supported:\n            \n                English, French, German, Italian, Portuguese, Russian, Spanish\n        \n    \n\n                            \n\n                            \n                                \n                                \n                                \n    \n\n\n\n\n    \n        Get this app\n    \n    \n        Please sign in before purchasing\n        (Why?)\n    \n\n    \n        \n            \n            \n            \n                \n            \n        \n\n\n        \n            Sign in\n        \n    \n\n\n(function(f) {var _np=(window.P._namespace(\"masrw-sign-in\"));if(_np.guardFatal){_np.guardFatal(f)(_np);}else{f(_np);}}(function(P) {\n    P.when('A').execute(function(A) {\n        A.declarative('masrw-sign-in', 'click', function(event) {\n            window.location.href= \"/gp/sign-in.html/ref=mas_buy_sign_in?path=%2Fdp%2FB00G36GT5A&useRedirectOnSuccess=1\";\n        });\n    });\n}));\nLearn how buying works\n\n                                By placing your order, you agree to our Terms of Use\n                        \n                    \n                (function(f) {var _np=(window.P._namespace(\"\"));if(_np.guardFatal){_np.guardFatal(f)(_np);}else{f(_np);}}(function(P) {\n                if (typeof uet == 'function') {\n                    uet('af');\n                }\n            }));\n            \n                \n                    \n\n                \n                \n                    \n\n\n\n\n\n    \n        \n        \n            Screenshots\n        \n    \n\n\n\n    \n    \n        \n        \n        \n            0:000:00This video is not intended for all audiences. What date were you born?JanuaryFebruaryMarchAprilMayJuneJulyAugustSeptemberOctoberNovemberDecember123456789101112131415161718192021222324252627282930312023202220212020201920182017201620152014201320122011201020092008200720062005200420032002200120001999199819971996199519941993199219911990198919881987198619851984198319821981198019791978197719761975197419731972197119701969196819671966196519641963196219611960195919581957195619551954195319521951195019491948194719461945194419431942194119401939193819371936193519341933193219311930192919281927192619251924192319221921192019191918191719161915191419131912191119101909190819071906190519041903190219011900SubmitAdobe Flash Player is required to watch this video.Install Flash Player\n        \n    \n\n    \n    \n    \n    \n    \n    \n        \n    \n\n\n    \n    \n        \n            \n        \n        \n            \n                \n                \n\n                \n                    \n                        \n                            \n                        \n                    \n                    \n                \n                    \n                        \n                            \n                        \n                    \n                    \n                \n                    \n                        \n                            \n                        \n                    \n                    \n                \n                    \n                        \n                            \n                        \n                    \n                    \n                \n                    \n                        \n                            \n                        \n                    \n                    \n                \n\n                \n                \n                    \n                        \n                            \n                        \n                    \n                \n            \n        \n        \n            \n        \n    \n    \n    \n        \n        \n        \n            \n                \n                    \n                \n                \n                    \n                                \n                            \n                                \n                            \n                                \n                            \n                                \n                            \n                                \n                            \n                            \n                        \n                \n                \n                    \n                \n            \n        \n    \n    \n\n\n    P.when('A', 'Airy', 'ready').execute(function(A, Airy) {\n        window.$ = A.$;\n        window.elements = {};\n        window.global = {};\n\n        global.hasCalculatedThumbCarousel = false;\n        global.hasCalculatedScreenshotCarousel = false;\n\n        global.thumbnailScroll = 600;    // Max Scroll amount for thumbnails\n        global.thumbnailScrollLeftDistance = 0; // Distance left to scroll on the left\n        global.thumbnailScrollRightDistance = 0; // Distance left to scroll on the right\n        global.thumbnailScrollPoint = 0; // Point to Scroll to on thumbnails - Jquery uses scrollLeft\n\n        global.screenshotScroll = 500;  //Max scroll amount for screenshots\n        global.screenshotScrollLeftDistance = 0; // Distance left to scroll on the left\n        global.screenshotScrollRightDistance = 0; // Distance left to scroll on the right\n        global.screenshotScrollPoint = 0; // Point to Scroll to on screenshots - Jquery uses scrollLeft\n\n        elements.$mainProductImage = $('#js-masrw-main-image');\n        elements.$thumbsCarouselContainer = $(\".masrw-screenshot-thumbnail-container .a-carousel-viewport\"); //This is the one that scrolls\n        elements.$thumbsCarouselList = $(\".masrw-screenshot-thumbnail-container .a-carousel\");\n        elements.$screenshotsCarouselContainer = $(\".masrw-screenshot-container .a-carousel-viewport\"); //This is the one that scrolls\n        elements.$screenshotsCarouselList = $(\".masrw-screenshot-container .a-carousel\");\n\n        initButtons();\n        initVideo();\n    });\n\n    function initButtons() {\n\n        $(\".masrw-screenshot-thumbnail-container\").bind(\"mouseenter\", function() {\n\n            if (!global.hasCalculatedThumbCarousel) {\n                // Make sure the cards have the right width and margin\n                $(\".masrw-thumb-card\").width(\"auto\");\n                $(\".masrw-thumb-card:not(:first)\").css(\"margin-left\", \"10px\");\n\n                // Update the Carousel List to have the right width\n                calculateCarouselWidth(elements.$thumbsCarouselList, \".masrw-thumb-card\");\n\n                global.hasCalculatedThumbCarousel = true;\n\n                if (elements.$thumbsCarouselList.width() > elements.$thumbsCarouselContainer.width()) {\n                    $('.masrw-carousel-control.masrw-screenshot-thumbs').show(500);\n                }\n            }\n        });\n\n        $(\".masrw-screenshot-container\").bind(\"mouseenter\", function() {\n\n            if (!global.hasCalculatedScreenshotCarousel) {\n                // Make sure the cards have the right width and margin\n                $(\".masrw-screenshot-card\").width(\"auto\");\n                $(\".masrw-screenshot-card:not(:first)\").css(\"margin-left\", \"25px\");\n\n                // Update the Carousel List to have the right width\n                calculateCarouselWidth(elements.$screenshotsCarouselList, \".masrw-screenshot-card\");\n\n                global.hasCalculatedScreenshotCarousel = true;\n\n                if (elements.$screenshotsCarouselList.width() > elements.$screenshotsCarouselContainer.width()) {\n                    $('.masrw-carousel-control.masrw-screenshots').show(500);\n                    global.screenshotScroll = Math.floor(elements.$screenshotsCarouselContainer.width());\n                }\n            }\n        });\n\n        $(\".js-masrw-show-screenshots\").bind(\"click\", function(e) {\n            e.preventDefault();\n            openScreenshotLightbox($(e.target).parentsUntil(\"ol\").index() - $(\".js-masrw-play-video-button\").length);\n\n        });\n\n        $(\"#masrw-lightbox-screenshot-close\").bind(\"click\", function(e) {\n            e.preventDefault();\n            closeScreenshotLightbox();\n        });\n\n        $(\"#masrw-lightbox-screenshots-dark\").bind(\"click\", function() {\n            closeScreenshotLightbox();\n        });\n\n        $(\".js-masrw-play-video-button\").bind(\"click\", function(e) {\n            e.preventDefault();\n            openVideoLightbox();\n        });\n\n        $(\"#masrw-lightbox-video-close\").bind(\"click\", function(e) {\n            e.preventDefault();\n            closeVideoLightbox();\n        });\n\n        $(\"#masrw-lightbox-dark\").bind(\"click\", function() {\n            closeVideoLightbox();\n        });\n\n        // Code for Scrolling buttons\n        // Thumbnails Left Scroll\n        $(\"#masrw-screenshot-thumbs-left\").bind(\"click\", function() {\n            // Check to see if you can still scroll to the left\n            if (elements.$thumbsCarouselContainer.scrollLeft() != 0) {\n                // Calculate amount left to scroll to the left\n                global.thumbnailScrollLeftDistance = elements.$thumbsCarouselContainer.scrollLeft();\n\n                // If amount left to scroll is greater then predefined scroll, then scroll the predefined amount\n                // Otherwise scroll what is left to scroll\n                if (global.thumbnailScrollLeftDistance > global.thumbnailScroll) {\n                    global.thumbnailScrollPoint = elements.$thumbsCarouselContainer.scrollLeft() - global.thumbnailScroll;\n                }\n                else {\n                    global.thumbnailScrollPoint = 0;\n\n                    // No more to scroll left\n                    $(\"#masrw-screenshot-thumbs-left\").removeClass(\"enabled\");\n                }\n\n                // Animate the Scroll to prevent automatic jumping to location\n                elements.$thumbsCarouselContainer.animate({\n                    scrollLeft : global.thumbnailScrollPoint\n                });\n\n                // We can scroll to the right\n                $(\"#masrw-screenshot-thumbs-right\").addClass(\"enabled\");\n            }\n        });\n\n        // Thumbnails Right Scroll\n        $(\"#masrw-screenshot-thumbs-right\").bind(\"click\", function() {\n            // Check to see if you can still scroll to the right\n            if (elements.$thumbsCarouselContainer.scrollLeft() + elements.$thumbsCarouselContainer.width() < elements.$thumbsCarouselList.width()) {\n                // Calculate amount left to scroll to the right\n                global.thumbnailScrollRightDistance = elements.$thumbsCarouselList.width() - (elements.$thumbsCarouselContainer.scrollLeft() + elements.$thumbsCarouselContainer.width());\n\n                // If amount left to scroll to the right is greater then predefined scroll, then scroll the predefined amount\n                // Otherwise scroll what is left to scroll\n                if (global.thumbnailScrollRightDistance > global.thumbnailScroll) {\n                    global.thumbnailScrollPoint = elements.$thumbsCarouselContainer.scrollLeft() + global.thumbnailScroll;\n                }\n                else {\n                    global.thumbnailScrollPoint = elements.$thumbsCarouselContainer.scrollLeft() + global.thumbnailScrollRightDistance;\n\n                    // No more right scrolling\n                    $(\"#masrw-screenshot-thumbs-right\").removeClass(\"enabled\");\n                }\n\n                // Animate the Scroll to prevent automatic jumping to location\n                elements.$thumbsCarouselContainer.animate({\n                    scrollLeft : global.thumbnailScrollPoint\n                });\n\n                // We can scroll to the left\n                $(\"#masrw-screenshot-thumbs-left\").addClass(\"enabled\");\n            }\n        });\n\n        // Scroll bar enable/disable scroll buttons accordingly\n        elements.$thumbsCarouselContainer.scroll(function() {\n\n            if (elements.$thumbsCarouselContainer.scrollLeft() == 0) {\n                // Disable the left button\n                global.thumbnailScrollPoint = 0;\n                // No more to scroll left\n                $(\"#masrw-screenshot-thumbs-left\").removeClass(\"enabled\");\n\n            }\n            else if (elements.$thumbsCarouselContainer.scrollLeft() > 0) {\n                // Enable the left button - We can scroll left now\n                $(\"#masrw-screenshot-thumbs-left\").addClass(\"enabled\");\n\n                // Calculate amount left to scroll to the right\n                global.thumbnailScrollRightDistance = elements.$thumbsCarouselList.width() - (elements.$thumbsCarouselContainer.scrollLeft() + elements.$thumbsCarouselContainer.width());\n\n                if (global.thumbnailScrollRightDistance < 2) {\n                    // No more right scrolling\n                    $(\"#masrw-screenshot-thumbs-right\").removeClass(\"enabled\");\n                }\n                else {\n                    // Has right scrolling\n                    $(\"#masrw-screenshot-thumbs-right\").addClass(\"enabled\");\n                }\n            }\n        });\n\n        // Scroll bar enable/disable scroll buttons accordingly\n        elements.$screenshotsCarouselContainer.scroll(function() {\n\n            if (elements.$screenshotsCarouselContainer.scrollLeft() == 0) {\n                // Disable the left button\n                global.screenshotScrollPoint = 0;\n                // No more to scroll left\n                $(\"#masrw-screenshots-left\").removeClass(\"enabled\");\n\n            }\n            else if (elements.$screenshotsCarouselContainer.scrollLeft() > 0) {\n                // Enable the left button - We can scroll left now\n                $(\"#masrw-screenshots-left\").addClass(\"enabled\");\n\n                // Calculate amount left to scroll to the right\n                global.screenshotScrollRightDistance = elements.$screenshotsCarouselList.width() - (elements.$screenshotsCarouselContainer.scrollLeft() + elements.$screenshotsCarouselContainer.width());\n\n                if (global.screenshotScrollRightDistance < 2) {\n                    // No more right scrolling\n                    $(\"#masrw-screenshots-right\").removeClass(\"enabled\");\n                }\n                else {\n                    // Has right scrolling\n                    $(\"#masrw-screenshots-right\").addClass(\"enabled\");\n                }\n            }\n        })\n\n        // Screenshots Left Scroll\n        $(\"#masrw-screenshots-left\").bind(\"click\", function() {\n            // Check to see if you can still scroll to the left\n            if (elements.$screenshotsCarouselContainer.scrollLeft() != 0) {\n                // Calculate amount left to scroll to the left\n                global.screenshotScrollLeftDistance = elements.$screenshotsCarouselContainer.scrollLeft();\n\n                // If amount left to scroll is greater then predefined scroll, then scroll the predefined amount\n                // Otherwise scroll what is left to scroll\n                if (global.screenshotScrollLeftDistance > global.screenshotScroll) {\n                    global.screenshotScrollPoint = elements.$screenshotsCarouselContainer.scrollLeft() - global.screenshotScroll;\n                }\n                else {\n                    global.screenshotScrollPoint = 0;\n\n                    // No more to scroll left\n                    $(\"#masrw-screenshots-left\").removeClass(\"enabled\");\n                }\n\n                // Animate the Scroll to prevent automatic jumping to location\n                elements.$screenshotsCarouselContainer.animate({\n                    scrollLeft : global.screenshotScrollPoint\n                }, 800, \"swing\");\n\n                // We can scroll to the right\n                $(\"#masrw-screenshots-right\").addClass(\"enabled\");\n            }\n        });\n\n        // Screenshots Right Scroll\n        $(\"#masrw-screenshots-right\").bind(\"click\", function() {\n            // Check to see if you can still scroll to the right\n            if (elements.$screenshotsCarouselContainer.scrollLeft() + elements.$screenshotsCarouselContainer.width() < elements.$screenshotsCarouselList.width()) {\n                // Calculate amount left to scroll to the right\n                global.screenshotScrollRightDistance = elements.$screenshotsCarouselList.width() - (elements.$screenshotsCarouselContainer.scrollLeft() + elements.$screenshotsCarouselContainer.width());\n\n                // If amount left to scroll to the right is greater then predefined scroll, then scroll the predefined amount\n                // Otherwise scroll what is left to scroll\n                if (global.screenshotScrollRightDistance > global.screenshotScroll) {\n                    global.screenshotScrollPoint = elements.$screenshotsCarouselContainer.scrollLeft() + global.screenshotScroll;\n                }\n                else {\n                    global.screenshotScrollPoint = elements.$screenshotsCarouselContainer.scrollLeft() + global.screenshotScrollRightDistance;\n\n                    // No more right scrolling\n                    $(\"#masrw-screenshots-right\").removeClass(\"enabled\");\n                }\n\n                //Animate the Scroll to prevent automatic jumping to location\n                elements.$screenshotsCarouselContainer.animate({\n                    scrollLeft : global.screenshotScrollPoint\n                }, 800, \"swing\");\n\n                // We can scroll to the left\n                $(\"#masrw-screenshots-left\").addClass(\"enabled\");\n            }\n        });\n    }\n\n    function initVideo() {\n        var mediaCentralVideoURL = \"\";\n        var mediaCentralBaseURL = \"https://images-na.ssl-images-amazon.com/images/G/01/\";\n        var airyOnMediaCentralBaseURL = mediaCentralBaseURL + \"vap/video/airy2/prod/2.0.989.0/\";\n\n        global.airyPlayer = Airy.embed({\n            parentId : \"masrw-airy-player-container\",\n            // Desktop - Specify multiple urls to support more browsers & devices.\n            streamingUrls : [\n                mediaCentralVideoURL\n            ],\n            swfUrl : airyOnMediaCentralBaseURL + \"flash/AiryBasicRenderer._TTW_.swf\"\n        });\n    }\n\n    function resetVideo() {\n        try {\n            global.airyPlayer.detach();\n        }\n        catch(err) {\n            log(err.message);\n        }\n\n        global.airyPlayer = {};\n\n        setTimeout(function(){\n            initVideo();\n        }, 100);\n    }\n\n    function stopVideo() {\n        global.airyPlayer.pause();\n        global.airyPlayer.detach();\n        global.airyPlayer = {};\n\n        initVideo();\n    }\n\n    function openVideoLightbox() {\n        $(\"#masrw-video-overlay\").removeClass(\"masrw-overlay-hidden\");\n\n        global.airyPlayer.play();\n\n        setTimeout(function(){\n            $(\"#masrw-video-overlay\").addClass(\"masrw-overlay-on\");\n            $(\"#masrw-lightbox\").addClass(\"masrw-overlay-on\");\n            $(\"#masrw-lightbox-video-close\").addClass(\"masrw-overlay-on\");\n        }, 10);\n    }\n\n    function closeVideoLightbox() {\n        try {\n            global.airyPlayer.pause();\n        }\n        catch(err) {\n            log(err.message);\n        }\n\n        $(\"#masrw-video-overlay\").removeClass(\"masrw-overlay-on\");\n        $(\"#masrw-lightbox\").removeClass(\"masrw-overlay-on\");\n        $(\"#masrw-lightbox-video-close\").removeClass(\"masrw-overlay-on\");\n\n        setTimeout(function(){\n            $(\"#masrw-video-overlay\").addClass(\"masrw-overlay-hidden\");\n            resetVideo();\n        }, 150);\n    }\n\n    function openScreenshotLightbox(defaultIndex) {\n        $(\"#masrw-screenshots-overlay\").removeClass(\"masrw-overlay-hidden\");\n\n        setTimeout(function(){\n            $(\"#masrw-screenshots-overlay\").addClass(\"masrw-overlay-on\");\n            $(\"#masrw-screenshots-lightbox\").addClass(\"masrw-overlay-on\");\n            $(\"#masrw-screenshots-lightbox\").find(\"li\")[defaultIndex].scrollIntoView({\n                behavior: 'auto',\n                block: 'center',\n                inline: 'center'\n            });\n            $(\"#masrw-lightbox-screenshot-close\").addClass(\"masrw-overlay-on\");\n        }, 150);\n    }\n\n    function closeScreenshotLightbox() {\n        $(\"#masrw-screenshots-overlay\").removeClass(\"masrw-overlay-on\");\n        $(\"#masrw-screenshots-lightbox\").removeClass(\"masrw-overlay-on\");\n        $(\"#masrw-lightbox-screenshot-close\").removeClass(\"masrw-overlay-on\");\n\n        setTimeout(function(){\n            $(\"#masrw-screenshots-overlay\").addClass(\"masrw-overlay-hidden\");\n            resetVideo();\n        }, 150);\n    }\n\n    function calculateCarouselWidth(element, elements) {\n        var width = 0;\n        $(elements).each(function(){\n            width += $(this).outerWidth(true);\n        });\n\n        element.width(width);\n    }\n\nLatest updatesWhat's new in version 1.1\n            - minor fix- improve performaceProduct DetailsRelease Date: 2013Date first listed on Amazon: October 30, 2013Developed By: SmallthingASIN: B00G36GT5ACustomer reviews: 4.1 out of 5 stars17  customer ratings\n\n\n\n\n\n\n    Developer info\n    \n        \n            \n                \n                    \n                    \n                    \n                        \n                            \n                                More apps by this developer\n                            \n                        \n                    \n                \n            \n            \n        \n    \n\n\n\n\n\n\n\n    \n        Product features\n    \n    \n        \n            \n                \n                    \n                        PLAY AT ANY TIME!\n                    \n                \n            \n                \n                    \n                        PLAY EASY\n                    \n                \n            \n                \n                    \n                        A SOLITARIE COMPLETE\n                    \n                \n            \n                \n                    \n                        CUSTOMIZATION\n                    \n                \n            \n                \n                    \n                        RELAX SOUND\n                    \n                \n            \n        \n    \n\n                \n\n                \n\n\n\n\n\n    \n    \n        Product description\n        \n            EASY SPIDER Solitaire is the famous solitaire card SPIDER, designed to be simple to use, relaxing, attention to detail... and it is FREE and COMPLETE 100% It's like the famous WINDOWS game that everyone plays! PLAY AT ANY TIME! You're play and must stop? You can close it in anyway : Spider Easy restart exactly where it left! PLAY EASY Drag the cards with your finger on the deck you want, Or tap a card and this will be in the best position ! A SOLITARIE COMPLETE We have not forgotten anything to make the full game and fun for everyone : > You can play with many suit. > You have the chance to come back as you like if you do not like the moves made . > You can ask for help if you do not know how to proceed, you will be given some tips how to proceed. > You have the essential statistics of the whole game if you want to improve yourself : number of moves, time and score . > You can see how you are ranking among players from around the world. > You do not know all the rules to play? You will find them described in detail. IS SUITABLE FOR ANY SCREEN Do you have a phone or a tablet? You can choose the design of the cards, big or small. So that they are clearly visible on your screen. Just a touch while you play. It takes advantage of the graphics of the iPhone 5 and iPad Retina of recent generations. CUSTOMIZATION You can change the design of the game board , the drawings on the cards, remove the writing on the screen ... You can do anything with just one touch. Edit it as you like ! RELAX SOUND We have created a series of relaxing music that will accompany you during the game. If you do not want, then you can also turn off . Search our official page Smallthing and follow us on Facebook and Twitter . You will discover exclusive news, trucch and curiosity about our games ! Visit our website : www.smallthinggame.com Follow also our blog: smallthinggame.blogspot.com If you want to propose improvements , advice write to: <EMAIL> ___________________________________________ Search our other products on store GP RetroBubble Box Breaker Blitz Wall Breaker\n        \n    \nTechnical detailsSize: 15.3MBVersion: 1.1Developed By: SmallthingApplication Permissions:\n                (\n            Help me understand what permissions mean\n                )\n            Access coarse (e.g., Cell-ID, Wi-Fi) locationAccess fine (e.g., GPS) locationAccess information about networksAccess information about Wi-Fi networksOpen network socketsWrite to external storageMinimum Operating System:  Android 2.2Approximate Download Time:  Less than 2 minutes\n  \n    \n\n  \n\n  (window.AmazonUIPageJS ? AmazonUIPageJS : P).load.js('https://images-na.ssl-images-amazon.com/images/I/41+hU3deU6L._RC|01JMhqKAiVL.js,11KGVmb0nxL.js,41dL09N-ijL.js,31OqjYy-bxL.js,01VSu9SK-XL.js_.js?AUIClients/DesktopMedleyFilteringMetaAsset&AXTJ5Fo9#386124-T1.666973-T1');\n\n\n        \n      Customer reviews4.1 out of 5 stars4.1 out of 5\n            17 global ratings\n\n\n\n\n\n\n\n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n  \n  \n\n\n\n\n\n\n\n\n\n\n\n  \n  \n\n    \n    \n\n      \n        \n          \n            5 star\n          \n        \n\n        \n      \n\n      \n        \n          \n        \n      \n\n      \n        \n        \n          \n            \n              68%\n            \n          \n        \n      \n    \n\n  \n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n  \n  \n\n\n\n\n\n\n\n\n\n\n\n  \n  \n\n    \n    \n\n      \n        \n          \n            4 star\n          \n        \n\n        \n      \n\n      \n        \n          \n        \n      \n\n      \n        \n        \n          \n            \n              7%\n            \n          \n        \n      \n    \n\n  \n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n  \n  \n\n\n\n\n\n\n\n\n\n\n\n  \n\n    \n    \n\n      \n        \n          3 star\n        \n\n        \n        \n          0% (0%)\n        \n\n        \n      \n\n      \n        \n      \n\n      \n        \n\n        \n          \n            0%\n          \n        \n      \n    \n\n  \n  \n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n  \n  \n\n\n\n\n\n\n\n\n\n\n\n  \n  \n\n    \n    \n\n      \n        \n          \n            2 star\n          \n        \n\n        \n      \n\n      \n        \n          \n        \n      \n\n      \n        \n        \n          \n            \n              17%\n            \n          \n        \n      \n    \n\n  \n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n  \n  \n\n\n\n\n\n\n\n\n\n\n\n  \n  \n\n    \n    \n\n      \n        \n          \n            1 star\n          \n        \n\n        \n      \n\n      \n        \n          \n        \n      \n\n      \n        \n        \n          \n            \n              8%\n            \n          \n        \n      \n    \n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    \n        \n            \n                How customer reviews and ratings work\n            \n            \n                \n                    Customer Reviews, including Product Star Ratings help customers to learn more about the product and decide whether it is the right product for them.\n                \n                \n                    To calculate the overall star rating and percentage breakdown by star, we don’t use a simple average. Instead, our system considers things like how recent a review is and if the reviewer bought the item on Amazon. It also analyzed reviews to verify trustworthiness.\n                \n                \n                    Learn more how customers reviews work on Amazon\n                \n            \n        \n        \n    \n\n  \n      \n\n\n\nvar instrumentation;!function(){\"use strict\";var e={568:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!(\"get\"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,\"default\",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)\"default\"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,\"__esModule\",{value:!0}),t.getSlotWithBrowseNode=t.getPlacementWithBrowseNode=t.getBrowseNode=t.csa=t.csm=t.AD_LOAD_COUNTERS=void 0;var a=r(922);Object.defineProperty(t,\"AD_LOAD_COUNTERS\",{enumerable:!0,get:function(){return a.AD_LOAD_COUNTERS}}),t.csm=i(r(472)),t.csa=i(r(495));var c=r(322);Object.defineProperty(t,\"getBrowseNode\",{enumerable:!0,get:function(){return c.getBrowseNode}}),Object.defineProperty(t,\"getPlacementWithBrowseNode\",{enumerable:!0,get:function(){return c.getPlacementWithBrowseNode}}),Object.defineProperty(t,\"getSlotWithBrowseNode\",{enumerable:!0,get:function(){return c.getSlotWithBrowseNode}})},322:function(e,t){Object.defineProperty(t,\"__esModule\",{value:!0}),t.getPlacementWithBrowseNode=t.getSlotWithBrowseNode=t.getBrowseNode=void 0;var r=/.*(\\/b|\\/s|\\/l).*(node=)(\\d{1,12}).*/;t.getBrowseNode=function(){var e=r.exec(window.location.href);return e&&e[3]?e[3]:null},t.getSlotWithBrowseNode=function(e){var r=(0,t.getBrowseNode)();if(!e||!r)return null;var n=e.split(\":\");return n.splice(n.length-1,0,r),n.join(\":\")},t.getPlacementWithBrowseNode=function(e){var r=(0,t.getBrowseNode)();return r?\"\".concat(e,\":\").concat(r):null}},922:function(e,t){Object.defineProperty(t,\"__esModule\",{value:!0}),t.AD_LOAD_COUNTERS=void 0,t.AD_LOAD_COUNTERS={HTML_REACHED:\"adload:htmlreached\"}},958:function(e,t,r){Object.defineProperty(t,\"__esModule\",{value:!0}),t.CsaEvents=void 0;var n=r(876);t.CsaEvents=function(){var e=this;if(this.log=function(t,r,o){if(e.events)try{e.events(\"log\",{schemaId:\"ApeSafeframe.csaEvent.1\",metricName:t+\":\"+r+\":\"+o,metricValue:1},{ent:\"all\"})}catch(e){(0,n.logError)(\"Error with 'logCsaEvent' CSA\",e)}},this.setEntity=function(t){if(e.events)try{e.events(\"setEntity\",{adCreativeMetaData:t})}catch(e){(0,n.logError)(\"Error with 'addCsaEntity' CSA\",e)}},window.csa)try{this.events=window.csa(\"Events\",{producerId:\"adplacements\"})}catch(e){(0,n.logError)(\"Error with initiating CSA\",e)}}},710:function(e,t,r){Object.defineProperty(t,\"__esModule\",{value:!0}),t.CsaLatency=void 0;var n=r(876);t.CsaLatency=function(e){var t=this;if(this.mark=function(e,r){if(t.latencyPlugin)try{t.latencyPlugin(\"mark\",e,r)}catch(e){(0,n.logError)(\"Error with 'markCsaLatencyMetric' CSA\",e)}},window.csa)try{this.latencyPlugin=window.csa(\"Content\",{element:e})}catch(e){(0,n.logError)(\"Error with initiating CSA\",e)}}},495:function(e,t,r){Object.defineProperty(t,\"__esModule\",{value:!0}),t.events=t.latency=void 0;var n=r(710),o=r(958);t.latency=function(e){return new n.CsaLatency(e)},t.events=function(){return new o.CsaEvents}},472:function(e,t,r){Object.defineProperty(t,\"__esModule\",{value:!0}),t.addCsmTag=t.sendCsmCounter=t.sendCsmLatencyMetric=void 0;var n,o=r(322);!function(e){e.bb=\"uet\",e.af=\"uet\",e.cf=\"uet\",e.be=\"uet\",e.ld=\"uex\"}(n||(n={})),t.sendCsmLatencyMetric=function(e,t,r,i,a){var c=n[e];if(\"function\"==typeof window[c]){var u=i?i+\":\":\"\",s=function(t,r){r&&window[t](e,\"adplacements:\"+u+r,{wb:1},a)},d=t.replace(/_/g,\":\");s(c,d),s(c,(0,o.getSlotWithBrowseNode)(d)),r&&(s(c,r),s(c,(0,o.getPlacementWithBrowseNode)(r)))}},t.sendCsmCounter=function(e,t,r,n){var i=function(e,t){var n;if(\"function\"==typeof(null===(n=null===window||void 0===window?void 0:window.ue)||void 0===n?void 0:n.count)&&e){var o=\"adplacements:\"+r;window.ue.count(\"\".concat(o,\":\").concat(e),t)}};if(e){var a=e.replace(/_/g,\":\");i(a,n),i((0,o.getSlotWithBrowseNode)(a),n)}t&&(i(t,n),i((0,o.getPlacementWithBrowseNode)(t),n))},t.addCsmTag=function(e,t,r,n){var o;if(null===(o=null===window||void 0===window?void 0:window.ue)||void 0===o?void 0:o.tag){if(t){var i=e+\":\"+t.replace(/_/g,\":\")+(n?\":\"+n:\"\");window.ue.tag(i)}if(r){var a=e+\":\"+r+(n?\":\"+n:\"\");window.ue.tag(a)}t||r||window.ue.tag(e+(n?\":\"+n:\"\"))}}},876:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!(\"get\"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,\"default\",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)\"default\"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,\"__esModule\",{value:!0}),t.logError=void 0;var a=i(r(472));t.logError=function(e,t){var r=t||new Error(e);console.error(e,t),a.sendCsmCounter(\"\",null,\"safeFrameError\",1),window.sfHostLogErrors&&(window.ueHostLogError?window.ueHostLogError(r,{logLevel:\"ERROR\",attribution:\"APE-safeframe\",message:e+\" \"}):\"undefined\"!=typeof console&&console.error&&console.error(e,r))}}},t={},r=function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}(568);instrumentation=r}();\nif (instrumentation) {instrumentation.csm.sendCsmCounter(\"Detail_customer-reviews-top_Glance\", \"a5a58631-9157-450d-8b12-21b15ea5926d\", instrumentation.AD_LOAD_COUNTERS.HTML_REACHED, 1);instrumentation.csa.events().log(instrumentation.AD_LOAD_COUNTERS.HTML_REACHED, \"Detail_customer-reviews-top_Glance\", \"a5a58631-9157-450d-8b12-21b15ea5926d\");if (typeof uet === 'function' && typeof ues === 'function') {var scope = 'Detail_customer-reviews-top_Glance';var placementId = 'a5a58631-9157-450d-8b12-21b15ea5926d';ues('wb', 'adplacements:' + scope.replace(/\\_/g, ':'), {wb:1}); uet('bb', 'adplacements:' + scope.replace(/\\_/g, ':'), {wb:1}); if (placementId) {ues('wb', 'adplacements:' + placementId, {wb:1});uet('bb', 'adplacements:' + placementId, {wb:1});}}}window.renderingWeblabs = window.renderingWeblabs ? window.renderingWeblabs : {};window.renderingWeblabs = Object.assign(window.renderingWeblabs, JSON.parse('{\"ADPT_SF_EXPANDABLE_ADS_716745\":\"C\",\"ADPT_SF_ARIA_HIDDEN_644521\":\"T1\",\"ADPT_SF_CLIENT_LATENCY_2023Q1_632539\":\"C\",\"ADPT_SF_MRC_RETEST_643771\":\"T1\",\"ADPT_SF_DOCWRITE_689243\":\"T1\",\"ADPT_SF_TRANSPARENCY_INFO_MANDATORY_FOR_EU_712921\":\"C\"}'));  (function(){function a(d,e){if(window.addEventListener){window.addEventListener(d,e,false);}else{if(window.attachEvent){window.attachEvent(\"on\"+d,e);}}}function c(d,e){if(window.removeEventListener){window.removeEventListener(d,e,false);}else{if(window.detachEvent){window.detachEvent(\"on\"+d,e);}}}var b=function(){(function(){(function(j,o){j.sfLogErrors=j.sfLogErrors||true;var r=r||function(w,v){v=v||new Error(w);if(j.ue&&typeof ue.count==\"function\"){ue.count(\"adplacements:safeFrameError\",1);}if(!j.sfLogErrors){return;}if(j.ueLogError){j.ueLogError(v,{logLevel:\"ERROR\",attribution:\"APE-safeframe\",message:w+\" \"});}else{if(typeof console!==\"undefined\"&&console.error){console.error(w,v);}}};j[\"customer-reviews-top\"]={};j[\"customer-reviews-top\"].adStartTime=(new Date()).getTime();function k(){return j.innerHeight||o.documentElement.clientHeight;}function t(){return j.innerWidth||o.documentElement.clientWidth;}function m(x,v,w){if(x>0){return(w>x);}else{return(v>0);}}var d=function(){return(Date.now?Date.now():new Date().getTime());};var h=function(w,y,C){var v,A,D;var B=null;var z=0;if(!C){C={};}var x=function(){z=C.leading===false?0:d();B=null;D=w.apply(v,A);if(!B){v=A=null;}};return function(){var F=d();if(!z&&C.leading===false){z=F;}var E=y-(F-z);v=this;A=arguments;if(E<=0||E>y){if(B){clearTimeout(B);B=null;}z=F;D=w.apply(v,A);if(!B){v=A=null;}}else{if(!B&&C.trailing!==false){B=setTimeout(x,E);}}return D;};};function i(D,B,w,y,v){try{var A=o.getElementById(D).getBoundingClientRect();if(m(A.top,A.bottom,k())&&m(A.left,A.right,t())){if(typeof uet==\"function\"){uet(\"bb\",\"adplacements:viewablelatency:\"+B,{wb:1});if(w){uet(\"bb\",\"adplacements:viewablelatency:\"+w,{wb:1});}}var x;if(j.csa){var C=o.getElementById(D);if(v==\"mobileads\"){x=j.csa(\"Content\",{element:C.parentNode});}else{x=j.csa(\"Content\",{element:C});}x(\"mark\",\"viewablelatency:bodyBegin\");if(j.apeViewableLatencyTrackers[y].loaded){x(\"mark\",\"viewablelatency:loaded\");}}if(typeof uex==\"function\"&&j.ue&&typeof ue.count==\"function\"){if(j.apeViewableLatencyTrackers[y].loaded){uex(\"ld\",\"adplacements:viewablelatency:\"+B,{wb:1});if(w){uex(\"ld\",\"adplacements:viewablelatency:\"+w,{wb:1});}ue.count(\"adplacements:htmlviewed:loaded:\"+B,1);if(w){ue.count(\"adplacements:htmlviewed:loaded:\"+w,1);}}ue.count(\"adplacements:htmlviewed:\"+B,1);if(w){ue.count(\"adplacements:htmlviewed:\"+w,1);}}j.apeViewableLatencyTrackers[y].viewed=true;if(j.apeViewableLatencyTrackers[y].tracker){c(\"scroll\",j.apeViewableLatencyTrackers[y].tracker);c(\"resize\",j.apeViewableLatencyTrackers[y].tracker);}}}catch(z){j.apeViewableLatencyTrackers[y].valid=false;}}try{j.apeViewableLatencyTrackers=j.apeViewableLatencyTrackers||{};var n=\"ape_Detail_customer-reviews-top_Glance_placement\";var f=\"Detail_customer-reviews-top_Glance\".replace(/\\_/g,\":\");var p=\"a5a58631-9157-450d-8b12-21b15ea5926d\";var e=\"d0db2c3f80184e59884aed966d96682f\";var g=\"amazon\";j.apeViewableLatencyTrackers[e]=j.apeViewableLatencyTrackers[e]||{};j.apeViewableLatencyTrackers[e].valid=true;i(n,f,p,e,g);if(j.apeViewableLatencyTrackers[e].valid&&!j.apeViewableLatencyTrackers[e].viewed){j.apeViewableLatencyTrackers[e].tracker=h(function(){i(n,f,p,e,g);},20);a(\"scroll\",j.apeViewableLatencyTrackers[e].tracker);a(\"resize\",j.apeViewableLatencyTrackers[e].tracker);}}catch(q){if(j.apeViewableLatencyTrackers&&j.apeViewableLatencyTrackers.d0db2c3f80184e59884aed966d96682f){j.apeViewableLatencyTrackers.d0db2c3f80184e59884aed966d96682f.valid=false;}r(\"Error initializing viewable latency instrumentation\",q);}if(j.csa){var l;var u=o.getElementById(n);if(g==\"mobileads\"){l=j.csa(\"Content\",{element:u.parentNode});}else{l=j.csa(\"Content\",{element:u});}l(\"mark\",\"bodyBegin\");}try{if(j.DAsf){j.DAsf.loadAds();}else{var s=o.createElement(\"script\");s.type=\"text/javascript\";s.async=true;s.charset=\"utf-8\";s.src=\"https://images-na.ssl-images-amazon.com/images/S/apesafeframe/ape/sf/desktop/DAsf-1.50.d76dcf8e.js?csm_attribution=APE-SafeFrame\";s.onerror=function(v){r(\"Error loading SafeFrame library: https://images-na.ssl-images-amazon.com/images/S/apesafeframe/ape/sf/desktop/DAsf-1.50.d76dcf8e.js?csm_attribution=APE-SafeFrame\");};s.setAttribute(\"crossorigin\",\"anonymous\");(o.getElementsByTagName(\"head\")[0]||o.getElementsByTagName(\"body\")[0]).appendChild(s);}}catch(q){r(\"Error appending DAsf library\",q);}}(window,document));})();};b();})();\n\n      \n    \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n\n    \n      Sort reviews by\n    \n    \n\n      \n        Top reviews\n      \n\n      \n        Most recent\n      \n\n    \n        Top reviews\n      \n  \n\n\n\n\n\n\n\n\n\n\n\n  Top reviews from the United States\n\n        \n\n      There was a problem filtering reviews right now. Please try again later.<img src=\"https://images-na.ssl-images-amazon.com/images/S/amazon-avatars-global/default._CR0,0,1024,1024_SX48_.png\"/>Diane Felice5.0 out of 5 stars\n\n\n\n\n\n\n\n  \n  \n    Fun game!!\n  \nReviewed in the United States 🇺🇸 on March 2, 2023Verified Purchase\n\n\n\n\n\n\n\n  \n  \n    This game is really fun, easy enough to learn but always challenging at the same time.  The graphics are nice and the gameplay is intuitive.\n  \nRead more\n  \n    \n          \n              \n                  Helpful\n              \n          \n      Sending feedback...Thank you for your feedback.Sorry, we failed to record your vote. Please try again\n        Report\n  <img src=\"https://images-na.ssl-images-amazon.com/images/S/amazon-avatars-global/default._CR0,0,1024,1024_SX48_.png\"/>Kindle Customer4.0 out of 5 stars\n\n\n\n\n\n\n\n  \n  \n    Four Stars\n  \nReviewed in the United States 🇺🇸 on February 9, 2017Verified Purchase\n\n\n\n\n\n\n\n  \n  \n    Enjoy playing\n  \nRead more\n  \n    \n          \n              \n                  Helpful\n              \n          \n      Sending feedback...Thank you for your feedback.Sorry, we failed to record your vote. Please try again\n        Report\n  <img src=\"https://images-na.ssl-images-amazon.com/images/S/amazon-avatars-global/default._CR0,0,1024,1024_SX48_.png\"/>David Brown5.0 out of 5 stars\n\n\n\n\n\n\n\n  \n  \n    Five Stars\n  \nReviewed in the United States 🇺🇸 on November 17, 2014Verified Purchase\n\n\n\n\n\n\n\n  \n  \n    GREAT\n  \nRead more\n  \n    \n          \n              \n                  Helpful\n              \n          \n      Sending feedback...Thank you for your feedback.Sorry, we failed to record your vote. Please try again\n        Report\n  See all reviews\n\n  \n      \n\n\n\n\n\n  \n\n\n\n\n  \n    \n      \n        \n          Top reviews from other countries\n        \n        \n      \n    \n  \n    \n      \n      \n        Translate all reviews to English\n      \n    \n  \n  \n  \n    \n      \n      \n        \n    \n      \n    \n  \n<img src=\"https://images-eu.ssl-images-amazon.com/images/S/amazon-avatars-global/default._CR0,0,1024,1024_SX48_.png\"/>Juan Rivera4.0 out of 5 stars\n\n\n\n\n\n\n\n  \n    Me gusta\n  \n  \nReviewed in Spain 🇪🇸 on May 31, 2014Verified Purchase\n\n\n\n\n\n\n\n  \n    Está bien, me gusta, pero algo lento en la ejecución. Además tienes que cogerle \"el tranquillo\" al unir las cartas, tienes que darle un tic.\n  \n  \nRead more\n  \n        Report\n  Translate review to EnglishSee all reviews\n      \n    \n  \n\n\n  Amazon Appstore Return Policy\n        \n    \n    //Code to replace low resolution image with high resolution image once it is loaded.\n    var currentBgImage = document.getElementById(\"atf-dp-bg-img\");\n    var highResBgImage = new Image();\n    highResBgImage.src = \"https://m.media-amazon.com/images/I/91bVia+AraL._SL500_UX1920_CR0%2C0%2C1920%2C1080_.jpg\";\n    highResBgImage.onload = function () {\n        currentBgImage.src = highResBgImage.src;\n    };\n\n    var selectedTwisterElement = document.querySelector('.qsTwister .a-color-price');\n    var selectedTwister = selectedTwisterElement ? selectedTwisterElement.id : \"apponly\";\n    P.when('A', 'ready').execute(function (A) {\n        A.on('a:button-group:quickSubsToggle:toggle', function (data) {\n            selectedTwister = data.selectedButton.buttonName;\n            $('.twister-price').removeClass(\"a-color-price\");\n            $('.qsTwister').find('#' + selectedTwister + '.twister-price').addClass(\"a-color-price\");\n        });\n\n        // Emit clickstream metrics on page load succeed\n        emitMetrics(A, '/hz/mas/detail/pageloaded');\n    });\n\n    function emitMetrics(A, endpoint) {\n        var $ = A.$;\n        let dataParams = { 'asin': \"B00G36GT5A\" };\n        // Emit clickstream metrics on page load succeed\n        let anti_csrf = document.querySelector('meta[name=\"csrf-token\"]') === null ?\n                                                  'faketoken': document.querySelector('meta[name=\"csrf-token\"]')\n                                                  .getAttribute('content');\n        A.post(endpoint, {\n            headers: {\"anti-csrftoken-a2z\": anti_csrf},\n            params: dataParams,\n            success: function (response) {\n                if (response === true) {\n                    console.log('Client page load success');\n                } else {\n                    console.log('Client page load failed');\n                }\n            },\n            error: function () {\n                console.log('Client page load unknown error');\n            }\n        });\n    }\n\n\n\n\n\n\n\n\n\n  \n\n\n\n  (function ($Nav) {\n\"use strict\";\n\nif (typeof $Nav === 'undefined' || $Nav === null || typeof $Nav.when !== 'function') {\n    return;\n}\n\n$Nav.when('$', 'data', 'flyout.yourAccount', 'sidepanel.csYourAccount',\n          'config')\n    .run(\"BuyitAgain-YourAccount-SidePanel\",\n    function ($, data, yaFlyout, csYourAccount, config) {\n        if (config.disableBuyItAgain) {\n            return;\n        }\n        var render = function (data) {\n            if (data.dramResult) {\n                var widgetHtml = data.dramResult;\n                navbar.sidePanel({\n                    flyoutName: 'yourAccount',\n                    data: {html: widgetHtml}\n                });\n            }\n        };\n\n        var renderBuyItAgain = function (biaData) {\n            if (csYourAccount) {\n                csYourAccount.register(render, biaData);\n            } else {\n                render(biaData);\n            }\n        };\n\n        var truncateAndRestructureYaFlyout = function() {\n            if (window.P) {\n                P.when('A', 'a-truncate').execute(function(A, truncate) {\n                    var truncateElements = A.$('.a-truncate');\n                    A.each(truncateElements, function(element) {\n                        truncate.get(element).update();\n                    });\n                    var recommendationsWidget = document.getElementById('bia-hcb-widget');\n                    if (recommendationsWidget) { \n                        var navFlyout = recommendationsWidget.parentElement;\n                        var navFlyoutPaddingBottom = parseInt(window.getComputedStyle(navFlyout).getPropertyValue('padding-bottom'));\n                        var navFlyoutContentHeight = navFlyout.clientHeight - navFlyoutPaddingBottom;\n                        while (recommendationsWidget.offsetHeight > navFlyoutContentHeight && recommendationsWidget.offsetHeight > 0){\n                            var recommendations = recommendationsWidget.querySelectorAll('.biaNavFlyoutFaceout');\n                            if (recommendations.length <= 1) {\n                                break;\n                            }\n                            var lastRecommendation = recommendations[recommendations.length - 1];\n                            lastRecommendation.parentElement.removeChild(lastRecommendation);\n                        }\n                    }\n               });\n            }\n        };\n\n        yaFlyout.sidePanel.onData(truncateAndRestructureYaFlyout);\n        yaFlyout.onShow(truncateAndRestructureYaFlyout);\n\n    yaFlyout.onRender(function() {\n            $.ajax({\n                url: '/gp/bia/external/bia-hcb-ajax-handler.html',\n                data: {\"biaHcbRid\":\"K1JQ9VEP43RRWKW775V5\"},\n                dataType: 'json',\n                timeout: 4*1000,\n                success: renderBuyItAgain,\n                error: function (jqXHR, textStatus, errorThrown) {\n                }\n            });\n        });\n    });\n})(window.$Nav);\n\n\n\n  window.$Nav && $Nav.when(\"data\").run(function (data) {\n    data({\n      \"accountListContent\": { \"html\": \"<div id='nav-al-container'><div id='nav-al-signin'><div id='nav-flyout-ya-signin' class='nav-flyout-content nav-flyout-accessibility'><a href='https://www.amazon.com/ap/signin?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.com%2FSmallthing-Easy-Spider-Solitaire%2Fdp%2FB00G36GT5A%2F%3F_encoding%3DUTF8%26*Version*%3D1%26*entries*%3D0%26asin%3DB00G36GT5A%26ie%3DUTF8%26ref_%3Dnav_signin&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0' rel='nofollow' class='nav-action-signin-button' data-nav-role='signin' data-nav-ref='nav_signin'><span class='nav-action-inner'>Sign in</span></a><div id='nav-flyout-ya-newCust' class='nav_pop_new_cust nav-flyout-content nav-flyout-accessibility'>New customer? <a href='https://www.amazon.com/ap/register?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.com%2FSmallthing-Easy-Spider-Solitaire%2Fdp%2FB00G36GT5A%2F%3F_encoding%3DUTF8%26*Version*%3D1%26*entries*%3D0%26asin%3DB00G36GT5A%26ie%3DUTF8%26ref_%3Dnav_newcust&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0' rel='nofollow' class='nav-a'>Start here.</a></div></div></div><div id='nav-al-wishlist' class='nav-al-column nav-tpl-itemList nav-flyout-content nav-flyout-accessibility'><div class='nav-title' id='nav-al-title'>Your Lists</div><a href='/hz/wishlist/ls?triggerElementID=createList&ref_=nav_ListFlyout_navFlyout_createList_lv_redirect' class='nav-link nav-item'><span class='nav-text'>Create a List</span></a> <a href='/registries?ref_=nav_ListFlyout_find' class='nav-link nav-item'><span class='nav-text'>Find a List or Registry</span></a></div><div id='nav-al-your-account' class='nav-al-column nav-template nav-flyout-content nav-tpl-itemList nav-flyout-accessibility'><div class='nav-title'>Your Account</div><a href='/gp/css/homepage.html?ref_=nav_AccountFlyout_ya' class='nav-link nav-item'><span class='nav-text'>Account</span></a> <a id='nav_prefetch_yourorders' href='/gp/css/order-history?ref_=nav_AccountFlyout_orders' class='nav-link nav-item'><span class='nav-text'>Orders</span></a> <a href='/gp/yourstore?ref_=nav_AccountFlyout_recs' class='nav-link nav-item'><span class='nav-text'>Recommendations</span></a> <a href='/gp/history?ref_=nav_AccountFlyout_browsinghistory' class='nav-link nav-item'><span class='nav-text'>Browsing History</span></a> <a href='/gp/video/watchlist?ref_=nav_AccountFlyout_ywl' class='nav-link nav-item'><span class='nav-text'>Watchlist</span></a> <a href='/gp/video/library?ref_=nav_AccountFlyout_yvl' class='nav-link nav-item'><span class='nav-text'>Video Purchases & Rentals</span></a> <a href='/gp/kindle/ku/ku_central?ref_=nav_AccountFlyout_ku' class='nav-link nav-item'><span class='nav-text'>Kindle Unlimited</span></a> <a href='/hz/mycd/myx?pageType=content&ref_=nav_AccountFlyout_myk' class='nav-link nav-item'><span class='nav-text'>Content & Devices</span></a> <a href='/gp/subscribe-and-save/manager/viewsubscriptions?ref_=nav_AccountFlyout_sns' class='nav-link nav-item'><span class='nav-text'>Subscribe & Save Items</span></a> <a href='/hz5/yourmembershipsandsubscriptions?ref_=nav_AccountFlyout_digital_subscriptions' class='nav-link nav-item'><span class='nav-text'>Memberships & Subscriptions</span></a> <a href='https://music.amazon.com?ref=nav_youraccount_cldplyr' class='nav-link nav-item'><span class='nav-text'>Music Library</span></a></div></div>\" },\n      \"tooltipContent\": { \"html\": \"\" },\n      \"signinContent\": { \"html\": \"<div id='nav-signin-tooltip'><a href='https://www.amazon.com/ap/signin?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.com%2FSmallthing-Easy-Spider-Solitaire%2Fdp%2FB00G36GT5A%2F%3F_encoding%3DUTF8%26*Version*%3D1%26*entries*%3D0%26asin%3DB00G36GT5A%26ie%3DUTF8%26ref_%3Dnav_custrec_signin&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0' class='nav-action-signin-button' data-nav-role='signin' data-nav-ref='nav_custrec_signin'><span class='nav-action-inner'>Sign in</span></a><div class='nav-signin-tooltip-footer'>New customer? <a href='https://www.amazon.com/ap/register?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.com%2FSmallthing-Easy-Spider-Solitaire%2Fdp%2FB00G36GT5A%2F%3F_encoding%3DUTF8%26*Version*%3D1%26*entries*%3D0%26asin%3DB00G36GT5A%26ie%3DUTF8%26ref_%3Dnav_custrec_newcust&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0' class='nav-a'>Start here.</a></div></div>\" },\n      \"templates\": {\"itemList\":\"<# var hasColumns = (function () {  var checkColumns = function (_items) {    if (!_items) {      return false;    }    for (var i=0; i<_items.length; i++) {      if (_items[i].columnBreak || (_items[i].items && checkColumns(_items[i].items))) {        return true;      }    }    return false;  };  return checkColumns(items);}()); #><# if(hasColumns) { #>  <# if(items[0].image && items[0].image.src) { #>    <div class='nav-column nav-column-first nav-column-image'>  <# } else if (items[0].greeting) { #>    <div class='nav-column nav-column-first nav-column-greeting'>  <# } else { #>    <div class='nav-column nav-column-first'>  <# } #><# } #><# var renderItems = function(items) { #>  <# jQuery.each(items, function (i, item) { #>    <# if(hasColumns && item.columnBreak) { #>      <# if(item.image && item.image.src) { #>        </div><div class='nav-column nav-column-notfirst nav-column-break nav-column-image'>      <# } else if (item.greeting) { #>        </div><div class='nav-column nav-column-notfirst nav-column-break nav-column-greeting'>      <# } else { #>        </div><div class='nav-column nav-column-notfirst nav-column-break'>      <# } #>    <# } #>    <# if(item.dividerBefore) { #>      <div class='nav-divider'></div>    <# } #>    <# if(item.text || item.content) { #>      <# if(item.url) { #>        <a href='<#=item.url #>' class='nav-link      <# } else {#>        <span class='      <# } #>      <# if(item.panelKey) { #>        nav-hasPanel      <# } #>      <# if(item.items) { #>        nav-title      <# } #>      <# if(item.decorate == 'carat') { #>        nav-carat      <# } #>      <# if(item.decorate == 'nav-action-button') { #>        nav-action-button      <# } #>      nav-item'      <# if(item.extra) { #>        <#=item.extra #>      <# } #>      <# if(item.id) { #>        id='<#=item.id #>'      <# } #>      <# if(item.dataNavRole) { #>        data-nav-role='<#=item.dataNavRole #>'      <# } #>      <# if(item.dataNavRef) { #>        data-nav-ref='<#=item.dataNavRef #>'      <# } #>      <# if(item.panelKey) { #>        data-nav-panelkey='<#=item.panelKey #>'        role='navigation'        aria-label='<#=item.text#>'      <# } #>      <# if(item.subtextKey) { #>        data-nav-subtextkey='<#=item.subtextKey #>'      <# } #>      <# if(item.image && item.image.height > 16) { #>        style='line-height:<#=item.image.height #>px;'      <# } #>      >      <# if(item.decorate == 'carat') { #>        <i class='nav-icon'></i>      <# } #>      <# if(item.image && item.image.src) { #>        <img class='nav-image' src='<#=item.image.src #>' style='height:<#=item.image.height #>px; width:<#=item.image.width #>px;' />      <# } #>      <# if(item.text) { #>        <span class='nav-text<# if(item.classname) { #> <#=item.classname #><# } #>'><#=item.text#><# if(item.badgeText) { #>          <span class='nav-badge'><#=item.badgeText#></span>        <# } #></span>      <# } else if (item.content) { #>        <span class='nav-content'><# jQuery.each(item.content, function (j, cItem) { #><# if(cItem.url && cItem.text) { #><a href='<#=cItem.url #>' class='nav-a'><#=cItem.text #></a><# } else if (cItem.text) { #><#=cItem.text#><# } #><# }); #></span>      <# } #>      <# if(item.subtext) { #>        <span class='nav-subtext'><#=item.subtext #></span>      <# } #>      <# if(item.url) { #>        </a>      <# } else {#>        </span>      <# } #>    <# } #>    <# if(item.image && item.image.src) { #>      <# if(item.url) { #>        <a href='<#=item.url #>'>       <# } #>      <img class='nav-image'      <# if(item.id) { #>        id='<#=item.id #>'      <# } #>      src='<#=item.image.src #>' <# if (item.alt) { #> alt='<#= item.alt #>'<# } #>/>      <# if(item.url) { #>        </a>       <# } #>    <# } #>    <# if(item.items) { #>      <div class='nav-panel'> <# renderItems(item.items); #> </div>    <# } #>  <# }); #><# }; #><# renderItems(items); #><# if(hasColumns) { #>  </div><# } #>\",\"subnav\":\"<# if (obj && obj.type === 'vertical') { #>  <# jQuery.each(obj.rows, function (i, row) { #>    <# if (row.flyoutElement === 'button') { #>      <div class='nav_sv_fo_v_button'        <# if (row.elementStyle) { #>          style='<#= row.elementStyle #>'        <# } #>      >        <a href='<#=row.url #>' class='nav-action-button nav-sprite'>          <#=row.text #>        </a>      </div>    <# } else if (row.flyoutElement === 'list' && row.list) { #>      <# jQuery.each(row.list, function (j, list) { #>        <div class='nav_sv_fo_v_column <#=(j === 0) ? 'nav_sv_fo_v_first' : '' #>'>          <ul class='<#=list.elementClass #>'>          <# jQuery.each(list.linkList, function (k, link) { #>            <# if (k === 0) { link.elementClass += ' nav_sv_fo_v_first'; } #>            <li class='<#=link.elementClass #>'>              <# if (link.url) { #>                <a href='<#=link.url #>' class='nav_a'><#=link.text #></a>              <# } else { #>                <span class='nav_sv_fo_v_span'><#=link.text #></span>              <# } #>            </li>          <# }); #>          </ul>        </div>      <# }); #>    <# } else if (row.flyoutElement === 'link') { #>      <# if (row.topSpacer) { #>        <div class='nav_sv_fo_v_clear'></div>      <# } #>      <div class='<#=row.elementClass #>'>        <a href='<#=row.url #>' class='nav_sv_fo_v_lmargin nav_a'>          <#=row.text #>        </a>      </div>    <# } #>  <# }); #><# } else if (obj) { #>  <div class='nav_sv_fo_scheduled'>    <#= obj #>  </div><# } #>\",\"htmlList\":\"<# jQuery.each(items, function (i, item) { #>  <div class='nav-item'>    <#=item #>  </div><# }); #>\"}\n    })\n  })\n\n\n\n  window.$Nav && $Nav.declare('config.flyoutURL', null);\n  window.$Nav && $Nav.declare('btf.lite');\n  window.$Nav && $Nav.declare('btf.full');\n  window.$Nav && $Nav.declare('btf.exists');\n  (window.AmazonUIPageJS ? AmazonUIPageJS : P).register('navCF');\n\n\n  \n      window.$Nav && $Nav.when('$').run('CBIMarketplaceRedirectOverlayNavyaan', function($) {\n              $.ajax({\n                  type: 'POST',\n                  url: '/cross_border_interstitial/render',\n                  data: JSON.stringify({\n                      marketplaceId: 'ATVPDKIKX0DER',\n                      localCountryCode: 'US',\n                         customerId: null,\n                      sessionId: '134\\x2D1570085\\x2D9450222',\n                      deviceType: 'DESKTOP',\n                      referrer: 'https\\x3A\\x2F\\x2Fwww.google.com\\x2F',\n                      url: '\\x2FSmallthing\\x2DEasy\\x2DSpider\\x2DSolitaire\\x2Fdp\\x2FB00G36GT5A',\n                      pageType: 'MASDetailPage',\n                      languageOfPreference: 'en_US',\n                      queryParams: {},\n                      interstitialRequestType: 'CBI',\n                      weblabTreatmentMap: {\"CROSS_BORDER_INTERSTITIAL_ES_US_340017\":\"C\",\"CROSS_BORDER_INTERSTITIAL_MX_US_341718\":\"C\",\"CROSS_BORDER_INTERSTITIAL_ZA_659308\":\"C\",\"CBI_355055\":\"C\",\"NARX_INTERSTITIAL_NEW_CX_372291\":\"C\",\"MWEB_CROSS_BORDER_INTERSTITIAL_SE_366766\":\"T1\",\"MWEB_CROSS_BORDER_INTERSTITIAL_SA_366767\":\"T1\",\"MWEB_CROSS_BORDER_INTERSTITIAL_PL_366768\":\"T1\",\"MWEB_CROSS_BORDER_INTERSTITIAL_NL_366769\":\"T1\",\"MWEB_CROSS_BORDER_INTERSTITIAL_ZA_642039\":\"C\",\"NARX_INTERSTITIAL_AUI_MIGRATION_446901\":\"C\",\"TEST_ACS_CONFIGURATION_486322\":\"C\",\"CROSS_BORDER_INTERSTITIAL_ACS_SHADOW_TESTING_486317\":\"C\",\"NARX_INTERSTITIAL_SEMBU_MIGRATION_542466\":\"T2\",\"CROSS_BORDER_INTERSTITIAL_CUSTOMER_OPS_594994\":\"C\",\"INTERSTITIAL_PROTOTYPE_IP_ADDRESS_BR_598850\":\"C\"},\n                      slateToken: 'AQGJRntv0WvYIQGzHwAl+licRmczDpx7WNipgME1ZG6vwYnX3kOyXR855wAAAAgGcmV0YWlsCmFtYXpvbi5jb20ABWVuLVVTVVNEAAA='\n                  }),\n                  contentType: \"application/json\",\n                  dataType: \"html\",\n                  success: function(data) {\n                      if (data) {\n                          $('body').append(data);\n                      }\n                  }\n              });\n      });\n  \n\n\n\n\n\n\n\n\n\n\n  \n  \n\n               { \"rhfHandlerParams\":{\"currentPageType\":\"MASDetailPage\",\"currentSubPageType\":\"\",\"excludeAsin\":\"\",\"fieldKeywords\":\"\",\"k\":\"\",\"keywords\":\"\",\"search\":\"\",\"auditEnabled\":\"\",\"previewCampaigns\":\"\",\"forceWidgets\":\"\",\"searchAlias\":\"\"} }     <div class='rhf-border'> <div class='rhf-header'> Your recently viewed items and featured recommendations </div> <div class='rhf-footer'> <div class='rvi-container'> <div class='ybh-edit'> <div class='ybh-edit-arrow'> &#8250; </div> <div class='ybh-edit-link'> <a href='/gp/history'> View or edit your browsing history </a> </div> </div> <span class='no-rvi-message'> After viewing product detail pages, look here to find an easy way to navigate back to pages you are interested in. </span> </div> </div> </div>     Your recently viewed items and featured recommendations      ›    View or edit your browsing history     After viewing product detail pages, look here to find an easy way to navigate back to pages you are interested in.        \n\n  \n\n  \n  \n    Back to top\n  \n  \n\n\n  \n\n  \n        \n          Get to Know Us\n        \n            \n              Careers\n            \n            \n              Blog\n            \n            \n              About Amazon\n            \n            \n              Investor Relations\n            \n            \n              Amazon Devices\n            \n            \n              Amazon Science\n            \n        \n      \n        \n        \n          Make Money with Us\n        \n            \n              Sell products on Amazon\n            \n            \n              Sell on Amazon Business\n            \n            \n              Sell apps on Amazon\n            \n            \n              Become an Affiliate\n            \n            \n              Advertise Your Products\n            \n            \n              Self-Publish with Us\n            \n            \n              Host an Amazon Hub\n            \n            \n              ›See More Make Money with Us\n            \n        \n      \n        \n        \n          Amazon Payment Products\n        \n            \n              Amazon Business Card\n            \n            \n              Shop with Points\n            \n            \n              Reload Your Balance\n            \n            \n              Amazon Currency Converter\n            \n        \n      \n        \n        \n          Let Us Help You\n        \n            \n              Amazon and COVID-19\n            \n            \n              Your Account\n            \n            \n              Your Orders\n            \n            \n              Shipping Rates & Policies\n            \n            \n              Returns & Replacements\n            \n            \n              Manage Your Content and Devices\n            \n            \n              Amazon Assistant\n            \n            \n              Help\n            \n        \n      \n  \n\n\n\n  \n    \n      \n        \n        \n        \n      \n\n    \n      \n\n  #icp-touch-link-language { display: none; }\n\n\n\n\n  English\n\n\n\n\n\n  #icp-touch-link-cop { display: none; }\n\n\n\n  $USD - U.S. Dollar\n\n\n\n#icp-touch-link-country { display: none; }\n\n\n  United States\n\n\n    \n  \n  \n  \n  \n    \n      \nAmazon MusicStream millionsof songs\nAmazon AdvertisingFind, attract, andengage customers\n6pmScore dealson fashion brands\nAbeBooksBooks, art& collectibles\nACX Audiobook PublishingMade Easy\nSell on AmazonStart a Selling Account\nAmazon BusinessEverything ForYour Business\n \n\nAmazonGlobalShip OrdersInternationally\nHome ServicesExperienced ProsHappiness Guarantee\nAmazon IgniteSell your originalDigital EducationalResources\nAmazon Web ServicesScalable CloudComputing Services\nAudibleListen to Books & OriginalAudio Performances\nBook DepositoryBooks With FreeDelivery Worldwide\nBox Office MojoFind MovieBox Office Data\n \n\nComiXologyThousands ofDigital Comics\nDPReviewDigitalPhotography\nFabricSewing, Quilting& Knitting\nGoodreadsBook reviews& recommendations\nIMDbMovies, TV& Celebrities\nIMDbProGet Info EntertainmentProfessionals Need\nKindle Direct PublishingIndie Digital & Print PublishingMade Easy\n\n \n\nPrime Video DirectVideo DistributionMade Easy\nShopbopDesignerFashion Brands\nWoot!Deals and Shenanigans\nZapposShoes &Clothing\nRingSmart HomeSecurity Systems\n\neero WiFiStream 4K Videoin Every Room\nBlinkSmart Securityfor Every Home\n\n \n\n \n\n \n\nNeighbors App  Real-Time Crime& Safety Alerts\n\nAmazon Subscription BoxesTop subscription boxes – right to your door\nPillPackPharmacy Simplified\n \n\n \n\n\n    \n  \n\n  \n\n  Conditions of Use Privacy Notice Your Ads Privacy Choices  © 1996-2023, Amazon.com, Inc. or its affiliates\n\n\n  \n\n(function(a,b){a.attachEvent?a.attachEvent(\"onload\",b):a.addEventListener&&a.addEventListener(\"load\",b,!1)})(window,function(){setTimeout(function(){var el=document.getElementById(\"sis_pixel_r2\");el&&(el.innerHTML='<iframe id=\"DAsis\" src=\"//s.amazon-adsystem.com/iu3?d=amazon.com&slot=navFooter&a2=01010375035a9f90bfa8f6f3d347bdd993013e4c52fa4a2aea8267174c26d6d48781&old_oo=0&ts=1689104642269&s=AWqiUGUsYdL4l37wA-Id9bgBG0OHYJ7llBBGz84nxJoU&gdpr_consent=&gdpr_consent_avl=&cb=1689104642269\" width=\"1\" height=\"1\" frameborder=\"0\" marginwidth=\"0\" marginheight=\"0\" scrolling=\"no\" tabindex=\"-1\" sandbox></iframe>')},300)});\n\n  \n\n\n\n\n\n(window.AmazonUIPageJS ? AmazonUIPageJS : P).when('afterLoad').execute(function() {\n(function(d,h,z){var w=d.ue||{},p=function(){},n=function(d){return function(b,g){g||(g=\"ERROR\");b=b&&b.stack&&b.message?b:JSON.stringify(b);d({logLevel:g,attribution:\"MSAVowelsJavascriptAssets\",message:b})}}(d.ueLogError||p),t=function(d){return function(b,g){d(\"MSAVowelsJavascriptAssets:\"+b,g)}}(w.count||p),u=function(d){return function(){try{return d.apply(this,arguments)}catch(b){n(b,\"FATAL\")}}},k=function(){var g=Array.prototype.slice.call(arguments,0);g[0]=u(g[0]);return d.setTimeout.apply(null,\ng)},v=function(){t(\"invoked\",1);var g=function(){try{var d=h.createElement(\"link\").relList.supports(\"preload\")}catch(H){d=!1}return function(b){var f=d?h.createElement(\"link\"):new Image;f.onerror=f.onload=u(function(){f&&f.parentElement&&f.parentElement.removeChild(f)});d?(f.rel=\"preload\",f.as=\"image\",f.referrerPolicy=\"strict-origin-when-cross-origin\",f.href=b,h.head.appendChild(f)):(f.style.display=\"none\",f.referrerPolicy=\"strict-origin-when-cross-origin\",f.src=b,h.documentElement.appendChild(f))}}(),\nb=function(b){b={events:[{data:b}]};b=JSON.stringify(b);var g=p(d.location.href),f=\"https://unagi-na.amazon.com\";g&&g.toLowerCase().endsWith(\"amazon.cn\")&&(f=\"https://unagi.amazon.cn\");g=f+\"/1/events/com.amazon.Vowels.ClientMetrics\";navigator&&navigator.sendBeacon?navigator.sendBeacon(g,b):fetch(g,{method:\"POST\",body:b}).catch(function(b){n(b,\"WARN\")})},p=function(b){return(b=b.match(/^https?:\\/\\/([^\\/?#]+)(?:[\\/?#]|$)/i))&&b[1]};k(g,0,\"https://redirect.prod.experiment.routing.cloudfront.aws.a2z.com/x.png\");\nk(function(){P.when(\"3p-promise\").execute(function(g){function p(){for(var a=new URLSearchParams,c=0;c<arguments.length;c++){var b=arguments[c],l;for(l in b)a.set(l,b[l])}return a.toString()}function f(){for(var a={},c=0;c<arguments.length;c++){var b=arguments[c],l;for(l in b)a[l]=b[l]}return a}function k(a,c){var A=a.name,l=a.startTime||0,e=a.connectEnd||0,x=a.connectStart||0,d=a.domainLookupEnd||0,g=a.domainLookupStart||0,m=a.requestStart||0,k=a.responseEnd||0,h=a.responseStart||0,p=a.secureConnectionStart||\n0,t=a.transferSize||0,n=a.duration||0;0>=n&&0<l&&0<k&&(n=k-l);var q={src:A,sy:c+\".2023-05-05\"};0<=A.indexOf(\"/images/G/01/msa/vowels/metrics\")?q.l=r(n):(0<t&&(q.siz=t),0<h-m&&0<h&&0<m&&(q.ttf=r(h-m)),0<k-h&&0<k&&0<h&&(q.con=r(k-h)),0<n&&(q.dur=r(n)));0<d-g&&0<d&&0<g&&(q.dns=r(d-g));0<e-x&&0<e&&0<x&&(q.tcp=r(e-x));0<e-p&&0<e&&0<p&&(q.tls=r(e-p));\"serverTiming\"in a&&a.serverTiming.slice(0,15).forEach(function(c){var a=\"st_\"+c.name,e=c.description.substring(0,Math.min(64,c.description.length));q[a]=\nencodeURIComponent(c.duration+\";\"+e)});a=f(B,q);b(a)}function u(a){var c=f(B,{src:a,error:1}),b=m.now();return fetch(a).then(function(l){var e=m.now();var d=l.headers.get(\"x-cache\")||\"\";d=0<=d.indexOf(\"cloudfront\")?\"c\":0<=d.indexOf(\"akamai\")?\"a\":0<=d.indexOf(\"fastly\")?\"f\":\"u\";if(!l.ok)throw c.l=r(e-b),c.status=l.status,c.sy=d+\".2023-05-05\",v(d,c),c;l={name:a,duration:e-b};e=m.getEntriesByName(a);k(1===e.length?e[0]:l,d);return{cdn:d,url:a}},function(a){c.message=a.message;c.sy=\"u.2023-05-05\";v(\"u\",\nc);throw a;})}function v(a,c){try{var d=new URL(c.src)}catch(e){throw e;}var l=c.status;\"u\"===a?w(d,l).then(function(a){a.forEach(function(a){c.sy=a+\".2023-05-05\";b(c)})}):b(c)}function w(a,c){var b=[];a=[y(a,\"f\"),y(a,\"c\"),y(a,\"a\")];return g.all(a.map(D)).then(function(a){a.forEach(function(a){\"fulfilled\"===a.status?c&&a.value.statusCode===c&&b.push(a.value.cdn):c===z&&b.push(a.reason)});return 0<b.length?b:[\"u\"]})}function D(a){return a.then(function(a){return{value:a,status:\"fulfilled\"}},function(a){return{reason:a,\nstatus:\"rejected\"}})}function y(a,c){a.hostname=E[c];return fetch(a).then(function(a){return{cdn:c,statusCode:a.status}}).catch(function(a){n(a,\"WARN\");throw c;})}function F(){for(var a=new RegExp(\"^https://(.*.(images|ssl-images|media)-amazon.(?:com|cn)|\"+d.location.hostname+\")/images/\",\"i\"),c={},b=0,g=\"\",e,f,h=m.getEntriesByType(\"resource\"),k=h.length-1;0<=k;k--)e=h[k],0<e.transferSize&&e.transferSize>=e.encodedBodySize&&(e=a.exec(String(h[k].name)))&&3===e.length&&(e=e[1],f=c[e]=(c[e]||0)+1,f>\nb&&(g=e,b=f));return g}var C=navigator.userAgent,r=Math.round,m=d.performance,B={s:h.domain,u:d.location.pathname,tz:C},E={a:\"a.media-amazon.com\",c:\"dk9ps7goqoeef.cloudfront.net\",f:\"f.media-amazon.com\"},G=d.URLSearchParams&&d.fetch;m&&\"function\"===typeof m.getEntriesByName&&\"function\"===typeof m.getEntriesByType&&\"function\"===typeof m.now&&0>C.toLowerCase().indexOf(\"firefox\")&&G?function(){var a=F()||\"m.media-amazon.com\";u(\"https://\"+a+\"/images/G/01/msa/vowels/metrics.jpg?\"+p({time:+new Date,rand:r(1E6*\nMath.random()).toString()})).then(function(){var c=\"STID\"+r(1E6*Math.random()).toString()+\"-\"+ +new Date;return u(\"https://\"+a+\"/images/G/01/msa/vowels/metrics._\"+c+\"_.jpg\")}).then(function(c){c=c.cdn;t(\"cdn:\"+c,1);var b=m.getEntriesByType(\"resource\");if(b===z||0>=b.length)c=0;else{var d=\"https://\"+a+\"/images/\",e,g=0;for(e=0;e<b.length&&3>g;e++){var f=b[e];var h=f.name;0<f.transferSize&&f.transferSize>=f.encodedBodySize&&0===h.indexOf(d)&&!(0<h.indexOf(\"/images/G/01/msa/vowels/metrics\"))&&(k(f,c),\ng++)}c=g}t(\"resourceCount\",c)},function(a){t(\"resourceError\",1);a instanceof TypeError&&a.message&&a.message.includes(\"Failed to fetch\")?n(a,\"WARN\"):n(a,\"ERROR\")})}():t(\"unsupportedBrowser\",1)})},4E3)};\"loading\"!==h.readyState?k(v,1E3):d.addEventListener&&d.addEventListener(\"DOMContentLoaded\",function(){k(v,1E3)});t(\"registered\",1)})(window,document);\n});\n\n\n\n\n\n\n\nwindow.ue_ibe = (window.ue_ibe || 0) + 1;\nif (window.ue_ibe === 1) {\n(function(e,c){function h(b,a){f.push([b,a])}function g(b,a){if(b){var c=e.head||e.getElementsByTagName(\"head\")[0]||e.documentElement,d=e.createElement(\"script\");d.async=\"async\";d.src=b;d.setAttribute(\"crossorigin\",\"anonymous\");a&&a.onerror&&(d.onerror=a.onerror);a&&a.onload&&(d.onload=a.onload);c.insertBefore(d,c.firstChild)}}function k(){ue.uels=g;for(var b=0;b<f.length;b++){var a=f[b];g(a[0],a[1])}ue.deffered=1}var f=[];c.ue&&(ue.uels=h,c.ue.attach&&c.ue.attach(\"load\",k))})(document,window);\n\n\nif (window.ue && window.ue.uels) {\n        var cel_widgets = [ { \"c\":\"celwidget\" },{ \"s\":\"#nav-swmslot > div\", \"id_gen\":function(elem, index){ return 'nav_sitewide_msg'; } } ];\n\n                ue.uels(\"https://images-na.ssl-images-amazon.com/images/I/31bJewCvY-L.js\");\n}\nvar ue_mbl=ue_csm.ue.exec(function(h,a){function s(c){b=c||{};a.AMZNPerformance=b;b.transition=b.transition||{};b.timing=b.timing||{};if(a.csa){var d;b.timing.transitionStart&&(d=b.timing.transitionStart);b.timing.processStart&&(d=b.timing.processStart);d&&(csa(\"PageTiming\")(\"mark\",\"nativeTransitionStart\",d),csa(\"PageTiming\")(\"mark\",\"transitionStart\",d))}h.ue.exec(t,\"csm-android-check\")()&&b.tags instanceof Array&&(c=-1!=b.tags.indexOf(\"usesAppStartTime\")||b.transition.type?!b.transition.type&&-1<\nb.tags.indexOf(\"usesAppStartTime\")?\"warm-start\":void 0:\"view-transition\",c&&(b.transition.type=c));n=null;\"reload\"===e._nt&&h.ue_orct||\"intrapage-transition\"===e._nt?u(b):\"undefined\"===typeof e._nt&&f&&f.timing&&f.timing.navigationStart&&a.history&&\"function\"===typeof a.History&&\"object\"===typeof a.history&&a.history.length&&1!=a.history.length&&(b.timing.transitionStart=f.timing.navigationStart);p&&e.ssw(q,\"\"+(b.timing.transitionStart||n||\"\"));c=b.transition;d=e._nt?e._nt:void 0;c.subType=d;a.ue&&\na.ue.tag&&a.ue.tag(\"has-AMZNPerformance\");e.isl&&a.uex&&a.uex(\"at\",\"csm-timing\");v()}function w(c){a.ue&&a.ue.count&&a.ue.count(\"csm-cordova-plugin-failed\",1)}function t(){return a.cordova&&a.cordova.platformId&&\"android\"==a.cordova.platformId}function u(){if(p){var c=e.ssw(q),a=function(){},x=e.count||a,a=e.tag||a,k=b.timing.transitionStart,g=c&&!c.e&&c.val;n=c=g?+c.val:null;k&&g&&k>c?(x(\"csm.jumpStart.mtsDiff\",k-c||0),a(\"csm-rld-mts-gt\")):k&&g?a(\"csm-rld-mts-leq\"):g?k||a(\"csm-rld-mts-no-new\"):a(\"csm-rld-mts-no-old\")}f&&\nf.timing&&f.timing.navigationStart?b.timing.transitionStart=f.timing.navigationStart:delete b.timing.transitionStart}function v(){try{a.P.register(\"AMZNPerformance\",function(){return b})}catch(c){}}function r(){if(!b)return\"\";ue_mbl.cnt=null;var c=b.timing,d=b.transition,d=[\"mts\",l(c.transitionStart),\"mps\",l(c.processStart),\"mtt\",d.type,\"mtst\",d.subType,\"mtlt\",d.launchType];a.ue&&a.ue.tag&&(c.fr_ovr&&a.ue.tag(\"fr_ovr\"),c.fcp_ovr&&a.ue.tag(\"fcp_ovr\"),d.push(\"fr_ovr\",l(c.fr_ovr),\"fcp_ovr\",l(c.fcp_ovr)));\nfor(var c=\"\",e=0;e<d.length;e+=2){var f=d[e],g=d[e+1];\"undefined\"!==typeof g&&(c+=\"&\"+f+\"=\"+g)}return c}function l(a){if(\"undefined\"!==typeof a&&\"undefined\"!==typeof m)return a-m}function y(a,d){b&&(m=d,b.timing.transitionStart=a,b.transition.type=\"view-transition\",b.transition.subType=\"ajax-transition\",b.transition.launchType=\"normal\",ue_mbl.cnt=r)}var e=h.ue||{},m=h.ue_t0,q=\"csm-last-mts\",p=1===h.ue_sswmts,n,f=a.performance,b;if(a.P&&a.P.when&&a.P.register)return 1===a.ue_fnt&&(m=a.aPageStart||\nh.ue_t0),a.P.when(\"CSMPlugin\").execute(function(a){a.buildAMZNPerformance&&a.buildAMZNPerformance({successCallback:s,failCallback:w})}),{cnt:r,ajax:y}},\"mobile-timing\")(ue_csm,ue_csm.window);\n\n(function(d){d._uess=function(){var a=\"\";screen&&screen.width&&screen.height&&(a+=\"&sw=\"+screen.width+\"&sh=\"+screen.height);var b=function(a){var b=document.documentElement[\"client\"+a];return\"CSS1Compat\"===document.compatMode&&b||document.body[\"client\"+a]||b},c=b(\"Width\"),b=b(\"Height\");c&&b&&(a+=\"&vw=\"+c+\"&vh=\"+b);return a}})(ue_csm);\n\n(function(a){function c(a){d(\"log\",a)}var d=csa(\"Errors\",{producerId:\"csa\",logOptions:{ent:\"all\"}});a.ue_err.buffer&&d&&(a.ue_err.buffer.forEach(c),a.ue_err.buffer.push=c);var b=document.ue_backdetect;b&&b.ue_back&&a.ue&&(a.ue.bfini=b.ue_back.value);a.uet&&a.uet(\"be\");a.onLdEnd&&(window.addEventListener?window.addEventListener(\"load\",a.onLdEnd,!1):window.attachEvent&&window.attachEvent(\"onload\",a.onLdEnd));a.ueh&&a.ueh(0,window,\"load\",a.onLd,1);a.ue&&a.ue.tag&&(a.ue_furl?(b=a.ue_furl.replace(/\\./g,\n\"-\"),a.ue.tag(b)):a.ue.tag(\"nofls\"))})(ue_csm);\n\n(function(g,h){function d(a,d){var b={};if(!e||!f)try{var c=h.sessionStorage;c?a&&(\"undefined\"!==typeof d?c.setItem(a,d):b.val=c.getItem(a)):f=1}catch(g){e=1}e&&(b.e=1);return b}var b=g.ue||{},a=\"\",f,e,c,a=d(\"csmtid\");f?a=\"NA\":a.e?a=\"ET\":(a=a.val,a||(a=b.oid||\"NI\",d(\"csmtid\",a)),c=d(b.oid),c.e||(c.val=c.val||0,d(b.oid,c.val+1)),b.ssw=d);b.tabid=a})(ue_csm,ue_csm.window);\n\nue_csm.ue.exec(function(e,f){var a=e.ue||{},b=a._wlo,d;if(a.ssw){d=a.ssw(\"CSM_previousURL\").val;var c=f.location,b=b?b:c&&c.href?c.href.split(\"#\")[0]:void 0;c=(b||\"\")===a.ssw(\"CSM_previousURL\").val;!c&&b&&a.ssw(\"CSM_previousURL\",b);d=c?\"reload\":d?\"intrapage-transition\":\"first-view\"}else d=\"unknown\";a._nt=d},\"NavTypeModule\")(ue_csm,window);\nue_csm.ue.exec(function(c,a){function g(a){a.run(function(e){d.tag(\"csm-feature-\"+a.name+\":\"+e);d.isl&&c.uex(\"at\")})}if(a.addEventListener)for(var d=c.ue||{},f=[{name:\"touch-enabled\",run:function(b){var e=function(){a.removeEventListener(\"touchstart\",c,!0);a.removeEventListener(\"mousemove\",d,!0)},c=function(){b(\"true\");e()},d=function(){b(\"false\");e()};a.addEventListener(\"touchstart\",c,!0);a.addEventListener(\"mousemove\",d,!0)}}],b=0;b<f.length;b++)g(f[b])},\"csm-features\")(ue_csm,window);\n\n\n(function(a,e){function c(a){d(\"recordCounter\",a.c,a.v)}var b=e.images,d=csa(\"Metrics\",{producerId:\"csa\"});b&&b.length&&a.ue.count(\"totalImages\",b.length);a.ue.cv.buffer&&d&&(a.ue.cv.buffer.forEach(c),a.ue.cv.buffer.push=c)})(ue_csm,document);\n(function(b){function c(){var d=[];a.log&&a.log.isStub&&a.log.replay(function(a){e(d,a)});a.clog&&a.clog.isStub&&a.clog.replay(function(a){e(d,a)});d.length&&(a._flhs+=1,n(d),p(d))}function g(){a.log&&a.log.isStub&&(a.onflush&&a.onflush.replay&&a.onflush.replay(function(a){a[0]()}),a.onunload&&a.onunload.replay&&a.onunload.replay(function(a){a[0]()}),c())}function e(d,b){var c=b[1],f=b[0],e={};a._lpn[c]=(a._lpn[c]||0)+1;e[c]=f;d.push(e)}function n(b){q&&(a._lpn.csm=(a._lpn.csm||0)+1,b.push({csm:{k:\"chk\",\nf:a._flhs,l:a._lpn,s:\"inln\"}}))}function p(a){if(h)a=k(a),b.navigator.sendBeacon(l,a);else{a=k(a);var c=new b[f];c.open(\"POST\",l,!0);c.setRequestHeader&&c.setRequestHeader(\"Content-type\",\"text/plain\");c.send(a)}}function k(a){return JSON.stringify({rid:b.ue_id,sid:b.ue_sid,mid:b.ue_mid,mkt:b.ue_mkt,sn:b.ue_sn,reqs:a})}var f=\"XMLHttpRequest\",q=1===b.ue_ddq,a=b.ue,r=b[f]&&\"withCredentials\"in new b[f],h=b.navigator&&b.navigator.sendBeacon,l=\"//\"+b.ue_furl+\"/1/batch/1/OE/\",m=b.ue_fci_ft||5E3;a&&(r||h)&&\n(a._flhs=a._flhs||0,a._lpn=a._lpn||{},a.attach&&(a.attach(\"beforeunload\",a.exec(g,\"fcli-bfu\")),a.attach(\"pagehide\",a.exec(g,\"fcli-ph\"))),m&&b.setTimeout(a.exec(c,\"fcli-t\"),m),a._ffci=a.exec(c))})(window);\n\n\n(function(k,c){function l(a,b){return a.filter(function(a){return a.initiatorType==b})}function f(a,c){if(b.t[a]){var g=b.t[a]-b._t0,e=c.filter(function(a){return 0!==a.responseEnd&&m(a)<g}),f=l(e,\"script\"),h=l(e,\"link\"),k=l(e,\"img\"),n=e.map(function(a){return a.name.split(\"/\")[2]}).filter(function(a,b,c){return a&&c.lastIndexOf(a)==b}),q=e.filter(function(a){return a.duration<p}),s=g-Math.max.apply(null,e.map(m))<r|0;\"af\"==a&&(b._afjs=f.length);return a+\":\"+[e[d],f[d],h[d],k[d],n[d],q[d],s].join(\"-\")}}\nfunction m(a){return a.responseEnd-(b._t0-c.timing.navigationStart)}function n(){var a=c[h](\"resource\"),d=f(\"cf\",a),g=f(\"af\",a),a=f(\"ld\",a);delete b._rt;b._ld=b.t.ld-b._t0;b._art&&b._art();return[d,g,a].join(\"_\")}var p=20,r=50,d=\"length\",b=k.ue,h=\"getEntriesByType\";b._rre=m;b._rt=c&&c.timing&&c[h]&&n})(ue_csm,window.performance);\n\n\n(function(c,d){var b=c.ue,a=d.navigator;b&&b.tag&&a&&(a=a.connection||a.mozConnection||a.webkitConnection)&&a.type&&b.tag(\"netInfo:\"+a.type)})(ue_csm,window);\n\n\n(function(c,d){function h(a,b){for(var c=[],d=0;d<a.length;d++){var e=a[d],f=b.encode(e);if(e[k]){var g=b.metaSep,e=e[k],l=b.metaPairSep,h=[],m=void 0;for(m in e)e.hasOwnProperty(m)&&h.push(m+\"=\"+e[m]);e=h.join(l);f+=g+e}c.push(f)}return c.join(b.resourceSep)}function s(a){var b=a[k]=a[k]||{};b[t]||(b[t]=c.ue_mid);b[u]||(b[u]=c.ue_sid);b[f]||(b[f]=c.ue_id);b.csm=1;a=\"//\"+c.ue_furl+\"/1/\"+a[v]+\"/1/OP/\"+a[w]+\"/\"+a[x]+\"/\"+h([a],y);if(n)try{n.call(d[p],a)}catch(g){c.ue.sbf=1,(new Image).src=a}else(new Image).src=\na}function q(){g&&g.isStub&&g.replay(function(a,b,c){a=a[0];b=a[k]=a[k]||{};b[f]=b[f]||c;s(a)});l.impression=s;g=null}if(!(1<c.ueinit)){var k=\"metadata\",x=\"impressionType\",v=\"foresterChannel\",w=\"programGroup\",t=\"marketplaceId\",u=\"session\",f=\"requestId\",p=\"navigator\",l=c.ue||{},n=d[p]&&d[p].sendBeacon,r=function(a,b,c,d){return{encode:d,resourceSep:a,metaSep:b,metaPairSep:c}},y=r(\"\",\"?\",\"&\",function(a){return h(a.impressionData,z)}),z=r(\"/\",\":\",\",\",function(a){return a.featureName+\":\"+h(a.resources,\nA)}),A=r(\",\",\"@\",\"|\",function(a){return a.id}),g=l.impression;n?q():(l.attach(\"load\",q),l.attach(\"beforeunload\",q));try{d.P&&d.P.register&&d.P.register(\"impression-client\",function(){})}catch(B){c.ueLogError(B,{logLevel:\"WARN\"})}}})(ue_csm,window);\n\n\n\nvar ue_pty = \"MASDetailPage\";\n\n\n\n\nvar ue_adb = 4;\nvar ue_adb_rtla = 1;\nue_csm.ue.exec(function(y,a){function t(){if(d&&f){var a;a:{try{a=d.getItem(g);break a}catch(c){}a=void 0}if(a)return b=a,!0}return!1}function u(){if(a.fetch)fetch(m).then(function(a){if(!a.ok)throw Error(a.statusText);return a.text?a.text():null}).then(function(b){b?(-1<b.indexOf(\"window.ue_adb_chk = 1\")&&(a.ue_adb_chk=1),n()):h()})[\"catch\"](h);else e.uels(m,{onerror:h,onload:n})}function h(){b=k;l();if(f)try{d.setItem(g,b)}catch(a){}}function n(){b=1===a.ue_adb_chk?p:k;l();if(f)try{d.setItem(g,\nb)}catch(c){}}function q(){a.ue_adb_rtla&&c&&0<c.ec&&!1===r&&(c.elh=null,ueLogError({m:\"Hit Info\",fromOnError:1},{logLevel:\"INFO\",adb:b}),r=!0)}function l(){e.tag(b);e.isl&&a.uex&&uex(\"at\",b);s&&s.updateCsmHit(\"adb\",b);c&&0<c.ec?q():a.ue_adb_rtla&&c&&(c.elh=q)}function v(){return b}if(a.ue_adb){a.ue_fadb=a.ue_fadb||10;var e=a.ue,k=\"adblk_yes\",p=\"adblk_no\",m=\"https://m.media-amazon.com/images/G/01/csm/showads.v2.js?ad_size=-ad-util-&adstype=-ad-sidebar-&advertiser=-ad-banner-\",b=\"adblk_unk\",d;a:{try{d=\na.localStorage;break a}catch(z){}d=void 0}var g=\"csm:adb\",c=a.ue_err,s=e.cookie,f=void 0!==a.localStorage,w=Math.random()>1-1/a.ue_fadb,r=!1,x=t();w||!x?u():l();a.ue_isAdb=v;a.ue_isAdb.unk=\"adblk_unk\";a.ue_isAdb.no=p;a.ue_isAdb.yes=k}},\"adb\")(document,window);\n\n\n\n\n(function(c,l,m){function h(a){if(a)try{if(a.id)return\"//*[@id='\"+a.id+\"']\";var b,d=1,e;for(e=a.previousSibling;e;e=e.previousSibling)e.nodeName===a.nodeName&&(d+=1);b=d;var c=a.nodeName;1!==b&&(c+=\"[\"+b+\"]\");a.parentNode&&(c=h(a.parentNode)+\"/\"+c);return c}catch(f){return\"DETACHED\"}}function f(a){if(a&&a.getAttribute)return a.getAttribute(k)?a.getAttribute(k):f(a.parentElement)}var k=\"data-cel-widget\",g=!1,d=[];(c.ue||{}).isBF=function(){try{var a=JSON.parse(localStorage[\"csm-bf\"]||\"[]\"),b=0<=a.indexOf(c.ue_id);\na.unshift(c.ue_id);a=a.slice(0,20);localStorage[\"csm-bf\"]=JSON.stringify(a);return b}catch(d){return!1}}();c.ue_utils={getXPath:h,getFirstAscendingWidget:function(a,b){c.ue_cel&&c.ue_fem?!0===g?b(f(a)):d.push({element:a,callback:b}):b()},notifyWidgetsLabeled:function(){if(!1===g){g=!0;for(var a=f,b=0;b<d.length;b++)if(d[b].hasOwnProperty(\"callback\")&&d[b].hasOwnProperty(\"element\")){var c=d[b].callback,e=d[b].element;\"function\"===typeof c&&\"function\"===typeof a&&c(a(e))}d=null}},extractStringValue:function(a){if(\"string\"===\ntypeof a)return a}}})(ue_csm,window,document);\n\n\n(function(a){a.ue_cel||(a.ue_cel=function(){function f(a,c){c?c.r=v:c={r:v,c:1};!ue_csm.ue_sclog&&c.clog&&d.clog?d.clog(a,c.ns||q,c):c.glog&&d.glog?d.glog(a,c.ns||q,c):d.log(a,c.ns||q,c)}function m(a,d){\"function\"===typeof g&&g(\"log\",{schemaId:s+\".RdCSI.1\",eventType:a,clientData:d},{ent:{page:[\"requestId\"]}})}function c(){var a=n.length;if(0<a){for(var c=[],b=0;b<a;b++){var F=n[b].api;F.ready()?(F.on({ts:d.d,ns:q}),e.push(n[b]),f({k:\"mso\",n:n[b].name,t:d.d()})):c.push(n[b])}n=c}}function h(){if(!h.executed){for(var a=\n0;a<e.length;a++)e[a].api.off&&e[a].api.off({ts:d.d,ns:q});A();f({k:\"eod\",t0:d.t0,t:d.d()},{c:1,il:1});h.executed=1;for(a=0;a<e.length;a++)n.push(e[a]);e=[];b(t);b(x)}}function A(a){f({k:\"hrt\",t:d.d()},{c:1,il:1,n:a});l=Math.min(w,r*l);y()}function y(){b(x);x=k(function(){A(!0)},l)}function u(){h.executed||A()}var p=a.window,k=p.setTimeout,b=p.clearTimeout,r=1.5,w=p.ue_cel_max_hrt||3E4,s=\"robotdetection\",n=[],e=[],q=a.ue_cel_ns||\"cel\",t,x,d=a.ue,E=a.uet,B=a.uex,v=d.rid,C=p.csa,g,l=p.ue_cel_hrt_int||\n3E3,z=p.requestAnimationFrame||function(a){a()};!a.ue_cel_lclia&&C&&(g=C(\"Events\",{producerId:s}));if(d.isBF)f({k:\"bft\",t:d.d()});else{\"function\"==typeof E&&E(\"bb\",\"csmCELLSframework\",{wb:1});k(c,0);d.onunload(h);if(d.onflush)d.onflush(u);t=k(h,6E5);y();\"function\"==typeof B&&B(\"ld\",\"csmCELLSframework\",{wb:1});return{registerModule:function(a,b){n.push({name:a,api:b});f({k:\"mrg\",n:a,t:d.d()});c()},reset:function(a){f({k:\"rst\",t0:d.t0,t:d.d()});n=n.concat(e);e=[];for(var r=n.length,g=0;g<r;g++)n[g].api.off(),\nn[g].api.reset();v=a||d.rid;c();b(t);t=k(h,6E5);h.executed=0},timeout:function(a,d){return k(function(){z(function(){h.executed||a()})},d)},log:f,csaEventLog:m,off:h}}}())})(ue_csm);\n(function(a){a.ue_pdm||!a.ue_cel||a.ue.isBF||(a.ue_pdm=function(){function f(){try{var d=b.screen;if(d){var c={w:d.width,aw:d.availWidth,h:d.height,ah:d.availHeight,cd:d.colorDepth,pd:d.pixelDepth};e&&e.w===c.w&&e.h===c.h&&e.aw===c.aw&&e.ah===c.ah&&e.pd===c.pd&&e.cd===c.cd||(e=c,e.t=s(),e.k=\"sci\",E(e),!C&&g&&l(\"sci\",{h:(e.h||\"0\")+\"\"}))}var k=r.body||{},h=r.documentElement||{},m={w:Math.max(k.scrollWidth||0,k.offsetWidth||0,h.clientWidth||0,h.scrollWidth||0,h.offsetWidth||0),h:Math.max(k.scrollHeight||\n0,k.offsetHeight||0,h.clientHeight||0,h.scrollHeight||0,h.offsetHeight||0)};q&&q.w===m.w&&q.h===m.h||(q=m,q.t=s(),q.k=\"doi\",E(q));w=a.ue_cel.timeout(f,n);x+=1}catch(p){b.ueLogError&&ueLogError(p,{attribution:\"csm-cel-page-module\",logLevel:\"WARN\"})}}function m(){u(\"ebl\",\"default\",!1)}function c(){u(\"efo\",\"default\",!0)}function h(){u(\"ebl\",\"app\",!1)}function A(){u(\"efo\",\"app\",!0)}function y(){b.setTimeout(function(){r[H]?u(\"ebl\",\"pageviz\",!1):u(\"efo\",\"pageviz\",!0)},0)}function u(a,d,c){t!==c&&(E({k:a,\nt:s(),s:d},{ff:!0===c?0:1}),!C&&g&&l(a,{t:(s()||\"0\")+\"\",s:d}));t=c}function p(){d.attach&&(z&&d.attach(D,y,r),I&&P.when(\"mash\").execute(function(a){a&&a.addEventListener&&(a.addEventListener(\"appPause\",h),a.addEventListener(\"appResume\",A))}),d.attach(\"blur\",m,b),d.attach(\"focus\",c,b))}function k(){d.detach&&(z&&d.detach(D,y,r),I&&P.when(\"mash\").execute(function(a){a&&a.removeEventListener&&(a.removeEventListener(\"appPause\",h),a.removeEventListener(\"appResume\",A))}),d.detach(\"blur\",m,b),d.detach(\"focus\",\nc,b))}var b=a.window,r=a.document,w,s,n,e,q,t=null,x=0,d=a.ue,E=a.ue_cel.log,B=a.uet,v=a.uex,C=a.ue_cel_lclia,g=b.csa,l=a.ue_cel.csaEventLog,z=!!d.pageViz,D=z&&d.pageViz.event,H=z&&d.pageViz.propHid,I=b.P&&b.P.when;\"function\"==typeof B&&B(\"bb\",\"csmCELLSpdm\",{wb:1});return{on:function(a){n=a.timespan||500;s=a.ts;p();a=b.location;E({k:\"pmd\",o:a.origin,p:a.pathname,t:s()});f();\"function\"==typeof v&&v(\"ld\",\"csmCELLSpdm\",{wb:1})},off:function(a){clearTimeout(w);k();d.count&&d.count(\"cel.PDM.TotalExecutions\",\nx)},ready:function(){return r.body&&a.ue_cel&&a.ue_cel.log},reset:function(){e=q=null}}}(),a.ue_cel&&a.ue_cel.registerModule(\"page module\",a.ue_pdm))})(ue_csm);\n(function(a){a.ue_vpm||!a.ue_cel||a.ue.isBF||(a.ue_vpm=function(){function f(){var a=y(),b={w:k.innerWidth,h:k.innerHeight,x:k.pageXOffset,y:k.pageYOffset};c&&c.w==b.w&&c.h==b.h&&c.x==b.x&&c.y==b.y||(b.t=a,b.k=\"vpi\",c=b,r(c,{clog:1}),!q&&t&&x(\"vpi\",{t:(c.t||\"0\")+\"\",h:(c.h||\"0\")+\"\",y:(c.y||\"0\")+\"\",w:(c.w||\"0\")+\"\",x:(c.x||\"0\")+\"\"}));h=0;u=y()-a;p+=1}function m(){h||(h=a.ue_cel.timeout(f,A))}var c,h,A,y,u=0,p=0,k=a.window,b=a.ue,r=a.ue_cel.log,w=a.uet,s=a.uex,n=b.attach,e=b.detach,q=a.ue_cel_lclia,t=\nk.csa,x=a.ue_cel.csaEventLog;\"function\"==typeof w&&w(\"bb\",\"csmCELLSvpm\",{wb:1});return{on:function(a){y=a.ts;A=a.timespan||100;f();n&&(n(\"scroll\",m),n(\"resize\",m));\"function\"==typeof s&&s(\"ld\",\"csmCELLSvpm\",{wb:1})},off:function(a){clearTimeout(h);e&&(e(\"scroll\",m),e(\"resize\",m));b.count&&(b.count(\"cel.VPI.TotalExecutions\",p),b.count(\"cel.VPI.TotalExecutionTime\",u),b.count(\"cel.VPI.AverageExecutionTime\",u/p))},ready:function(){return a.ue_cel&&a.ue_cel.log},reset:function(){c=void 0},getVpi:function(){return c}}}(),\na.ue_cel&&a.ue_cel.registerModule(\"viewport module\",a.ue_vpm))})(ue_csm);\n(function(a){if(!a.ue_fem&&a.ue_cel&&a.ue_utils){var f=a.ue||{},m=a.window,c=m.document;!f.isBF&&!a.ue_fem&&c.querySelector&&m.getComputedStyle&&[].forEach&&(a.ue_fem=function(){function h(a,d){return a>d?3>a-d:3>d-a}function A(a,d){var c=m.pageXOffset,b=m.pageYOffset,k;a:{try{if(a){var g=a.getBoundingClientRect(),e,r=0===a.offsetWidth&&0===a.offsetHeight;c:{for(var f=a.parentNode,w=g.left||0,n=g.top||0,p=g.width||0,q=g.height||0;f&&f!==document.body;){var l;d:{try{var s=void 0;if(f)var G=f.getBoundingClientRect(),\ns={x:G.left||0,y:G.top||0,w:G.width||0,h:G.height||0};else s=void 0;l=s;break d}catch(v){}l=void 0}var t=window.getComputedStyle(f),u=\"hidden\"===t.overflow,y=u||\"hidden\"===t.overflowX,z=u||\"hidden\"===t.overflowY,J=n+q-1<l.y+1||n+1>l.y+l.h-1;if((w+p-1<l.x+1||w+1>l.x+l.w-1)&&y||J&&z){e=!0;break c}f=f.parentNode}e=!1}k={x:g.left+c||0,y:g.top+b||0,w:g.width||0,h:g.height||0,d:(r||e)|0}}else k=void 0;break a}catch(A){}k=void 0}if(k&&!a.cel_b)a.cel_b=k,C({n:a.getAttribute(x),w:a.cel_b.w,h:a.cel_b.h,d:a.cel_b.d,\nx:a.cel_b.x,y:a.cel_b.y,t:d,k:\"ewi\",cl:a.className},{clog:1});else{if(c=k)c=a.cel_b,b=k,c=b.d===c.d&&1===b.d?!1:!(h(c.x,b.x)&&h(c.y,b.y)&&h(c.w,b.w)&&h(c.h,b.h)&&c.d===b.d);c&&(a.cel_b=k,C({n:a.getAttribute(x),w:a.cel_b.w,h:a.cel_b.h,d:a.cel_b.d,x:a.cel_b.x,y:a.cel_b.y,t:d,k:\"ewi\"},{clog:1}))}}function y(b,g){var h;h=b.c?c.getElementsByClassName(b.c):b.id?[c.getElementById(b.id)]:c.querySelectorAll(b.s);b.w=[];for(var f=0;f<h.length;f++){var e=h[f];if(e){if(!e.getAttribute(x)){var r=e.getAttribute(\"cel_widget_id\")||\n(b.id_gen||v)(e,f)||e.id;e.setAttribute(x,r)}b.w.push(e);k(Q,e,g)}}!1===B&&(E++,E===d.length&&(B=!0,a.ue_utils.notifyWidgetsLabeled()))}function u(a,c){g.contains(a)||C({n:a.getAttribute(x),t:c,k:\"ewd\"},{clog:1})}function p(a){K.length&&ue_cel.timeout(function(){if(q){for(var c=R(),d=!1;R()-c<e&&!d;){for(d=S;0<d--&&0<K.length;){var b=K.shift();T[b.type](b.elem,b.time)}d=0===K.length}U++;p(a)}},0)}function k(a,c,d){K.push({type:a,elem:c,time:d})}function b(a,c){for(var b=0;b<d.length;b++)for(var e=\nd[b].w||[],g=0;g<e.length;g++)k(a,e[g],c)}function r(){M||(M=a.ue_cel.timeout(function(){M=null;var c=t();b(W,c);for(var g=0;g<d.length;g++)k(X,d[g],c);0===d.length&&!1===B&&(B=!0,a.ue_utils.notifyWidgetsLabeled());p(c)},n))}function w(){M||N||(N=a.ue_cel.timeout(function(){N=null;var a=t();b(Q,a);p(a)},n))}function s(){return z&&D&&g&&g.contains&&g.getBoundingClientRect&&t}var n=50,e=4.5,q=!1,t,x=\"data-cel-widget\",d=[],E=0,B=!1,v=function(){},C=a.ue_cel.log,g,l,z,D,H=m.MutationObserver||m.WebKitMutationObserver||\nm.MozMutationObserver,I=!!H,F,G,O=\"DOMAttrModified\",L=\"DOMNodeInserted\",J=\"DOMNodeRemoved\",N,M,K=[],U=0,S=null,W=\"removedWidget\",X=\"updateWidgets\",Q=\"processWidget\",T,V=m.performance||{},R=V.now&&function(){return V.now()}||function(){return Date.now()};\"function\"==typeof uet&&uet(\"bb\",\"csmCELLSfem\",{wb:1});return{on:function(b){function e(){if(s()){T={removedWidget:u,updateWidgets:y,processWidget:A};if(I){var a={attributes:!0,subtree:!0};F=new H(w);G=new H(r);F.observe(g,a);G.observe(g,{childList:!0,\nsubtree:!0});G.observe(l,a)}else z.call(g,O,w),z.call(g,L,r),z.call(g,J,r),z.call(l,L,w),z.call(l,J,w);r()}}g=c.body;l=c.head;z=g.addEventListener;D=g.removeEventListener;t=b.ts;d=a.cel_widgets||[];S=b.bs||5;f.deffered?e():f.attach&&f.attach(\"load\",e);\"function\"==typeof uex&&uex(\"ld\",\"csmCELLSfem\",{wb:1});q=!0},off:function(){s()&&(G&&(G.disconnect(),G=null),F&&(F.disconnect(),F=null),D.call(g,O,w),D.call(g,L,r),D.call(g,J,r),D.call(l,L,w),D.call(l,J,w));f.count&&f.count(\"cel.widgets.batchesProcessed\",\nU);q=!1},ready:function(){return a.ue_cel&&a.ue_cel.log},reset:function(){d=a.cel_widgets||[]}}}(),a.ue_cel&&a.ue_fem&&a.ue_cel.registerModule(\"features module\",a.ue_fem))}})(ue_csm);\n(function(a){!a.ue_mcm&&a.ue_cel&&a.ue_utils&&!a.ue.isBF&&(a.ue_mcm=function(){function f(a,b){var h=a.srcElement||a.target||{},f={k:m,w:(b||{}).ow||(A.body||{}).scrollWidth,h:(b||{}).oh||(A.body||{}).scrollHeight,t:(b||{}).ots||c(),x:a.pageX,y:a.pageY,p:p.getXPath(h),n:h.nodeName};y&&\"function\"===typeof y.now&&a.timeStamp&&(f.dt=(b||{}).odt||y.now()-a.timeStamp,f.dt=parseFloat(f.dt.toFixed(2)));a.button&&(f.b=a.button);h.href&&(f.r=p.extractStringValue(h.href));h.id&&(f.i=h.id);h.className&&h.className.split&&\n(f.c=h.className.split(/\\s+/));u(f,{c:1})}var m=\"mcm\",c,h=a.window,A=h.document,y=h.performance,u=a.ue_cel.log,p=a.ue_utils;return{on:function(k){c=k.ts;a.ue_cel_stub&&a.ue_cel_stub.replayModule(m,f);h.addEventListener&&h.addEventListener(\"mousedown\",f,!0)},off:function(a){h.addEventListener&&h.removeEventListener(\"mousedown\",f,!0)},ready:function(){return a.ue_cel&&a.ue_cel.log},reset:function(){}}}(),a.ue_cel&&a.ue_cel.registerModule(\"mouse click module\",a.ue_mcm))})(ue_csm);\n(function(a){a.ue_mmm||!a.ue_cel||a.ue.isBF||(a.ue_mmm=function(f){function m(a,c){var b={x:a.pageX||a.x||0,y:a.pageY||a.y||0,t:p()};!c&&l&&(b.t-l.t<A||b.x==l.x&&b.y==l.y)||(l=b,v.push(b))}function c(){if(v.length){E=F.now();for(var a=0;a<v.length;a++){var c=v[a],b=a;z=v[g];D=c;var e=void 0;if(!(e=2>b)){e=void 0;a:if(v[b].t-v[b-1].t>h)e=0;else{for(e=g+1;e<b;e++){var f=z,k=D,l=v[e];H=(k.x-f.x)*(f.y-l.y)-(f.x-l.x)*(k.y-f.y);if(H*H/((k.x-f.x)*(k.x-f.x)+(k.y-f.y)*(k.y-f.y))>y){e=0;break a}}e=1}e=!e}(I=\ne)?g=b-1:C.pop();C.push(c)}B=F.now()-E;q=Math.min(q,B);t=Math.max(t,B);x=(x*d+B)/(d+1);d+=1;n({k:u,e:C,min:Math.floor(1E3*q),max:Math.floor(1E3*t),avg:Math.floor(1E3*x)},{c:1});v=[];C=[];g=0}}var h=100,A=20,y=25,u=\"mmm1\",p,k,b=a.window,r=b.document,w=b.setInterval,s=a.ue,n=a.ue_cel.log,e,q=1E3,t=0,x=0,d=0,E,B,v=[],C=[],g=0,l,z,D,H,I,F=f&&f.now&&f||Date.now&&Date||{now:function(){return(new Date).getTime()}};return{on:function(a){p=a.ts;k=a.ns;s.attach&&s.attach(\"mousemove\",m,r);e=w(c,3E3)},off:function(a){k&&\n(l&&m(l,!0),c());clearInterval(e);s.detach&&s.detach(\"mousemove\",m,r)},ready:function(){return a.ue_cel&&a.ue_cel.log},reset:function(){v=[];C=[];g=0;l=null}}}(window.performance),a.ue_cel&&a.ue_cel.registerModule(\"mouse move module\",a.ue_mmm))})(ue_csm);\n\n\n\nue_csm.ue.exec(function(b,c){var e=function(){},f=function(){return{send:function(b,d){if(d&&b){var a;if(c.XDomainRequest)a=new XDomainRequest,a.onerror=e,a.ontimeout=e,a.onprogress=e,a.onload=e,a.timeout=0;else if(c.XMLHttpRequest){if(a=new XMLHttpRequest,!(\"withCredentials\"in a))throw\"\";}else a=void 0;if(!a)throw\"\";a.open(\"POST\",b,!0);a.setRequestHeader&&a.setRequestHeader(\"Content-type\",\"text/plain\");a.send(d)}},isSupported:!0}}(),g=function(){return{send:function(c,d){if(c&&d)if(navigator.sendBeacon(c,\nd))b.ue_sbuimp&&b.ue&&b.ue.ssw&&b.ue.ssw(\"eelsts\",\"scs\");else throw\"\";},isSupported:!!navigator.sendBeacon&&!(c.cordova&&c.cordova.platformId&&\"ios\"==c.cordova.platformId)}}();b.ue._ajx=f;b.ue._sBcn=g},\"Transportation-clients\")(ue_csm,window);\nue_csm.ue.exec(function(b,k){function B(){for(var a=0;a<arguments.length;a++){var c=arguments[a];try{var g;if(c.isSupported){var f=u.buildPayload(l,e);g=c.send(K,f)}else throw dummyException;return g}catch(d){}}a={m:\"All supported clients failed\",attribution:\"CSMSushiClient_TRANSPORTATION_FAIL\",f:\"sushi-client.js\",logLevel:\"ERROR\"};C(a,k.ue_err_chan||\"jserr\");b.ue_err.buffer&&b.ue_err.buffer.push(a)}function m(){if(e.length){for(var a=0;a<n.length;a++)n[a]();B(d._sBcn||{},d._ajx||{});e=[];h={};l=\n{};v=w=r=x=0}}function L(){var a=new Date,c=function(a){return 10>a?\"0\"+a:a};return Date.prototype.toISOString?a.toISOString():a.getUTCFullYear()+\"-\"+c(a.getUTCMonth()+1)+\"-\"+c(a.getUTCDate())+\"T\"+c(a.getUTCHours())+\":\"+c(a.getUTCMinutes())+\":\"+c(a.getUTCSeconds())+\".\"+String((a.getUTCMilliseconds()/1E3).toFixed(3)).slice(2,5)+\"Z\"}function y(a){try{return JSON.stringify(a)}catch(c){}return null}function D(a,c,g,f){var q=!1;f=f||{};s++;if(s==E){var p={m:\"Max number of Sushi Logs exceeded\",f:\"sushi-client.js\",\nlogLevel:\"ERROR\",attribution:\"CSMSushiClient_MAX_CALLS\"};C(p,k.ue_err_chan||\"jserr\");b.ue_err.buffer&&b.ue_err.buffer.push(p)}if(p=!(s>=E))(p=a&&-1<a.constructor.toString().indexOf(\"Object\")&&c&&-1<c.constructor.toString().indexOf(\"String\")&&g&&-1<g.constructor.toString().indexOf(\"String\"))||M++;p&&(d.count&&d.count(\"Event:\"+g,1),a.producerId=a.producerId||c,a.schemaId=a.schemaId||g,a.timestamp=L(),c=Date.now?Date.now():+new Date,g=Math.random().toString().substring(2,12),a.messageId=b.ue_id+\"-\"+\nc+\"-\"+g,f&&!f.ssd&&(a.sessionId=a.sessionId||b.ue_sid,a.requestId=a.requestId||b.ue_id,a.obfuscatedMarketplaceId=a.obfuscatedMarketplaceId||b.ue_mid),(c=y(a))?(c=c.length,(e.length==N||r+c>O)&&m(),r+=c,a={data:u.compressEvent(a)},e.push(a),(f||{}).n?0===F?m():v||(v=k.setTimeout(m,F)):w||(w=k.setTimeout(m,P)),q=!0):q=!1);!q&&b.ue_int&&console.error(\"Invalid JS Nexus API call\");return q}function G(){if(!H){for(var a=0;a<z.length;a++)z[a]();for(a=0;a<n.length;a++)n[a]();e.length&&(b.ue_sbuimp&&b.ue&&\nb.ue.ssw&&(a=y({dct:l,evt:e}),b.ue.ssw(\"eeldata\",a),b.ue.ssw(\"eelsts\",\"unk\")),B(d._sBcn||{}));H=!0}}function I(a){z.push(a)}function J(a){n.push(a)}var E=1E3,N=499,O=524288,t=function(){},d=b.ue||{},C=d.log||t,Q=b.uex||t;(b.uet||t)(\"bb\",\"ue_sushi_v1\",{wb:1});var K=b.ue_surl||\"https://unagi-na.amazon.com/1/events/com.amazon.csm.nexusclient.gamma\",R=[\"messageId\",\"timestamp\"],A=\"#\",e=[],h={},l={},r=0,x=0,M=0,s=0,z=[],n=[],H=!1,v,w,F=void 0===b.ue_hpsi?1E3:b.ue_hpsi,P=void 0===b.ue_lpsi?1E4:b.ue_lpsi,\nu=function(){function a(a){h[a]=A+x++;l[h[a]]=a;return h[a]}function c(b){if(!(b instanceof Function)){if(b instanceof Array){for(var f=[],d=b.length,e=0;e<d;e++)f[e]=c(b[e]);return f}if(b instanceof Object){f={};for(d in b)b.hasOwnProperty(d)&&(f[h[d]?h[d]:a(d)]=-1===R.indexOf(d)?c(b[d]):b[d]);return f}return\"string\"===typeof b&&(b.length>(A+x).length||b.charAt(0)===A)?h[b]?h[b]:a(b):b}}return{compressEvent:c,buildPayload:function(){return y({cs:{dct:l},events:e})}}}();(function(){if(d.event&&d.event.isStub){if(b.ue_sbuimp&&\nb.ue&&b.ue.ssw){var a=b.ue.ssw(\"eelsts\").val;if(a&&\"unk\"===a&&(a=b.ue.ssw(\"eeldata\").val)){var c;a:{try{c=JSON.parse(a);break a}catch(g){}c=null}c&&c.evt instanceof Array&&c.dct instanceof Object&&(e=c.evt,l=c.dct,e&&l&&(m(),b.ue.ssw(\"eeldata\",\"{}\"),b.ue.ssw(\"eelsts\",\"scs\")))}}d.event.replay(function(a){a[3]=a[3]||{};a[3].n=1;D.apply(this,a)});d.onSushiUnload.replay(function(a){I(a[0])});d.onSushiFlush.replay(function(a){J(a[0])})}})();d.attach(\"beforeunload\",G);d.attach(\"pagehide\",G);d._cmps=u;d.event=\nD;d.event.reset=function(){s=0};d.onSushiUnload=I;d.onSushiFlush=J;try{k.P&&k.P.register&&k.P.register(\"sushi-client\",t)}catch(S){b.ueLogError(S,{logLevel:\"WARN\"})}Q(\"ld\",\"ue_sushi_v1\",{wb:1})},\"Nxs-JS-Client\")(ue_csm,window);\n\n\nue_csm.ue_unrt = 1500;\n(function(d,b,t){function u(a,g){var c=a.srcElement||a.target||{},b={k:v,t:g.t,dt:g.dt,x:a.pageX,y:a.pageY,p:e.getXPath(c),n:c.nodeName};a.button&&(b.b=a.button);c.type&&(b.ty=c.type);c.href&&(b.r=e.extractStringValue(c.href));c.id&&(b.i=c.id);c.className&&c.className.split&&(b.c=c.className.split(/\\s+/));h+=1;e.getFirstAscendingWidget(c,function(a){b.wd=a;d.ue.log(b,r)})}function w(a){if(!x(a.srcElement||a.target)){m+=1;n=!0;var g=f=d.ue.d(),c;p&&\"function\"===typeof p.now&&a.timeStamp&&(c=p.now()-\na.timeStamp,c=parseFloat(c.toFixed(2)));s=b.setTimeout(function(){u(a,{t:g,dt:c})},y)}}function z(a){if(a){var b=a.filter(A);a.length!==b.length&&(q=!0,k=d.ue.d(),n&&q&&(k&&f&&d.ue.log({k:B,t:f,m:Math.abs(k-f)},r),l(),q=!1,k=0))}}function A(a){if(!a)return!1;var b=\"characterData\"===a.type?a.target.parentElement:a.target;if(!b||!b.hasAttributes||!b.attributes)return!1;var c={\"class\":\"gw-clock gw-clock-aria s-item-container-height-auto feed-carousel using-mouse kfs-inner-container\".split(\" \"),id:[\"dealClock\",\n\"deal_expiry_timer\",\"timer\"],role:[\"timer\"]},d=!1;Object.keys(c).forEach(function(a){var e=b.attributes[a]?b.attributes[a].value:\"\";(c[a]||\"\").forEach(function(a){-1!==e.indexOf(a)&&(d=!0)})});return d}function x(a){if(!a)return!1;var b=(e.extractStringValue(a.nodeName)||\"\").toLowerCase(),c=(e.extractStringValue(a.type)||\"\").toLowerCase(),d=(e.extractStringValue(a.href)||\"\").toLowerCase();a=(e.extractStringValue(a.id)||\"\").toLowerCase();var f=\"checkbox color date datetime-local email file month number password radio range reset search tel text time url week\".split(\" \");\nif(-1!==[\"select\",\"textarea\",\"html\"].indexOf(b)||\"input\"===b&&-1!==f.indexOf(c)||\"a\"===b&&-1!==d.indexOf(\"http\")||-1!==[\"sitbreaderrightpageturner\",\"sitbreaderleftpageturner\",\"sitbreaderpagecontainer\"].indexOf(a))return!0}function l(){n=!1;f=0;b.clearTimeout(s)}function C(){b.ue.onunload(function(){ue.count(\"armored-cxguardrails.unresponsive-clicks.violations\",h);ue.count(\"armored-cxguardrails.unresponsive-clicks.violationRate\",h/m*100||0)})}if(b.MutationObserver&&b.addEventListener&&Object.keys&&\nd&&d.ue&&d.ue.log&&d.ue_unrt&&d.ue_utils){var y=d.ue_unrt,r=\"cel\",v=\"unr_mcm\",B=\"res_mcm\",p=b.performance,e=d.ue_utils,n=!1,f=0,s=0,q=!1,k=0,h=0,m=0;b.addEventListener&&(b.addEventListener(\"mousedown\",w,!0),b.addEventListener(\"beforeunload\",l,!0),b.addEventListener(\"visibilitychange\",l,!0),b.addEventListener(\"pagehide\",l,!0));b.ue&&b.ue.event&&b.ue.onSushiUnload&&b.ue.onunload&&C();(new MutationObserver(z)).observe(t,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}})(ue_csm,window,document);\n\n\nue_csm.ue.exec(function(g,e){if(e.ue_err){var f=\"\";e.ue_err.errorHandlers||(e.ue_err.errorHandlers=[]);e.ue_err.errorHandlers.push({name:\"fctx\",handler:function(a){if(!a.logLevel||\"FATAL\"===a.logLevel)if(f=g.getElementsByTagName(\"html\")[0].innerHTML){var b=f.indexOf(\"var ue_t0=ue_t0||+new Date();\");if(-1!==b){var b=f.substr(0,b).split(String.fromCharCode(10)),d=Math.max(b.length-10-1,0),b=b.slice(d,b.length-1);a.fcsmln=d+b.length+1;a.cinfo=a.cinfo||{};for(var c=0;c<b.length;c++)a.cinfo[d+c+1+\"\"]=\nb[c]}b=f.split(String.fromCharCode(10));a.cinfo=a.cinfo||{};if(!(a.f||void 0===a.l||a.l in a.cinfo))for(c=+a.l-1,d=Math.max(c-5,0),c=Math.min(c+5,b.length-1);d<=c;d++)a.cinfo[d+1+\"\"]=b[d]}}})}},\"fatals-context\")(document,window);\n\n\n(function(m,a){function c(k){function f(b){b&&\"string\"===typeof b&&(b=(b=b.match(/^(?:https?:)?\\/\\/(.*?)(\\/|$)/i))&&1<b.length?b[1]:null,b&&b&&(\"number\"===typeof e[b]?e[b]++:e[b]=1))}function d(b){var e=10,d=+new Date;b&&b.timeRemaining?e=b.timeRemaining():b={timeRemaining:function(){return Math.max(0,e-(+new Date-d))}};for(var c=a.performance.getEntries(),k=e;g<c.length&&k>n;)c[g].name&&f(c[g].name),g++,k=b.timeRemaining();g>=c.length?h(!0):l()}function h(b){if(!b){b=m.scripts;var c;if(b)for(var d=\n0;d<b.length;d++)(c=b[d].getAttribute(\"src\"))&&\"undefined\"!==c&&f(c)}0<Object.keys(e).length&&(p&&ue_csm.ue&&ue_csm.ue.event&&ue_csm.ue.event({domains:e,pageType:a.ue_pty||null,subPageType:a.ue_spty||null,pageTypeId:a.ue_pti||null},\"csm\",\"csm.CrossOriginDomains.2\"),a.ue_ext=e)}function l(){!0===k?d():a.requestIdleCallback?a.requestIdleCallback(d):a.requestAnimationFrame?a.requestAnimationFrame(d):a.setTimeout(d,100)}function c(){if(a.performance&&a.performance.getEntries){var b=a.performance.getEntries();\n!b||0>=b.length?h(!1):l()}else h(!1)}var e=a.ue_ext||{};a.ue_ext||c();return e}function q(){setTimeout(c,r)}var s=a.ue_dserr||!1,p=!0,n=1,r=2E3,g=0;a.ue_err&&s&&(a.ue_err.errorHandlers||(a.ue_err.errorHandlers=[]),a.ue_err.errorHandlers.push({name:\"ext\",handler:function(a){if(!a.logLevel||\"FATAL\"===a.logLevel){var f=c(!0),d=[],h;for(h in f){var f=h,g=f.match(/amazon(\\.com?)?\\.\\w{2,3}$/i);g&&1<g.length||-1!==f.indexOf(\"amazon-adsystem.com\")||-1!==f.indexOf(\"amazonpay.com\")||-1!==f.indexOf(\"cloudfront-labs.amazonaws.com\")||\nd.push(h)}a.ext=d}}}));a.ue&&a.ue.isl?c():a.ue&&ue.attach&&ue.attach(\"load\",q)})(document,window);\n\n\n\n\n\nvar ue_wtc_c = 3;\nue_csm.ue.exec(function(b,e){function l(){for(var a=0;a<f.length;a++)a:for(var d=s.replace(A,f[a])+g[f[a]]+t,c=arguments,b=0;b<c.length;b++)try{c[b].send(d);break a}catch(e){}g={};f=[];n=0;k=p}function u(){B?l(q):l(C,q)}function v(a,m,c){r++;if(r>w)d.count&&1==r-w&&(d.count(\"WeblabTriggerThresholdReached\",1),b.ue_int&&console.error(\"Number of max call reached. Data will no longer be send\"));else{var h=c||{};h&&-1<h.constructor.toString().indexOf(D)&&a&&-1<a.constructor.toString().indexOf(x)&&m&&-1<\nm.constructor.toString().indexOf(x)?(h=b.ue_id,c&&c.rid&&(h=c.rid),c=h,a=encodeURIComponent(\",wl=\"+a+\"/\"+m),2E3>a.length+p?(2E3<k+a.length&&u(),void 0===g[c]&&(g[c]=\"\",f.push(c)),g[c]+=a,k+=a.length,n||(n=e.setTimeout(u,E))):b.ue_int&&console.error(\"Invalid API call. The input provided is over 2000 chars.\")):d.count&&(d.count(\"WeblabTriggerImproperAPICall\",1),b.ue_int&&console.error(\"Invalid API call. The input provided does not match the API protocol i.e ue.trigger(String, String, Object).\"))}}function F(){d.trigger&&\nd.trigger.isStub&&d.trigger.replay(function(a){v.apply(this,a)})}function y(){z||(f.length&&l(q),z=!0)}var t=\":1234\",s=\"//\"+b.ue_furl+\"/1/remote-weblab-triggers/1/OE/\"+b.ue_mid+\":\"+b.ue_sid+\":PLCHLDR_RID$s:wl-client-id%3DCSMTriger\",A=\"PLCHLDR_RID\",E=b.wtt||1E4,p=s.length+t.length,w=b.mwtc||2E3,G=1===e.ue_wtc_c,B=3===e.ue_wtc_c,H=e.XMLHttpRequest&&\"withCredentials\"in new e.XMLHttpRequest,x=\"String\",D=\"Object\",d=b.ue,g={},f=[],k=p,n,z=!1,r=0,C=function(){return{send:function(a){if(H){var b=new e.XMLHttpRequest;\nb.open(\"GET\",a,!0);G&&(b.withCredentials=!0);b.send()}else throw\"\";}}}(),q=function(){return{send:function(a){(new Image).src=a}}}();e.encodeURIComponent&&(d.attach&&(d.attach(\"beforeunload\",y),d.attach(\"pagehide\",y)),F(),d.trigger=v)},\"client-wbl-trg\")(ue_csm,window);\n\n\n(function(k,d,h){function f(a,c,b){a&&a.indexOf&&0===a.indexOf(\"http\")&&0!==a.indexOf(\"https\")&&l(s,c,a,b)}function g(a,c,b){a&&a.indexOf&&(location.href.split(\"#\")[0]!=a&&null!==a&&\"undefined\"!==typeof a||l(t,c,a,b))}function l(a,c,b,e){m[b]||(e=u&&e?n(e):\"N/A\",d.ueLogError&&d.ueLogError({message:a+c+\" : \"+b,logLevel:v,stack:\"N/A\"},{attribution:e}),m[b]=1,p++)}function e(a,c){if(a&&c)for(var b=0;b<a.length;b++)try{c(a[b])}catch(d){}}function q(){return d.performance&&d.performance.getEntriesByType?\nd.performance.getEntriesByType(\"resource\"):[]}function n(a){if(a.id)return\"//*[@id='\"+a.id+\"']\";var c;c=1;var b;for(b=a.previousSibling;b;b=b.previousSibling)b.nodeName==a.nodeName&&(c+=1);b=a.nodeName;1!=c&&(b+=\"[\"+c+\"]\");a.parentNode&&(b=n(a.parentNode)+\"/\"+b);return b}function w(){var a=h.images;a&&a.length&&e(a,function(a){var b=a.getAttribute(\"src\");f(b,\"img\",a);g(b,\"img\",a)})}function x(){var a=h.scripts;a&&a.length&&e(a,function(a){var b=a.getAttribute(\"src\");f(b,\"script\",a);g(b,\"script\",a)})}\nfunction y(){var a=h.styleSheets;a&&a.length&&e(a,function(a){if(a=a.ownerNode){var b=a.getAttribute(\"href\");f(b,\"style\",a);g(b,\"style\",a)}})}function z(){if(A){var a=q();e(a,function(a){f(a.name,a.initiatorType)})}}function B(){e(q(),function(a){g(a.name,a.initiatorType)})}function r(){var a;a=d.location&&d.location.protocol?d.location.protocol:void 0;\"https:\"==a&&(z(),w(),x(),y(),B(),p<C&&setTimeout(r,D))}var s=\"[CSM] Insecure content detected \",t=\"[CSM] Ajax request to same page detected \",v=\"WARN\",\nm={},p=0,D=k.ue_nsip||1E3,C=5,A=1==k.ue_urt,u=!0;ue_csm.ue_disableNonSecure||(d.performance&&d.performance.setResourceTimingBufferSize&&d.performance.setResourceTimingBufferSize(300),r())})(ue_csm,window,document);\n\n\nvar ue_aa_a = \"C\";\nif (ue.trigger && (ue_aa_a === \"C\" || ue_aa_a === \"T1\")) {\n    ue.trigger(\"UEDATA_AA_SERVERSIDE_ASSIGNMENT_CLIENTSIDE_TRIGGER_190249\", ue_aa_a);\n}\n(function(f,b){function g(){try{b.PerformanceObserver&&\"function\"===typeof b.PerformanceObserver&&(a=new b.PerformanceObserver(function(b){c(b.getEntries())}),a.observe(d))}catch(h){k()}}function m(){for(var h=d.entryTypes,a=0;a<h.length;a++)c(b.performance.getEntriesByType(h[a]))}function c(a){if(a&&Array.isArray(a)){for(var c=0,e=0;e<a.length;e++){var d=l.indexOf(a[e].name);if(-1!==d){var g=Math.round(b.performance.timing.navigationStart+a[e].startTime);f.uet(n[d],void 0,void 0,g);c++}}l.length===\nc&&k()}}function k(){a&&a.disconnect&&\"function\"===typeof a.disconnect&&a.disconnect()}if(\"function\"===typeof f.uet&&b.performance&&\"object\"===typeof b.performance&&b.performance.getEntriesByType&&\"function\"===typeof b.performance.getEntriesByType&&b.performance.timing&&\"object\"===typeof b.performance.timing&&\"number\"===typeof b.performance.timing.navigationStart){var d={entryTypes:[\"paint\"]},l=[\"first-paint\",\"first-contentful-paint\"],n=[\"fp\",\"fcp\"],a;try{m(),g()}catch(p){f.ueLogError(p,{logLevel:\"ERROR\",\nattribution:\"performanceMetrics\"})}}})(ue_csm,window);\n\n\nif (window.csa) {\n    csa(\"Events\")(\"setEntity\", {\n        page:{pageType: \"MASDetailPage\", subPageType: \"\", pageTypeId: \"\"}\n    });\n}\ncsa.plugin(function(c){var m=\"transitionStart\",n=\"pageVisible\",e=\"PageTiming\",t=\"visibilitychange\",s=\"$latency.visible\",i=c.global,r=(i.performance||{}).timing,a=[\"navigationStart\",\"unloadEventStart\",\"unloadEventEnd\",\"redirectStart\",\"redirectEnd\",\"fetchStart\",\"domainLookupStart\",\"domainLookupEnd\",\"connectStart\",\"connectEnd\",\"secureConnectionStart\",\"requestStart\",\"responseStart\",\"responseEnd\",\"domLoading\",\"domInteractive\",\"domContentLoadedEventStart\",\"domContentLoadedEventEnd\",\"domComplete\",\"loadEventStart\",\"loadEventEnd\"],o=i.Math,u=o.max,l=o.floor,d=i.document||{},g=(r||{}).navigationStart,f=g,v=0,p=null;if(i.Object.keys&&[].forEach&&!c.config[\"KillSwitch.\"+e]){if(!r||null===g||g<=0||void 0===g)return c.error(\"Invalid navigation timing data: \"+g);p=new S({schemaId:\"<ns>.PageLatency.5\",producerId:\"csa\"}),\"boolean\"!=typeof d.hidden&&\"string\"!=typeof d.visibilityState||!d.removeEventListener?c.emit(s):h()?(c.emit(s),E(n,g)):c.on(d,t,function e(){h()&&(f=c.time(),d.removeEventListener(t,e),E(m,f),E(n,f),c.emit(s))}),c.once(\"$unload\",I),c.once(\"$load\",I),c.on(\"$pageTransition\",function(){f=c.time()}),c.register(e,{mark:E,instance:function(e){return new S(e)}})}function S(e){var i,r=null,a=e.ent||{page:[\"pageType\",\"subPageType\",\"requestId\"]},o=e.logger||c(\"Events\",{producerId:e.producerId});if(!e||!e.producerId||!e.schemaId)return c.error(\"The producer id and schema Id must be defined for PageLatencyInstance.\");function d(){return i||f}function n(){r=c.UUID()}this.mark=function(n,t){if(null!=n)return t=t||c.time(),n===m&&(i=t),c.once(s,function(){o(\"log\",{messageId:r,__merge:function(e){e.markers[n]=function(e,n){return u(0,n-(e||f))}(d(),t),e.markerTimestamps[n]=l(t)},markers:{},markerTimestamps:{},navigationStartTimestamp:d()?new Date(d()).toISOString():null,schemaId:e.schemaId},{ent:a})}),t},n(),c.on(\"$beforePageTransition\",n)}function E(e,n){e===m&&(f=n);var t=p.mark(e,n);c.emit(\"$timing:\"+e,t)}function I(){if(!v){for(var e=0;e<a.length;e++)r[a[e]]&&E(a[e],r[a[e]]);v=1}}function h(){return!d.hidden||\"visible\"===d.visibilityState}});csa.plugin(function(f){var u,c,l=\"length\",a=\"parentElement\",t=\"target\",i=\"getEntriesByName\",e=\"perf\",n=null,r=\"_csa_flt\",o=\"_csa_llt\",s=\"previousSibling\",d=\"_osrc\",g=\"_elt\",h=\"_eid\",m=10,p=5,v=15,y=100,E=f.global,S=f.timeout,b=E.Math,x=b.max,L=b.floor,O=b.ceil,_=E.document,w=E.performance||{},T=(w.timing||{}).navigationStart,I=Date.now,N=Object.values||(f.types||{}).ovl,k=f(\"PageTiming\"),B=f(\"SpeedIndexBuffers\"),Y=[],C=[],F=[],H=[],M=[],R=[],V=.1,W=.1,$=0,P=0,X=!0,D=0,J=0,j=1==f.config[\"SpeedIndex.ForceReplay\"],q=0,Q=1,U=0,z={},A=[],G=0,K={buffered:1};function Z(e){f.global.ue_csa_ss_tag||f.emit(\"$csmTag:\"+e,0,K)}function ee(){for(var e=I(),n=0;u;){if(0!==u[l]){if(!1!==u.h(u[0])&&u.shift(),n++,!j&&n%m==0&&I()-e>p)break}else u=u.n}$=0,u&&($||(!0===_.hidden?(j=1,ee()):f.timeout(ee,0)))}function ne(e,n,t,i,r){U=L(e),Y=n,C=t,F=i,R=r;var o=_.createTreeWalker(_.body,NodeFilter.SHOW_TEXT,null,null),a={w:E.innerWidth,h:E.innerHeight,x:E.pageXOffset,y:E.pageYOffset};_.body[g]=e,H.push({w:o,vp:a}),M.push({img:_.images,iter:0}),Y.h=te,(Y.n=C).h=ie,(C.n=F).h=re,(F.n=H).h=oe,(H.n=M).h=ae,(M.n=R).h=fe,u=Y,ee()}function te(e){e.m.forEach(function(e){for(var n=e;n&&(e===n||!n[r]||!n[o]);)n[r]||(n[r]=e[r]),n[o]||(n[o]=e[o]),n[g]=n[r]-T,n=n[s]})}function ie(e){e.m.forEach(function(e){var n=e[t];d in n||(n[d]=e.oldValue)})}function re(n){n.m.forEach(function(e){e[t][g]=n.t-T})}function oe(e){for(var n,t=e.vp,i=e.w,r=m;(n=i.nextNode())&&0<r;){r-=1;var o=(n[a]||{}).nodeName;\"SCRIPT\"!==o&&\"STYLE\"!==o&&\"NOSCRIPT\"!==o&&\"BODY\"!==o&&0!==(n.nodeValue||\"\").trim()[l]&&de(n[a],ue(n),t)}return!n}function ae(e){for(var n={w:E.innerWidth,h:E.innerHeight,x:E.pageXOffset,y:E.pageYOffset},t=m;e.iter<e.img[l]&&0<t;){var i,r=e.img[e.iter],o=se(r),a=o&&ue(o)||ue(r);o?(o[g]=a,i=le(o.querySelector('[aria-posinset=\"1\"] img')||r)||a,r=o):i=le(r)||a,J&&c<i&&(i=a),de(r,i,n),e.iter+=1,t-=1}return e.img[l]<=e.iter}function fe(e){var n=[],i=0,r=0,o=P,t=L(e.y/y),a=O((e.y+E.innerHeight)/y);A.slice(t,a).forEach(function(e){(e.elems||[]).forEach(function(e){e.lt in n||(n[e.lt]={}),e.id in n[e.lt]||(i+=(n[e.lt][e.id]=e).a)})}),Z(\"startVL\"),N(n).forEach(function(e){N(e).forEach(function(e){var n=1-r/i,t=x(e.lt,o);G+=n*(t-o),o=t,function(e,n){var t;for(;V<=1&&V-.01<=e;)ge(\"visuallyLoaded\"+(t=(100*V).toFixed(0)),n.lt),\"50\"!==t&&\"90\"!==t||f(\"Content\",{target:n.e})(\"mark\",\"visuallyLoaded\"+t,T+O(n.lt||0)),V+=W}((r+=e.a)/i,e)})}),Z(\"endVL\"),P=e.t-T,R[l]<=1&&(ge(\"speedIndex\",G),ge(\"visuallyLoaded0\",U)),X&&(X=!1,ge(\"atfSpeedIndex\",G))}function ue(e){for(var n=e[a],t=v;n&&0<t;){if(n[g]||0===n[g])return x(n[g],U);n=n.parentElement,t-=1}}function ce(e,n){if(e){if(!e.indexOf(\"data:\"))return ue(n);var t=w[i](e)||[];if(0<t[l])return x(O(t[0].responseEnd||0),U)}}function le(e){return ce(e[d],e)||ce(e.currentSrc,e)||ce(e.src,e)}function se(e){for(var n=10,t=e.parentElement;t&&0<n;){if(t.classList&&t.classList.contains(\"a-carousel-viewport\"))return t;t=t.parentElement,n-=1}return null}function de(e,n,t){if((n||0===n)&&!e[h]){var i=e.getBoundingClientRect(),r=i.width*i.height,o=i.width/2,a=Q++;if(0!=r&&!(o<i.right-t.w||i.right<o)){for(var f={e:e,lt:n,a:r,id:a},u=L((i.top+t.y)/y),c=O((i.top+t.y+i.height)/y),l=u;l<=c;l++)l in A||(A[l]={elems:[],lt:0}),A[l].elems.push(f);e[h]=a}}}function ge(e,n){k(\"mark\",e,T+O((z[e]=n)||0))}function he(e){q||(Z(\"browserQuite\"+e),B(\"getBuffers\",ne),q=1)}T&&N&&w[i]?(Z(e+\"Yes\"),B(\"registerListener\",function(){J&&(clearTimeout(D),D=S(he.bind(n,\"Mut\"),2500))}),f.once(\"$unload\",function(){j=1,he(\"Ud\")}),f.once(\"$load\",function(){J=1,c=I()-T,D=S(he.bind(n,\"Ld\"),2500)}),f.once(\"$timing:functional\",he.bind(n,\"Fn\")),B(\"replayModuleIsLive\"),f.register(\"SpeedIndex\",{getMarkers:function(e){e&&e(JSON.parse(JSON.stringify(z)))}})):Z(e+\"No\")});csa.plugin(function(e){var m=!!e.config[\"LCP.elementDedup\"],t=!1,n=e(\"PageTiming\"),r=e.global.PerformanceObserver,a=e.global.performance;function i(){return a.timing.navigationStart}function o(){t||function(o){var l=new r(function(e){var t=e.getEntries();if(0!==t.length){var n=t[t.length-1];if(m&&\"\"!==n.id&&n.element&&\"IMG\"===n.element.tagName){for(var r={},a=t[0],i=0;i<t.length;i++)t[i].id in r||(\"\"!==t[i].id&&(r[t[i].id]=!0),a.startTime<t[i].startTime&&(a=t[i]));n=a}l.disconnect(),o({startTime:n.startTime,renderTime:n.renderTime,loadTime:n.loadTime})}});try{l.observe({type:\"largest-contentful-paint\",buffered:!0})}catch(e){}}(function(e){e&&(t=!0,n(\"mark\",\"largestContentfulPaint\",Math.floor(e.startTime+i())),e.renderTime&&n(\"mark\",\"largestContentfulPaint.render\",Math.floor(e.renderTime+i())),e.loadTime&&n(\"mark\",\"largestContentfulPaint.load\",Math.floor(e.loadTime+i())))})}r&&a&&a.timing&&(e.once(\"$unload\",o),e.once(\"$load\",o),e.register(\"LargestContentfulPaint\",{}))});csa.plugin(function(r){var e=r(\"Metrics\",{producerId:\"csa\"}),n=r.global.PerformanceObserver;n&&(n=new n(function(r){var t=r.getEntries();if(0===t.length||!t[0].processingStart||!t[0].startTime)return;!function(r){r=r||0,n.disconnect(),0<=r?e(\"recordMetric\",\"firstInputDelay\",r):e(\"recordMetric\",\"firstInputDelay.invalid\",1)}(t[0].processingStart-t[0].startTime)}),function(){try{n.observe({type:\"first-input\",buffered:!0})}catch(r){}}())});csa.plugin(function(d){var e=\"Metrics\",g=0;function r(i){var c,t,e=i.producerId,r=i.logger,s=r||d(\"Events\",{producerId:e}),o=(i||{}).dimensions||{},u={},n=-1;if(!e&&!r)return d.error(\"Either a producer id or custom logger must be defined\");function a(){n!==g&&(c=d.UUID(),t=d.UUID(),u={},n=g)}this.recordMetric=function(r,n){var e=i.logOptions||{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}};e.debugMetric=i.debugMetric,a(),s(\"log\",{messageId:c,schemaId:i.schemaId||\"<ns>.Metric.3\",metrics:{},dimensions:o,__merge:function(e){e.metrics[r]=n}},e)},this.recordCounter=function(r,e){var n=i.logOptions||{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}};if(\"string\"!=typeof r||\"number\"!=typeof e||!isFinite(e))return d.error(\"Invalid type given for counter name or counter value: \"+r+\"/\"+e);a(),r in u||(u[r]={});var c=u[r];\"f\"in c||(c.f=e),c.c=(c.c||0)+1,c.s=(c.s||0)+e,c.l=e,s(\"log\",{messageId:t,schemaId:i.schemaId||\"<ns>.InternalCounters.2\",c:{},__merge:function(e){r in e.c||(e.c[r]={}),c.fs||(c.fs=1,e.c[r].f=c.f),1<c.c&&(e.c[r].s=c.s,e.c[r].l=c.l,e.c[r].c=c.c)}},n)}}d.config[\"KillSwitch.\"+e]||(new r({producerId:\"csa\"}).recordMetric(\"baselineMetricEvent\",1),d.on(\"$beforePageTransition\",function(){g++}),d.register(e,{instance:function(e){return new r(e||{})}}))});csa.plugin(function(t){var a,r=(t.global.performance||{}).timing,s=(r||{}).navigationStart||t.time();function e(){a=t.UUID()}function n(i){var r=(i=i||{}).producerId,e=i.logger,o=e||t(\"Events\",{producerId:r});if(!r&&!e)return t.error(\"Either a producer id or custom logger must be defined\");this.mark=function(e,r){var n=(void 0===r?t.time():r)-s;o(\"log\",{messageId:a,schemaId:i.schemaId||\"<ns>.Timer.1\",markers:{},__merge:function(r){r.markers[e]=n}},i.logOptions)}}r&&(e(),t.on(\"$beforePageTransition\",e),t.register(\"Timers\",{instance:function(r){return new n(r||{})}}))});csa.plugin(function(t){var e=\"takeRecords\",i=\"disconnect\",n=\"function\",o=t(\"Metrics\",{producerId:\"csa\"}),c=t(\"PageTiming\"),a=t.global,u=t.timeout,r=t.on,f=a.PerformanceObserver,m=0,l=!1,s=0,d=a.performance,h=a.document,v=null,y=!1,g=t.blank;function p(){l||(l=!0,clearTimeout(v),typeof f[e]===n&&f[e](),typeof f[i]===n&&f[i](),o(\"recordMetric\",\"documentCumulativeLayoutShift\",m),c(\"mark\",\"cumulativeLayoutShiftLastTimestamp\",Math.floor(s+d.timing.navigationStart)))}f&&d&&d.timing&&h&&(f=new f(function(t){v&&clearTimeout(v);t.getEntries().forEach(function(t){t.hadRecentInput||(m+=t.value,s<t.startTime&&(s=t.startTime))}),v=u(p,5e3)}),function(){try{f.observe({type:\"layout-shift\",buffered:!0}),v=u(p,5e3)}catch(t){}}(),g=r(h,\"click\",function(t){y||(y=!0,o(\"recordMetric\",\"documentCumulativeLayoutShiftToFirstInput\",m),g())}),r(h,\"visibilitychange\",function(){\"hidden\"===h.visibilityState&&p()}),t.once(\"$unload\",p))});csa.plugin(function(e){var t,n=e.global,r=n.PerformanceObserver,c=e(\"Metrics\",{producerId:\"csa\"}),o=0,i=0,a=-1,l=n.Math,f=l.max,u=l.ceil;if(r){t=new r(function(e){e.getEntries().forEach(function(e){var t=e.duration;o+=t,i+=t,a=f(t,a)})});try{t.observe({type:\"longtask\",buffered:!0})}catch(e){}t=new r(function(e){0<e.getEntries().length&&(i=0,a=-1)});try{t.observe({type:\"largest-contentful-paint\",buffered:!0})}catch(e){}e.on(\"$unload\",g),e.on(\"$beforePageTransition\",g)}function g(){c(\"recordMetric\",\"totalBlockingTime\",u(i||0)),c(\"recordMetric\",\"totalBlockingTimeInclLCP\",u(o||0)),c(\"recordMetric\",\"maxBlockingTime\",u(a||0)),i=o=0,a=-1}});csa.plugin(function(r){var e=\"CacheDetection\",o=\"csa-ctoken-\",n=r.store,t=r.deleteStored,c=r.config,a=c[e+\".RequestID\"],i=c[e+\".Callback\"],s=r.global,u=s.document||{},d=s.Date,f=r(\"Events\"),l=r(\"Events\",{producerId:\"csa\"});function p(e){try{var n=u.cookie.match(RegExp(\"(^| )\"+e+\"=([^;]+)\"));return n&&n[2].trim()}catch(e){}}!function(){var e=function(){var e=p(\"cdn-rid\");if(e)return{r:e,s:\"cdn\"}}()||function(){if(r.store(o+a))return{r:r.UUID().toUpperCase().replace(/-/g,\"\").slice(0,20),s:\"device\"}}()||{},n=e.r,c=e.s;if(!!n){var t=p(\"session-id\");!function(e,n,c,t){f(\"setEntity\",{page:{pageSource:\"cache\",requestId:e,cacheRequestId:a,cacheSource:t},session:{id:c}})}(n,0,t,c),\"device\"===c&&l(\"log\",{schemaId:\"<ns>.CacheImpression.1\"},{ent:\"all\"}),i&&i(n,t,c)}}(),n(o+a,d.now()+36e5),r.once(\"$load\",function(){var c=d.now();t(function(e,n){return 0==e.indexOf(o)&&parseInt(n)<c})})});csa.plugin(function(u){var i,t=\"Content\",e=\"MutationObserver\",n=\"addedNodes\",a=\"querySelectorAll\",f=\"matches\",o=\"getAttributeNames\",r=\"getAttribute\",s=\"dataset\",c=\"widget\",l=\"producerId\",d=\"slotId\",h=\"iSlotId\",g={ent:{element:1,page:[\"pageType\",\"subPageType\",\"requestId\"]}},p=5,m=u.config[t+\".BubbleUp.SearchDepth\"]||35,y=u.config[t+\".SearchPage\"]||0,v=\"csaC\",b=v+\"Id\",E=\"logRender\",w={},I=u.config,O=I[t+\".Selectors\"]||[],C=I[t+\".WhitelistedAttributes\"]||{href:1,class:1},N=I[t+\".EnableContentEntities\"],S=I[\"KillSwitch.ContentRendered\"],k=u.global,A=k.document||{},U=A.documentElement,L=k.HTMLElement,R={},_=[],j=function(t,e,n,i){var r=this,o=u(\"Events\",{producerId:t||\"csa\"});e.type=e.type||c,r.id=e.id,r.l=o,r.e=e,r.el=n,r.rt=i,r.dlo=g,r.op=W(n,\"csaOp\"),r.log=function(t,e){o(\"log\",t,e||g)},e.id&&o(\"setEntity\",{element:e})},x=j.prototype;function D(t){var e=(t=t||{}).element,n=t.target;return e?function(t,e){var n;n=t instanceof L?K(t)||Y(e[l],t,z,u.time()):R[t.id]||H(e[l],0,t,u.time());return n}(e,t):n?M(n):u.error(\"No element or target argument provided.\")}function M(t){var e=function(t){var e=null,n=0;for(;t&&n<m;){if(n++,P(t,b)){e=t;break}t=t.parentElement}return e}(t);return e?K(e):new j(\"csa\",{id:null},null,u.time())}function P(t,e){if(t&&t.dataset)return t.dataset[e]}function T(t,e,n){_.push({n:n,e:t,t:e}),B()}function q(){for(var t=u.time(),e=0;0<_.length;){var n=_.shift();if(w[n.n](n.e,n.t),++e%10==0&&u.time()-t>p)break}i=0,_.length&&B()}function B(){i=i||u.raf(q)}function X(t,e,n){return{n:t,e:e,t:n}}function Y(t,e,n,i){var r=u.UUID(),o={id:r},c=M(e);return e[s][b]=r,n(o,e),c&&c.id&&(o.parentId=c.id),H(t,e,o,i)}function $(t){return isNaN(t)?null:Math.round(t)}function H(t,e,n,i){N&&(n.schemaId=\"<ns>.ContentEntity.2\"),n.id=n.id||u.UUID();var r=new j(t,n,e,i);return function(t){return!S&&((t.op||{}).hasOwnProperty(E)||y)}(r)&&function(t,e){var n={},i=u.exec($);t.el&&(n=t.el.getBoundingClientRect()),t.log({schemaId:\"<ns>.ContentRender.2\",timestamp:e,width:i(n.width),height:i(n.height),positionX:i(n.left+k.pageXOffset),positionY:i(n.top+k.pageYOffset)})}(r,i),u.emit(\"$content.register\",r),R[n.id]=r}function K(t){return R[(t[s]||{})[b]]}function W(n,i){var r={};return o in(n=n||{})&&Object.keys(n[s]).forEach(function(t){if(!t.indexOf(i)&&i.length<t.length){var e=function(t){return(t[0]||\"\").toLowerCase()+t.slice(1)}(t.slice(i.length));r[e]=n[s][t]}}),r}function z(t,e){o in e&&(function(t,e){var n=W(t,v);Object.keys(n).forEach(function(t){e[t]=n[t]})}(e,t),d in t&&(t[h]=t[d]),function(e,n){(e[o]()||[]).forEach(function(t){t in C&&(n[t]=e[r](t))})}(e,t))}U&&A[a]&&k[e]&&(O.push({selector:\"*[data-csa-c-type]\",entity:z}),O.push({selector:\".celwidget\",entity:function(t,e){z(t,e),t[d]=t[d]||e[r](\"cel_widget_id\")||e.id,t.legacyId=e[r](\"cel_widget_id\")||e.id,t.type=t.type||c}}),w[1]=function(t,e){t.forEach(function(t){t[n]&&t[n].constructor&&\"NodeList\"===t[n].constructor.name&&Array.prototype.forEach.call(t[n],function(t){_.unshift(X(2,t,e))})})},w[2]=function(o,c){a in o&&f in o&&O.forEach(function(t){for(var e=t.selector,n=o[f](e),i=o[a](e),r=i.length-1;0<=r;r--)_.unshift(X(3,{e:i[r],s:t},c));n&&_.unshift(X(3,{e:o,s:t},c))})},w[3]=function(t,e){var n=t.e;K(n)||Y(\"csa\",n,t.s.entity,e)},w[4]=function(){u.register(t,{instance:D})},new k[e](function(t){T(t,u.time(),1)}).observe(U,{childList:!0,subtree:!0}),T(U,u.time(),2),T(null,u.time(),4),u.on(\"$content.export\",function(e){Object.keys(e).forEach(function(t){x[t]=e[t]})}))});csa.plugin(function(o){var i,t=\"ContentImpressions\",e=\"KillSwitch.\",n=\"IntersectionObserver\",r=\"getAttribute\",s=\"dataset\",c=\"intersectionRatio\",a=\"csaCId\",m=1e3,l=o.global,f=o.config,u=f[e+t],v=f[e+t+\".ContentViews\"],g=((l.performance||{}).timing||{}).navigationStart||o.time(),d={};function h(t){t&&(t.v=1,function(t){t.vt=o.time(),t.el.log({schemaId:\"<ns>.ContentView.3\",timeToViewed:t.vt-t.el.rt,pageFirstPaintToElementViewed:t.vt-g})}(t))}function I(t){t&&!t.it&&(t.i=o.time()-t.is>m,function(t){t.it=o.time(),t.el.log({schemaId:\"<ns>.ContentImpressed.2\",timeToImpressed:t.it-t.el.rt,pageFirstPaintToElementImpressed:t.it-g})}(t))}!u&&l[n]&&(i=new l[n](function(t){var n=o.time();t.forEach(function(t){var e=function(t){if(t&&t[r])return d[t[s][a]]}(t.target);if(e){o.emit(\"$content.intersection\",{meta:e.el,t:n,e:t});var i=t.intersectionRect;t.isIntersecting&&0<i.width&&0<i.height&&(v||e.v||h(e),.5<=t[c]&&!e.is&&(e.is=n,e.timer=o.timeout(function(){I(e)},m))),t[c]<.5&&!e.it&&e.timer&&(l.clearTimeout(e.timer),e.is=0,e.timer=0)}})},{threshold:[0,.5,.99]}),o.on(\"$content.register\",function(t){var e=t.el;e&&(d[t.id]={el:t,v:0,i:0,is:0,vt:0,it:0},i.observe(e))}))});csa.plugin(function(e){e.config[\"KillSwitch.ContentLatency\"]||e.emit(\"$content.export\",{mark:function(t,n){var o=this;o.t||(o.t=e(\"Timers\",{logger:o.l,schemaId:\"<ns>.ContentLatency.1\",logOptions:o.dlo})),o.t(\"mark\",t,n)}})});csa.plugin(function(t){function n(i,e,o){var c={};function r(t,n,e){t in c&&o<=n-c[t].s&&(function(n,e,i){if(!p)return;E(function(t){T(n,t),t.w[n][e]=a((t.w[n][e]||0)+i)})}(t,i,n-c[t].d),c[t].d=n),e||delete c[t]}this.update=function(t,n){n.isIntersecting&&e<=n.intersectionRatio?function(t,n){t in c||(c[t]={s:n,d:n})}(t,u()):r(t,u())},this.stopAll=function(t){var n=u();for(var e in c)r(e,n,t)},this.reset=function(){var t=u();for(var n in c)c[n].s=t,c[n].d=t}}var e=t.config,u=t.time,i=\"ContentInteractionsSummary\",o=e[i+\".FlushInterval\"]||5e3,c=e[i+\".FlushBackoff\"]||1.5,r=t.global,s=t.on,a=Math.floor,f=(r.document||{}).documentElement||{},l=((r.performance||{}).timing||{}).responseStart||t.time(),d=o,m=0,p=!0,v=t.UUID(),g=t(\"Events\",{producerId:\"csa\"}),w=new n(\"it0\",0,0),I=new n(\"it50\",.5,1e3),h=new n(\"it100\",.99,0),A={},b={};function $(){w.stopAll(!0),I.stopAll(!0),h.stopAll(!0),S()}function C(){w.reset(),I.reset(),h.reset(),S()}function S(){d&&(clearTimeout(m),m=t.timeout($,d),d*=c)}function U(n){E(function(t){T(n,t),t.w[n].mc=(t.w[n].mc||0)+1})}function E(t){g(\"log\",{messageId:v,schemaId:\"<ns>.ContentInteractionsSummary.1\",w:{},__merge:t},{ent:{page:[\"requestId\"]}})}function T(t,n){t in n.w||(n.w[t]={})}e[\"KillSwitch.\"+i]||(s(\"$content.intersection\",function(t){if(t&&t.meta&&t.e){var n=t.meta.id;if(n in A){var e=t.e.boundingClientRect||{};e.width<5||e.height<5||(w.update(n,t.e),I.update(n,t.e),h.update(n,t.e),!t.e.isIntersecting||n in b||(b[n]=1,function(n,e){E(function(t){T(n,t),t.w[n].ttfv=a(e)})}(n,u()-l)))}}}),s(\"$content.register\",function(t){(t.e||{}).slotId&&(A[t.id]={},function(e){E(function(t){var n=e.id;T(n,t),t.w[n].sid=(e.e||{}).slotId,t.w[n].cid=(e.e||{}).contentId})}(t))}),s(\"$beforePageTransition\",function(){$(),C(),v=t.UUID(),S()}),s(\"$beforeunload\",function(){w.stopAll(),I.stopAll(),h.stopAll(),d=null}),s(\"$visible\",function(t){t?C():($(),clearTimeout(m)),p=t},{buffered:1}),s(f,\"click\",function(t){for(var n=t.target,e=25;n&&0<e;){var i=(n.dataset||{}).csaCId;i&&U(i),n=n.parentElement,e-=1}},{capture:!0,passive:!0}),S())});csa.plugin(function(o){var t,n,i=\"normal\",s=\"reload\",e=\"history\",d=\"new-tab\",a=\"ajax\",r=1,c=2,u=\"lastActive\",l=\"lastInteraction\",f=\"used\",p=\"csa-tabbed-browsing\",g=\"visibilityState\",v={\"back-memory-cache\":1,\"tab-switch\":1,\"history-navigation-page-cache\":1},b=\"<ns>.TabbedBrowsing.2\",m=\"visible\",y=o.global,I=o(\"Events\",{producerId:\"csa\"}),h=y.location||{},T=y.document,w=y.JSON,z=((y.performance||{}).navigation||{}).type,P=o.store,S=o.on,k=o.storageSupport(),x=!1,A={},C={},O={},$={},j=!1,q=!1,B=!1;function E(i){try{return w.parse(P(p,void 0,{session:i})||\"{}\")||{}}catch(i){o.error('Could not parse storage value for key \"'+p+'\": '+i)}return{}}function J(i,t){P(p,w.stringify(t||{}),{session:i})}function N(i){var t=C.tid||i.id,n=A[u]||{};n.tid===t&&(n.pid=i.id),$={pid:i.id,tid:t,lastInteraction:C[l]||{},initialized:!0},O={lastActive:n,lastInteraction:A[l]||{},time:o.time()}}function D(i){var t=i===d,n=T.referrer,e=!(n&&n.length)||!~n.indexOf(h.origin||\"\"),a=t&&e,r={type:i,toTabId:$.tid,toPageId:$.pid,transitTime:o.time()-A.time||null};a||function(i,t,n){var e=i===s,a=t?A[u]||{}:C,r=A[l]||{},o=C[l]||{},d=t?r:o;n.fromTabId=a.tid,n.fromPageId=a.pid,e||!d.id||d[f]||(n.interactionId=d.id||null,r.id===d.id&&(r[f]=!0),o.id===d.id&&(o[f]=!0))}(i,t,r),I(\"log\",{navigation:r,schemaId:b},{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}})}function F(i){B=function(i){return i&&i in v}(i.transitionType),function(){A=E(!1),C=E(!0);var i=A[l],t=C[l],n=!1,e=!1;i&&t&&i.id===t.id&&i[f]!==t[f]&&(n=!i[f],e=!t[f],t[f]=i[f]=!0,n&&J(!1,A),e&&J(!0,C))}(),N(i),j=!0,function(i){var t,n;t=H(),n=K(),(t||n)&&N(i)}(i)}function G(){x&&!B?D(a):(x=!0,D(z===c||B?e:z===r?C.initialized?s:d:C.initialized?i:d))}function H(){return!(!j||!t)&&(C[l]={id:t.messageId,used:!(A[l]={id:t.messageId,used:!1})},!(t=null))}function K(){var i=!1;if(q=T[g]===m,j){var t=A[u]||{};i=function(i,t,n){var e=!1,a=i[u];return q?a&&a.tid===$.tid&&a[m]&&a.pid===n||(i[u]={visible:!0,pid:n,tid:t},e=!0):a&&a.tid===$.tid&&a[m]&&(e=!(a[m]=!1)),e}(A,C.tid||t.tid||$.tid,C.pid||t.pid||$.pid)}return i}k.local&&k.session&&w&&T&&g in T&&(n=function(){try{return y.self!==y.top}catch(i){return!0}}(),S(\"$pageChange\",function(i){n||(F(i),G(),J(!1,O),J(!0,$),C=$,A=O)},{buffered:1}),S(\"$content.interaction\",function(i){t=i,H()&&(J(!1,A),J(!0,C))}),S(T,\"visibilitychange\",function(){!n&&K()&&J(!1,A)},{capture:!1,passive:!0}))});csa.plugin(function(c){var e=c(\"Metrics\",{producerId:\"csa\"});c.on(c.global,\"pageshow\",function(c){c&&c.persisted&&e(\"recordMetric\",\"bfCache\",1)})});csa.plugin(function(n){var e,t,i,o,r,a,c,u,f,s,l,d,m,p,g,v,h=\"hasFocus\",b=\"$app.\",y=\"avail\",S=\"client\",T=\"document\",$=\"inner\",I=\"offset\",P=\"screen\",w=\"scroll\",D=\"Width\",E=\"Height\",F=y+D,O=y+E,q=S+D,x=S+E,z=$+D,C=$+E,H=I+D,K=I+E,M=w+D,W=w+E,X=n.config[\"KillSwitch.PageInteractionsSummary\"],Y=n(\"Events\",{producerId:\"csa\"}),j=1,k=n.global||{},A=n.time,B=n.on,G=n.once,J=k[T]||{},L=k[P]||{},N=k.Math||{},Q=N.abs,R=N.max,U=N.ceil,V=((k.performance||{}).timing||{}).responseStart,Z=function(){return J[h]()},_=1,nn=100,en={},tn=1;function on(){c=t=o=r=e,i=0,a=u=f=s=0,cn(),an()}function rn(){V&&!o&&(c=U((o=l)-V),tn=1)}function an(){u=U(R(u,m+v)),d&&(f=U(R(f,d+g))),tn=1}function cn(){l=A(),d=Q(k.pageXOffset||0),m=Q(k.pageYOffset||0),p=0<d||0<m,g=k[z]||0,v=k[C]||0}function un(){cn(),rn(),function(){var n=m-i;t&&!(t&&t<=l)||(n&&(++a,tn=1),i=m,n),t=l+nn}(),an()}function fn(){if(r){var n=U(A()-r);s+=n,r=e,tn=0<n}}function sn(){r=r||A()}function ln(n,e,t,i){e[n+D]=U(t||0),e[n+E]=U(i||0)}function dn(n){var e=n===en,t=Z();if(t||tn){if(!e){if(!j)return;j=0,t&&fn()}var i=function(){var n={},e=J.documentElement||{},t=J.body||{};return ln(\"availableScreen\",n,L[F],L[O]),ln(T,n,R(t[M]||0,t[H]||0,e[q]||0,e[M]||0,e[H]||0),R(t[W]||0,t[K]||0,e[x]||0,e[W]||0,e[K]||0)),ln(P,n,L.width,L.height),ln(\"viewport\",n,k[z],k[C]),n}(),o=function(){var n={scrollCounts:a,reachedDepth:u,horizontalScrollDistance:f,dwellTime:s};return\"number\"==typeof c&&(n.clientTimeToFirstScroll=c),n}();e?tn=0:(on(),V=A(),t&&(r=V)),Y(\"log\",{activity:o,dimensions:i,schemaId:\"<ns>.PageInteractionsSummary.1\"},{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}})}}function mn(){fn(),dn(en)}function pn(n,e){return function(){_=e,n()}}function gn(){Z=function(){return _},_&&!r&&(r=A())}\"function\"!=typeof J[h]||X||(on(),p&&rn(),B(k,w,un,{passive:!0}),B(k,\"blur\",mn),B(k,\"focus\",pn(sn,1)),G(b+\"android\",gn),G(b+\"ios\",gn),B(b+\"pause\",pn(mn,0)),B(b+\"resume\",pn(sn,1)),B(b+\"resign\",pn(mn,0)),B(b+\"active\",pn(sn,1)),Z()&&(r=V||A()),G(\"$beforeunload\",dn),B(\"$beforeunload\",dn),B(\"$document.hidden\",mn),B(\"$beforePageTransition\",dn),B(\"$afterPageTransition\",function(){tn=j=1}))});csa.plugin(function(e){var o,n,r=\"<ns>.Navigator.4\",a=e.global,i=a.navigator||{},d=i.connection||{},c=a.Math.round,t=e(\"Events\",{producerId:\"csa\"});function l(){o={network:{downlink:void 0,downlinkMax:void 0,rtt:void 0,type:void 0,effectiveType:void 0,saveData:void 0},language:void 0,doNotTrack:void 0,hardwareConcurrency:void 0,deviceMemory:void 0,cookieEnabled:void 0,webdriver:void 0},v(),o.language=i.language||null,o.doNotTrack=function(){switch(i.doNotTrack){case\"1\":return\"enabled\";case\"0\":return\"disabled\";case\"unspecified\":return i.doNotTrack;default:return null}}(),o.hardwareConcurrency=\"hardwareConcurrency\"in i?c(i.hardwareConcurrency||0):null,o.deviceMemory=\"deviceMemory\"in i?c(i.deviceMemory||0):null,o.cookieEnabled=\"cookieEnabled\"in i?i.cookieEnabled:null,o.webdriver=\"webdriver\"in i?i.webdriver:null}function u(){t(\"log\",{network:(n={},Object.keys(o.network).forEach(function(e){n[e]=o.network[e]+\"\"}),n),language:o.language,doNotTrack:o.doNotTrack,hardwareConcurrency:o.hardwareConcurrency,deviceMemory:o.deviceMemory,cookieEnabled:o.cookieEnabled,webdriver:o.webdriver,schemaId:r},{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}})}function v(){!function(n){Object.keys(o.network).forEach(function(e){o.network[e]=n[e]})}({downlink:\"downlink\"in d?c(d.downlink||0):null,downlinkMax:\"downlinkMax\"in d?c(d.downlinkMax||0):null,rtt:\"rtt\"in d?(d.rtt||0).toFixed():null,type:d.type||null,effectiveType:d.effectiveType||null,saveData:\"saveData\"in d?d.saveData:null})}function k(){v(),u()}function w(){l(),u()}l(),u(),e.on(\"$afterPageTransition\",w),e.on(d,\"change\",k)});\n(function(t,z,C){var u=function(){var a,c=function(){return null!=a?a:a=function(){var a=[],c=\"unknown\",b={trident:0,gecko:0,presto:0,webkit:0,unknown:-1},d,e={},c=document.createElement(\"nadu\");for(d in c.style)if(c=(/^([A-Za-z][a-z]*)[A-Z]/.exec(d)||[])[1])c=c.toLowerCase(),c in e?e[c]++:e[c]=1;for(var n in e)a.push([n,e[n]]);a=a.sort(function(a,c){return c[1]-a[1]}).slice(0,10);for(d=0;d<a.length;d++)switch(a[d][0]){case \"moz\":b.gecko+=5;break;case \"ms\":b.trident+=5;break;case \"get\":b.webkit++;\nbreak;case \"webkit\":b.webkit+=5;break;case \"o\":b.presto+=2;break;case \"xv\":b.presto+=2}\"onhelp\"in window&&b.trident++;\"maxConnectionsPerServer\"in window&&b.trident++;try{void 0!==window.ActiveXObject.toString&&(b.trident+=5)}catch(r){}void 0!==function(){}.toSource&&(b.gecko+=5);var a=\"unknown\",q;for(q in b)b[q]>b[a]&&(a=q);return a}()},b=function(){return!!window.opera||!!window.opr&&!!window.opr.addons},d=function(){return!!document.documentMode},e=function(){return!d()&&\"undefined\"!==typeof CSS&&\nCSS.supports(\"(-ms-ime-align:auto)\")},n=function(){return\"webkit\"==c()},r=function(){return void 0!==z.chrome&&\"Opera Software ASA\"!=navigator.vendor&&void 0===navigator.msLaunchUri&&n()};return{isOpera:b,isIE:d,isEdge:e,isFirefox:function(){return\"undefined\"!==typeof InstallTrigger},isWebkit:n,isChrome:r,isSafari:function(){return!r()&&!e()&&!b()&&\"WebkitAppearance\"in document.documentElement.style}}}(),q=function(a,c,b,d){a.addEventListener?a.addEventListener(c,b,d):a.attachEvent&&a.attachEvent(\"on\"+\nc,b)},r=function(a,c,b,d){document.removeEventListener?a.removeEventListener(c,b,d||!1):document.detachEvent&&a.detachEvent(\"on\"+c,b)},H=function(a){var c;a=a.document;\"undefined\"!==typeof a.hidden?c=\"visibilitychange\":\"undefined\"!==typeof a.mozHidden?c=\"mozvisibilitychange\":\"undefined\"!==typeof a.msHidden?c=\"msvisibilitychange\":\"undefined\"!==typeof a.webkitHidden&&(c=\"webkitvisibilitychange\");return c},T=function(a,c){var b=H(a),d=a.document;b&&q(d,b,c,!1)},U=function(a){var c=window.addEventListener?\n\"addEventListener\":\"attachEvent\";(0,window[c])(\"attachEvent\"==c?\"onmessage\":\"message\",function(c){a(c[c.message?\"message\":\"data\"])},!1)},V=function(a,c){\"function\"===typeof a&&Math.random()<c/100&&a.call(this)},v=function(a){try{for(var c=Array.prototype.slice.call(arguments,1),b=0;b<c.length;b++){if(!a)return!0;var d=a[c[b]];if(null===d||void 0===d)return!0;a=d}return!1}catch(e){return!0}},A=function(a){try{if(!a)return a;for(var c=Array.prototype.slice.call(arguments,1),b,d=0;d<c.length;d++){b=\na[c[d]];if(!b)break;a=b}return b}catch(e){return null}},W=function(a,c){try{if(!a)return!1;for(var b=Array.prototype.slice.call(arguments,2),d=0;d<b.length;d++){var e=a[b[d]];if(null===e||void 0===e)return d===b.length-1?typeof e===c:!1;a=e}return typeof e===c}catch(n){return!1}},I=function(a){a&&document.body&&a.parentNode===document.body&&document.body.removeChild(a)},J=function(a,c,b){var d=window.document.createElement(\"IFRAME\");d.id=c;d.height=\"5px\";d.width=\"5px\";d.src=b?b:\"javascript:'1'\";d.style.position=\n\"absolute\";d.style.top=\"-10000px\";d.style.left=\"-10000px\";d.style.visibility=\"hidden\";d.style.opacity=0;window.document.body.appendChild(d);try{var e=d.contentDocument;if(e&&(e.open(),e.writeln(\"<!DOCTYPE html><html><head><title></title></head><body></body></html>\"),e.close(),a)){var r=e.createElement(\"script\");r&&(r.type=\"text/javascript\",r.text=a,e.body.appendChild(r))}}catch(q){n(q,\"JS exception while injecting iframe\")}return d},n=function(a,c){t.ueLogError(a,{logLevel:\"ERROR\",attribution:\"A9TQForensics\",\nmessage:c})},X=function(a,c,b){a={vfrd:1===c?\"8\":\"4\",dbg:a};\"object\"===typeof b?a.info=JSON.stringify(b):\"string\"===typeof b&&(a.info=b);return{server:window.location.hostname,fmp:a}};(function(a){function c(a,c,b){var d=\"chrm msie ffox sfri opra phnt slnm othr extr xpcm invs poev njs cjs rhn clik1 rfs uam clik stln mua nfo hlpx clkh mmh chrm1 chrm2 wgl srvr zdim nomime chrm3 otch ivm.tst ivm.clk mmh2 clkh2 achf nopl spfp4 uam1 lsph nmim1 slnm2 crtt spfp misp spfp1 spfp2 clik2 clik3 spfp3 estr\".split(\" \");\nF=a<d.length?d[a]:\"othr\";t.ue&&t.ue.event&&t.ue.event(X(F,c,b),\"a9_tq\",\"a9_tq.FraudMetrics.3\")}function b(){var c=!1,m=\"\",b=6,d=function(a,c){var f,m,b=!1;for(f in a)b=b||-1<c.indexOf(f);if(\"function\"===typeof Object.getOwnPropertyNames)for(f=Object.getOwnPropertyNames(a),m=0;m<f.length;m++)b=b||-1<c.indexOf(f[m]);if(\"function\"===typeof Object.keys)for(f=Object.keys(a),m=0;m<f.length;m++)b=b||-1<c.indexOf(f[m]);return b},k=function(a){try{return!!a.toString().match(/\\{\\s*\\[native code\\]\\s*\\}$/m)}catch(c){return!1}},\nl=0;\"undefined\"!==typeof _evaluate&&-1<_evaluate.toString().indexOf(\"browser.runScript\")&&l++;\"undefined\"!==typeof ArrayBuffer&&\"undefined\"!==typeof print&&k(ArrayBuffer)&&!k(print)&&l++;\"undefined\"!==typeof ABORT_ERR&&l++;try{\"undefined\"!==typeof browser&&\"undefined\"!==typeof browser._windowInScope&&\"undefined\"!==typeof browser._windowInScope._response&&l++}catch(Z){}3<=l&&(c=!0);k=[function(){if(!0===d(C,\"__webdriver_evaluate __selenium_evaluate __fxdriver_evaluate __driver_evaluate __webdriver_unwrapped __selenium_unwrapped __fxdriver_unwrapped __driver_unwrapped __webdriver_script_function __webdriver_script_func __webdriver_script_fn webdriver _Selenium_IDE_Recorder _selenium calledSelenium $cdc_asdjflasutopfhvcZLmcfl_ $chrome_asyncScriptInfo __$webdriverAsyncExecutor\".split(\" \")))return!0;\nvar c=function(c){return c.match(/\\$[a-z]dc_/)&&a.document[c]&&a.document[c].cache_},f;for(f in C)if(c(f))return m=f,!0;if(\"function\"===typeof Object.getOwnPropertyNames)for(var b=Object.getOwnPropertyNames(C),l=0;l<b.length;l++)if(c(b[l]))return m=f,!0;return!1},function(){return d(a,\"_phantom __nightmare _selenium callPhantom callSelenium _Selenium_IDE_Recorder webdriver __webdriverFunc domAutomation domAutomationController __lastWatirAlert __lastWatirConfirm __lastWatirPrompt _WEBDRIVER_ELEM_CACHE\".split(\" \"))||\n\"function\"===typeof a.cdc_adoQpoasnfa76pfcZLmcfl_Promise||\"function\"===typeof a.cdc_adoQpoasnfa76pfcZLmcfl_Array||\"function\"===typeof a.cdc_adoQpoasnfa76pfcZLmcfl_Symbol?!0:!1},function(){return a.webdriver||a.document.webdriver||a.document.documentElement.hasAttribute(\"webdriver\")||a.document.documentElement.hasAttribute(\"selenium\")||a.document.documentElement.hasAttribute(\"driver\")||navigator.webdriver||A(p,\"navigator\",\"webdriver\")||\"object\"===typeof a.$cdc_asdjflasutopfhvcZLmcfl_||\"object\"===typeof a.document.$cdc_asdjflasutopfhvcZLmcfl_||\nnull!=a.name&&-1<a.name.indexOf(\"driver\")?(m=null!=a.name?a.name:\"\",!0):!1},function(){return a.external&&\"function\"===typeof a.external.toString&&a.external.toString()&&-1!=a.external.toString().indexOf(\"Sequentum\")?(m=a.external.toString(),!0):!1},function(){for(var c in a){var f;a:{if((f=c)&&33<=f.length&&\"function\"===typeof a[f]){var b=a[f];if(/\\.*_Array$/.test(f)||/\\.*_Promise$/.test(f)||/\\.*_Symbol$/.test(f)||34===f.length&&\"resolve\"in b&&\"reject\"in b&&\"prototype\"in b||33===f.length&&\"isConcatSpreadable\"in\nb&&\"unscopables\"in b&&\"toStringTag\"in b&&\"match\"in b){f=!0;break a}}f=!1}if(f)return m=c,!0}return!1},function(){var a=!1;if(!u.isFirefox())return!1;var c;c=navigator.userAgent.match(/(firefox(?=\\/))\\/?\\s*(\\d+)/i)||[];c=3<=c.length?c[2]:null;if(!c)return!1;60<=c&&void 0===navigator.webdriver?a=!0:60>c&&\"webdriver\"in navigator&&(a=!0);return a?(b=43,m=c.toString(),!0):!1}];for(l=0;l<k.length;l++)if(k[l].call()){c=!0;break}return{isSel:c,isTest:!1,info:m,headlessCode:b}}function d(a){var b=new Date;\n!v(a,\"Function\",\"prototype\",\"toString\")&&W(b,\"function\",\"toLocaleString\")&&(a=a.Function.prototype.toString.call(b.toLocaleString))&&100<a.length&&0<=a.indexOf(\"this.getTime\")&&c(41)}function e(){var a=\"AddChannel AddDesktopComponent AddFavorite AddSearchProvider AddService AddToFavoritesBar AutoCompleteSaveForm AutoScan bubbleEvent ContentDiscoveryReset ImportExportFavorites InPrivateFilteringEnabled IsSearchProviderInstalled IsServiceInstalled IsSubscribed msActiveXFilteringEnabled msAddSiteMode msAddTrackingProtectionList msClearTile msEnableTileNotificationQueue msEnableTileNotificationQueueForSquare150x150 msEnableTileNotificationQueueForSquare310x310 msEnableTileNotificationQueueForWide310x150 msIsSiteMode msIsSiteModeFirstRun msPinnedSiteState msProvisionNetworks msRemoveScheduledTileNotification msReportSafeUrl msScheduledTileNotification msSiteModeActivate msSiteModeAddButtonStyle msSiteModeAddJumpListItem msSiteModeAddThumbBarButton msSiteModeClearBadge msSiteModeClearIconOverlay msSiteModeClearJumpList msSiteModeCreateJumpList msSiteModeRefreshBadge msSiteModeSetIconOverlay msSiteModeShowButtonStyle msSiteModeShowJumpList msSiteModeShowThumbBar msSiteModeUpdateThumbBarButton msStartPeriodicBadgeUpdate msStartPeriodicTileUpdate msStartPeriodicTileUpdateBatch msStopPeriodicBadgeUpdate msStopPeriodicTileUpdate msTrackingProtectionEnabled NavigateAndFind raiseEvent setContextMenu ShowBrowserUI menuArguments onvisibilitychange scrollbar selectableContent version visibility mssitepinned AddUrlAuthentication CloseRegPopup FeatureEnabled GetJsonWebData GetRegValue Log LogShellErrorsOnly OpenPopup ReadFile RunGutsScript SaveRegInfo SetAppMaximizeTimeToRestart SetAppMinimizeTimeToRestart SetAutoStart SetAutoStartMinimized SetMaxMemory ShareEventFromBrowser ShowPopup ShowRadar WriteFile WriteRegValue summonWalrus\".split(\" \"),\nb=-1,d,h;\"Microsoft Internet Explorer\"===navigator.appName?(d=navigator.userAgent,h=/MSIE ([0-9]{1,}[\\.0-9]{0,})/,null!==h.exec(d)&&(b=parseFloat(RegExp.$1))):\"Netscape\"===navigator.appName&&(d=navigator.userAgent,h=/Trident\\/.*rv:([0-9]{1,}[\\.0-9]{0,})/,null!==h.exec(d)&&(b=parseFloat(RegExp.$1)));if(-1===b&&null===navigator.userAgent.match(/Windows Phone/)&&window.external){for(d=b=0;d<a.length;d++)if(\"unknown\"===typeof window.external[a[d]]){b++;break}0<b&&c(17)}}function z(){var f=a.navigator.userAgent;\nif(f&&!/8.0 Safari|iPhone|iPad/.test(f)){var b={clearInterval:42,clearTimeout:41,eval:33,alert:34,prompt:35,scroll:35},d={clearInterval:46,clearTimeout:45,eval:37,alert:38,prompt:39,scroll:39},h=0;if(/Chrome/.test(f))d=b;else if(b=/Firefox/.test(f),f=/Safari/.test(f),!b&&!f)return;if(Function.prototype&&Function.prototype.toString)for(var k in d)\"function\"===typeof window[k]&&(f=Function.prototype.toString.call(window[k]))&&f.length!==d[k]&&(h+=1);3<=h&&(6<=h?c(40,0,h.toString()):c(40,1,h.toString()))}}\nfunction S(){var a=Math.random().toString(36).substr(2),b=null;U(function(d){try{var h=d.split(\" \");if(null!==b&&h[0]===a&&0<h[1].length){var k=JSON.parse(h[1]);for(d=0;d<k.length;d++)1==d&&\"R29vZ2xlIFN3aWZ0U2hhZGVy\"==k[d]&&c(27)}}catch(l){}});b=J('(function fg45s() {                     var payload = [];                     var canvas = document.createElement(\"canvas\");                     var gl = canvas.getContext(\"webgl\") || canvas.getContext(\"experimental-webgl\") || canvas.getContext(\"moz-webgl\");                     if (gl != null) {                         var debugInfo = gl.getExtension(\"WEBGL_debug_renderer_info\");                         if (debugInfo != null) {                             payload.push(btoa(gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL)));                             payload.push(btoa(gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)));                             parent.postMessage(window.frameElement.id + \" \" + JSON.stringify(payload), parent.location.origin);                         }                     }                 }             )();',\na);window.setTimeout(function(){try{b&&document.body&&b.parentNode===document.body&&document.body.removeChild(b),b=null}catch(a){n(a,\"JS exception while removing iframe\")}},5E3)}function L(){function b(){r(a,\"mousemove\",e);r(a,\"click\",d)}function d(){try{c(23),b(),r(a.document,l,h)}catch(m){n(m,\"JS exception - detectClickDuringTabInactive\")}}function e(){try{k||(k=!0,c(24),b(),r(a.document,l,h))}catch(d){n(d,\"JS exception - detectMouseMovementsDuringTabInactive\")}}function h(){try{var c;\"undefined\"!==\ntypeof document.hidden?c=\"hidden\":\"undefined\"!==typeof document.mozHidden?c=\"mozHidden\":\"undefined\"!==typeof document.msHidden?c=\"msHidden\":\"undefined\"!==typeof document.webkitHidden&&(c=\"webkitHidden\");!0!==document[c]===!1?(q(a,\"mousemove\",e,!1),q(a,\"click\",d,!1)):b()}catch(l){n(l,\"JS exception - handleVisibilityChangeDuringTabInactive\")}}var k=!1,l=H(a);T(a,h)}var M=function(){var a=window.navigator,c=a.vendor,b=\"undefined\"!==typeof window.opr,d=-1<a.userAgent.indexOf(\"Edg\"),a=/Chrome/.test(a.userAgent);\nreturn/Google Inc\\./.test(c)&&a&&!b&&!d},F=null,N=function(a){var b=[],d=(new Date).getTime(),h=!1,k=!1,l,e,D=function(){r(a,\"mousemove\",s);r(a,\"click\",g)},s=function(a){try{var f=(new Date).getTime();if(!(100>f-d)){b.push({x:a.clientX,y:a.clientY});if(50<b.length&&(b.shift(),!(50>b.length))){var l=b[0].x,g=b[49].x,k=b[0].y,h=b[49].y;a=h-k;for(var e=l-g,l=k*g-l*h,g=a/e*-1,s=b[49].ts-b[0].ts,k=!0,h=0;h<b.length;h++)if(0!=a*b[h].x+e*b[h].y+l){k=!1;break}!0==k&&(s={grdt:g,tmsp:s},D(),c(19,0,s))}d=f}}catch(B){n(B,\n\"JS exception - recordHoverPosition\")}},g=function(a){try{var d=a.clientX,f=a.clientY,l={hcc:b.length,cx:d,cy:f};if(0===b.length)D(),c(18,0,l);else if(null!=d&&null!=f){var g;l.hpos=b;var k=b[b.length-1];g=Math.sqrt(Math.pow(d-k.x,2)+Math.pow(f-k.y,2));100<g&&(l.hcc=b.length,l.cx=d,l.cy=f,l.dhp=g,D(),c(15,0,l))}}catch(h){n(h,\"JS exception - checkClick\")}},B=function(c){try{l=c.clientX,e=c.clientY,h=!0,r(a,\"mouseup\",B)}catch(b){n(b,\"JS exception - checkMouseUp\")}},p=function(){try{k=!0,r(a,\"mousedown\",\np)}catch(c){n(c,\"JS exception - checkMouseDown\")}},t=function(b){try{h||k||c(49);var d=b.clientX-l,g=b.clientY-e;0<d&&1>d&&0<g&&1>g&&c(50,0,{xDiff:d,yDiff:g});r(a,\"click\",t)}catch(m){n(m,\"JS exception - checkFirstClick\")}};q(a,\"mousemove\",s,!1);q(a,\"mouseup\",B,!1);q(a,\"mousedown\",p,!1);q(a,\"click\",t,!1);q(a,\"click\",g,!1)},O=function(){if(u.isFirefox()){var a=0;void 0!==window.onstorage&&a++;var b=document.createElement(\"div\");b.style.wordSpacing=\"22%\";\"22%\"===b.style.wordSpacing&&a++;\"function\"===\ntypeof b.getAttributeNames&&a++;2<a&&(void 0===window.onabsolutedeviceorientation||void 0===navigator.permissions)&&c(37,0,a)}},w=function(){return null===navigator.userAgent.match(/(iPad|iPhone|iPod|android|webOS)/i)},G=function(a,b){var d=a&&a.navigator;!d||!w()||d.mimeTypes&&0!=d.mimeTypes.length||(x?c(b,0,\"chrm\"):u.isFirefox()&&c(b,0,\"ff\"))},R=function(){var a=function(a,c){for(var b,d=\"\",f={},e={},s=0,g=0;g<c.length;g++)e[c[g]]=String.fromCharCode(126-g);var s=0,m;for(m in a)-1<c.indexOf(m)&&\n(f[m]=1,s++);d=d+s+\"!\";if(\"function\"===typeof Object.getOwnPropertyNames){b=Object.getOwnPropertyNames(a);for(g=s=0;g<b.length;g++)-1<c.indexOf(b[g])&&(f[b[g]]=1,s++);d=d+s+\"!\"}if(\"function\"===typeof Object.keys){b=Object.keys(a);for(g=s=0;g<b.length;g++)-1<c.indexOf(b[g])&&(f[b[g]]=1,s++);d=d+s+\"!\"}if(\"prototype\"in Object&&\"hasOwnProperty\"in Object.prototype)for(m in f)Object.prototype.hasOwnProperty.call(f,m)&&(d+=e[m]);else for(m in f)d+=e[m];return encodeURIComponent(d)},c=document.createElement(\"nadu\"),\na={w:a(window,\"SVGFESpotLightElement XMLHttpRequestEventTarget onratechange StereoPannerNode dolphinInfo VTTCue globalStorage WebKitCSSRegionRule MozSmsFilter MediaController mozInnerScreenX onwebkitwillrevealleft DOMMatrix GeckoActiveXObject MediaQueryListEvent PhoneNumberService ServiceWorkerContainer yandex vc2hxtaq9c NavigatorDeviceStorage HTMLHtmlElement ScreenOrientation MSGesture mozCancelRequestAnimationFrame GetSVGDocument MediaSource webkitMediaStream DeviceMotionEvent webkitPostMessage doNotTrack WebKitMediaKeyError HTMLCollection InstallTrigger StorageObsolete CustomEvent orientation XMLHttpRequest Worker showModelessDialog EventSource onmouseleave SVGAnimatedPathData TouchList TextTrackCue onanimationend HTMLBodyElement fluid MSFrameUITab Generator SecurityPolicyViolationEvent ClientRectList SmartCardEvent CSSSupportsRule mmbrowser\".split(\" \")),\nc:a(c.style,\"XvPhonemes MozTextAlignLast webkitFilter MozPerspective msTextSizeAdjust OAnimationFillMode borderImageSource MozOSXFontSmoothing border-inline-start-color MozOsxFontSmoothing columns touchAction scroll-snap-coordinate webkitAnimationFillMode webkitLineSnap webkitGridAutoColumns animationDuration isolation overflowWrap offsetRotation webkitShapeOutside MozOpacity position justifySelf borderRight webkitMatchNearestMailBlockquoteColor msImeAlign parentRule MozColumnFill cssText borderRightStyle textOverflow webkitGridRow webkitBackgroundComposite length -moz-column-fill enableBackground flex-basis\".split(\" \"))};\nt.ue&&t.ue.event&&(a={vfrd:\"0\",info:JSON.stringify(a)},t.ue.event({server:window.location.hostname,fmp:a},\"a9_tq\",\"a9_tq.FraudMetrics.3\"))},P=function(){var b=function(a){try{return\"function\"!==typeof a||v(p,\"Function\",\"prototype\",\"toString\")?null:p.Function.prototype.toString.call(a)}catch(b){return null}},d=function(a,c){try{if(\"function\"!==typeof Object.getOwnPropertyDescriptor)return null;var d=Object.getPrototypeOf(a);if(!d)return null;var e=Object.getOwnPropertyDescriptor(d,c);return e?b(e.get):\nnull}catch(g){return null}},e=function(a){var b=[28,161,141];!v(a,\"getDetails\",\"a\")&&\"s\"===a.getDetails.a&&0<=b.indexOf(a.getDetails.l)&&c(45,0,k)},h=function(){(function(){if(\"function\"===typeof Object.getOwnPropertyNames&&w()){var a=Object.getOwnPropertyNames(navigator);a&&1<a.length&&c(47,0,a.length.toString())}})();0<navigator.hardwareConcurrency&&!v(p,\"navigator\",\"hardwareConcurrency\")&&p.navigator.hardwareConcurrency!==navigator.hardwareConcurrency&&c(48,0,p.navigator.hardwareConcurrency.toString());\n(function(){var b=[];if(!v(p,\"navigator\")){p===a&&(b.push(\"iwin\"),c(51,0,b));for(var d=\"platform vendor maxTouchPoints userAgent deviceMemory webdriver hardwareConcurrency appVersion mimeTypes plugins languages\".split(\" \"),f=0;f<d.length;f++){var e=d[f],g;if(\"object\"===typeof navigator[e]){g=navigator[e];var h=p.navigator[e],k=!1;g||h?(g&&h?g.length!==h.length?k=!0:0<g.length&&0<h.length&&\"string\"===typeof g[0]&&g[0]!==h[0]&&(k=!0):k=!0,g=k):g=!1}else g=navigator[e],h=p.navigator[e],g=g||h?g!==h?\n!0:!1:!1;g&&b.push(e)}0<b.length&&(0<=b.indexOf(\"webdriver\")?c(51,0,b):c(39,1,b))}})()},k=function(a){if(!a)return null;for(var c={},e=0,h=0,g=0;g<a.length;g++)for(var k=a[g].obj,n=a[g].props,r=0;r<n.length;r++){var p=n[r];c[p]={};var q=A(k,n[r]);if(null===q||void 0===q)h+=1,c[p].a=\"m\",c[p].l=0;else if(q=\"function\"===typeof q?b(q):d(k,p))q&&!/\\[native code\\]/.test(q)?(c[p].a=\"s\",e+=1):c[p].a=\"p\",c[p].l=q.length}return{sig:c,sCount:e,mCount:h}}([{obj:A(a,\"chrome\",\"app\"),props:[\"getDetails\",\"getIsInstalled\",\n\"runningState\"]},{obj:a.chrome,props:[\"csi\",\"loadTimes\",\"runtime\"]},{obj:navigator,props:\"platform vendor userAgent mimeTypes plugins webdriver languages\".split(\" \")}]);k&&(e(k.sig),x&&w()&&3<=k.mCount&&(6<=k.mCount?c(46,0,k):c(46,1,k)),h())},Q=function(){var b=a.Document&&a.Document.prototype.evaluate;b&&(a.Document.prototype.evaluate=function(){try{var d=Error(\"test error\"),e=d.stack&&d.stack.toString();e&&e.match(/(puppeteer|phantomjs|apply.xpath)/)&&c(52,0,e);a.Document.prototype.evaluate=b;return b.apply(this,\narguments)}catch(h){return n(h,\"JS exception while overidding evaluate\"),a.Document.prototype.evaluate=b,b.apply(this,arguments)}})};try{v(navigator,\"userAgent\")&&c(20);var x=M(),y,p;(a.callPhantom||a._phantom||a.PhantomEmitter||a.__phantomas||/PhantomJS/.test(navigator.userAgent))&&c(5);a.Buffer&&c(12);a.emit&&c(13);a.spawn&&c(14);(null!=a.domAutomation||null!=a.domAutomationController||null!=a._WEBDRIVER_ELEM_CACHE||/HeadlessChrome/.test(navigator.userAgent)||\"\"===navigator.languages)&&c(0);if(u.isChrome()&&\na.webkitRequestFileSystem)a.webkitRequestFileSystem(a.TEMPORARY,1,function(){},function(){c(0)});else if(u.isSafari()&&a.localStorage){try{a.localStorage.setItem(\"__nadu\",\"\")}catch($){c(3)}a.localStorage.removeItem(\"__nadu\")}G(a,30);u.isWebkit()&&x&&w()&&(void 0===a.chrome&&c(0),a.chrome&&a.chrome.app&&!1!==a.chrome.app.isInstalled&&void 0!==navigator.languages&&c(31));a.external&&\"function\"===typeof a.external.toString&&a.external.toString()&&-1<a.external.toString().indexOf(\"RuntimeObject\")&&c(8);\na.FirefoxInterfaces&&\"function\"===typeof a.FirefoxInterfaces&&a.FirefoxInterfaces(\"wdICoordinate\",\"wdIMouse\",\"wdIStatus\")&&c(2);a.XPCOMUtils&&c(9);(a.Components&&(a.Components.interfaces&&a.Components.interfaces.nsICommandProcessor||a.Components.wdICoordinate||a.Components.wdIMouse||a.Components.wdIStatus||a.Components.classes)||a.netscape&&a.netscape.security&&a.netscape.security.privilegemanager)&&c(8);a.isExternalUrlSafeForNavigation&&c(1);!a.opera||null===a.opera._browserjsran||0!==a.opera._browserjsran&&\n!1!==a.opera._browserjsran||c(4);a.screen&&(1>=a.screen.availHeight||1>=a.screen.availWidth||1>=a.screen.height||1>=a.screen.width||0>=a.screen.devicePixelRatio)&&c(10);var E=window.setInterval(function(){try{var a=b();a.isSel&&(c(a.headlessCode,!0===a.isTest?1:0,a.info),window.clearInterval(E),I(y))}catch(d){window.clearInterval(E),n(d,\"JS exception - detectSelenium\")}},1E3);window.setTimeout(function(){try{window.clearInterval(E),I(y)}catch(a){n(a,\"JS exception - clearInterval for detectSelenium\")}},\n1E4);var K=a.PointerEvent;a.PointerEvent=function(){c(11);if(void 0!==K){var a=Array.prototype.slice.call(arguments);return new K(a)}return null};e();N(a);L();S();0!==a.outerHeight&&0!==a.outerWidth||c(29);O();!w()||navigator.plugins&&0!=navigator.plugins.length||(x?c(38,0,\"chrm\"):u.isFirefox()&&c(38,0,\"ff\"));V(R,10);x&&w()&&a.chrome&&!a.chrome.csi&&!a.chrome.loadTimes&&c(25);z();y=J(null,Math.random().toString(36).substr(2));p=v(y,\"contentWindow\")?a:y.contentWindow;d(p);G(p,42);0===A(navigator,\"connection\",\n\"rtt\")&&c(44);P();Q()}catch(Y){n(Y,\"JS exception - \")}})(z)})(ue_csm,window,document);\n\n\n\nue.exec(function(d,c){function g(e,c){e&&ue.tag(e+c);return!!e}function n(){for(var e=RegExp(\"^https://(.*\\.(images|ssl-images|media)-amazon\\.com|\"+c.location.hostname+\")/images/\",\"i\"),d={},h=0,k=c.performance.getEntriesByType(\"resource\"),l=!1,b,a,m,f=0;f<k.length;f++)if(a=k[f],0<a.transferSize&&a.transferSize>=a.encodedBodySize&&(b=e.exec(String(a.name)))&&3===b.length){a:{b=a.serverTiming||[];for(a=0;a<b.length;a++)if(\"provider\"===b[a].name){b=b[a].description;break a}b=void 0}b&&(l||(l=g(b,\"_cdn_fr\")),\na=d[b]=(d[b]||0)+1,a>h&&(m=b,h=a))}g(m,\"_cdn_mp\")}d.ue&&\"function\"===typeof d.ue.tag&&c.performance&&c.location&&n()},\"cdnTagging\")(ue_csm,window);\n\n\n}\n!function(n){function r(r){var o=n([1,function(n){b(n).t[S(n)]=w(n)},2,function(n){m(n).t[S(n)]=w(n)},3,function(n){n.u=w(n)},4,function(n){var r=w(n),t=w(n),n=w(n);d(n)||(n[t]=r)},10,function(n){b(n).o.push(w(n))},12,function(n){for(var r=g(n);0<r--;)n.i.push(I(n))},29,function(n){n.u=!w(n)},42,function(){},43,function(n){for(var r=g(n);0<r--;)b(n).t.push(n.v.pop())},45,a(!0),44,a(!1),48,v(0,y),49,v(1,y),50,v(2,y),51,v(-1,y),52,v(0,_),53,v(1,_),54,v(2,_),55,v(-1,_),58,function(n){A(n,F(n))},59,l(!0),60,l(!1),64,function(n){var r=F(n),t=h(n,b(n).l);n.u=t,A(n,r)},65,function(n){var r=g(n),t=F(n),u=h(n,b(n).l);b(n).t[r]=u,A(n,t)}]),i=n([16,s(\"+\"),17,s(\"-\"),18,s(\"*\"),19,s(\"/\"),20,Math.pow,21,s(\"%\"),22,s(\"&\"),23,s(\"|\"),24,s(\"^\"),25,s(\"<<\"),26,s(\">>\"),27,s(\"&&\"),28,s(\"||\"),30,s(\">\"),32,s(\">=\"),31,s(\"<\"),33,s(\"<=\"),34,s(\"==\"),35,s(\"===\"),36,s(\"!=\"),37,s(\"!==\"),38,s(\" in \"),39,function(n,r){return\"__rx_cls\"in n?n.__rx_cls===r.__rx_ref:n instanceof r}]),t=n([10,C,11,null,14,!0,15,!1]),u=n([1,function(n){return n.u},17,g,18,function(n){return k(n,Int32Array,4)},19,function(n){return k(n,Float32Array,4)},12,I,13,function(n){return n.i[g(n)]},20,function(){return[]},21,function(n){for(var r=g(n),t=[];0<r--;)t.unshift(w(n));return t},22,function(){return{}},23,function(n){for(var r=g(n)/2,t={};0<r--;){var u=w(n);t[w(n)]=u}return t},32,function(n){return b(n).t[g(n)]},33,function(n){return m(n).t[g(n)]},48,function(n){var r=w(n),n=w(n);return d(n)?n:(\"function\"==typeof(r=n[r])&&(r._=n),r)},51,function(n){var r=w(n),t=0;return d(r)?r:function(){return{value:r[t],done:!(t++<r.length)}}},50,function(n){return b(n).o.pop()},52,function(n){return typeof w(n)}]);function f(n){for(;0<(r=n).h.length&&b(r).l<r.p.length;)c(S(n),n);var r}function c(n,r){var t,u;return n in i?(t=w(r),u=w(r),r.u=i[n](u,t)):n in o?o[n](r):V(\"e2:\"+n+\":\"+b(r).l),r.u}function e(n,r){return{m:n,l:n,t:[],o:[],A:r}}function n(n){for(var r={},t=0;t<n.length;t+=2)r[n[t]]=n[t+1];return r}function a(u){return function(n){var r=u?w(n):C,t=n.h.pop();n.u=t.A?t.t[0]:r,n.v=[],p(n,b(n).m)}}function v(u,o){return function(n){var r=w(n),t=u;for(-1===u&&(t=g(n));0<t--;)n.v.push(w(n));n.u=C,r&&o(r,n)}}function l(u){return function(n){var r=w(n),t=F(n);(u&&r||!r&&!u)&&A(n,t)}}function s(n){return Function(\"a\",\"b\",\"return a\"+n+\"b\")}function _(n,r){if(n.__rx_ref&&n.S===r){var t=e(n.__rx_ref,!0);t.t.push({__rx_cls:n.__rx_ref}),r.h.push(t),p(r,t.m)}else if(\"function\"==typeof n){r.v.reverse();t=Function.prototype.bind.apply(n,[null].concat(r.v.splice(0)));try{r.u=new t}catch(n){}}else V(\"e5:\"+n+\":\"+b(r).l)}function y(n,r){if(n.__rx_ref&&n.S===r){var t=e(n.__rx_ref);t.t.push(n._||this),r.h.push(t),p(r,t.m)}else if(\"function\"==typeof n){r.v.reverse();try{r.u=n.apply(n._||this,r.v.splice(0))}catch(n){}}else V(\"e4:\"+n)}function w(n){var r=S(n);return 0<(128&r)?c(127&r,n):r in t?t[r]:r in u?u[r](n):void V(\"e3:\"+r)}function h(t,u){var n=P(function(){var n=e(u),r=n.t;return r.push(this),r.push.apply(r,arguments),t.h.push(n),p(t,n.m),f(t),t.u});return n.__rx_ref=u,n.S=t,n}function d(n){return(n===C||null===n)&&(r&&V(\"e10\"+n),1)}function p(n,r){n.k=r%127+37}function b(n){return n.h[n.h.length-1]}function m(n){return n.h[0]}function x(n){return b(n).l}function A(n,r){b(n).l+=r}function S(n){return n.p[b(n).l++]^n.k}function k(n,r,t){for(var u=x(n),o=n.p.slice(u,u+t),i=0;i<o.length;i++)o[i]=o[i]^n.k;u=new r(new Uint8Array(o).buffer);return A(n,t),u[0]}function F(n){return k(n,Int16Array,2)}function g(n){for(var r,t=0,u=0,o=x(n);t|=(127&(r=n.p[o+u]^n.k))<<7*u,u+=1,0<(128&r););return A(n,u),t}function I(n){for(var r=g(n),t=\"\";0<r--;)t+=String.fromCharCode(S(n));return t}function P(n){return function(){try{return n.apply(this,arguments)}catch(n){V(n)}}}function V(n){if(r)throw Error(n)}this.execute=P(function(n,r){var t,u;return 82!==n[0]&&88!==n[1]?V(\"e1\"):(n=n,t=3,(u=e(0)).t[0]=(r=r)||{},u.l=t,p(r={p:n,u:0,h:[u],v:[],i:[],k:0},0),f(t=r),t)})}var C;(n=\"undefined\"==typeof window?n:window).RXVM=r,n.$RX=new r}(window);\n!function(n){for(var o=\"undefined\"==typeof window?n:window,r=0,i=\"sendBeacon\",n=\"addEventListener\",f=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\".split(\"\"),u=[],t=o.rx||{},e=o.navigator||{},c=t.c||{},d=c.rxp||\"/rd/uedata\",a=c.fi||5e3,w={},s={},v=[],x=0,h=0;h<64;h++)u[f[h]]=h;function l(n,o){return function(){try{return n.apply(this,arguments)}catch(n){y(n.message||n,n)}}}function y(n,o){n=(\"\"+(n||\"\")).substring(0,100),v.push(r),v.push(n.length);for(var i=0;i<n.length;i++)v.push(n.charCodeAt(i));if(c.DEBUG)throw o||n;b()}function g(n,o){n in w||(w[n]=o,(s[n]||[]).forEach(function(n){n(o)}))}function m(n){for(var o=0,i=0,r=\"\",t=0;t<n.length;t+=1)for(i+=8,o=o<<8|n[t];6<=i;)r+=f[o>>i-6],o&=255>>8-(i-=6);return 0<i&&(r+=f[o<<6-i]),r}function p(n){for(var o=0,i=0,r=[],t=0;t<n.length&&\"=\"!==n[t];t+=1)for(i+=6,o=o<<6|u[n[t]];8<=i;)r.push(o>>i-8),o&=255>>8-(i-=8);return r}function b(){x||(setTimeout(l(A),a),x=1)}function A(){x=0,rx.ep&&rx.ep(v,B),v=[]}function B(n){n=m(new Uint8Array(n));n=d+\"?rid=\"+rx.rid+\"&sid=\"+rx.sid+\"&rx=\"+n;i in e?e[i](n):(new Image).src=n}function E(n){g(\"unload\",n),A()}(o.rx=t).err=y,t.r=l(function(n,o){o=l(o),n in s||(s[n]=[]),s[n].push(o),n in w&&o()}),t.e=l(g),t.exec=l,t.p=l(function(n,o){v.push(255&n),v=v.concat(o),b()}),t.ex64=l(function(n){n=p(n),o.$RX&&$RX.execute(n,o)}),t.e64=l(m),t.d64=l(p),n in o&&(o[n](\"load\",l(function(n){g(\"load\",n)})),o[n](\"beforeunload\",l(E)),o[n](\"pagehide\",l(E)))}(window);\nrx.ex64(\"UlgBKTUnV10vcExLUR1kV1dEXCNJQEtCUU0hUU1ASy9KS0ZKSFVJQFFALUZESUlHREZOJlZAUSNWUEdRSUAiQEtGV1xVUSFLREhAImRgdghmZ2YjQUxCQFZRInZtZAgXEBMhQF1ARiZXTEEmVkxBJCQVKCUFJSQnuTMVKSRGBSQkJrkVKSNGV1xVUUoFJRUpLUhWZldcVVFKBSVkI2MlbkRwdUhEZEV1SEdlRERHRERGVEV52nVIR2VEZUZhRXR1SU8mLSQ3BiohIAQxZURlRk9EQXdlRmVHREbVVERlRn+UumhlR2QiYiWkjbMpr46CjIKPvr+CjK+Or42ijrWgj7Mpr46Ci6iPi6+Ngoqvjs+cjxMICTQ8GTkJNT9LXEpMVU0ZORWFjou9gouvjqKOo2QtniVwWWfHxnpfxnpeWlt3a2tXWDU0LGtXXx86Lz56W0hKs1xaUVpqa1dePTc0NClrV18WOi8zeltpR1prV1k8L2tWW3pbWlhabmtWWnpby2tWWXtaSl9aX1pua1dQDjI1L2hpGikpOiJ6W05ae1hYa1ddOS49PT4pWlFabmtWWnpbaVFaamtWXXtfaWlrVl17X0pfe1poa1ZTa1ZcelhYe196X0xdSttaVll6XldZMi1WUVZSUVppelx7WWl3ZCzFJbCZqrqdu5qampqqup27mZqZmqmrlpCrlpy6mLualpeRmtvXm2tBc3BMSSktMC8yNAslOXBNR2FDRVVCTEckJSMyOTA0TUhPV0RRwEFNQk1KTUlgQUxDMiE3SkEASUBRe3h+WntXW35KQXJhR3JybUGRmqm6nKmpmpiaqauWkKuWnLqYu5mWl5Ga24ybAigbGSUsWkVASkwJKDg5OCkrLCgECCyRmqm6nKmpmp+ap6uXnt/e2c7cupm+m6qrl5j69/erl5zL6fT28uj+upuOmbufu5iRmp+pl5/ExP7pupq3FBUoKAUkBS0vJCEXKSdAVQUkGb4VKCoFJBUoKwUkMSUUFSgoBSQFLBckFSgqBSQVKCsFJA==\");\nrx.ex64(\"UlgBKS8sUkBHQVdMU0BXI2pHT0BGUSFOQFxWIkxLQUBdakMhQUpLQCBTRElQQCBkV1dEXCN2XEhHSkkgdVdKXVwnV10kJDQ1JCc0JCQmuTMVKSxLRFNMQkRRSlcFJSQhuTMVKS1BSkZQSEBLUQUlZCA2JbK1BQWolJLo9Pnh7+rx//DsuZhkIyglGh2slhEwPTAAPTARM2QiGCVrcHFMQ3FMQGBBYEFAQHJAcWFAQENxTERAfeJOcUxFQFhBcHFMQmFDTUUiJSIeYlBBQH1AQ0FsT3uUvmxOZC2JJaizso+Aso+Do4KjgoODsYOyooODgLKPh4O+IY2yj4aDCoKzso+BooCOhN3D8PDj+6KTgoOZgyePhKKAmSGyj4SjgrKigKOCg76DgIKvjLOyj4GigI6F3dH77+Dt7qKTgoOZgyePhaKAmSGyj4WjgrKigKOCg76DgIKvjLOyj4GigI6E3dLw7fr7opOCg5mDJ4+KooCZIbKPiqOCsqKAo4KDvoOAgq+MuOR9r41kLAUlHhmokhU0OD9XVVhYZFxVWkBbWZIVNDg8a0RcVVpAW1lkL+ElcmloVFU7Kj05LD0dND01PTYseVxUXjs5Ni45K1lZWWloVFI/PSwbNzYsPSAseFlUXS89Oj80WVpZZMV4WlpYdVZpaFRUPz0sHSAsPTYrMTc2eFpUQQ8dGh8UBzw9Oi0/Byo9Njw9Kj0qBzE2PjdbaFRPDRYVGQsTHRwHCh0WHB0KHQoHDx0aHxRZUllpaFRUPz0sCDkqOTU9LD0qeFpqWVtZZPtSeFtaWHVWaWhVW3hbVFMLLzE+LAswOTw9KnhJWFl1WWQuBiWLjAKRrarIz8/E0+nEyMbJ1YChka2qztTVxNPpxMjGydWAoWQpDiVjZOp5RUMgJycsOx4gLT0haEl5RUIqJSAsJz0eIC09IdVfeUVNKyYtMGhNJCgwLQUpBS4FLwUsBS0FIgUjBSBkK0Ilpo2NnYyNjp2MsBO8gIrg6eLr+OStgayOtoy9vICI6fTp77yBha2MvKyOrYG8jY2PjbAvhqyPj4yNj4KwrI+GjI2NGxWsjp2NrI2NjhydjayOtjlzvryAjfy8gYWtjJmOrI2tjq2NoBcVKSRXFSgsBSUFKykhSUpEQQ==\");\n/* ◬ */\n\n\n\n\n\n    <img height=\"1\" width=\"1\" style='display:none;visibility:hidden;' src='//fls-na.amazon.com/1/batch/1/OP/ATVPDKIKX0DER:134-1570085-9450222:K1JQ9VEP43RRWKW775V5$uedata=s:%2Frd%2Fuedata%3Fnoscript%26id%3DK1JQ9VEP43RRWKW775V5:0' alt=\"\"/>\n\n\nwindow.ue && ue.count && ue.count('CSMLibrarySize', 92747)\n\n\n\n\n\n     ✍操作台（点此拖动，左上角调整大小）  \n              ● 已选中1个元素，您可以:\n                  确认采集    取消选择  Path: /html/body  \n\n\n  \n    \n  \n  \n    \n\n  Close menu\n\n\n\n    \n      \n    \n  \n    \n      Hello, sign in\n    \n  \n\n\n\n    \n      \n\n\ndigital content & devicesAmazon MusicKindle E-readers & BooksAmazon Appstoreshop by departmentElectronicsComputersSmart HomeArts & CraftsAutomotiveBabyBeauty and personal careWomen's FashionMen's FashionGirls' FashionBoys' FashionHealth and HouseholdHome and KitchenIndustrial and ScientificLuggageMovies & TelevisionPet suppliesSoftwareSports and OutdoorsTools & Home ImprovementToys and GamesVideo Gamessee allsee lessprograms & featuresGift CardsShop By InterestAmazon LiveInternational ShoppingAmazon Second Chancesee allsee lesshelp & settingsYour AccountEnglishUnited StatesCustomer ServiceSign in\n\n\n    \n    Back to top\n  \n\n"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.google.com/q=easyspider", "links": "https://www.google.com/search?q=easyspider", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [19, 3], "isInLoop": false, "position": 1, "parameters": {"history": 6, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"pnnext\"]/span[2]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[6]/div[1]/div[13]/div[1]/div[4]/div[1]/div[3]/table[1]/tbody[1]/tr[1]/td[8]/a[1]/span[2]", "//span[contains(., 'Next')]", "/html/body/div[last()-9]/div/div[last()-7]/div/div[last()-3]/div/div/table/tbody/tr/td/a/span"]}}, {"id": 4, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 6, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"pnnext\"]/span[2]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[6]/div[1]/div[13]/div[1]/div[4]/div[1]/div[3]/table[1]/tbody[1]/tr[1]/td[8]/a[1]/span[2]", "//span[contains(., 'Next')]", "/html/body/div[last()-9]/div/div[last()-7]/div/div[last()-3]/div/div/table/tbody/tr/td/a/span"], "loopType": 0}}, {"id": -1, "index": 4, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 5, "parentId": 3, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 7, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[6]/div[1]/div[13]/div[1]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/div[1]/a[1]/h3[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 1}}, {"id": -1, "index": 6, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [7, 8], "isInLoop": true, "position": 0, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[6]/div[1]/div[13]/div[1]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/div[1]/a[1]/h3[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 7, "parentId": 3, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 7, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[6]/div[1]/div[13]/div[1]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/div[1]/a[1]/h3[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ""}}, {"id": -1, "index": 8, "parentId": 3, "type": 1, "option": 8, "title": "循环", "sequence": [9, 10], "isInLoop": true, "position": 1, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 9, "parentId": 6, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 2, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ""}}, {"id": -1, "index": 10, "parentId": 6, "type": 1, "option": 8, "title": "循环", "sequence": [11, 12], "isInLoop": true, "position": 1, "parameters": {"history": 2, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 11, "parentId": 8, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 2, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ""}}, {"id": -1, "index": 12, "parentId": 8, "type": 1, "option": 8, "title": "循环", "sequence": [14, 15, 16, 17, 18, 13], "isInLoop": true, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"quark-5cb43f1780772100479b2052__nav-channel__tabs\")]/li[1]/nav[1]/a[3]/span[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[3]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/ul[1]/li[1]/nav[1]/a[3]/span[1]", "//span[contains(., '荣耀')]", "//SPAN[@class='quark-5cb43f1780772100479b2052__nav-channel__sub-tab-name']", "/html/body/div[last()-4]/div/div/div[last()-13]/div/div/div[last()-1]/div/div[last()-1]/div/div/div/div/div[last()-1]/ul/li[last()-3]/nav/a[last()-3]/span"]}}, {"id": -1, "index": 13, "parentId": 10, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 5, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "//*[contains(@class, \"quark-5cb43f1780772100479b2052__nav-channel__tabs\")]/li[1]/nav[1]/a[3]/span[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[3]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/ul[1]/li[1]/nav[1]/a[3]/span[1]", "//span[contains(., '荣耀')]", "//SPAN[@class='quark-5cb43f1780772100479b2052__nav-channel__sub-tab-name']", "/html/body/div[last()-4]/div/div/div[last()-13]/div/div/div[last()-1]/div/div[last()-1]/div/div/div/div/div[last()-1]/ul/li[last()-3]/nav/a[last()-3]/span"], "loopType": 0}}, {"id": -1, "index": 14, "parentId": 10, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"r\")]/div[11]/a[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[11]/a[1]", "//a[contains(., '路由')]", "//A[@class='nav_title']", "/html/body/div[last()-11]/div/div[last()-5]/div/div/div/div/div/div/div/div/div/div/div/div/div/div/div/div/div/div/div[last()-2]/a"]}}, {"id": -1, "index": 15, "parentId": 10, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"342434258\"]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/a[3]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/a[3]", "//a[contains(., '')]", "//A[@class='hot-spot-link']", "/html/body/div[last()-11]/div/div[last()-4]/div/div/div/div[last()-15]/div/div/div/div/div/a[last()-6]"]}}, {"id": -1, "index": 16, "parentId": 10, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"342434258\"]/div[1]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/div[1]/a[2]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/div[1]/a[2]", "//a[contains(., '')]", "//A[@class='hot-spot-link']", "/html/body/div[last()-11]/div/div[last()-4]/div/div/div/div[last()-14]/div/div/div/div/div/a"]}}, {"id": -1, "index": 17, "parentId": 10, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 3, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"342434258\"]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/a[3]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/a[3]", "//a[contains(., '')]", "//A[@class='hot-spot-link']", "/html/body/div[last()-11]/div/div[last()-4]/div/div/div/div[last()-15]/div/div/div/div/div/a[last()-6]"]}}, {"id": -1, "index": 18, "parentId": 10, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 4, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"rso\"]/div[1]/div[1]/div[1]/div[1]/div[1]/a[1]/h3[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[6]/div[1]/div[13]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a[1]/h3[1]", "//h3[contains(., 'Easy Spide')]", "//H3[@class='LC20lb MBeuO DKV0Md']", "/html/body/div[last()-10]/div/div[last()-6]/div/div[last()-5]/div/div/div/div[last()-9]/div/div/div[last()-2]/div/a/h3"]}}, {"id": 3, "index": 19, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [20, 21], "isInLoop": true, "position": 0, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[6]/div[1]/div[13]/div[1]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/div[1]/a[1]/h3[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 5, "index": 20, "parentId": 3, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 8, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[6]/div[1]/div[13]/div[1]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/div[1]/a[1]/h3[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ""}}, {"id": 6, "index": 21, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": ["/html/body", "//body[contains(., '{\"AUI_TNR_')]", "//BODY[@class='a-m-us a-aui_72554-c a-aui_accordion_a11y_role_354025-t1 a-aui_killswitch_csa_logger_372963-c a-aui_pci_risk_banner_210084-c a-aui_preload_261698-c a-aui_rel_noreferrer_noopener_309527-c a-aui_template_weblab_cache_333406-c a-aui_tnr_v2_180836-c a-meter-animate']", "/html/body"], "exampleValues": [{"num": 0, "value": "{\"AUI_TNR_V2_180836\":\"C\",\"AUI_ACCORDION_A11Y_ROLE_354025\":\"T1\",\"AUI_PRELOAD_261698\":\"C\",\"AUI_TEMPLATE_WEBLAB_CACHE_333406\":\"C\",\"AUI_72554\":\"C\",\"AUI_KILLSWITCH_CSA_LOGGER_372963\":\"C\",\"AUI_REL_NOREFERRER_NOOPENER_309527\":\"C\",\"AUI_PCI_RISK_BANNER_210084\":\"C\"}typeof uex === 'function' && uex('ld', 'portal-bb', {wb: 1})\n\n\n\n\n!function(){function n(n,t){var r=i(n);return t&&(r=r(\"instance\",t)),r}var r=[],c=0,i=function(t){return function(){var n=c++;return r.push([t,[].slice.call(arguments,0),n,{time:Date.now()}]),i(n)}};n._s=r,this.csa=n}();;\ncsa('Config', {\"ContentInteractionsSummary.FlushInterval\":5000,\"AddMissingPluginsToEnd\":1});\nif (window.csa) {\n    csa(\"Config\", {\n        'Application': 'Retail:Prod:www.amazon.com',\n        'Events.Namespace': 'csa',\n        'ObfuscatedMarketplaceId': 'ATVPDKIKX0DER',\n        'Events.SushiEndpoint': 'https://unagi.amazon.com/1/events/com.amazon.csm.csa.prod',\n        'CacheDetection.RequestID': \"K1JQ9VEP43RRWKW775V5\",\n        'CacheDetection.Callback': window.ue && ue.reset,\n        'LCP.elementDedup': 1\n    });\n\n    csa(\"Events\")(\"setEntity\", {\n        page: {requestId: \"K1JQ9VEP43RRWKW775V5\", meaningful: \"interactive\"},\n        session: {id: \"134-1570085-9450222\"}\n    });\n}\n!function(i){var r,e,o=\"splice\",u=i.csa,f={},c={},a=i.csa._s,s=0,l=0,g=-1,h={},d={},v={},n=Object.keys,p=function(){};function t(n,t){return u(n,t)}function m(n,t){var i=c[n]||{};O(i,t),c[n]=i,l++,S(U,0)}function w(n,t,i){var r=!0;return t=D(t),i&&i.buffered&&(r=(v[n]||[]).every(function(n){return!1!==t(n)})),r?(h[n]||(h[n]=[]),h[n].push(t),function(){!function(n,t){var i=h[n];i&&i[o](i.indexOf(t),1)}(n,t)}):p}function b(n,t){if(t=D(t),n in d)return t(d[n]),p;return w(n,function(n){return t(n),!1})}function E(n,t){if(u(\"Errors\")(\"logError\",n),f.DEBUG)throw t||n}function y(){return Math.abs(4294967295*Math.random()|0).toString(36)}function D(n,t){return function(){try{return n.apply(this,arguments)}catch(n){E(n.message||n,n)}}}function S(n,t){return i.setTimeout(D(n),t)}function U(){for(var n=0;n<a.length;){var t=a[n],i=t[0]in c;if(!i&&!e)return void(s=f.AddMissingPluginsToEnd?a.length:t.length);i?(a[o](s=n,1),I(t)):n++}g=l}function I(n){var arguments,t=c[n[0]],i=(arguments=n[1])[0];if(!t||!t[i])return E(\"Undefined function: \"+t+\"/\"+i);r=n[3],c[n[2]]=t[i].apply(t,arguments.slice(1))||{},r=0}function M(){e=1,U()}function O(t,i){n(i).forEach(function(n){t[n]=i[n]})}b(\"$beforeunload\",M),m(\"Config\",{instance:function(n){O(f,n)}}),u.plugin=D(function(n){n(t)}),t.config=f,t.register=m,t.on=w,t.once=b,t.blank=p,t.emit=function(n,t,i){for(var r=h[n]||[],e=0;e<r.length;)!1===r[e](t)?r[o](e,1):e++;d[n]=t||{},i&&i.buffered&&(v[n]||(v[n]=[]),100<=v[n].length&&v[n].shift(),v[n].push(t||{}))},t.UUID=function(){return[y(),y(),y(),y()].join(\"-\")},t.time=function(n){var t=r?new Date(r.time):new Date;return\"ISO\"===n?t.toISOString():t.getTime()},t.error=E,t.warn=function(n,t){if(u(\"Errors\")(\"logWarn\",n),f.DEBUG)throw t||n},t.exec=D,t.timeout=S,t.interval=function(n,t){return i.setInterval(D(n),t)},(t.global=i).csa._s.push=function(n){n[0]in c&&(!a.length||e)?(I(n),a.length&&g!==l&&U()):a[o](s++,0,n)},U(),S(function(){S(M,f.SkipMissingPluginsTimeout||5e3)},1)}(\"undefined\"!=typeof window?window:global);csa.plugin(function(o){var f=\"addEventListener\",e=\"requestAnimationFrame\",t=o.exec,r=o.global,u=o.on;o.raf=function(n){if(r[e])return r[e](t(n))},o.on=function(n,e,t,r){if(n&&\"function\"==typeof n[f]){var i=o.exec(t);return n[f](e,i,r),function(){n.removeEventListener(e,i,r)}}return\"string\"==typeof n?u(n,e,t,r):o.blank}});csa.plugin(function(o){var t,n,r={},e=\"localStorage\",c=\"sessionStorage\",a=\"local\",i=\"session\",u=o.exec;function s(e,t){var n;try{r[t]=!!(n=o.global[e]),n=n||{}}catch(e){r[t]=!(n={})}return n}function f(){t=t||s(e,a),n=n||s(c,i)}function l(e){return e&&e[i]?n:t}o.store=u(function(e,t,n){f();var o=l(n);return e?t?void(o[e]=t):o[e]:Object.keys(o)}),o.storageSupport=u(function(){return f(),r}),o.deleteStored=u(function(e,t){f();var n=l(t);if(\"function\"==typeof e)for(var o in n)n.hasOwnProperty(o)&&e(o,n[o])&&delete n[o];else delete n[e]})});csa.plugin(function(n){n.types={ovl:function(n){var r=[];if(n)for(var i in n)n.hasOwnProperty(i)&&r.push(n[i]);return r}}});csa.plugin(function(c){function e(n){return function(e){c(\"Metrics\",{producerId:\"csa\",dimensions:{message:e}})(\"recordMetric\",n,1)}}function n(r){var t,o,l=c(\"Events\",{producerId:r.producerId}),u=[\"name\",\"type\",\"csm\",\"adb\"],i={url:\"pageURL\",file:\"f\",line:\"l\",column:\"c\"};this.log=function(e){if(!function(e){if(!e)return!0;for(var n in e)return!1;return!0}(e)){var n=r.logOptions||{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}};l(\"log\",function(n){return t=c.UUID(),o={messageId:t,schemaId:r.schemaId||\"<ns>.Error.5\",errorMessage:n.m||null,attribution:n.attribution||null,logLevel:\"FATAL\",url:null,file:null,line:null,column:null,stack:n.s||[],context:n.cinfo||{},metadata:{}},n.logLevel&&(o.logLevel=\"\"+n.logLevel),u.forEach(function(e){n[e]&&(o.metadata[e]=n[e])}),\"INFO\"===n.logLevel||Object.keys(i).forEach(function(e){\"number\"!=typeof n[i[e]]&&\"string\"!=typeof n[i[e]]||(o[e]=\"\"+n[i[e]])}),o}(e),n)}}}c.register(\"Errors\",{instance:function(e){return new n(e||{})},logError:e(\"jsError\"),logWarn:e(\"jsWarn\")})});csa.plugin(function(o){var r,e,n,t,a,i=\"function\",u=\"willDisappear\",f=\"$app.\",p=\"$document.\",c=\"focus\",s=\"blur\",d=\"active\",l=\"resign\",$=o.global,b=o.exec,m=o.config[\"Transport.AnonymizeRequests\"]||!1,g=o(\"Events\"),h=$.location,v=$.document||{},y=$.P||{},P=(($.performance||{}).navigation||{}).type,w=o.on,k=o.emit,E=v.hidden,T={};h&&v&&(w($,\"beforeunload\",D),w($,\"pagehide\",D),w(v,\"visibilitychange\",R(p,function(){return v.visibilityState||\"unknown\"})),w(v,c,R(p+c)),w(v,s,R(p+s)),y.when&&y.when(\"mash\").execute(function(e){e&&(w(e,\"appPause\",R(f+\"pause\")),w(e,\"appResume\",R(f+\"resume\")),R(f+\"deviceready\")(),$.cordova&&$.cordova.platformId&&R(f+cordova.platformId)(),w(v,d,R(f+d)),w(v,l,R(f+l)))}),e=$.app||{},n=b(function(){k(f+\"willDisappear\"),D()}),a=typeof(t=e[u])==i,e[u]=b(function(){n(),a&&t()}),$.app||($.app=e),\"complete\"===v.readyState?A():w($,\"load\",A),E?S():x(),o.on(\"$app.blur\",S),o.on(\"$app.focus\",x),o.on(\"$document.blur\",S),o.on(\"$document.focus\",x),o.on(\"$document.hidden\",S),o.on(\"$document.visible\",x),o.register(\"SPA\",{newPage:I}),I({transitionType:{0:\"hard\",1:\"refresh\",2:\"back-button\"}[P]||\"unknown\"}));function I(n,e){var t=!!r,a=(e=e||{}).keepPageAttributes;t&&(k(\"$beforePageTransition\"),k(\"$pageTransition\")),t&&!a&&g(\"removeEntity\",\"page\"),r=o.UUID(),a?T.id=r:T={schemaId:\"<ns>.PageEntity.1\",id:r,url:m?h.href.split(\"?\")[0]:h.href,server:h.hostname,path:h.pathname,referrer:m?v.referrer.split(\"?\")[0]:v.referrer,title:v.title},Object.keys(n||{}).forEach(function(e){T[e]=n[e]}),g(\"setEntity\",{page:T}),k(\"$pageChange\",T,{buffered:1}),t&&k(\"$afterPageTransition\")}function A(){k(\"$load\"),k(\"$ready\"),k(\"$afterload\")}function D(){k(\"$ready\"),k(\"$beforeunload\"),k(\"$unload\"),k(\"$afterunload\")}function S(){E||(k(\"$visible\",!1,{buffered:1}),E=!0)}function x(){E&&(k(\"$visible\",!0,{buffered:1}),E=!1)}function R(n,t){return b(function(){var e=typeof t==i?n+t():n;k(e)})}});csa.plugin(function(c){var e=\"Events\",t=\"UNKNOWN\",s=\"id\",a=\"all\",n=\"messageId\",i=\"timestamp\",u=\"producerId\",o=\"application\",r=\"obfuscatedMarketplaceId\",f=\"entities\",d=\"schemaId\",l=\"version\",p=\"attributes\",v=\"<ns>\",g=\"session\",h=c.config,m=(c.global.location||{}).host,y=h[e+\".Namespace\"]||\"csa_other\",I=h.Application||\"Other\"+(m?\":\"+m:\"\"),b=h[\"Transport.AnonymizeRequests\"]||!1,O=c(\"Transport\"),E={},U=function(e,t){Object.keys(e).forEach(t)};function A(n,i,o){U(i,function(e){var t=o===a||(o||{})[e];e in n||(n[e]={version:1,id:i[e][s]||c.UUID()}),N(n[e],i[e],t)})}function N(t,n,i){U(n,function(e){!function(e,t,n){return\"string\"!=typeof t&&e!==l?c.error(\"Attribute is not of type string: \"+e):!0===n||1===n||(e===s||!!~(n||[]).indexOf(e))}(e,n[e],i)||(t[e]=n[e])})}function S(o,e,r){U(e,function(e){var t=o[e];if(t[d]){var n={},i={};n[s]=t[s],n[u]=t[u]||r,n[d]=t[d],n[l]=t[l]++,n[p]=i,k(n),N(i,t,1),w(i),O(\"log\",n)}})}function k(e){e[i]=function(e){return\"number\"==typeof e&&(e=new Date(e).toISOString()),e||c.time(\"ISO\")}(e[i]),e[n]=e[n]||c.UUID(),e[o]=I,e[r]=h.ObfuscatedMarketplaceId||t,e[d]=e[d].replace(v,y)}function w(e){delete e[l],delete e[d],delete e[u]}function D(o){var r={};this.log=function(e,t){var n={},i=(t||{}).ent;return e?\"string\"!=typeof e[d]?c.error(\"A valid schema id is required for the event\"):(k(e),A(n,E,i),A(n,r,i),A(n,e[f]||{},i),U(n,function(e){w(n[e])}),e[u]=o[u],e[f]=n,void O(\"log\",e,t)):c.error(\"The event cannot be undefined\")},this.setEntity=function(e){b&&delete e[g],A(r,e,a),S(r,e,o[u])}}h[\"KillSwitch.\"+e]||c.register(e,{setEntity:function(e){b&&delete e[g],A(E,e,a),S(E,e,\"csa\")},removeEntity:function(e){delete E[e]},instance:function(e){return new D(e)}})});csa.plugin(function(s){var c,g=\"Transport\",l=\"post\",f=\"preflight\",r=\"csa.cajun.\",i=\"store\",a=\"deleteStored\",u=\"sendBeacon\",t=\"__merge\",e=\"messageId\",n=\".FlushInterval\",o=0,d=s.config[g+\".BufferSize\"]||2e3,h=s.config[g+\".RetryDelay\"]||1500,p=s.config[g+\".AnonymizeRequests\"]||!1,v={},y=0,m=[],E=s.global,R=E.document,b=s.timeout,k=E.Object.keys,w=s.config[g+n]||5e3,I=w,O=s.config[g+n+\".BackoffFactor\"]||1,S=s.config[g+n+\".BackoffLimit\"]||3e4,B=0;function T(n){if(864e5<s.time()-+new Date(n.timestamp))return s.warn(\"Event is too old: \"+n);y<d&&(n[e]in v||(v[n[e]]=n,y++),\"function\"==typeof n[t]&&n[t](v[n[e]]),!B&&o&&(B=b(q,function(){var n=I;return I=Math.min(n*O,S),n}())))}function q(){m.forEach(function(e){var o=[];k(v).forEach(function(n){var t=v[n];e.accepts(t)&&o.push(t)}),o.length&&(e.chunks?e.chunks(o).forEach(function(n){D(e,n)}):D(e,o))}),v={},B=0}function D(t,e){function o(){s[a](r+n)}var n=s.UUID();s[i](r+n,JSON.stringify(e)),[function(n,t,e){var o=E.navigator||{},r=E.cordova||{};if(p)return 0;if(!o[u]||!n[l])return 0;n[f]&&r&&\"ios\"===r.platformId&&!c&&((new Image).src=n[f]().url,c=1);var i=n[l](t);if(!i.type&&o[u](i.url,i.body))return e(),1},function(n,t,e){if(!n[l])return 0;var o=n[l](t),r=o.url,i=o.body,c=o.type,f=new XMLHttpRequest,a=0;function u(n,t,e){f.open(\"POST\",n),f.withCredentials=!p,e&&f.setRequestHeader(\"Content-Type\",e),f.send(t)}return f.onload=function(){f.status<299?e():s.config[g+\".XHRRetries\"]&&a<3&&b(function(){u(r,i,c)},++a*h)},u(r,i,c),1}].some(function(n){try{return n(t,e,o)}catch(n){}})}k&&(s.once(\"$afterload\",function(){o=1,function(e){(s[i]()||[]).forEach(function(n){if(!n.indexOf(r))try{var t=s[i](n);s[a](n),JSON.parse(t).forEach(e)}catch(n){s.error(n)}})}(T),s.on(R,\"visibilitychange\",q,!1),q()}),s.once(\"$afterunload\",function(){o=1,q()}),s.on(\"$afterPageTransition\",function(){y=0,I=w}),s.register(g,{log:T,register:function(n){m.push(n)}}))});csa.plugin(function(n){var r=n.config[\"Events.SushiEndpoint\"];n(\"Transport\")(\"register\",{accepts:function(n){return n.schemaId},post:function(n){var t=n.map(function(n){return{data:n}});return{url:r,body:JSON.stringify({events:t})}},preflight:function(){var n,t=/\\/\\/(.*?)\\//.exec(r);return t&&t[1]&&(n=\"https://\"+t[1]+\"/ping\"),{url:n}},chunks:function(n){for(var t=[];500<n.length;)t.push(n.splice(0,500));return t.push(n),t}})});csa.plugin(function(n){var t,a,o,r,e=n.config,i=\"PageViews\",d=e[i+\".ImpressionMinimumTime\"]||1e3,s=\"hidden\",c=\"innerHeight\",g=\"innerWidth\",l=\"renderedTo\",f=l+\"Viewed\",m=l+\"Meaningful\",u=l+\"Impressed\",p=1,v=2,h=3,w=4,y=5,P=\"loaded\",I=7,T=8,b=n.global,E=n.on,V=n(\"Events\",{producerId:\"csa\"}),$=b.document,M={},S={},H=y;function K(e){if(!M[I]){var i;if(M[e]=n.time(),e!==h&&e!==P||(t=t||M[e]),t&&H===w)a=a||M[e],(i={})[m]=t-o,i[f]=a-o,R(\"PageView.4\",i),r=r||n.timeout(j,d);if(e!==y&&e!==p&&e!==v||(clearTimeout(r),r=0),e!==p&&e!==v||R(\"PageRender.3\",{transitionType:e===p?\"hard\":\"soft\"}),e===I)(i={})[m]=t-o,i[f]=a-o,i[u]=M[e]-o,R(\"PageImpressed.2\",i)}}function R(e,i){S[e]||(i.schemaId=\"<ns>.\"+e,V(\"log\",i,{ent:\"all\"}),S[e]=1)}function W(){0===b[c]&&0===b[g]?(H=T,n(\"Events\")(\"setEntity\",{page:{viewport:\"hidden-iframe\"}})):H=$[s]?y:w,K(H)}function j(){K(I),r=0}function k(){var e=o?v:p;M={},S={},a=t=0,o=n.time(),K(e),W()}function q(){var e=$.readyState;\"interactive\"===e&&K(h),\"complete\"===e&&K(P)}e[\"KillSwitch.\"+i]||($&&void 0!==$[s]?(k(),E($,\"visibilitychange\",W,!1),E($,\"readystatechange\",q,!1),E(\"$afterPageTransition\",k),E(\"$timing:loaded\",q),n.once(\"$load\",q)):n.warn(\"Page visibility not supported\"))});csa.plugin(function(c){var s=c.config[\"Interactions.ParentChainLength\"]||35,e=\"click\",r=\"touches\",f=\"timeStamp\",o=\"length\",u=\"pageX\",g=\"pageY\",p=\"pageXOffset\",h=\"pageYOffset\",m=250,v=5,d=200,l=.5,t={capture:!0,passive:!0},X=c.global,Y=c.emit,n=c.on,x=X.Math.abs,a=(X.document||{}).documentElement||{},y={x:0,y:0,t:0,sX:0,sY:0},N={x:0,y:0,t:0,sX:0,sY:0};function b(t){if(t.id)return\"//*[@id='\"+t.id+\"']\";var e=function(t){var e,n=1;for(e=t.previousSibling;e;e=e.previousSibling)e.nodeName===t.nodeName&&(n+=1);return n}(t),n=t.nodeName;return 1!==e&&(n+=\"[\"+e+\"]\"),t.parentNode&&(n=b(t.parentNode)+\"/\"+n),n}function I(t,e,n){var a=c(\"Content\",{target:n}),i={schemaId:\"<ns>.ContentInteraction.1\",interaction:t,interactionData:e,messageId:c.UUID()};if(n){var r=b(n);r&&(i.attribution=r);var o=function(t){for(var e=t,n=e.tagName,a=!1,i=t?t.href:null,r=0;r<s;r++){if(!e||!e.parentElement){a=!0;break}n=(e=e.parentElement).tagName+\"/\"+n,i=i||e.href}return a||(n=\".../\"+n),{pc:n,hr:i}}(n);o.pc&&(i.interactionData.parentChain=o.pc),o.hr&&(i.interactionData.href=o.hr)}a(\"log\",i),Y(\"$content.interaction\",i)}function i(t){I(e,{interactionX:\"\"+t.pageX,interactionY:\"\"+t.pageY},t.target)}function C(t){if(t&&t[r]&&1===t[r][o]){var e=t[r][0];N=y={e:t.target,x:e[u],y:e[g],t:t[f],sX:X[p],sY:X[h]}}}function D(t){if(t&&t[r]&&1===t[r][o]&&y&&N){var e=t[r][0],n=t[f],a=n-N.t,i={e:t.target,x:e[u],y:e[g],t:n,sX:X[p],sY:X[h]};N=i,d<=a&&(y=i)}}function E(t){if(t){var e=x(y.x-N.x),n=x(y.y-N.y),a=x(y.sX-N.sX),i=x(y.sY-N.sY),r=t[f]-y.t;if(m<1e3*e/r&&v<e||m<1e3*n/r&&v<n){var o=n<e;o&&a&&e*l<=a||!o&&i&&n*l<=i||I((o?\"horizontal\":\"vertical\")+\"-swipe\",{interactionX:\"\"+y.x,interactionY:\"\"+y.y,endX:\"\"+N.x,endY:\"\"+N.y},y.e)}}}n(a,e,i,t),n(a,\"touchstart\",C,t),n(a,\"touchmove\",D,t),n(a,\"touchend\",E,t)});csa.plugin(function(r){var a,o,t,e=\"MutationObserver\",c=\"observe\",n=\"disconnect\",s=\"mutObs\",f=\"_csa_flt\",l=\"_csa_llt\",b=\"_csa_mr\",d=\"_csa_mi\",m=\"lastChild\",p=\"length\",_={childList:!0,subtree:!0},g=10,h=4,u=r.global,i=u.document,v=i.body||i.documentElement,y=Date.now,O=[],k=[],w=[],L=0,B=0,I=0,M=1,Y=[],$=[],x=0,A=r.blank,C={buffered:1},D=0;function E(e){r.global.ue_csa_ss_tag||r.emit(\"$csmTag:\"+e,0,C)}y&&u[e]?(E(s+\"Yes\"),L=0,o=new u[e](N),(t=new u[e](F))[c](v,{attributes:!0,subtree:!0,attributeFilter:[\"src\"],attributeOldValue:!0}),A=r.on(u,\"scroll\",S,{passive:!0}),r.once(\"$ready\",V),M&&T(),r.register(\"SpeedIndexBuffers\",{getBuffers:function(e){e&&(V(),S(),e(L,Y,O,k,w),o&&o[n](),t&&t[n](),A())},registerListener:function(e){a=e},replayModuleIsLive:function(){r.raf(V)}})):E(s+\"No\");function F(e){O.push({t:y(),m:e})}function N(e){k.push({t:y(),m:e}),D||E(s+\"Active\"),D=I=1,a&&a()}function S(){I&&(w.push({t:y(),y:B}),B=u.pageYOffset,I=0)}function T(){for(var e=v,t=y(),n=[],s=[],u=0,i=0;e;)e[f]?++u:(e[f]=t,n.push(e),i=1),s[p]<h&&s.push(e),e[d]=x,e[l]=t,e=e[m];i&&(u<$[p]&&function(e){for(var t=e,n=$[p];t<n;t++){var s=$[t];if(s){if(s[b])break;if(s[d]<x){s[b]=1,o[c](s,_);break}}}}(u),$=s,Y.push({t:t,m:n}),++x,I=i,a&&a()),M&&(i?r.raf(T):r.timeout(T,g))}function V(){M&&(M=0,T(),o[c](v,_))}});\n\nvar ue_csa_ss_tag = false;\ncsa.plugin(function(b){var a=b.global,e=a.uet,f=a.uex,c=a.ue,d=a.Object,g={largestContentfulPaint:\"lcp\",speedIndex:\"si\",atfSpeedIndex:\"atfsi\",visuallyLoaded50:\"vl50\",visuallyLoaded90:\"vl90\",visuallyLoaded100:\"vl100\"},k=\"perfNo perfYes browserQuiteFn browserQuiteUd browserQuiteLd browserQuiteMut mutObsNo mutObsYes mutObsActive startVL endVL\".split(\" \");b&&e&&f&&d.keys&&c&&(d.keys(g).forEach(function(h){b.on(\"$timing:\"+h,function(a){var b=g[h];if(c.isl){var d=\"csa:\"+b;e(b,d,void 0,a);f(\"at\",d)}else e(b,\nvoid 0,void 0,a)})}),a.ue_csa_ss_tag||k.forEach(function(a){b.on(\"$csmTag:\"+a,function(){c.tag&&c.tag(a);c.isl&&f(\"at\",\"csa:\"+a)},{buffered:1})}))});\n\n\nwindow.rx = { 'rid':'K1JQ9VEP43RRWKW775V5', 'sid':'134-1570085-9450222', 'c':{  'rxp':'/rd/uedata', }};\n\nwindow.ue && ue.count && ue.count('CSMLibrarySize', 15975)\n\n\n\n\n!function(n){function e(n,e){return{m:n,a:function(n){return[].slice.call(n)}(e)}}document.createElement(\"header\");var r=function(n){function u(n,r,u){n[u]=function(){a._replay.push(r.concat(e(u,arguments)))}}var a={};return a._sourceName=n,a._replay=[],a.getNow=function(n,e){return e},a.when=function(){var n=[e(\"when\",arguments)],r={};return u(r,n,\"run\"),u(r,n,\"declare\"),u(r,n,\"publish\"),u(r,n,\"build\"),r.depends=n,r.iff=function(){var r=n.concat([e(\"iff\",arguments)]),a={};return u(a,r,\"run\"),u(a,r,\"declare\"),u(a,r,\"publish\"),u(a,r,\"build\"),a},r},u(a,[],\"declare\"),u(a,[],\"build\"),u(a,[],\"publish\"),u(a,[],\"importEvent\"),r._shims.push(a),a};r._shims=[],n.$Nav||(n.$Nav=r(\"rcx-nav\")),n.$Nav.make||(n.$Nav.make=r)}(window)\n$Nav.importEvent('navbarJS-beaconbelt');\n$Nav.declare('img.sprite', {\n  'png32': 'https://m.media-amazon.com/images/G/01/gno/sprites/nav-sprite-global-1x-reorg-privacy._CB587940754_.png',\n  'png32-2x': 'https://m.media-amazon.com/images/G/01/gno/sprites/nav-sprite-global-2x-reorg-privacy._CB587940754_.png'\n});\n$Nav.declare('img.timeline', {\n  'timeline-icon-2x': 'https://m.media-amazon.com/images/G/01/gno/sprites/timeline_sprite_2x._CB443581191_.png'\n});\nwindow._navbarSpriteUrl = 'https://m.media-amazon.com/images/G/01/gno/sprites/nav-sprite-global-1x-reorg-privacy._CB587940754_.png';\n$Nav.declare('img.pixel', 'https://m.media-amazon.com/images/G/01/x-locale/common/transparent-pixel._CB485935036_.gif');\n\n\n\nvar nav_t_after_preload_sprite = + new Date();\n\n(window.AmazonUIPageJS ? AmazonUIPageJS : P).when('navCF').execute(function() {\n  (window.AmazonUIPageJS ? AmazonUIPageJS : P).load.js('https://images-na.ssl-images-amazon.com/images/I/41X+pNHyLAL._RC|71q7BAoqR6L.js,01QvReFeJyL.js,01phmzCOwJL.js,01eOvPdxG7L.js,71f2YMGfmTL.js,41gNKoK0s7L.js,115pV8Rl02L.js,01+pnQJuQ0L.js,21rDHgaooIL.js,41rU9l+NGKL.js,51t-JTxfnwL.js,317BC63dC8L.js,11lEMI5MhIL.js,31c7Fn9h9gL.js,01LEzWzrPZL.js,01AqeWA7PKL.js_.js?AUIClients/NavDesktopUberAsset&FmhEG1Hq#desktop.language-en.us.488400-T1.488413-T1.375680-T1.366740-T1.310484-T1.651011-T1');\n});\n\n\n\n\n\n\n\n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    if(window.navmet===undefined) {\n      window.navmet=[];\n      if (window.performance && window.performance.timing && window.ue_t0) {\n        var t = window.performance.timing;\n        var now = + new Date();\n        window.navmet.basic = {\n          'networkLatency': (t.responseStart - t.fetchStart),\n          'navFirstPaint': (now - t.responseStart),\n          'NavStart': (now - window.ue_t0)\n        };\n        window.navmet.push({key:\"NavFirstPaintStart\",end:+new Date(),begin:window.ue_t0});\n      }\n    }\n    if (window.ue_t0) {\n      window.navmet.push({key:\"NavMainStart\",end:+new Date(),begin:window.ue_t0});\n    }\n\n\n\n\n\nwindow.navmet.tmp=+new Date();\n  \n    // Nav start should be logged at this place only if request is NOT progressively loaded.\n    // For progressive loading case this metric is logged as part of skeleton.\n    // Presence of skeleton signals that request is progressively loaded.\n    if(!document.getElementById(\"navbar-skeleton\")) {\n      window.uet && uet('ns');\n    }\n    window._navbar = (function (o) {\n      o.componentLoaded = o.loading = function(){};\n      o.browsepromos = {};\n      o.issPromos = [];\n      return o;\n    }(window._navbar || {}));\n    window._navbar.declareOnLoad = function () { window.$Nav && $Nav.declare('page.load'); };\n    if (window.addEventListener) {\n      window.addEventListener(\"load\", window._navbar.declareOnLoad, false);\n    } else if (window.attachEvent) {\n      window.attachEvent(\"onload\", window._navbar.declareOnLoad);\n    } else if (window.$Nav) {\n      $Nav.when('page.domReady').run(\"OnloadFallbackSetup\", function () {\n        window._navbar.declareOnLoad();\n      });\n    }\n    window.$Nav && $Nav.declare('logEvent.enabled',\n      'false');\n\n    window.$Nav && $Nav.declare('config.lightningDeals', {});\n  \n\n    \n       #nav-flyout-ewc .nav-flyout-buffer-left { display: none; } #nav-flyout-ewc .nav-flyout-buffer-right { display: none; } div#navSwmHoliday.nav-focus {border: none;margin: 0;}\n    \n    \n      try {\n        if(window.navmet===undefined)window.navmet=[]; if(window.$Nav) { $Nav.when('$', 'config', 'flyout.accountList', 'SignInRedirect', 'dataPanel').run('accountListRedirectFix', function ($, config, flyout, SignInRedirect, dataPanel) { if (!config.accountList) { return; } flyout.getPanel().onData(function (data) { if (SignInRedirect) { var $anchors = $('[data-nav-role=signin]', flyout.elem()); $.each($anchors, function(i, anchorEl) {SignInRedirect.setRedirectUrl($(anchorEl), null, null);});}});}); $Nav.when('$').run('defineIsArray', function(jQuery) { if(jQuery.isArray===undefined) { jQuery.isArray=function(param) { if(param.length===undefined) { return false; } return true; }; } }); $Nav.declare('config.cartFlyoutDisabled', 'true'); $Nav.when('$','$F','config','logEvent','panels','phoneHome','dataPanel','flyouts.renderPromo','flyouts.sloppyTrigger','flyouts.accessibility','util.mouseOut','util.onKey','debug.param').build('flyouts.buildSubPanels',function($,$F,config,logEvent,panels,phoneHome,dataPanel,renderPromo,createSloppyTrigger,a11yHandler,mouseOutUtility,onKey,debugParam){var flyoutDebug=debugParam('navFlyoutClick');return function(flyout,event){var linkKeys=[];$('.nav-item',flyout.elem()).each(function(){var $item=$(this);linkKeys.push({link:$item,panelKey:$item.attr('data-nav-panelkey')});});if(linkKeys.length===0){return;} var visible=false;var $parent=$('<div class=\\'nav-subcats\\'></div>').appendTo(flyout.elem());var panelGroup=flyout.getName()+'SubCats';var hideTimeout=null;var sloppyTrigger=createSloppyTrigger($parent);var showParent=function(){if(hideTimeout){clearTimeout(hideTimeout);hideTimeout=null;} if(visible){return;} var height=$('#nav-flyout-shopAll').height(); $parent.css({'height': height});$parent.animate({width:'show'},{duration:200,complete:function(){$parent.css({overflow:'visible'});}});visible=true;};var hideParentNow=function(){$parent.stop().css({overflow:'hidden',display:'none',width:'auto',height:'auto'});panels.hideAll({group:panelGroup});visible=false;if(hideTimeout){clearTimeout(hideTimeout);hideTimeout=null;}};var hideParent=function(){if(!visible){return;} if(hideTimeout){clearTimeout(hideTimeout);hideTimeout=null;} hideTimeout=setTimeout(hideParentNow,10);};flyout.onHide(function(){sloppyTrigger.disable();hideParentNow();this.elem().hide();});var addPanel=function($link,panelKey){var panel=dataPanel({className:'nav-subcat',dataKey:panelKey,groups:[panelGroup],spinner:false,visible:false});if(!flyoutDebug){var mouseout=mouseOutUtility();mouseout.add(flyout.elem());mouseout.action(function(){panel.hide();});mouseout.enable();} var a11y=a11yHandler({link:$link,onEscape:function(){panel.hide();$link.focus();}});var logPanelInteraction=function(promoID,wlTriggers){var logNow=$F.once().on(function(){var panelEvent=$.extend({},event,{id:promoID});if(config.browsePromos&&!!config.browsePromos[promoID]){panelEvent.bp=1;} logEvent(panelEvent);phoneHome.trigger(wlTriggers);});if(panel.isVisible()&&panel.hasInteracted()){logNow();}else{panel.onInteract(logNow);}};panel.onData(function(data){renderPromo(data.promoID,panel.elem());logPanelInteraction(data.promoID,data.wlTriggers);});panel.onShow(function(){var columnCount=$('.nav-column',panel.elem()).length;panel.elem().addClass('nav-colcount-'+columnCount);showParent();var $subCatLinks=$('.nav-subcat-links > a',panel.elem());var length=$subCatLinks.length;if(length>0){var firstElementLeftPos=$subCatLinks.eq(0).offset().left;for(var i=1;i<length;i++){if(firstElementLeftPos===$subCatLinks.eq(i).offset().left){$subCatLinks.eq(i).addClass('nav_linestart');}} if($('span.nav-title.nav-item',panel.elem()).length===0){var catTitle=$.trim($link.html());catTitle=catTitle.replace(/ref=sa_menu_top/g,'ref=sa_menu');var $subPanelTitle=$('<span class=\\'nav-title nav-item\\'>'+ catTitle+'</span>');panel.elem().prepend($subPanelTitle);}} $link.addClass('nav-active');});panel.onHide(function(){$link.removeClass('nav-active');hideParent();a11y.disable();sloppyTrigger.disable();});panel.onShow(function(){a11y.elems($('a, area',panel.elem()));});sloppyTrigger.register($link,panel);if(flyoutDebug){$link.click(function(){if(panel.isVisible()){panel.hide();}else{panel.show();}});} var panelKeyHandler=onKey($link,function(){if(this.isEnter()||this.isSpace()){panel.show();}},'keydown',false);$link.focus(function(){panelKeyHandler.bind();}).blur(function(){panelKeyHandler.unbind();});panel.elem().appendTo($parent);};var hideParentAndResetTrigger=function(){hideParent();sloppyTrigger.disable();};for(var i=0;i<linkKeys.length;i++){var item=linkKeys[i];if(item.panelKey){addPanel(item.link,item.panelKey);}else{item.link.mouseover(hideParentAndResetTrigger);}}};});};\n      } catch ( err ) {\n        if ( window.$Nav ) {\n          window.$Nav.when('metrics', 'logUeError').run(function(metrics, log) {\n            metrics.increment('NavJS:AboveNavInjection:error');\n            log(err.toString(), {\n              'attribution': 'rcx-nav',\n              'logLevel': 'FATAL'\n            });\n          });\n        }\n      }\n    \n\n  \n    <style type=\"text/css\"><!--\n      #navbar #nav-shop .nav-a:hover {\n        color: #ff9900;\n        text-decoration: underline;\n      }\n      #navbar #nav-search .nav-search-facade,\n      #navbar #nav-tools .nav-icon,\n      #navbar #nav-shop .nav-icon,\n      #navbar #nav-subnav .nav-hasArrow .nav-arrow {\n        display: none;\n      }\n      #navbar #nav-search .nav-search-submit,\n      #navbar #nav-search .nav-search-scope {\n        display: block;\n      }\n      #nav-search .nav-search-scope {\n        padding: 0 5px;\n      }\n      #navbar #nav-search .nav-search-dropdown {\n        position: relative;\n        top: 5px;\n        height: 23px;\n        font-size: 14px;\n        opacity: 1;\n        filter: alpha(opacity = 100);\n      }\n    --></style>\n \nwindow.navmet.push({key:'PreNav',end:+new Date(),begin:window.navmet.tmp});\n\n\n\n\n\n\nSkip to main content\n\nwindow.navmet.tmp=+new Date();\n\n    \n      \n      \n    \nwindow.navmet.push({key:'UpNav',end:+new Date(),begin:window.navmet.tmp});\n\n\nwindow.navmet.main=+new Date();\n\n\n\n\n\n   \n  \n    \n      \n        window.navmet.tmp=+new Date();\n  \n    \n      \n      \n      .us\n    \n  \nwindow.navmet.push({key:'Logo',end:+new Date(),begin:window.navmet.tmp});\n        \n\n    \n        \n            \n            \n                \n                   Deliver to\n                \n                \n                   Singapore\n                \n            \n        \n        \n        \n        \n        \n\n\n\n\n\n      \n          \n            window.navmet.tmp=+new Date();\n\n  \n  \n\n    \n      \n        \n  \n    \n      All\n      \n    \n    Select the department you want to search in\n    \n        All Departments\n        Arts & Crafts\n        Automotive\n        Baby\n        Beauty & Personal Care\n        Books\n        Boys' Fashion\n        Computers\n        Deals\n        Digital Music\n        Electronics\n        Girls' Fashion\n        Health & Household\n        Home & Kitchen\n        Industrial & Scientific\n        Kindle Store\n        Luggage\n        Men's Fashion\n        Movies & TV\n        Music, CDs & Vinyl\n        Pet Supplies\n        Prime Video\n        Software\n        Sports & Outdoors\n        Tools & Home Improvement\n        Toys & Games\n        Video Games\n        Women's Fashion\n    \n  \n\n      \n    \n    \n      \n        Search Amazon\n        \n      \n      \n    \n    \n      \n        \n          \n        \n      \n    \n  \n\nwindow.navmet.push({key:'Search',end:+new Date(),begin:window.navmet.tmp});\n          \n      \n          window.navmet.tmp=+new Date();\n          \n              \n              \n              \n              \n  \n    \n      \n      \n      \n        \n          EN\n        \n      \n    \n  \n\n              \n  \n  Hello, sign in\n  Account & Lists\n  \n\n\n              \n\n  Returns\n  & Orders\n\n\n              \n              \n  \n    \n      0\n      \n    \n    \n      \n        \n      \n      \n        Cart\n        \n      \n    \n  \n\n          \n          window.navmet.push({key:'Tools',end:+new Date(),begin:window.navmet.tmp});\n\n      \n    Sign inNew customer? Start here.Your ListsCreate a List Find a List or RegistryYour AccountAccount Orders Recommendations Browsing History Watchlist Video Purchases & Rentals Kindle Unlimited Content & Devices Subscribe & Save Items Memberships & Subscriptions Music LibrarySign inNew customer? Start here.\n    \n      \n        window.navmet.tmp=+new Date();\n  \n    \n    All\n  \n  \n\n  var hmenu = document.getElementById(\"nav-hamburger-menu\");\n  hmenu.setAttribute(\"href\", \"javascript: void(0)\");\n  window.navHamburgerMetricLogger = function() {\n    if (window.ue && window.ue.count) {\n      var metricName = \"Nav:Hmenu:IconClickActionPending\";\n      window.ue.count(metricName, (ue.count(metricName) || 0) + 1);\n    }\n    window.$Nav && $Nav.declare(\"navHMenuIconClicked\",!0);\n    window.$Nav && $Nav.declare(\"navHMenuIconClickedNotReadyTimeStamp\", Date.now());\n  };\n  hmenu.addEventListener(\"click\", window.navHamburgerMetricLogger);\n  window.$Nav && $Nav.declare('hamburgerMenuIconAvailableOnLoad', false);\n  \nwindow.navmet.push({key:'HamburgerMenuIcon',end:+new Date(),begin:window.navmet.tmp});\n      \n      \n        \n \n \n        \n          \n            window.navmet.tmp=+new Date();\nToday's Deals\n\nCustomer Service\n\nRegistry\n\nGift Cards\n\nSell\n\nDisability Customer Support\nwindow.navmet.push({key:'CrossShop',end:+new Date(),begin:window.navmet.tmp});\n          \n        \n      \n      \n        window.navmet.tmp=+new Date();\n\n  \nwindow.navmet.push({key:'SWM',end:+new Date(),begin:window.navmet.tmp});\n      \n    \n\n    \n\n    \n    \n      window.navmet.tmp=+new Date();\n\n\n  \n    \n      Appstore for Android\n      \n    \n    \n  \n  \n    \n      Amazon Coins\n      \n    \n    \n  \n  \n    \n      Fire Tablet Apps\n      \n    \n  \n  \n    \n      Fire TV Apps\n      \n    \n  \n  \n    \n      Games\n      \n    \n  \n  \n    \n      Your Apps & Subscriptions\n      \n    \n  \n  \n    \n      Help\n      \n    \n  \n\n\n\nwindow.navmet.push({key:'Subnav',end:+new Date(),begin:window.navmet.tmp});\n    \n\n    \n(function() {\n  var viewportWidth = function() {\n    return window.innerWidth ||\n      document.documentElement.clientWidth ||\n      document.body.clientWidth;\n  };\n\n  if (typeof uet === 'function') {  uet('x1', 'ewc', {wb: 1}); }\n\n  window.$Nav && $Nav.declare('config.ewc', (function() {\n    var config = {\"enablePersistent\":true,\"viewportWidthForPersistent\":1400,\"isEWCLogging\":1,\"isEWCStateExpanded\":true,\"EWCStateReason\":\"fixed\",\"isSmallScreenEnabled\":true,\"isFreshCustomer\":false,\"errorContent\":{\"html\":\"<div class='nav-ewc-error'><span class='nav-title'>Oops!</span><p class='nav-paragraph'>There's a problem loading your cart right now.</p><a href='/gp/cart/view.html?ref_=nav_err_ewc_timeout' class='nav-action-button'><span class='nav-action-inner'>Your Cart</span></a></div>\"},\"url\":\"/cart/ewc/compact?hostPageType=MASDetailPage&hostSubPageType=null&hostPageRID=K1JQ9VEP43RRWKW775V5&prerender=0&storeName=mobile-apps\",\"cartCount\":0,\"freshCartCount\":0,\"almCartCount\":0,\"primeWardrobeCartCount\":0,\"isCompactViewEnabled\":true,\"isCompactEWCRendered\":true,\"isWiderCompactEWCRendered\":true,\"EWCBrowserCacheKey\":\"EWC_Cache_134-1570085-9450222__USD_en_US\",\"isContentRepainted\":false,\"clearCache\":false,\"loadFromCacheWithDelay\":0,\"delayRenderingTillATF\":false};\n    var hasAui = window.P && window.P.AUI_BUILD_DATE;\n    var isRTLEnabled = (document.dir === 'rtl');\n    config.pinnable = config.pinnable && hasAui;\n    config.isMigrationTreatment = true;\n\n    config.flyout = (function() {\n      var navbelt = document.getElementById('nav-belt');\n      var navCart = document.getElementById('nav-cart');\n      var ewcFlyout = document.getElementById('nav-flyout-ewc');\n      var persistentClassOnBody = 'nav-ewc-persistent-hover nav-ewc-full-height-persistent-hover';\n      var flyout = {};\n\n      var getDocumentScrollTop = function() {\n        return (document.documentElement && document.documentElement.scrollTop) || document.body.scrollTop;\n      };\n\n      var isWindow = function(obj) {\n        return obj != null && obj === obj.window;\n      };\n\n      var getWindow = function(elem) {\n        return isWindow(elem) ? elem : elem.nodeType === 9 && elem.defaultView;\n      };\n\n      var getOffset = function(elem) {\n        if (elem.getClientRects && !elem.getClientRects().length) {\n          return {top: 0};\n        }\n\n        var rect = elem.getBoundingClientRect\n          ? elem.getBoundingClientRect()\n          : {top: 0};\n\n        if (rect.width || rect.height) {\n          var doc = elem.ownerDocument;\n          var win = getWindow(doc);\n          return {\n            top: rect.top + win.pageYOffset - doc.documentElement.clientTop\n          };\n        }\n        return rect;\n      };\n\n      flyout.align = function() {\n        var newTop = getOffset(navbelt).top - getDocumentScrollTop();\n        ewcFlyout.style.top = (newTop > 0 ? newTop + 'px' : 0);\n      };\n\n      flyout.hide = function() {\n        isRTLEnabled\n          ? (ewcFlyout.style.left = '')\n          : (ewcFlyout.style.right = '');\n      };\n\n      if(typeof config.isCompactEWCRendered === 'undefined') {\n        if (\n          (config.isSmallScreenEnabled && viewportWidth() < 1400) ||\n          (config.isCompactViewEnabled && viewportWidth() >= 1400)\n        ) {\n          config.isCompactEWCRendered = true;\n          config.isEWCStateExpanded = true;\n          config.url = config.url.replace(\"/gp/navcart/sidebar\", \"/cart/ewc/compact\");\n        } else {\n          config.isCompactEWCRendered = false;\n        }\n      }\n\n      var viewportQualifyForPersistent = function () {\n        return (config.isCompactEWCRendered)\n          ? true\n          : viewportWidth() >= 1400;\n      }\n\n      flyout.hasQualifiedViewportForPersistent = viewportQualifyForPersistent;\n\n      var getEWCRightOffset = function() {\n        if (!config.isCompactEWCRendered) {\n          return 0;\n        }\n\n        var $navbelt = document.getElementById('nav-belt');\n        if ($navbelt === undefined || $navbelt === null) {\n          return 0;\n        }\n\n        var EWCCompactViewWidth = (config.isWiderCompactEWCRendered  && viewportWidth() >= 1280) ? 130 : 100;\n        var scrollLeft = (window.pageXOffset !== undefined)\n          ? window.pageXOffset\n          : (document.documentElement || document.body.parentNode || document.body).scrollLeft;\n        var scrollXAxis = Math.abs(scrollLeft);\n        var windowWidth = document.documentElement.clientWidth;\n        var navbeltWidth = $navbelt.offsetWidth;\n        var isPartOfNavbarNotVisible = (navbeltWidth + EWCCompactViewWidth) > windowWidth;\n\n        if (isPartOfNavbarNotVisible) {\n          return 0 - (navbeltWidth - scrollXAxis - windowWidth + EWCCompactViewWidth);\n        } else {\n          return 0;\n        }\n      }\n\n      flyout.getEWCRightOffsetCssProperty = function () {\n        return getEWCRightOffset() + 'px';\n      }\n\n      if (config.isCompactEWCRendered) {\n        persistentClassOnBody = 'nav-ewc-persistent-hover nav-ewc-compact-view';\n        if (config.isWiderCompactEWCRendered) { persistentClassOnBody += ' nav-ewc-wider-compact-view'; }\n      }\n\n      flyout.show = function() {\n        isRTLEnabled\n          ? (ewcFlyout.style.left = flyout.getEWCRightOffsetCssProperty())\n          : (ewcFlyout.style.right = flyout.getEWCRightOffsetCssProperty());\n      };\n\n      var isIOSDevice = function() {\n        return (/iPad|iPhone|iPod/.test(navigator.platform) ||\n                (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1)) &&\n                !window.MSStream;\n      }\n\n      var checkForPersistent = function() {\n        if (!hasAui) {\n          return { result: false, reason: 'noAui' };\n        }\n        if (!config.enablePersistent) {\n          return { result: false, reason: 'config' };\n        }\n        if (!viewportQualifyForPersistent()) {\n          return { result: false, reason: 'viewport' };\n        }\n        if (isIOSDevice()) {\n          return { result: false, reason: 'iOS' };\n        }\n        if (!config.cartCount > 0) {\n          return { result: false, reason: 'emptycart' };\n        }\n        return { result: true };\n      };\n\n      flyout.ableToPersist = function() {\n        return checkForPersistent().result;\n      };\n      var persistentClassRegExp = '(?:^|\\\\s)' + persistentClassOnBody + '(?!\\\\S)';\n      flyout.applyPageLayoutForPersistent = function() {\n        if (!document.documentElement.className.match( new RegExp(persistentClassRegExp) )) {\n          document.documentElement.className += ' ' + persistentClassOnBody;\n        }\n      };\n\n      flyout.unapplyPageLayoutForPersistent = function() {\n        document.documentElement.className = document.documentElement.className.replace( new RegExp(persistentClassRegExp, 'g'), '');\n      };\n\n      flyout.persist = function() {\n        flyout.applyPageLayoutForPersistent();\n        flyout.show();\n        if (config.isCompactEWCRendered) {\n          flyout.align();\n        }\n      };\n\n      flyout.unpersist = function() {\n        flyout.unapplyPageLayoutForPersistent();\n        flyout.hide();\n      };\n      \n      var persistentCheck = checkForPersistent();\n    \n\n      var resizeCallback = function() {\n        \n        if (flyout.ableToPersist()) {\n          flyout.persist();\n        }\n        else {\n          flyout.unpersist();\n        }\n      };\n\n      flyout.bindEvents = function() {\n        if (window.addEventListener) {\n          window.addEventListener('resize', resizeCallback, false);\n          \n          if (config.isCompactEWCRendered) {\n            window.addEventListener('scroll', flyout.align, false);\n          }\n        }\n      };\n\n      flyout.unbindEvents = function() {\n        if (window.removeEventListener) {\n          window.removeEventListener('resize', resizeCallback, false);\n          \n          if (config.isCompactEWCRendered) {\n            window.removeEventListener('scroll', flyout.align, false);\n          }\n        }\n      };\n      \n      var ewcDefaultPersistence = function() {\n      \n        if (persistentCheck.result) {\n          flyout.persist();\n          if (window.ue && ue.tag) {\n            ue.tag('ewc:persist');\n          }\n        } else {\n          if (window.ue && ue.tag) {\n            ue.tag('ewc:unpersist');\n            if (persistentCheck.reason === 'noAui') {\n              ue.tag('ewc:unpersist:noAui');\n            }\n            if (persistentCheck.reason === 'viewport') {\n              ue.tag('ewc:unpersist:viewport');\n            }\n            if (persistentCheck.reason === 'emptycart') {\n              ue.tag('ewc:unpersist:emptycart');\n            }\n            if (persistentCheck.reason === 'iOS') {\n              ue.tag('ewc:unpersist:iOS');\n            }\n          }\n        }\n      };\n      \n      ewcDefaultPersistence();\n      \n      if (window.ue && ue.tag)  {\n        if (flyout.hasQualifiedViewportForPersistent()) {\n          ue.tag('ewc:bview');\n        }\n        else {\n          ue.tag('ewc:sview');\n        }\n      }\n      flyout.bindEvents();\n      flyout.cache = function () {\n    const cache = window.sessionStorage;\n    const CACHE_KEY = \"EWCBrowserCacheKey\";\n    const CACHE_EXPIRY = \"EWCBrowserCacheExpiry\"; \n    const CACHE_VALUE = \"EWCBrowserCacheValue\"; \n    const isSessionStorageValid = function () {\n        return window && cache && cache instanceof Storage;\n    };\n    const isCachePresent = function (key) {\n        return cache.length > 0 && cache.getItem(key);\n    }\n    const isValidType = function (value) {\n        // Prevents accessing empty key-value and internal methods(prototypes) of storage\n        // TODO: Log metrics for invalid access;\n        return value && value.constructor == String;\n    }\n    return {\n        getCache: function (key) {\n            const value = isCachePresent(key);\n            return (isValidType(value)) ? value : null;\n        },\n        setCache: function (key, value) {\n            const oldValue = isCachePresent(key);\n            const cacheExpiryTime = isCachePresent(CACHE_EXPIRY);\n            // Set the expiry when there's no existing cache - to prevent resetting expiry on page navigation\n            if (!cacheExpiryTime) {\n                var currentTime = new Date();\n                cache.setItem(CACHE_EXPIRY, new Date(currentTime.getTime() + 5 * 60000))\n            }\n            // TODO: Log length of old and new cache values when logMetrics is true\n            cache.setItem(key, value);\n        },\n        updateCacheAndEwcContainer: function (cacheKey, newEwcContent) {\n            const $ = $Nav.getNow(\"$\");\n            const $currentEwc = $(\"#ewc-content\");\n            if (!$currentEwc.length) {\n                var $content = $('#nav-flyout-ewc .nav-ewc-content');\n                $content.html(newEwcContent);\n                this.setCache(CACHE_KEY, cacheKey);\n                if (window.ue && window.ue.count) {\n                    var current = window.ue.count(\"ewc-init-cache\") || 0;\n                    window.ue.count(\"ewc-init-cache\", current + 1);\n                }\n            } else {\n                var $newEwcContent = $('<div />');\n                var EWC_CONTENT_BODY_SCROLL_SELECTOR = \".ewc-scroller--selected\";\n                if (newEwcContent) { // 1. Updates EWC container with new HTML \n                    const $newEwcHtml = $newEwcContent.html(newEwcContent).find(\"#ewc-content\");\n                    const offSet = $currentEwc.find(EWC_CONTENT_BODY_SCROLL_SELECTOR).position().top - $currentEwc.find(\".ewc-active-cart--selected\").position().top;\n                    $currentEwc.html($newEwcHtml.html());\n                    $currentEwc.find(EWC_CONTENT_BODY_SCROLL_SELECTOR).scrollTop(offSet);\n                    if (typeof window.uex === 'function') {\n                        window.uex('ld', 'ewc-reflect-new-state', {wb: 1});\n                    }\n                } else {\n                    // 2. Fetches cached response and updates it's html with new state on EWC Update\n                    const cachedEwc = this.getCache(CACHE_VALUE);\n                    $newEwcContent = $newEwcContent[0];\n                    $(cachedEwc).map(function (elementIndex, element) {\n                         $newEwcContent.appendChild((element.id === \"ewc-content\") ? $currentEwc.clone()[0] : element);\n                    });\n                    newEwcContent = $newEwcContent.innerHTML;\n                    if (window.ue && window.ue.count) {\n                        var current = window.ue.count(\"ewc-update-cache\") || 0;\n                        window.ue.count(\"ewc-update-cache\", current + 1);\n                    }\n                }\n                $newEwcContent.remove();\n            }\n            this.setCache(CACHE_VALUE, newEwcContent);\n        },\n        removeCache: function (key) {\n            return cache.removeItem(key);\n        }\n    }\n}\n;\n      return flyout;\n    }());\n     \n     \n     \nconst CACHE_KEY = \"EWCBrowserCacheKey\";\nconst CACHE_VALUE = \"EWCBrowserCacheValue\"; \nconst CACHE_EXPIRY = \"EWCBrowserCacheExpiry\"; \nvar cache = config.flyout.cache();\n\nconst isCacheValid = function () {\n  // Check for page types and tenure of the cache\n  const clearCache = config.clearCache;\n  const cacheExpiryTime = cache.getCache(CACHE_EXPIRY);\n  const isCacheExpired = new Date() > new Date(cacheExpiryTime);\n  const cacheKey = config.EWCBrowserCacheKey;\n  const oldCacheKey = cache.getCache(CACHE_KEY);\n  const isCacheValid = !clearCache && !isCacheExpired && cacheKey == oldCacheKey;\n  if (!isCacheValid && window.ue && window.ue.count) {\n    var current = window.ue.count(\"ewc-cache-invalidated\") || 0;\n    window.ue.count(\"ewc-cache-invalidated\", current + 1);\n  }\n  return isCacheValid;\n}\nfunction loadFromCache() {\n    if (window.uet && typeof window.uet === 'function') {\n        window.uet('bb', 'ewc-loaded-from-cache', {wb: 1});\n    }\n    if (cache) {\n        if (isCacheValid()) {\n            var content = cache.getCache(CACHE_VALUE);\n            if (content) {\n                var $ewcContainer = document.getElementById(\"nav-flyout-ewc\").getElementsByClassName(\"nav-ewc-content\")[0];\n                var $ewcContent = document.getElementById(\"ewc-content\");\n                if ($ewcContainer && !$ewcContent) {\n                    $ewcContainer.innerHTML = content;\n                    // Execute scripts from cache\n                    const ewcJavascript = document.getElementById(\"ewc-content\").parentNode.querySelectorAll(':scope > script');\n                    ewcJavascript.forEach(function (script) {\n                        var scriptTag = document.createElement(\"script\");\n                        scriptTag.innerHTML = script.innerHTML;\n                        document.body.appendChild(scriptTag);\n                    });\n                    if (typeof window.uex === 'function') {\n                        window.uex('ld', 'ewc-loaded-from-cache', {wb: 1});\n                    }\n                } else if (window.ue && window.ue.count && typeof window.ue.count === 'function') {\n                    var currentFailure = window.ue.count(\"ewc-slow-cache\") || 0;\n                    window.ue.count(\"ewc-slow-cache\", currentFailure + 1);\n                }\n            }\n        } else {\n            cache.removeCache(CACHE_VALUE);\n            cache.removeCache(CACHE_KEY);\n            cache.removeCache(CACHE_EXPIRY);\n        }\n    }\n}\nfunction delayBy(delayTime) {\n    if (delayTime) {\n        window.setTimeout(function() {\n            loadFromCache();\n        }, delayTime)\n    } else {\n        loadFromCache();\n    }\n}\nif(config.delayRenderingTillATF) {\n    (window.AmazonUIPageJS ? AmazonUIPageJS : P).when('atf').execute(\"EverywhereCartLoadFromCacheOnAtf\", function () {\n        delayBy(config.loadFromCacheWithDelay);\n    });\n} else {\n    delayBy(config.loadFromCacheWithDelay);\n}\n\n    return config;\n  }()));\n\n  if (typeof uet === 'function') {\n    uet('x2', 'ewc', {wb: 1});\n  }\n\n  if (window.ue && ue.tag) {\n    ue.tag('ewc');\n    ue.tag('ewc:unrec');\n    ue.tag('ewc:cartsize:0');\n\n    if ( window.P && window.P.AUI_BUILD_DATE ) {\n      ue.tag('ewc:aui');\n    } else {\n      ue.tag('ewc:noAui');\n    }\n  }\n}());\n\n   \n\n  \n  \n\n\n\n\nwindow.navmet.push({key:'NavBar',end:+new Date(),begin:window.navmet.main});\n\n\n\n  if (window.ue_t0) {\n    window.navmet.push({key:\"NavMainPaintEnd\",end:+new Date(),begin:window.ue_t0});\n    window.navmet.push({key:\"NavFirstPaintEnd\",end:+new Date(),begin:window.ue_t0});\n  }\n\n\n\n\n    <!--\n    \n    window.$Nav && $Nav.when(\"data\").run(function(data) { data({\"freshTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"title\":\"<style>#nav-flyout-fresh{width:269px;padding:0;}#nav-flyout-fresh .nav-flyout-content{padding:0;}</style><a href='/amazonfresh'><img src='https://images-na.ssl-images-amazon.com/images/G/01/omaha/images/yoda/flyout_72dpi._V270255989_.png' /></a>\"}}}},\"cartTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"button\":{\"text\":\"Your Cart\",\"url\":\"/gp/cart/view.html?ref_=nav_err_cart_timeout\"},\"title\":\"Oops!\",\"paragraph\":\"Unable to retrieve your cart.\"}}}},\"primeTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"title\":\"<a href='/gp/prime'><img src='https://images-na.ssl-images-amazon.com/images/G/01/prime/piv/YourPrimePIV_fallback_CTA._V327346943_.jpg' /></a>\"}}}},\"ewcTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"button\":{\"text\":\"Your Cart\",\"url\":\"/gp/cart/view.html?ref_=nav_err_ewc_timeout\"},\"title\":\"Oops!\",\"paragraph\":\"There's a problem loading your cart right now.\"}}}},\"errorWishlist\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"button\":{\"text\":\"Your Wishlist\",\"url\":\"/gp/registry/wishlist/?ref_=nav_err_wishlist\"},\"title\":\"Oops!\",\"paragraph\":\"Unable to retrieve your wishlist\"}}}},\"emptyWishlist\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"button\":{\"text\":\"Your Wishlist\",\"url\":\"/gp/registry/wishlist/?ref_=nav_err_empty_wishlist\"},\"title\":\"Oops!\",\"paragraph\":\"Your list is empty\"}}}},\"yourAccountContent\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"button\":{\"text\":\"Your Account\",\"url\":\"/gp/css/homepage.html?ref_=nav_err_youraccount\"},\"title\":\"Oops!\",\"paragraph\":\"Unable to retrieve your account\"}}}},\"shopAllTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"paragraph\":\"Unable to retrieve departments, please try again later\"}}}},\"kindleTimeout\":{\"template\":{\"name\":\"flyoutError\",\"data\":{\"error\":{\"paragraph\":\"Unable to retrieve list, please try again later\"}}}}}); });\nwindow.$Nav && $Nav.when(\"util.templates\").run(\"FlyoutErrorTemplate\", function(templates) {\n      templates.add(\"flyoutError\", \"<# if(error.title) { #><span class='nav-title'><#=error.title #></span><# } #><# if(error.paragraph) { #><p class='nav-paragraph'><#=error.paragraph #></p><# } #><# if(error.button) { #><a href='<#=error.button.url #>' class='nav-action-button' ><span class='nav-action-inner'><#=error.button.text #></span></a><# } #>\");\n    });\n\n    if (typeof uet == 'function') {\n    uet('bb', 'iss-init-pc', {wb: 1});\n  }\n  if (!window.$SearchJS && window.$Nav) {\n    window.$SearchJS = $Nav.make('sx');\n  }\n\n  var opts = {\n    host: \"completion.amazon.com/search/complete\"\n  , marketId: \"1\"\n  , obfuscatedMarketId: \"ATVPDKIKX0DER\"\n  , searchAliases: [\"aps\",\"amazon-custom-products\",\"amazon-devices\",\"amazonbasics\",\"amazonfresh\",\"amazon-pharmacy\",\"wholefoods\",\"allthebestpets\",\"bartelldrugs\",\"bristolfarms\",\"cardenas\",\"familyfare\",\"freshthyme\",\"kegnbottle\",\"missionwinespirits\",\"petfoodexpress\",\"savemart\",\"sousaswineliquors\",\"surdyksliquorcheeseshop\",\"weis\",\"stripbooks\",\"popular\",\"apparel\",\"electronics\",\"sporting\",\"sports-and-fitness\",\"outdoor-recreation\",\"fan-shop\",\"garden\",\"videogames\",\"toys-and-games\",\"jewelry\",\"digital-text\",\"digital-music\",\"prime-digital-music\",\"watches\",\"grocery\",\"hpc\",\"instant-video\",\"handmade\",\"handmade-jewelry\",\"handmade-home-and-kitchen\",\"prime-instant-video\",\"shop-instant-video\",\"baby-products\",\"office-products\",\"software\",\"smart-home\",\"magazines\",\"tools\",\"automotive\",\"misc\",\"industrial\",\"mi\",\"pet-supplies\",\"digital-music-track\",\"digital-music-album\",\"mobile\",\"mobile-apps\",\"movies-tv\",\"music-artist\",\"music-album\",\"music-song\",\"stripbooks-spanish\",\"electronics-accessories\",\"photo\",\"audio-video\",\"computers\",\"furniture\",\"kitchen\",\"audible\",\"audiobooks\",\"beauty\",\"shoes\",\"arts-crafts\",\"appliances\",\"gift-cards\",\"pets\",\"outdoor\",\"lawngarden\",\"collectibles\",\"replacement-parts\",\"financial\",\"fine-art\",\"fashion\",\"fashion-womens\",\"fashion-womens-clothing\",\"fashion-womens-jewelry\",\"fashion-womens-shoes\",\"fashion-womens-watches\",\"fashion-womens-handbags\",\"fashion-mens\",\"fashion-mens-clothing\",\"fashion-mens-jewelry\",\"fashion-mens-shoes\",\"fashion-mens-watches\",\"fashion-girls\",\"fashion-girls-clothing\",\"fashion-girls-jewelry\",\"fashion-girls-shoes\",\"fashion-girls-watches\",\"fashion-boys\",\"fashion-boys-clothing\",\"fashion-boys-jewelry\",\"fashion-boys-shoes\",\"fashion-boys-watches\",\"fashion-baby\",\"fashion-baby-boys\",\"fashion-baby-girls\",\"fashion-luggage\",\"3d-printing\",\"tradein-aps\",\"todays-deals\",\"live-explorations\",\"local-services\",\"vehicles\",\"video-shorts\",\"warehouse-deals\",\"luxury-beauty\",\"banjo-apps\",\"black-friday\",\"cyber-monday\",\"alexa-skills\",\"subscribe-with-amazon\",\"courses\",\"edu-alt-content\",\"amazon-global-store\",\"prime-wardrobe\",\"under-ten-dollars\",\"tempo\",\"specialty-aps-sns\",\"luxury\"]\n  , filterAliases: []\n  , pageType: \"MASDetailPage\"\n  , requestId: \"K1JQ9VEP43RRWKW775V5\"\n  , sessionId: \"134-1570085-9450222\"\n  , language: \"en_US\"\n  , customerId: \"\"\n  , asin: \"\"\n  , b2b: 0\n  , fresh: 0\n  , isJpOrCn: 0\n  , isUseAuiIss: 1\n};\n\nvar issOpts = {\n    fallbackFlag: 1\n  , isDigitalFeaturesEnabled: 0\n  , isWayfindingEnabled: 1\n  , dropdown: \"select.searchSelect\"\n  , departmentText: \"in {department}\"\n  , suggestionText: \"Search suggestions\"\n  , recentSearchesTreatment: \"C\"\n  , authorSuggestionText: \"Explore books by XXAUTHXX\"\n  , translatedStringsMap: {\"sx-recent-searches\":\"Recent searches\",\"sx-your-recent-search\":\"Inspired by your recent search\"}\n  , biaTitleText: \"\"\n  , biaPurchasedText: \"\"\n  , biaViewAllText: \"\"\n  , biaViewAllManageText: \"\"\n  , biaAndText: \"\"\n  , biaManageText: \"\"\n  , biaWeblabTreatment: \"\"\n  , issNavConfig: {}\n  , np: 0\n  , issCorpus: []\n  , cf: 1\n  , removeDeepNodeISS: \"\"\n  , trendingTreatment: \"C\"\n  , useAPIV2: \"\"\n  , opfSwitch: \"\"\n  , isISSDesktopRefactorEnabled: \"1\"\n  , useServiceHighlighting: \"true\"\n  , isInternal: 0\n  , isAPICachingDisabled: true\n  , isBrowseNodeScopingEnabled: false\n  , isStorefrontTemplateEnabled: false\n  , disableAutocompleteOnFocus: \"\"\n};\n\n  if (opts.isUseAuiIss === 1 && window.$Nav) {\n  window.$Nav.when('sx.iss').run('iss-mason-init', function(iss){\n    var issInitObj = buildIssInitObject(opts, issOpts, true);\n    new iss.IssParentCoordinator(issInitObj);\n\n    $SearchJS.declare('canCreateAutocomplete', issInitObj);\n  });\n} else if (window.$SearchJS) {\n  var iss;\n\n  // BEGIN Deprecated globals\n  var issHost = opts.host\n    , issMktid = opts.marketId\n    , issSearchAliases = opts.searchAliases\n    , updateISSCompletion = function() { iss.updateAutoCompletion(); };\n  // END deprecated globals\n\n\n  $SearchJS.when('jQuery', 'search-js-autocomplete-lib').run('autocomplete-init', initializeAutocomplete);\n  $SearchJS.when('canCreateAutocomplete').run('createAutocomplete', createAutocomplete);\n\n} // END conditional for window.$SearchJS\n  function initializeAutocomplete(jQuery) {\n  var issInitObj = buildIssInitObject(opts, issOpts);\n  $SearchJS.declare(\"canCreateAutocomplete\", issInitObj);\n} // END initializeAutocomplete\n  function initSearchCsl(searchCSL, issInitObject) {\n  searchCSL.init(\n    opts.pageType,\n    (window.ue && window.ue.rid) || opts.requestId\n  );\n  $SearchJS.declare(\"canCreateAutocomplete\", issInitObject);\n} // END initSearchCsl\n  function createAutocomplete(issObject) {\n  iss = new AutoComplete(issObject);\n\n  $SearchJS.publish(\"search-js-autocomplete\", iss);\n\n  logMetrics();\n} // END createAutocomplete\n  function buildIssInitObject(opts, issOpts, isNewIss) {\n    var issInitObj = {\n        src: opts.host\n      , sessionId: opts.sessionId\n      , requestId: opts.requestId\n      , mkt: opts.marketId\n      , obfMkt: opts.obfuscatedMarketId\n      , pageType: opts.pageType\n      , language: opts.language\n      , customerId: opts.customerId\n      , fresh: opts.fresh\n      , b2b: opts.b2b\n      , aliases: opts.searchAliases\n      , fb: issOpts.fallbackFlag\n      , isDigitalFeaturesEnabled: issOpts.isDigitalFeaturesEnabled\n      , isWayfindingEnabled: issOpts.isWayfindingEnabled\n      , issPrimeEligible: issOpts.issPrimeEligible\n      , deptText: issOpts.departmentText\n      , sugText: issOpts.suggestionText\n      , filterAliases: opts.filterAliases\n      , biaWidgetUrl: opts.biaWidgetUrl\n      , recentSearchesTreatment: issOpts.recentSearchesTreatment\n      , authorSuggestionText: issOpts.authorSuggestionText\n      , translatedStringsMap: issOpts.translatedStringsMap\n      , biaTitleText: \"\"\n      , biaPurchasedText: \"\"\n      , biaViewAllText: \"\"\n      , biaViewAllManageText: \"\"\n      , biaAndText: \"\"\n      , biaManageText: \"\"\n      , biaWeblabTreatment: \"\"\n      , issNavConfig: issOpts.issNavConfig\n      , cf: issOpts.cf\n      , ime: opts.isJpOrCn\n      , mktid: opts.marketId\n      , qs: opts.isJpOrCn\n      , issCorpus: issOpts.issCorpus\n      , deepNodeISS: {\n          searchAliasAccessor: function($) {\n            return (window.SearchPageAccess && window.SearchPageAccess.searchAlias()) ||\n                   $('select.searchSelect').children().attr('data-root-alias');\n          },\n          searchAliasDisplayNameAccessor: function() {\n            return (window.SearchPageAccess && window.SearchPageAccess.searchAliasDisplayName());\n          }\n        }\n      , removeDeepNodeISS: issOpts.removeDeepNodeISS\n      , trendingTreatment: issOpts.trendingTreatment\n      , useAPIV2: issOpts.useAPIV2\n      , opfSwitch: issOpts.opfSwitch\n      , isISSDesktopRefactorEnabled: issOpts.isISSDesktopRefactorEnabled\n      , useServiceHighlighting: issOpts.useServiceHighlighting\n      , isInternal: issOpts.isInternal\n      , isAPICachingDisabled: issOpts.isAPICachingDisabled\n      , isBrowseNodeScopingEnabled: issOpts.isBrowseNodeScopingEnabled\n      , isStorefrontTemplateEnabled: issOpts.isStorefrontTemplateEnabled\n      , disableAutocompleteOnFocus: issOpts.disableAutocompleteOnFocus\n      , asin: opts.asin\n    };\n  \n    // If we aren't using the new ISS then we need to add these properties\n    \n    if (!isNewIss) {\n      issInitObj.dd = issOpts.dropdown; // The element with id searchDropdownBox doesn't exist in C.\n      issInitObj.imeSpacing = issOpts.imeSpacing;\n      issInitObj.isNavInline = 1;\n      issInitObj.triggerISSOnClick = 0;\n      issInitObj.sc = 1;\n      issInitObj.np = issOpts.np;\n    }\n  \n    return issInitObj;\n  } // END buildIssInitObject\n  function logMetrics() {\n  if (typeof uet == 'function' && typeof uex == 'function') {\n      uet('be', 'iss-init-pc',\n          {\n              wb: 1\n          });\n      uex('ld', 'iss-init-pc',\n          {\n              wb: 1\n          });\n  }\n} // END logMetrics\n  \n    \nwindow.$Nav && $Nav.declare('config.navDeviceType','desktop');\n\nwindow.$Nav && $Nav.declare('config.navDebugHighres',false);\n\nwindow.$Nav && $Nav.declare('config.pageType','MASDetailPage');\nwindow.$Nav && $Nav.declare('config.subPageType','null');\n\nwindow.$Nav && $Nav.declare('config.dynamicMenuUrl','\\x2Fgp\\x2Fnavigation\\x2Fajax\\x2Fdynamic\\x2Dmenu.html');\n\nwindow.$Nav && $Nav.declare('config.dismissNotificationUrl','\\x2Fgp\\x2Fnavigation\\x2Fajax\\x2Fdismissnotification.html');\n\nwindow.$Nav && $Nav.declare('config.enableDynamicMenus',true);\n\nwindow.$Nav && $Nav.declare('config.isInternal',false);\n\nwindow.$Nav && $Nav.declare('config.isBackup',false);\n\nwindow.$Nav && $Nav.declare('config.isRecognized',false);\n\nwindow.$Nav && $Nav.declare('config.transientFlyoutTrigger','\\x23nav\\x2Dtransient\\x2Dflyout\\x2Dtrigger');\n\nwindow.$Nav && $Nav.declare('config.subnavFlyoutUrl','\\x2Fnav\\x2Fajax\\x2FsubnavFlyout');\nwindow.$Nav && $Nav.declare('config.isSubnavFlyoutMigrationEnabled',true);\n\nwindow.$Nav && $Nav.declare('config.recordEvUrl','\\x2Fgp\\x2Fnavigation\\x2Fajax\\x2Frecordevent.html');\nwindow.$Nav && $Nav.declare('config.recordEvInterval',15000);\nwindow.$Nav && $Nav.declare('config.sessionId','134\\x2D1570085\\x2D9450222');\nwindow.$Nav && $Nav.declare('config.requestId','K1JQ9VEP43RRWKW775V5');\n\nwindow.$Nav && $Nav.declare('config.alexaListEnabled',true);\n\nwindow.$Nav && $Nav.declare('config.readyOnATF',false);\n\nwindow.$Nav && $Nav.declare('config.dynamicMenuArgs',{\"rid\":\"K1JQ9VEP43RRWKW775V5\",\"isFullWidthPrime\":0,\"isPrime\":0,\"dynamicRequest\":1,\"weblabs\":\"\",\"isFreshRegionAndCustomer\":\"\",\"primeMenuWidth\":310});\n\nwindow.$Nav && $Nav.declare('config.customerName',false);\n\nwindow.$Nav && $Nav.declare('config.customerCountryCode','SG');\n\nwindow.$Nav && $Nav.declare('config.yourAccountPrimeURL',null);\n\nwindow.$Nav && $Nav.declare('config.yourAccountPrimeHover',true);\n\nwindow.$Nav && $Nav.declare('config.searchBackState',{});\n\nwindow.$Nav && $Nav.declare('nav.inline');\n\n(function (i) {\n  if(window._navbarSpriteUrl) {\n    i.onload = function() {window.uet && uet('ne')};\n    i.src = window._navbarSpriteUrl;\n  }\n}(new Image()));\n\nwindow.$Nav && $Nav.declare('config.autoFocus',false);\n\nwindow.$Nav && $Nav.declare('config.responsiveTouchAgents',[\"ieTouch\"]);\n\nwindow.$Nav && $Nav.declare('config.responsiveGW',false);\n\nwindow.$Nav && $Nav.declare('config.pageHideEnabled',false);\n\nwindow.$Nav && $Nav.declare('config.sslTriggerType','flyoutProximityLarge');\nwindow.$Nav && $Nav.declare('config.sslTriggerRetry',0);\n\nwindow.$Nav && $Nav.declare('config.doubleCart',false);\n\nwindow.$Nav && $Nav.declare('config.signInOverride',true);\n\nwindow.$Nav && $Nav.declare('config.signInTooltip',true);\n\nwindow.$Nav && $Nav.declare('config.isPrimeMember',false);\n\nwindow.$Nav && $Nav.declare('config.packardGlowTooltip',false);\n\nwindow.$Nav && $Nav.declare('config.packardGlowFlyout',false);\n\nwindow.$Nav && $Nav.declare('config.rightMarginAlignEnabled',true);\n\nwindow.$Nav && $Nav.declare('config.flyoutAnimation',false);\n\nwindow.$Nav && $Nav.declare('config.campusActivation','null');\n\nwindow.$Nav && $Nav.declare('config.primeTooltip',false);\n\nwindow.$Nav && $Nav.declare('config.primeDay',false);\n\nwindow.$Nav && $Nav.declare('config.disableBuyItAgain',false);\n\nwindow.$Nav && $Nav.declare('config.enableCrossShopBiaFlyout',false);\n\nwindow.$Nav && $Nav.declare('config.pseudoPrimeFirstBrowse',null);\n\nwindow.$Nav && $Nav.declare('config.sdaYourAccount',false);\n\nwindow.$Nav && $Nav.declare('config.csYourAccount',{\"url\":\"/gp/youraccount/navigation/sidepanel\"});\n\nwindow.$Nav && $Nav.declare('config.cartFlyoutDisabled',true);\n\nwindow.$Nav && $Nav.declare('config.isTabletBrowser',false);\n\nwindow.$Nav && $Nav.declare('config.HmenuProximityArea',[200,200,200,200]);\n\nwindow.$Nav && $Nav.declare('config.HMenuIsProximity',true);\n\nwindow.$Nav && $Nav.declare('config.isPureAjaxALF',false);\n\nwindow.$Nav && $Nav.declare('config.accountListFlyoutRedesign',false);\n\nwindow.$Nav && $Nav.declare('config.navfresh',false);\n\nwindow.$Nav && $Nav.declare('config.isFreshRegion',false);\n\nif (window.ue && ue.tag) { ue.tag('navbar'); };\n\nwindow.$Nav && $Nav.declare('config.blackbelt',true);\n\nwindow.$Nav && $Nav.declare('config.beaconbelt',true);\n\nwindow.$Nav && $Nav.declare('config.accountList',true);\n\nwindow.$Nav && $Nav.declare('config.iPadTablet',false);\n\nwindow.$Nav && $Nav.declare('config.searchapiEndpoint',false);\n\nwindow.$Nav && $Nav.declare('config.timeline',false);\n\nwindow.$Nav && $Nav.declare('config.timelineAsinPriceEnabled',false);\n\nwindow.$Nav && $Nav.declare('config.timelineDeleteEnabled',false);\n\n\n\nwindow.$Nav && $Nav.declare('config.extendedFlyout',false);\n\nwindow.$Nav && $Nav.declare('config.flyoutCloseDelay',600);\n\nwindow.$Nav && $Nav.declare('config.pssFlag',0);\n\nwindow.$Nav && $Nav.declare('config.isPrimeTooltipMigrated',false);\n\nwindow.$Nav && $Nav.declare('config.hashCustomerAndSessionId','cb6f45e5ba7a6bd7ebab810301f387e765ca16d5');\n\nwindow.$Nav && $Nav.declare('config.isExportMode',true);\n\nwindow.$Nav && $Nav.declare('config.languageCode','en_US');\n\nwindow.$Nav && $Nav.declare('config.environmentVFI','AmazonNavigationCards\\x2Fdevelopment\\x40B6135401680\\x2DAL2_x86_64');\n\nwindow.$Nav && $Nav.declare('config.isHMenuBrowserCacheDisable',false);\n\nwindow.$Nav && $Nav.declare('config.signInUrlWithRefTag','https\\x3A\\x2F\\x2Fwww.amazon.com\\x2Fap\\x2Fsignin\\x3Fopenid.pape.max_auth_age\\x3D0\\x26openid.return_to\\x3Dhttps\\x253A\\x252F\\x252Fwww.amazon.com\\x252FSmallthing\\x2DEasy\\x2DSpider\\x2DSolitaire\\x252Fdp\\x252FB00G36GT5A\\x252F\\x253F_encoding\\x253DUTF8\\x2526\\x2AVersion\\x2A\\x253D1\\x2526\\x2Aentries\\x2A\\x253D0\\x2526asin\\x253DB00G36GT5A\\x2526ie\\x253DUTF8\\x2526ref_\\x253DnavSignInUrlRefTagPlaceHolder\\x26openid.identity\\x3Dhttp\\x253A\\x252F\\x252Fspecs.openid.net\\x252Fauth\\x252F2.0\\x252Fidentifier_select\\x26openid.assoc_handle\\x3Dusflex\\x26openid.mode\\x3Dcheckid_setup\\x26openid.claimed_id\\x3Dhttp\\x253A\\x252F\\x252Fspecs.openid.net\\x252Fauth\\x252F2.0\\x252Fidentifier_select\\x26openid.ns\\x3Dhttp\\x253A\\x252F\\x252Fspecs.openid.net\\x252Fauth\\x252F2.0');\n\nwindow.$Nav && $Nav.declare('config.regionalStores',[]);\n\nwindow.$Nav && $Nav.declare('config.isALFRedesignPT2',true);\n\nwindow.$Nav && $Nav.declare('config.isNavALFRegistryGiftList',false);\n\nwindow.$Nav && $Nav.declare('config.marketplaceId','ATVPDKIKX0DER');\n\nwindow.$Nav && $Nav.declare('config.exportTransitionState','none');\n\nwindow.$Nav && $Nav.declare('config.enableAeeXopFlyout',false);\n\nwindow.$Nav && $Nav.declare('config.isPrimeFlyoutMigrationEnabled',false);\n\nwindow.$Nav && $Nav.declare('config.isAjaxMigrated',true);\n\nwindow.$Nav && $Nav.declare('config.isAjaxPaymentNotificationMigrated',false);\n\nif (window.P && typeof window.P.declare === \"function\" && typeof window.P.now === \"function\") {\n  window.P.now('packardGlowIngressJsEnabled').execute(function(glowEnabled) {\n    if (!glowEnabled) {\n      window.P.declare('packardGlowIngressJsEnabled', true);\n    }\n  });\n  window.P.now('packardGlowStoreName').execute(function(storeName) {\n    if (!storeName) {\n      window.P.declare('packardGlowStoreName','mobile\\x2Dapps');\n    }\n  });\n}\n\nwindow.$Nav && $Nav.declare('configComplete');\n\n    -->\n\n\n\n\n\nwindow.navmet.MainEnd = new Date();\n\n    if (window.ue_t0) {\n      window.navmet.push({key:\"NavMainEnd\",end:+new Date(),begin:window.ue_t0});\n    }\n\n\n\n\n    {\"AUI_ACCORDION_A11Y_ROLE_354025\":\"T1\"}\n            \n                        \n                                \n                                    \n                                    \n                                \n                            \n                        \n                            \n                                \n                                \n    \n      \n         \n                  Easy Spider SolitaireEasy Spider Solitaire\n             \n    \n    \n      by Smallthing\n\n\n                                 4.1 out of 5 stars17  customer ratings\n                                \n                                \n   Guidance Suggested\n\n\n                            \n                                \n                                    \n                                    \n\n\n\n\n   \n      \n         \n         \n      \n      \n   \n\n\n\n\n\n                                \n                                    \n                                    \n\n\n\n\n\n    \n\n    \n        \n            \n            \n                \n\n\n\n\n    \n        \n    \n    \n    \n\n\n\n    \n        \n            Price:\n        \n\n        \n            \n                \n                    \n                        Free Download\n                    \n                    \n                \n            \n\n            \n        \n    \n\n    \n\n\n            \n        \n\n        \n    \n\n\n                                    \n   Sold by:Amazon.com Services LLC\n\n                                    \n                                    \n                                    \n        \n            \n                Languages Supported:\n            \n                English, French, German, Italian, Portuguese, Russian, Spanish\n        \n    \n\n                            \n\n                            \n                                \n                                \n                                \n    \n\n\n\n\n    \n        Get this app\n    \n    \n        Please sign in before purchasing\n        (Why?)\n    \n\n    \n        \n            \n            \n            \n                \n            \n        \n\n\n        \n            Sign in\n        \n    \n\n\n(function(f) {var _np=(window.P._namespace(\"masrw-sign-in\"));if(_np.guardFatal){_np.guardFatal(f)(_np);}else{f(_np);}}(function(P) {\n    P.when('A').execute(function(A) {\n        A.declarative('masrw-sign-in', 'click', function(event) {\n            window.location.href= \"/gp/sign-in.html/ref=mas_buy_sign_in?path=%2Fdp%2FB00G36GT5A&useRedirectOnSuccess=1\";\n        });\n    });\n}));\nLearn how buying works\n\n                                By placing your order, you agree to our Terms of Use\n                        \n                    \n                (function(f) {var _np=(window.P._namespace(\"\"));if(_np.guardFatal){_np.guardFatal(f)(_np);}else{f(_np);}}(function(P) {\n                if (typeof uet == 'function') {\n                    uet('af');\n                }\n            }));\n            \n                \n                    \n\n                \n                \n                    \n\n\n\n\n\n    \n        \n        \n            Screenshots\n        \n    \n\n\n\n    \n    \n        \n        \n        \n            0:000:00This video is not intended for all audiences. What date were you born?JanuaryFebruaryMarchAprilMayJuneJulyAugustSeptemberOctoberNovemberDecember123456789101112131415161718192021222324252627282930312023202220212020201920182017201620152014201320122011201020092008200720062005200420032002200120001999199819971996199519941993199219911990198919881987198619851984198319821981198019791978197719761975197419731972197119701969196819671966196519641963196219611960195919581957195619551954195319521951195019491948194719461945194419431942194119401939193819371936193519341933193219311930192919281927192619251924192319221921192019191918191719161915191419131912191119101909190819071906190519041903190219011900SubmitAdobe Flash Player is required to watch this video.Install Flash Player\n        \n    \n\n    \n    \n    \n    \n    \n    \n        \n    \n\n\n    \n    \n        \n            \n        \n        \n            \n                \n                \n\n                \n                    \n                        \n                            \n                        \n                    \n                    \n                \n                    \n                        \n                            \n                        \n                    \n                    \n                \n                    \n                        \n                            \n                        \n                    \n                    \n                \n                    \n                        \n                            \n                        \n                    \n                    \n                \n                    \n                        \n                            \n                        \n                    \n                    \n                \n\n                \n                \n                    \n                        \n                            \n                        \n                    \n                \n            \n        \n        \n            \n        \n    \n    \n    \n        \n        \n        \n            \n                \n                    \n                \n                \n                    \n                                \n                            \n                                \n                            \n                                \n                            \n                                \n                            \n                                \n                            \n                            \n                        \n                \n                \n                    \n                \n            \n        \n    \n    \n\n\n    P.when('A', 'Airy', 'ready').execute(function(A, Airy) {\n        window.$ = A.$;\n        window.elements = {};\n        window.global = {};\n\n        global.hasCalculatedThumbCarousel = false;\n        global.hasCalculatedScreenshotCarousel = false;\n\n        global.thumbnailScroll = 600;    // Max Scroll amount for thumbnails\n        global.thumbnailScrollLeftDistance = 0; // Distance left to scroll on the left\n        global.thumbnailScrollRightDistance = 0; // Distance left to scroll on the right\n        global.thumbnailScrollPoint = 0; // Point to Scroll to on thumbnails - Jquery uses scrollLeft\n\n        global.screenshotScroll = 500;  //Max scroll amount for screenshots\n        global.screenshotScrollLeftDistance = 0; // Distance left to scroll on the left\n        global.screenshotScrollRightDistance = 0; // Distance left to scroll on the right\n        global.screenshotScrollPoint = 0; // Point to Scroll to on screenshots - Jquery uses scrollLeft\n\n        elements.$mainProductImage = $('#js-masrw-main-image');\n        elements.$thumbsCarouselContainer = $(\".masrw-screenshot-thumbnail-container .a-carousel-viewport\"); //This is the one that scrolls\n        elements.$thumbsCarouselList = $(\".masrw-screenshot-thumbnail-container .a-carousel\");\n        elements.$screenshotsCarouselContainer = $(\".masrw-screenshot-container .a-carousel-viewport\"); //This is the one that scrolls\n        elements.$screenshotsCarouselList = $(\".masrw-screenshot-container .a-carousel\");\n\n        initButtons();\n        initVideo();\n    });\n\n    function initButtons() {\n\n        $(\".masrw-screenshot-thumbnail-container\").bind(\"mouseenter\", function() {\n\n            if (!global.hasCalculatedThumbCarousel) {\n                // Make sure the cards have the right width and margin\n                $(\".masrw-thumb-card\").width(\"auto\");\n                $(\".masrw-thumb-card:not(:first)\").css(\"margin-left\", \"10px\");\n\n                // Update the Carousel List to have the right width\n                calculateCarouselWidth(elements.$thumbsCarouselList, \".masrw-thumb-card\");\n\n                global.hasCalculatedThumbCarousel = true;\n\n                if (elements.$thumbsCarouselList.width() > elements.$thumbsCarouselContainer.width()) {\n                    $('.masrw-carousel-control.masrw-screenshot-thumbs').show(500);\n                }\n            }\n        });\n\n        $(\".masrw-screenshot-container\").bind(\"mouseenter\", function() {\n\n            if (!global.hasCalculatedScreenshotCarousel) {\n                // Make sure the cards have the right width and margin\n                $(\".masrw-screenshot-card\").width(\"auto\");\n                $(\".masrw-screenshot-card:not(:first)\").css(\"margin-left\", \"25px\");\n\n                // Update the Carousel List to have the right width\n                calculateCarouselWidth(elements.$screenshotsCarouselList, \".masrw-screenshot-card\");\n\n                global.hasCalculatedScreenshotCarousel = true;\n\n                if (elements.$screenshotsCarouselList.width() > elements.$screenshotsCarouselContainer.width()) {\n                    $('.masrw-carousel-control.masrw-screenshots').show(500);\n                    global.screenshotScroll = Math.floor(elements.$screenshotsCarouselContainer.width());\n                }\n            }\n        });\n\n        $(\".js-masrw-show-screenshots\").bind(\"click\", function(e) {\n            e.preventDefault();\n            openScreenshotLightbox($(e.target).parentsUntil(\"ol\").index() - $(\".js-masrw-play-video-button\").length);\n\n        });\n\n        $(\"#masrw-lightbox-screenshot-close\").bind(\"click\", function(e) {\n            e.preventDefault();\n            closeScreenshotLightbox();\n        });\n\n        $(\"#masrw-lightbox-screenshots-dark\").bind(\"click\", function() {\n            closeScreenshotLightbox();\n        });\n\n        $(\".js-masrw-play-video-button\").bind(\"click\", function(e) {\n            e.preventDefault();\n            openVideoLightbox();\n        });\n\n        $(\"#masrw-lightbox-video-close\").bind(\"click\", function(e) {\n            e.preventDefault();\n            closeVideoLightbox();\n        });\n\n        $(\"#masrw-lightbox-dark\").bind(\"click\", function() {\n            closeVideoLightbox();\n        });\n\n        // Code for Scrolling buttons\n        // Thumbnails Left Scroll\n        $(\"#masrw-screenshot-thumbs-left\").bind(\"click\", function() {\n            // Check to see if you can still scroll to the left\n            if (elements.$thumbsCarouselContainer.scrollLeft() != 0) {\n                // Calculate amount left to scroll to the left\n                global.thumbnailScrollLeftDistance = elements.$thumbsCarouselContainer.scrollLeft();\n\n                // If amount left to scroll is greater then predefined scroll, then scroll the predefined amount\n                // Otherwise scroll what is left to scroll\n                if (global.thumbnailScrollLeftDistance > global.thumbnailScroll) {\n                    global.thumbnailScrollPoint = elements.$thumbsCarouselContainer.scrollLeft() - global.thumbnailScroll;\n                }\n                else {\n                    global.thumbnailScrollPoint = 0;\n\n                    // No more to scroll left\n                    $(\"#masrw-screenshot-thumbs-left\").removeClass(\"enabled\");\n                }\n\n                // Animate the Scroll to prevent automatic jumping to location\n                elements.$thumbsCarouselContainer.animate({\n                    scrollLeft : global.thumbnailScrollPoint\n                });\n\n                // We can scroll to the right\n                $(\"#masrw-screenshot-thumbs-right\").addClass(\"enabled\");\n            }\n        });\n\n        // Thumbnails Right Scroll\n        $(\"#masrw-screenshot-thumbs-right\").bind(\"click\", function() {\n            // Check to see if you can still scroll to the right\n            if (elements.$thumbsCarouselContainer.scrollLeft() + elements.$thumbsCarouselContainer.width() < elements.$thumbsCarouselList.width()) {\n                // Calculate amount left to scroll to the right\n                global.thumbnailScrollRightDistance = elements.$thumbsCarouselList.width() - (elements.$thumbsCarouselContainer.scrollLeft() + elements.$thumbsCarouselContainer.width());\n\n                // If amount left to scroll to the right is greater then predefined scroll, then scroll the predefined amount\n                // Otherwise scroll what is left to scroll\n                if (global.thumbnailScrollRightDistance > global.thumbnailScroll) {\n                    global.thumbnailScrollPoint = elements.$thumbsCarouselContainer.scrollLeft() + global.thumbnailScroll;\n                }\n                else {\n                    global.thumbnailScrollPoint = elements.$thumbsCarouselContainer.scrollLeft() + global.thumbnailScrollRightDistance;\n\n                    // No more right scrolling\n                    $(\"#masrw-screenshot-thumbs-right\").removeClass(\"enabled\");\n                }\n\n                // Animate the Scroll to prevent automatic jumping to location\n                elements.$thumbsCarouselContainer.animate({\n                    scrollLeft : global.thumbnailScrollPoint\n                });\n\n                // We can scroll to the left\n                $(\"#masrw-screenshot-thumbs-left\").addClass(\"enabled\");\n            }\n        });\n\n        // Scroll bar enable/disable scroll buttons accordingly\n        elements.$thumbsCarouselContainer.scroll(function() {\n\n            if (elements.$thumbsCarouselContainer.scrollLeft() == 0) {\n                // Disable the left button\n                global.thumbnailScrollPoint = 0;\n                // No more to scroll left\n                $(\"#masrw-screenshot-thumbs-left\").removeClass(\"enabled\");\n\n            }\n            else if (elements.$thumbsCarouselContainer.scrollLeft() > 0) {\n                // Enable the left button - We can scroll left now\n                $(\"#masrw-screenshot-thumbs-left\").addClass(\"enabled\");\n\n                // Calculate amount left to scroll to the right\n                global.thumbnailScrollRightDistance = elements.$thumbsCarouselList.width() - (elements.$thumbsCarouselContainer.scrollLeft() + elements.$thumbsCarouselContainer.width());\n\n                if (global.thumbnailScrollRightDistance < 2) {\n                    // No more right scrolling\n                    $(\"#masrw-screenshot-thumbs-right\").removeClass(\"enabled\");\n                }\n                else {\n                    // Has right scrolling\n                    $(\"#masrw-screenshot-thumbs-right\").addClass(\"enabled\");\n                }\n            }\n        });\n\n        // Scroll bar enable/disable scroll buttons accordingly\n        elements.$screenshotsCarouselContainer.scroll(function() {\n\n            if (elements.$screenshotsCarouselContainer.scrollLeft() == 0) {\n                // Disable the left button\n                global.screenshotScrollPoint = 0;\n                // No more to scroll left\n                $(\"#masrw-screenshots-left\").removeClass(\"enabled\");\n\n            }\n            else if (elements.$screenshotsCarouselContainer.scrollLeft() > 0) {\n                // Enable the left button - We can scroll left now\n                $(\"#masrw-screenshots-left\").addClass(\"enabled\");\n\n                // Calculate amount left to scroll to the right\n                global.screenshotScrollRightDistance = elements.$screenshotsCarouselList.width() - (elements.$screenshotsCarouselContainer.scrollLeft() + elements.$screenshotsCarouselContainer.width());\n\n                if (global.screenshotScrollRightDistance < 2) {\n                    // No more right scrolling\n                    $(\"#masrw-screenshots-right\").removeClass(\"enabled\");\n                }\n                else {\n                    // Has right scrolling\n                    $(\"#masrw-screenshots-right\").addClass(\"enabled\");\n                }\n            }\n        })\n\n        // Screenshots Left Scroll\n        $(\"#masrw-screenshots-left\").bind(\"click\", function() {\n            // Check to see if you can still scroll to the left\n            if (elements.$screenshotsCarouselContainer.scrollLeft() != 0) {\n                // Calculate amount left to scroll to the left\n                global.screenshotScrollLeftDistance = elements.$screenshotsCarouselContainer.scrollLeft();\n\n                // If amount left to scroll is greater then predefined scroll, then scroll the predefined amount\n                // Otherwise scroll what is left to scroll\n                if (global.screenshotScrollLeftDistance > global.screenshotScroll) {\n                    global.screenshotScrollPoint = elements.$screenshotsCarouselContainer.scrollLeft() - global.screenshotScroll;\n                }\n                else {\n                    global.screenshotScrollPoint = 0;\n\n                    // No more to scroll left\n                    $(\"#masrw-screenshots-left\").removeClass(\"enabled\");\n                }\n\n                // Animate the Scroll to prevent automatic jumping to location\n                elements.$screenshotsCarouselContainer.animate({\n                    scrollLeft : global.screenshotScrollPoint\n                }, 800, \"swing\");\n\n                // We can scroll to the right\n                $(\"#masrw-screenshots-right\").addClass(\"enabled\");\n            }\n        });\n\n        // Screenshots Right Scroll\n        $(\"#masrw-screenshots-right\").bind(\"click\", function() {\n            // Check to see if you can still scroll to the right\n            if (elements.$screenshotsCarouselContainer.scrollLeft() + elements.$screenshotsCarouselContainer.width() < elements.$screenshotsCarouselList.width()) {\n                // Calculate amount left to scroll to the right\n                global.screenshotScrollRightDistance = elements.$screenshotsCarouselList.width() - (elements.$screenshotsCarouselContainer.scrollLeft() + elements.$screenshotsCarouselContainer.width());\n\n                // If amount left to scroll to the right is greater then predefined scroll, then scroll the predefined amount\n                // Otherwise scroll what is left to scroll\n                if (global.screenshotScrollRightDistance > global.screenshotScroll) {\n                    global.screenshotScrollPoint = elements.$screenshotsCarouselContainer.scrollLeft() + global.screenshotScroll;\n                }\n                else {\n                    global.screenshotScrollPoint = elements.$screenshotsCarouselContainer.scrollLeft() + global.screenshotScrollRightDistance;\n\n                    // No more right scrolling\n                    $(\"#masrw-screenshots-right\").removeClass(\"enabled\");\n                }\n\n                //Animate the Scroll to prevent automatic jumping to location\n                elements.$screenshotsCarouselContainer.animate({\n                    scrollLeft : global.screenshotScrollPoint\n                }, 800, \"swing\");\n\n                // We can scroll to the left\n                $(\"#masrw-screenshots-left\").addClass(\"enabled\");\n            }\n        });\n    }\n\n    function initVideo() {\n        var mediaCentralVideoURL = \"\";\n        var mediaCentralBaseURL = \"https://images-na.ssl-images-amazon.com/images/G/01/\";\n        var airyOnMediaCentralBaseURL = mediaCentralBaseURL + \"vap/video/airy2/prod/2.0.989.0/\";\n\n        global.airyPlayer = Airy.embed({\n            parentId : \"masrw-airy-player-container\",\n            // Desktop - Specify multiple urls to support more browsers & devices.\n            streamingUrls : [\n                mediaCentralVideoURL\n            ],\n            swfUrl : airyOnMediaCentralBaseURL + \"flash/AiryBasicRenderer._TTW_.swf\"\n        });\n    }\n\n    function resetVideo() {\n        try {\n            global.airyPlayer.detach();\n        }\n        catch(err) {\n            log(err.message);\n        }\n\n        global.airyPlayer = {};\n\n        setTimeout(function(){\n            initVideo();\n        }, 100);\n    }\n\n    function stopVideo() {\n        global.airyPlayer.pause();\n        global.airyPlayer.detach();\n        global.airyPlayer = {};\n\n        initVideo();\n    }\n\n    function openVideoLightbox() {\n        $(\"#masrw-video-overlay\").removeClass(\"masrw-overlay-hidden\");\n\n        global.airyPlayer.play();\n\n        setTimeout(function(){\n            $(\"#masrw-video-overlay\").addClass(\"masrw-overlay-on\");\n            $(\"#masrw-lightbox\").addClass(\"masrw-overlay-on\");\n            $(\"#masrw-lightbox-video-close\").addClass(\"masrw-overlay-on\");\n        }, 10);\n    }\n\n    function closeVideoLightbox() {\n        try {\n            global.airyPlayer.pause();\n        }\n        catch(err) {\n            log(err.message);\n        }\n\n        $(\"#masrw-video-overlay\").removeClass(\"masrw-overlay-on\");\n        $(\"#masrw-lightbox\").removeClass(\"masrw-overlay-on\");\n        $(\"#masrw-lightbox-video-close\").removeClass(\"masrw-overlay-on\");\n\n        setTimeout(function(){\n            $(\"#masrw-video-overlay\").addClass(\"masrw-overlay-hidden\");\n            resetVideo();\n        }, 150);\n    }\n\n    function openScreenshotLightbox(defaultIndex) {\n        $(\"#masrw-screenshots-overlay\").removeClass(\"masrw-overlay-hidden\");\n\n        setTimeout(function(){\n            $(\"#masrw-screenshots-overlay\").addClass(\"masrw-overlay-on\");\n            $(\"#masrw-screenshots-lightbox\").addClass(\"masrw-overlay-on\");\n            $(\"#masrw-screenshots-lightbox\").find(\"li\")[defaultIndex].scrollIntoView({\n                behavior: 'auto',\n                block: 'center',\n                inline: 'center'\n            });\n            $(\"#masrw-lightbox-screenshot-close\").addClass(\"masrw-overlay-on\");\n        }, 150);\n    }\n\n    function closeScreenshotLightbox() {\n        $(\"#masrw-screenshots-overlay\").removeClass(\"masrw-overlay-on\");\n        $(\"#masrw-screenshots-lightbox\").removeClass(\"masrw-overlay-on\");\n        $(\"#masrw-lightbox-screenshot-close\").removeClass(\"masrw-overlay-on\");\n\n        setTimeout(function(){\n            $(\"#masrw-screenshots-overlay\").addClass(\"masrw-overlay-hidden\");\n            resetVideo();\n        }, 150);\n    }\n\n    function calculateCarouselWidth(element, elements) {\n        var width = 0;\n        $(elements).each(function(){\n            width += $(this).outerWidth(true);\n        });\n\n        element.width(width);\n    }\n\nLatest updatesWhat's new in version 1.1\n            - minor fix- improve performaceProduct DetailsRelease Date: 2013Date first listed on Amazon: October 30, 2013Developed By: SmallthingASIN: B00G36GT5ACustomer reviews: 4.1 out of 5 stars17  customer ratings\n\n\n\n\n\n\n    Developer info\n    \n        \n            \n                \n                    \n                    \n                    \n                        \n                            \n                                More apps by this developer\n                            \n                        \n                    \n                \n            \n            \n        \n    \n\n\n\n\n\n\n\n    \n        Product features\n    \n    \n        \n            \n                \n                    \n                        PLAY AT ANY TIME!\n                    \n                \n            \n                \n                    \n                        PLAY EASY\n                    \n                \n            \n                \n                    \n                        A SOLITARIE COMPLETE\n                    \n                \n            \n                \n                    \n                        CUSTOMIZATION\n                    \n                \n            \n                \n                    \n                        RELAX SOUND\n                    \n                \n            \n        \n    \n\n                \n\n                \n\n\n\n\n\n    \n    \n        Product description\n        \n            EASY SPIDER Solitaire is the famous solitaire card SPIDER, designed to be simple to use, relaxing, attention to detail... and it is FREE and COMPLETE 100% It's like the famous WINDOWS game that everyone plays! PLAY AT ANY TIME! You're play and must stop? You can close it in anyway : Spider Easy restart exactly where it left! PLAY EASY Drag the cards with your finger on the deck you want, Or tap a card and this will be in the best position ! A SOLITARIE COMPLETE We have not forgotten anything to make the full game and fun for everyone : > You can play with many suit. > You have the chance to come back as you like if you do not like the moves made . > You can ask for help if you do not know how to proceed, you will be given some tips how to proceed. > You have the essential statistics of the whole game if you want to improve yourself : number of moves, time and score . > You can see how you are ranking among players from around the world. > You do not know all the rules to play? You will find them described in detail. IS SUITABLE FOR ANY SCREEN Do you have a phone or a tablet? You can choose the design of the cards, big or small. So that they are clearly visible on your screen. Just a touch while you play. It takes advantage of the graphics of the iPhone 5 and iPad Retina of recent generations. CUSTOMIZATION You can change the design of the game board , the drawings on the cards, remove the writing on the screen ... You can do anything with just one touch. Edit it as you like ! RELAX SOUND We have created a series of relaxing music that will accompany you during the game. If you do not want, then you can also turn off . Search our official page Smallthing and follow us on Facebook and Twitter . You will discover exclusive news, trucch and curiosity about our games ! Visit our website : www.smallthinggame.com Follow also our blog: smallthinggame.blogspot.com If you want to propose improvements , advice write to: <EMAIL> ___________________________________________ Search our other products on store GP RetroBubble Box Breaker Blitz Wall Breaker\n        \n    \nTechnical detailsSize: 15.3MBVersion: 1.1Developed By: SmallthingApplication Permissions:\n                (\n            Help me understand what permissions mean\n                )\n            Access coarse (e.g., Cell-ID, Wi-Fi) locationAccess fine (e.g., GPS) locationAccess information about networksAccess information about Wi-Fi networksOpen network socketsWrite to external storageMinimum Operating System:  Android 2.2Approximate Download Time:  Less than 2 minutes\n  \n    \n\n  \n\n  (window.AmazonUIPageJS ? AmazonUIPageJS : P).load.js('https://images-na.ssl-images-amazon.com/images/I/41+hU3deU6L._RC|01JMhqKAiVL.js,11KGVmb0nxL.js,41dL09N-ijL.js,31OqjYy-bxL.js,01VSu9SK-XL.js_.js?AUIClients/DesktopMedleyFilteringMetaAsset&AXTJ5Fo9#386124-T1.666973-T1');\n\n\n        \n      Customer reviews4.1 out of 5 stars4.1 out of 5\n            17 global ratings\n\n\n\n\n\n\n\n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n  \n  \n\n\n\n\n\n\n\n\n\n\n\n  \n  \n\n    \n    \n\n      \n        \n          \n            5 star\n          \n        \n\n        \n      \n\n      \n        \n          \n        \n      \n\n      \n        \n        \n          \n            \n              68%\n            \n          \n        \n      \n    \n\n  \n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n  \n  \n\n\n\n\n\n\n\n\n\n\n\n  \n  \n\n    \n    \n\n      \n        \n          \n            4 star\n          \n        \n\n        \n      \n\n      \n        \n          \n        \n      \n\n      \n        \n        \n          \n            \n              7%\n            \n          \n        \n      \n    \n\n  \n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n  \n  \n\n\n\n\n\n\n\n\n\n\n\n  \n\n    \n    \n\n      \n        \n          3 star\n        \n\n        \n        \n          0% (0%)\n        \n\n        \n      \n\n      \n        \n      \n\n      \n        \n\n        \n          \n            0%\n          \n        \n      \n    \n\n  \n  \n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n  \n  \n\n\n\n\n\n\n\n\n\n\n\n  \n  \n\n    \n    \n\n      \n        \n          \n            2 star\n          \n        \n\n        \n      \n\n      \n        \n          \n        \n      \n\n      \n        \n        \n          \n            \n              17%\n            \n          \n        \n      \n    \n\n  \n\n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n  \n  \n\n\n\n\n\n\n\n\n\n\n\n  \n  \n\n    \n    \n\n      \n        \n          \n            1 star\n          \n        \n\n        \n      \n\n      \n        \n          \n        \n      \n\n      \n        \n        \n          \n            \n              8%\n            \n          \n        \n      \n    \n\n  \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    \n        \n            \n                How customer reviews and ratings work\n            \n            \n                \n                    Customer Reviews, including Product Star Ratings help customers to learn more about the product and decide whether it is the right product for them.\n                \n                \n                    To calculate the overall star rating and percentage breakdown by star, we don’t use a simple average. Instead, our system considers things like how recent a review is and if the reviewer bought the item on Amazon. It also analyzed reviews to verify trustworthiness.\n                \n                \n                    Learn more how customers reviews work on Amazon\n                \n            \n        \n        \n    \n\n  \n      \n\n\n\nvar instrumentation;!function(){\"use strict\";var e={568:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!(\"get\"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,\"default\",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)\"default\"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,\"__esModule\",{value:!0}),t.getSlotWithBrowseNode=t.getPlacementWithBrowseNode=t.getBrowseNode=t.csa=t.csm=t.AD_LOAD_COUNTERS=void 0;var a=r(922);Object.defineProperty(t,\"AD_LOAD_COUNTERS\",{enumerable:!0,get:function(){return a.AD_LOAD_COUNTERS}}),t.csm=i(r(472)),t.csa=i(r(495));var c=r(322);Object.defineProperty(t,\"getBrowseNode\",{enumerable:!0,get:function(){return c.getBrowseNode}}),Object.defineProperty(t,\"getPlacementWithBrowseNode\",{enumerable:!0,get:function(){return c.getPlacementWithBrowseNode}}),Object.defineProperty(t,\"getSlotWithBrowseNode\",{enumerable:!0,get:function(){return c.getSlotWithBrowseNode}})},322:function(e,t){Object.defineProperty(t,\"__esModule\",{value:!0}),t.getPlacementWithBrowseNode=t.getSlotWithBrowseNode=t.getBrowseNode=void 0;var r=/.*(\\/b|\\/s|\\/l).*(node=)(\\d{1,12}).*/;t.getBrowseNode=function(){var e=r.exec(window.location.href);return e&&e[3]?e[3]:null},t.getSlotWithBrowseNode=function(e){var r=(0,t.getBrowseNode)();if(!e||!r)return null;var n=e.split(\":\");return n.splice(n.length-1,0,r),n.join(\":\")},t.getPlacementWithBrowseNode=function(e){var r=(0,t.getBrowseNode)();return r?\"\".concat(e,\":\").concat(r):null}},922:function(e,t){Object.defineProperty(t,\"__esModule\",{value:!0}),t.AD_LOAD_COUNTERS=void 0,t.AD_LOAD_COUNTERS={HTML_REACHED:\"adload:htmlreached\"}},958:function(e,t,r){Object.defineProperty(t,\"__esModule\",{value:!0}),t.CsaEvents=void 0;var n=r(876);t.CsaEvents=function(){var e=this;if(this.log=function(t,r,o){if(e.events)try{e.events(\"log\",{schemaId:\"ApeSafeframe.csaEvent.1\",metricName:t+\":\"+r+\":\"+o,metricValue:1},{ent:\"all\"})}catch(e){(0,n.logError)(\"Error with 'logCsaEvent' CSA\",e)}},this.setEntity=function(t){if(e.events)try{e.events(\"setEntity\",{adCreativeMetaData:t})}catch(e){(0,n.logError)(\"Error with 'addCsaEntity' CSA\",e)}},window.csa)try{this.events=window.csa(\"Events\",{producerId:\"adplacements\"})}catch(e){(0,n.logError)(\"Error with initiating CSA\",e)}}},710:function(e,t,r){Object.defineProperty(t,\"__esModule\",{value:!0}),t.CsaLatency=void 0;var n=r(876);t.CsaLatency=function(e){var t=this;if(this.mark=function(e,r){if(t.latencyPlugin)try{t.latencyPlugin(\"mark\",e,r)}catch(e){(0,n.logError)(\"Error with 'markCsaLatencyMetric' CSA\",e)}},window.csa)try{this.latencyPlugin=window.csa(\"Content\",{element:e})}catch(e){(0,n.logError)(\"Error with initiating CSA\",e)}}},495:function(e,t,r){Object.defineProperty(t,\"__esModule\",{value:!0}),t.events=t.latency=void 0;var n=r(710),o=r(958);t.latency=function(e){return new n.CsaLatency(e)},t.events=function(){return new o.CsaEvents}},472:function(e,t,r){Object.defineProperty(t,\"__esModule\",{value:!0}),t.addCsmTag=t.sendCsmCounter=t.sendCsmLatencyMetric=void 0;var n,o=r(322);!function(e){e.bb=\"uet\",e.af=\"uet\",e.cf=\"uet\",e.be=\"uet\",e.ld=\"uex\"}(n||(n={})),t.sendCsmLatencyMetric=function(e,t,r,i,a){var c=n[e];if(\"function\"==typeof window[c]){var u=i?i+\":\":\"\",s=function(t,r){r&&window[t](e,\"adplacements:\"+u+r,{wb:1},a)},d=t.replace(/_/g,\":\");s(c,d),s(c,(0,o.getSlotWithBrowseNode)(d)),r&&(s(c,r),s(c,(0,o.getPlacementWithBrowseNode)(r)))}},t.sendCsmCounter=function(e,t,r,n){var i=function(e,t){var n;if(\"function\"==typeof(null===(n=null===window||void 0===window?void 0:window.ue)||void 0===n?void 0:n.count)&&e){var o=\"adplacements:\"+r;window.ue.count(\"\".concat(o,\":\").concat(e),t)}};if(e){var a=e.replace(/_/g,\":\");i(a,n),i((0,o.getSlotWithBrowseNode)(a),n)}t&&(i(t,n),i((0,o.getPlacementWithBrowseNode)(t),n))},t.addCsmTag=function(e,t,r,n){var o;if(null===(o=null===window||void 0===window?void 0:window.ue)||void 0===o?void 0:o.tag){if(t){var i=e+\":\"+t.replace(/_/g,\":\")+(n?\":\"+n:\"\");window.ue.tag(i)}if(r){var a=e+\":\"+r+(n?\":\"+n:\"\");window.ue.tag(a)}t||r||window.ue.tag(e+(n?\":\"+n:\"\"))}}},876:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!(\"get\"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,\"default\",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)\"default\"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,\"__esModule\",{value:!0}),t.logError=void 0;var a=i(r(472));t.logError=function(e,t){var r=t||new Error(e);console.error(e,t),a.sendCsmCounter(\"\",null,\"safeFrameError\",1),window.sfHostLogErrors&&(window.ueHostLogError?window.ueHostLogError(r,{logLevel:\"ERROR\",attribution:\"APE-safeframe\",message:e+\" \"}):\"undefined\"!=typeof console&&console.error&&console.error(e,r))}}},t={},r=function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}(568);instrumentation=r}();\nif (instrumentation) {instrumentation.csm.sendCsmCounter(\"Detail_customer-reviews-top_Glance\", \"a5a58631-9157-450d-8b12-21b15ea5926d\", instrumentation.AD_LOAD_COUNTERS.HTML_REACHED, 1);instrumentation.csa.events().log(instrumentation.AD_LOAD_COUNTERS.HTML_REACHED, \"Detail_customer-reviews-top_Glance\", \"a5a58631-9157-450d-8b12-21b15ea5926d\");if (typeof uet === 'function' && typeof ues === 'function') {var scope = 'Detail_customer-reviews-top_Glance';var placementId = 'a5a58631-9157-450d-8b12-21b15ea5926d';ues('wb', 'adplacements:' + scope.replace(/\\_/g, ':'), {wb:1}); uet('bb', 'adplacements:' + scope.replace(/\\_/g, ':'), {wb:1}); if (placementId) {ues('wb', 'adplacements:' + placementId, {wb:1});uet('bb', 'adplacements:' + placementId, {wb:1});}}}window.renderingWeblabs = window.renderingWeblabs ? window.renderingWeblabs : {};window.renderingWeblabs = Object.assign(window.renderingWeblabs, JSON.parse('{\"ADPT_SF_EXPANDABLE_ADS_716745\":\"C\",\"ADPT_SF_ARIA_HIDDEN_644521\":\"T1\",\"ADPT_SF_CLIENT_LATENCY_2023Q1_632539\":\"C\",\"ADPT_SF_MRC_RETEST_643771\":\"T1\",\"ADPT_SF_DOCWRITE_689243\":\"T1\",\"ADPT_SF_TRANSPARENCY_INFO_MANDATORY_FOR_EU_712921\":\"C\"}'));  (function(){function a(d,e){if(window.addEventListener){window.addEventListener(d,e,false);}else{if(window.attachEvent){window.attachEvent(\"on\"+d,e);}}}function c(d,e){if(window.removeEventListener){window.removeEventListener(d,e,false);}else{if(window.detachEvent){window.detachEvent(\"on\"+d,e);}}}var b=function(){(function(){(function(j,o){j.sfLogErrors=j.sfLogErrors||true;var r=r||function(w,v){v=v||new Error(w);if(j.ue&&typeof ue.count==\"function\"){ue.count(\"adplacements:safeFrameError\",1);}if(!j.sfLogErrors){return;}if(j.ueLogError){j.ueLogError(v,{logLevel:\"ERROR\",attribution:\"APE-safeframe\",message:w+\" \"});}else{if(typeof console!==\"undefined\"&&console.error){console.error(w,v);}}};j[\"customer-reviews-top\"]={};j[\"customer-reviews-top\"].adStartTime=(new Date()).getTime();function k(){return j.innerHeight||o.documentElement.clientHeight;}function t(){return j.innerWidth||o.documentElement.clientWidth;}function m(x,v,w){if(x>0){return(w>x);}else{return(v>0);}}var d=function(){return(Date.now?Date.now():new Date().getTime());};var h=function(w,y,C){var v,A,D;var B=null;var z=0;if(!C){C={};}var x=function(){z=C.leading===false?0:d();B=null;D=w.apply(v,A);if(!B){v=A=null;}};return function(){var F=d();if(!z&&C.leading===false){z=F;}var E=y-(F-z);v=this;A=arguments;if(E<=0||E>y){if(B){clearTimeout(B);B=null;}z=F;D=w.apply(v,A);if(!B){v=A=null;}}else{if(!B&&C.trailing!==false){B=setTimeout(x,E);}}return D;};};function i(D,B,w,y,v){try{var A=o.getElementById(D).getBoundingClientRect();if(m(A.top,A.bottom,k())&&m(A.left,A.right,t())){if(typeof uet==\"function\"){uet(\"bb\",\"adplacements:viewablelatency:\"+B,{wb:1});if(w){uet(\"bb\",\"adplacements:viewablelatency:\"+w,{wb:1});}}var x;if(j.csa){var C=o.getElementById(D);if(v==\"mobileads\"){x=j.csa(\"Content\",{element:C.parentNode});}else{x=j.csa(\"Content\",{element:C});}x(\"mark\",\"viewablelatency:bodyBegin\");if(j.apeViewableLatencyTrackers[y].loaded){x(\"mark\",\"viewablelatency:loaded\");}}if(typeof uex==\"function\"&&j.ue&&typeof ue.count==\"function\"){if(j.apeViewableLatencyTrackers[y].loaded){uex(\"ld\",\"adplacements:viewablelatency:\"+B,{wb:1});if(w){uex(\"ld\",\"adplacements:viewablelatency:\"+w,{wb:1});}ue.count(\"adplacements:htmlviewed:loaded:\"+B,1);if(w){ue.count(\"adplacements:htmlviewed:loaded:\"+w,1);}}ue.count(\"adplacements:htmlviewed:\"+B,1);if(w){ue.count(\"adplacements:htmlviewed:\"+w,1);}}j.apeViewableLatencyTrackers[y].viewed=true;if(j.apeViewableLatencyTrackers[y].tracker){c(\"scroll\",j.apeViewableLatencyTrackers[y].tracker);c(\"resize\",j.apeViewableLatencyTrackers[y].tracker);}}}catch(z){j.apeViewableLatencyTrackers[y].valid=false;}}try{j.apeViewableLatencyTrackers=j.apeViewableLatencyTrackers||{};var n=\"ape_Detail_customer-reviews-top_Glance_placement\";var f=\"Detail_customer-reviews-top_Glance\".replace(/\\_/g,\":\");var p=\"a5a58631-9157-450d-8b12-21b15ea5926d\";var e=\"d0db2c3f80184e59884aed966d96682f\";var g=\"amazon\";j.apeViewableLatencyTrackers[e]=j.apeViewableLatencyTrackers[e]||{};j.apeViewableLatencyTrackers[e].valid=true;i(n,f,p,e,g);if(j.apeViewableLatencyTrackers[e].valid&&!j.apeViewableLatencyTrackers[e].viewed){j.apeViewableLatencyTrackers[e].tracker=h(function(){i(n,f,p,e,g);},20);a(\"scroll\",j.apeViewableLatencyTrackers[e].tracker);a(\"resize\",j.apeViewableLatencyTrackers[e].tracker);}}catch(q){if(j.apeViewableLatencyTrackers&&j.apeViewableLatencyTrackers.d0db2c3f80184e59884aed966d96682f){j.apeViewableLatencyTrackers.d0db2c3f80184e59884aed966d96682f.valid=false;}r(\"Error initializing viewable latency instrumentation\",q);}if(j.csa){var l;var u=o.getElementById(n);if(g==\"mobileads\"){l=j.csa(\"Content\",{element:u.parentNode});}else{l=j.csa(\"Content\",{element:u});}l(\"mark\",\"bodyBegin\");}try{if(j.DAsf){j.DAsf.loadAds();}else{var s=o.createElement(\"script\");s.type=\"text/javascript\";s.async=true;s.charset=\"utf-8\";s.src=\"https://images-na.ssl-images-amazon.com/images/S/apesafeframe/ape/sf/desktop/DAsf-1.50.d76dcf8e.js?csm_attribution=APE-SafeFrame\";s.onerror=function(v){r(\"Error loading SafeFrame library: https://images-na.ssl-images-amazon.com/images/S/apesafeframe/ape/sf/desktop/DAsf-1.50.d76dcf8e.js?csm_attribution=APE-SafeFrame\");};s.setAttribute(\"crossorigin\",\"anonymous\");(o.getElementsByTagName(\"head\")[0]||o.getElementsByTagName(\"body\")[0]).appendChild(s);}}catch(q){r(\"Error appending DAsf library\",q);}}(window,document));})();};b();})();\n\n      \n    \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  \n    \n\n    \n      Sort reviews by\n    \n    \n\n      \n        Top reviews\n      \n\n      \n        Most recent\n      \n\n    \n        Top reviews\n      \n  \n\n\n\n\n\n\n\n\n\n\n\n  Top reviews from the United States\n\n        \n\n      There was a problem filtering reviews right now. Please try again later.<img src=\"https://images-na.ssl-images-amazon.com/images/S/amazon-avatars-global/default._CR0,0,1024,1024_SX48_.png\"/>Diane Felice5.0 out of 5 stars\n\n\n\n\n\n\n\n  \n  \n    Fun game!!\n  \nReviewed in the United States 🇺🇸 on March 2, 2023Verified Purchase\n\n\n\n\n\n\n\n  \n  \n    This game is really fun, easy enough to learn but always challenging at the same time.  The graphics are nice and the gameplay is intuitive.\n  \nRead more\n  \n    \n          \n              \n                  Helpful\n              \n          \n      Sending feedback...Thank you for your feedback.Sorry, we failed to record your vote. Please try again\n        Report\n  <img src=\"https://images-na.ssl-images-amazon.com/images/S/amazon-avatars-global/default._CR0,0,1024,1024_SX48_.png\"/>Kindle Customer4.0 out of 5 stars\n\n\n\n\n\n\n\n  \n  \n    Four Stars\n  \nReviewed in the United States 🇺🇸 on February 9, 2017Verified Purchase\n\n\n\n\n\n\n\n  \n  \n    Enjoy playing\n  \nRead more\n  \n    \n          \n              \n                  Helpful\n              \n          \n      Sending feedback...Thank you for your feedback.Sorry, we failed to record your vote. Please try again\n        Report\n  <img src=\"https://images-na.ssl-images-amazon.com/images/S/amazon-avatars-global/default._CR0,0,1024,1024_SX48_.png\"/>David Brown5.0 out of 5 stars\n\n\n\n\n\n\n\n  \n  \n    Five Stars\n  \nReviewed in the United States 🇺🇸 on November 17, 2014Verified Purchase\n\n\n\n\n\n\n\n  \n  \n    GREAT\n  \nRead more\n  \n    \n          \n              \n                  Helpful\n              \n          \n      Sending feedback...Thank you for your feedback.Sorry, we failed to record your vote. Please try again\n        Report\n  See all reviews\n\n  \n      \n\n\n\n\n\n  \n\n\n\n\n  \n    \n      \n        \n          Top reviews from other countries\n        \n        \n      \n    \n  \n    \n      \n      \n        Translate all reviews to English\n      \n    \n  \n  \n  \n    \n      \n      \n        \n    \n      \n    \n  \n<img src=\"https://images-eu.ssl-images-amazon.com/images/S/amazon-avatars-global/default._CR0,0,1024,1024_SX48_.png\"/>Juan Rivera4.0 out of 5 stars\n\n\n\n\n\n\n\n  \n    Me gusta\n  \n  \nReviewed in Spain 🇪🇸 on May 31, 2014Verified Purchase\n\n\n\n\n\n\n\n  \n    Está bien, me gusta, pero algo lento en la ejecución. Además tienes que cogerle \"el tranquillo\" al unir las cartas, tienes que darle un tic.\n  \n  \nRead more\n  \n        Report\n  Translate review to EnglishSee all reviews\n      \n    \n  \n\n\n  Amazon Appstore Return Policy\n        \n    \n    //Code to replace low resolution image with high resolution image once it is loaded.\n    var currentBgImage = document.getElementById(\"atf-dp-bg-img\");\n    var highResBgImage = new Image();\n    highResBgImage.src = \"https://m.media-amazon.com/images/I/91bVia+AraL._SL500_UX1920_CR0%2C0%2C1920%2C1080_.jpg\";\n    highResBgImage.onload = function () {\n        currentBgImage.src = highResBgImage.src;\n    };\n\n    var selectedTwisterElement = document.querySelector('.qsTwister .a-color-price');\n    var selectedTwister = selectedTwisterElement ? selectedTwisterElement.id : \"apponly\";\n    P.when('A', 'ready').execute(function (A) {\n        A.on('a:button-group:quickSubsToggle:toggle', function (data) {\n            selectedTwister = data.selectedButton.buttonName;\n            $('.twister-price').removeClass(\"a-color-price\");\n            $('.qsTwister').find('#' + selectedTwister + '.twister-price').addClass(\"a-color-price\");\n        });\n\n        // Emit clickstream metrics on page load succeed\n        emitMetrics(A, '/hz/mas/detail/pageloaded');\n    });\n\n    function emitMetrics(A, endpoint) {\n        var $ = A.$;\n        let dataParams = { 'asin': \"B00G36GT5A\" };\n        // Emit clickstream metrics on page load succeed\n        let anti_csrf = document.querySelector('meta[name=\"csrf-token\"]') === null ?\n                                                  'faketoken': document.querySelector('meta[name=\"csrf-token\"]')\n                                                  .getAttribute('content');\n        A.post(endpoint, {\n            headers: {\"anti-csrftoken-a2z\": anti_csrf},\n            params: dataParams,\n            success: function (response) {\n                if (response === true) {\n                    console.log('Client page load success');\n                } else {\n                    console.log('Client page load failed');\n                }\n            },\n            error: function () {\n                console.log('Client page load unknown error');\n            }\n        });\n    }\n\n\n\n\n\n\n\n\n\n  \n\n\n\n  (function ($Nav) {\n\"use strict\";\n\nif (typeof $Nav === 'undefined' || $Nav === null || typeof $Nav.when !== 'function') {\n    return;\n}\n\n$Nav.when('$', 'data', 'flyout.yourAccount', 'sidepanel.csYourAccount',\n          'config')\n    .run(\"BuyitAgain-YourAccount-SidePanel\",\n    function ($, data, yaFlyout, csYourAccount, config) {\n        if (config.disableBuyItAgain) {\n            return;\n        }\n        var render = function (data) {\n            if (data.dramResult) {\n                var widgetHtml = data.dramResult;\n                navbar.sidePanel({\n                    flyoutName: 'yourAccount',\n                    data: {html: widgetHtml}\n                });\n            }\n        };\n\n        var renderBuyItAgain = function (biaData) {\n            if (csYourAccount) {\n                csYourAccount.register(render, biaData);\n            } else {\n                render(biaData);\n            }\n        };\n\n        var truncateAndRestructureYaFlyout = function() {\n            if (window.P) {\n                P.when('A', 'a-truncate').execute(function(A, truncate) {\n                    var truncateElements = A.$('.a-truncate');\n                    A.each(truncateElements, function(element) {\n                        truncate.get(element).update();\n                    });\n                    var recommendationsWidget = document.getElementById('bia-hcb-widget');\n                    if (recommendationsWidget) { \n                        var navFlyout = recommendationsWidget.parentElement;\n                        var navFlyoutPaddingBottom = parseInt(window.getComputedStyle(navFlyout).getPropertyValue('padding-bottom'));\n                        var navFlyoutContentHeight = navFlyout.clientHeight - navFlyoutPaddingBottom;\n                        while (recommendationsWidget.offsetHeight > navFlyoutContentHeight && recommendationsWidget.offsetHeight > 0){\n                            var recommendations = recommendationsWidget.querySelectorAll('.biaNavFlyoutFaceout');\n                            if (recommendations.length <= 1) {\n                                break;\n                            }\n                            var lastRecommendation = recommendations[recommendations.length - 1];\n                            lastRecommendation.parentElement.removeChild(lastRecommendation);\n                        }\n                    }\n               });\n            }\n        };\n\n        yaFlyout.sidePanel.onData(truncateAndRestructureYaFlyout);\n        yaFlyout.onShow(truncateAndRestructureYaFlyout);\n\n    yaFlyout.onRender(function() {\n            $.ajax({\n                url: '/gp/bia/external/bia-hcb-ajax-handler.html',\n                data: {\"biaHcbRid\":\"K1JQ9VEP43RRWKW775V5\"},\n                dataType: 'json',\n                timeout: 4*1000,\n                success: renderBuyItAgain,\n                error: function (jqXHR, textStatus, errorThrown) {\n                }\n            });\n        });\n    });\n})(window.$Nav);\n\n\n\n  window.$Nav && $Nav.when(\"data\").run(function (data) {\n    data({\n      \"accountListContent\": { \"html\": \"<div id='nav-al-container'><div id='nav-al-signin'><div id='nav-flyout-ya-signin' class='nav-flyout-content nav-flyout-accessibility'><a href='https://www.amazon.com/ap/signin?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.com%2FSmallthing-Easy-Spider-Solitaire%2Fdp%2FB00G36GT5A%2F%3F_encoding%3DUTF8%26*Version*%3D1%26*entries*%3D0%26asin%3DB00G36GT5A%26ie%3DUTF8%26ref_%3Dnav_signin&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0' rel='nofollow' class='nav-action-signin-button' data-nav-role='signin' data-nav-ref='nav_signin'><span class='nav-action-inner'>Sign in</span></a><div id='nav-flyout-ya-newCust' class='nav_pop_new_cust nav-flyout-content nav-flyout-accessibility'>New customer? <a href='https://www.amazon.com/ap/register?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.com%2FSmallthing-Easy-Spider-Solitaire%2Fdp%2FB00G36GT5A%2F%3F_encoding%3DUTF8%26*Version*%3D1%26*entries*%3D0%26asin%3DB00G36GT5A%26ie%3DUTF8%26ref_%3Dnav_newcust&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0' rel='nofollow' class='nav-a'>Start here.</a></div></div></div><div id='nav-al-wishlist' class='nav-al-column nav-tpl-itemList nav-flyout-content nav-flyout-accessibility'><div class='nav-title' id='nav-al-title'>Your Lists</div><a href='/hz/wishlist/ls?triggerElementID=createList&ref_=nav_ListFlyout_navFlyout_createList_lv_redirect' class='nav-link nav-item'><span class='nav-text'>Create a List</span></a> <a href='/registries?ref_=nav_ListFlyout_find' class='nav-link nav-item'><span class='nav-text'>Find a List or Registry</span></a></div><div id='nav-al-your-account' class='nav-al-column nav-template nav-flyout-content nav-tpl-itemList nav-flyout-accessibility'><div class='nav-title'>Your Account</div><a href='/gp/css/homepage.html?ref_=nav_AccountFlyout_ya' class='nav-link nav-item'><span class='nav-text'>Account</span></a> <a id='nav_prefetch_yourorders' href='/gp/css/order-history?ref_=nav_AccountFlyout_orders' class='nav-link nav-item'><span class='nav-text'>Orders</span></a> <a href='/gp/yourstore?ref_=nav_AccountFlyout_recs' class='nav-link nav-item'><span class='nav-text'>Recommendations</span></a> <a href='/gp/history?ref_=nav_AccountFlyout_browsinghistory' class='nav-link nav-item'><span class='nav-text'>Browsing History</span></a> <a href='/gp/video/watchlist?ref_=nav_AccountFlyout_ywl' class='nav-link nav-item'><span class='nav-text'>Watchlist</span></a> <a href='/gp/video/library?ref_=nav_AccountFlyout_yvl' class='nav-link nav-item'><span class='nav-text'>Video Purchases & Rentals</span></a> <a href='/gp/kindle/ku/ku_central?ref_=nav_AccountFlyout_ku' class='nav-link nav-item'><span class='nav-text'>Kindle Unlimited</span></a> <a href='/hz/mycd/myx?pageType=content&ref_=nav_AccountFlyout_myk' class='nav-link nav-item'><span class='nav-text'>Content & Devices</span></a> <a href='/gp/subscribe-and-save/manager/viewsubscriptions?ref_=nav_AccountFlyout_sns' class='nav-link nav-item'><span class='nav-text'>Subscribe & Save Items</span></a> <a href='/hz5/yourmembershipsandsubscriptions?ref_=nav_AccountFlyout_digital_subscriptions' class='nav-link nav-item'><span class='nav-text'>Memberships & Subscriptions</span></a> <a href='https://music.amazon.com?ref=nav_youraccount_cldplyr' class='nav-link nav-item'><span class='nav-text'>Music Library</span></a></div></div>\" },\n      \"tooltipContent\": { \"html\": \"\" },\n      \"signinContent\": { \"html\": \"<div id='nav-signin-tooltip'><a href='https://www.amazon.com/ap/signin?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.com%2FSmallthing-Easy-Spider-Solitaire%2Fdp%2FB00G36GT5A%2F%3F_encoding%3DUTF8%26*Version*%3D1%26*entries*%3D0%26asin%3DB00G36GT5A%26ie%3DUTF8%26ref_%3Dnav_custrec_signin&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0' class='nav-action-signin-button' data-nav-role='signin' data-nav-ref='nav_custrec_signin'><span class='nav-action-inner'>Sign in</span></a><div class='nav-signin-tooltip-footer'>New customer? <a href='https://www.amazon.com/ap/register?openid.pape.max_auth_age=0&openid.return_to=https%3A%2F%2Fwww.amazon.com%2FSmallthing-Easy-Spider-Solitaire%2Fdp%2FB00G36GT5A%2F%3F_encoding%3DUTF8%26*Version*%3D1%26*entries*%3D0%26asin%3DB00G36GT5A%26ie%3DUTF8%26ref_%3Dnav_custrec_newcust&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.assoc_handle=usflex&openid.mode=checkid_setup&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0' class='nav-a'>Start here.</a></div></div>\" },\n      \"templates\": {\"itemList\":\"<# var hasColumns = (function () {  var checkColumns = function (_items) {    if (!_items) {      return false;    }    for (var i=0; i<_items.length; i++) {      if (_items[i].columnBreak || (_items[i].items && checkColumns(_items[i].items))) {        return true;      }    }    return false;  };  return checkColumns(items);}()); #><# if(hasColumns) { #>  <# if(items[0].image && items[0].image.src) { #>    <div class='nav-column nav-column-first nav-column-image'>  <# } else if (items[0].greeting) { #>    <div class='nav-column nav-column-first nav-column-greeting'>  <# } else { #>    <div class='nav-column nav-column-first'>  <# } #><# } #><# var renderItems = function(items) { #>  <# jQuery.each(items, function (i, item) { #>    <# if(hasColumns && item.columnBreak) { #>      <# if(item.image && item.image.src) { #>        </div><div class='nav-column nav-column-notfirst nav-column-break nav-column-image'>      <# } else if (item.greeting) { #>        </div><div class='nav-column nav-column-notfirst nav-column-break nav-column-greeting'>      <# } else { #>        </div><div class='nav-column nav-column-notfirst nav-column-break'>      <# } #>    <# } #>    <# if(item.dividerBefore) { #>      <div class='nav-divider'></div>    <# } #>    <# if(item.text || item.content) { #>      <# if(item.url) { #>        <a href='<#=item.url #>' class='nav-link      <# } else {#>        <span class='      <# } #>      <# if(item.panelKey) { #>        nav-hasPanel      <# } #>      <# if(item.items) { #>        nav-title      <# } #>      <# if(item.decorate == 'carat') { #>        nav-carat      <# } #>      <# if(item.decorate == 'nav-action-button') { #>        nav-action-button      <# } #>      nav-item'      <# if(item.extra) { #>        <#=item.extra #>      <# } #>      <# if(item.id) { #>        id='<#=item.id #>'      <# } #>      <# if(item.dataNavRole) { #>        data-nav-role='<#=item.dataNavRole #>'      <# } #>      <# if(item.dataNavRef) { #>        data-nav-ref='<#=item.dataNavRef #>'      <# } #>      <# if(item.panelKey) { #>        data-nav-panelkey='<#=item.panelKey #>'        role='navigation'        aria-label='<#=item.text#>'      <# } #>      <# if(item.subtextKey) { #>        data-nav-subtextkey='<#=item.subtextKey #>'      <# } #>      <# if(item.image && item.image.height > 16) { #>        style='line-height:<#=item.image.height #>px;'      <# } #>      >      <# if(item.decorate == 'carat') { #>        <i class='nav-icon'></i>      <# } #>      <# if(item.image && item.image.src) { #>        <img class='nav-image' src='<#=item.image.src #>' style='height:<#=item.image.height #>px; width:<#=item.image.width #>px;' />      <# } #>      <# if(item.text) { #>        <span class='nav-text<# if(item.classname) { #> <#=item.classname #><# } #>'><#=item.text#><# if(item.badgeText) { #>          <span class='nav-badge'><#=item.badgeText#></span>        <# } #></span>      <# } else if (item.content) { #>        <span class='nav-content'><# jQuery.each(item.content, function (j, cItem) { #><# if(cItem.url && cItem.text) { #><a href='<#=cItem.url #>' class='nav-a'><#=cItem.text #></a><# } else if (cItem.text) { #><#=cItem.text#><# } #><# }); #></span>      <# } #>      <# if(item.subtext) { #>        <span class='nav-subtext'><#=item.subtext #></span>      <# } #>      <# if(item.url) { #>        </a>      <# } else {#>        </span>      <# } #>    <# } #>    <# if(item.image && item.image.src) { #>      <# if(item.url) { #>        <a href='<#=item.url #>'>       <# } #>      <img class='nav-image'      <# if(item.id) { #>        id='<#=item.id #>'      <# } #>      src='<#=item.image.src #>' <# if (item.alt) { #> alt='<#= item.alt #>'<# } #>/>      <# if(item.url) { #>        </a>       <# } #>    <# } #>    <# if(item.items) { #>      <div class='nav-panel'> <# renderItems(item.items); #> </div>    <# } #>  <# }); #><# }; #><# renderItems(items); #><# if(hasColumns) { #>  </div><# } #>\",\"subnav\":\"<# if (obj && obj.type === 'vertical') { #>  <# jQuery.each(obj.rows, function (i, row) { #>    <# if (row.flyoutElement === 'button') { #>      <div class='nav_sv_fo_v_button'        <# if (row.elementStyle) { #>          style='<#= row.elementStyle #>'        <# } #>      >        <a href='<#=row.url #>' class='nav-action-button nav-sprite'>          <#=row.text #>        </a>      </div>    <# } else if (row.flyoutElement === 'list' && row.list) { #>      <# jQuery.each(row.list, function (j, list) { #>        <div class='nav_sv_fo_v_column <#=(j === 0) ? 'nav_sv_fo_v_first' : '' #>'>          <ul class='<#=list.elementClass #>'>          <# jQuery.each(list.linkList, function (k, link) { #>            <# if (k === 0) { link.elementClass += ' nav_sv_fo_v_first'; } #>            <li class='<#=link.elementClass #>'>              <# if (link.url) { #>                <a href='<#=link.url #>' class='nav_a'><#=link.text #></a>              <# } else { #>                <span class='nav_sv_fo_v_span'><#=link.text #></span>              <# } #>            </li>          <# }); #>          </ul>        </div>      <# }); #>    <# } else if (row.flyoutElement === 'link') { #>      <# if (row.topSpacer) { #>        <div class='nav_sv_fo_v_clear'></div>      <# } #>      <div class='<#=row.elementClass #>'>        <a href='<#=row.url #>' class='nav_sv_fo_v_lmargin nav_a'>          <#=row.text #>        </a>      </div>    <# } #>  <# }); #><# } else if (obj) { #>  <div class='nav_sv_fo_scheduled'>    <#= obj #>  </div><# } #>\",\"htmlList\":\"<# jQuery.each(items, function (i, item) { #>  <div class='nav-item'>    <#=item #>  </div><# }); #>\"}\n    })\n  })\n\n\n\n  window.$Nav && $Nav.declare('config.flyoutURL', null);\n  window.$Nav && $Nav.declare('btf.lite');\n  window.$Nav && $Nav.declare('btf.full');\n  window.$Nav && $Nav.declare('btf.exists');\n  (window.AmazonUIPageJS ? AmazonUIPageJS : P).register('navCF');\n\n\n  \n      window.$Nav && $Nav.when('$').run('CBIMarketplaceRedirectOverlayNavyaan', function($) {\n              $.ajax({\n                  type: 'POST',\n                  url: '/cross_border_interstitial/render',\n                  data: JSON.stringify({\n                      marketplaceId: 'ATVPDKIKX0DER',\n                      localCountryCode: 'US',\n                         customerId: null,\n                      sessionId: '134\\x2D1570085\\x2D9450222',\n                      deviceType: 'DESKTOP',\n                      referrer: 'https\\x3A\\x2F\\x2Fwww.google.com\\x2F',\n                      url: '\\x2FSmallthing\\x2DEasy\\x2DSpider\\x2DSolitaire\\x2Fdp\\x2FB00G36GT5A',\n                      pageType: 'MASDetailPage',\n                      languageOfPreference: 'en_US',\n                      queryParams: {},\n                      interstitialRequestType: 'CBI',\n                      weblabTreatmentMap: {\"CROSS_BORDER_INTERSTITIAL_ES_US_340017\":\"C\",\"CROSS_BORDER_INTERSTITIAL_MX_US_341718\":\"C\",\"CROSS_BORDER_INTERSTITIAL_ZA_659308\":\"C\",\"CBI_355055\":\"C\",\"NARX_INTERSTITIAL_NEW_CX_372291\":\"C\",\"MWEB_CROSS_BORDER_INTERSTITIAL_SE_366766\":\"T1\",\"MWEB_CROSS_BORDER_INTERSTITIAL_SA_366767\":\"T1\",\"MWEB_CROSS_BORDER_INTERSTITIAL_PL_366768\":\"T1\",\"MWEB_CROSS_BORDER_INTERSTITIAL_NL_366769\":\"T1\",\"MWEB_CROSS_BORDER_INTERSTITIAL_ZA_642039\":\"C\",\"NARX_INTERSTITIAL_AUI_MIGRATION_446901\":\"C\",\"TEST_ACS_CONFIGURATION_486322\":\"C\",\"CROSS_BORDER_INTERSTITIAL_ACS_SHADOW_TESTING_486317\":\"C\",\"NARX_INTERSTITIAL_SEMBU_MIGRATION_542466\":\"T2\",\"CROSS_BORDER_INTERSTITIAL_CUSTOMER_OPS_594994\":\"C\",\"INTERSTITIAL_PROTOTYPE_IP_ADDRESS_BR_598850\":\"C\"},\n                      slateToken: 'AQGJRntv0WvYIQGzHwAl+licRmczDpx7WNipgME1ZG6vwYnX3kOyXR855wAAAAgGcmV0YWlsCmFtYXpvbi5jb20ABWVuLVVTVVNEAAA='\n                  }),\n                  contentType: \"application/json\",\n                  dataType: \"html\",\n                  success: function(data) {\n                      if (data) {\n                          $('body').append(data);\n                      }\n                  }\n              });\n      });\n  \n\n\n\n\n\n\n\n\n\n\n  \n  \n\n               { \"rhfHandlerParams\":{\"currentPageType\":\"MASDetailPage\",\"currentSubPageType\":\"\",\"excludeAsin\":\"\",\"fieldKeywords\":\"\",\"k\":\"\",\"keywords\":\"\",\"search\":\"\",\"auditEnabled\":\"\",\"previewCampaigns\":\"\",\"forceWidgets\":\"\",\"searchAlias\":\"\"} }     <div class='rhf-border'> <div class='rhf-header'> Your recently viewed items and featured recommendations </div> <div class='rhf-footer'> <div class='rvi-container'> <div class='ybh-edit'> <div class='ybh-edit-arrow'> &#8250; </div> <div class='ybh-edit-link'> <a href='/gp/history'> View or edit your browsing history </a> </div> </div> <span class='no-rvi-message'> After viewing product detail pages, look here to find an easy way to navigate back to pages you are interested in. </span> </div> </div> </div>     Your recently viewed items and featured recommendations      ›    View or edit your browsing history     After viewing product detail pages, look here to find an easy way to navigate back to pages you are interested in.        \n\n  \n\n  \n  \n    Back to top\n  \n  \n\n\n  \n\n  \n        \n          Get to Know Us\n        \n            \n              Careers\n            \n            \n              Blog\n            \n            \n              About Amazon\n            \n            \n              Investor Relations\n            \n            \n              Amazon Devices\n            \n            \n              Amazon Science\n            \n        \n      \n        \n        \n          Make Money with Us\n        \n            \n              Sell products on Amazon\n            \n            \n              Sell on Amazon Business\n            \n            \n              Sell apps on Amazon\n            \n            \n              Become an Affiliate\n            \n            \n              Advertise Your Products\n            \n            \n              Self-Publish with Us\n            \n            \n              Host an Amazon Hub\n            \n            \n              ›See More Make Money with Us\n            \n        \n      \n        \n        \n          Amazon Payment Products\n        \n            \n              Amazon Business Card\n            \n            \n              Shop with Points\n            \n            \n              Reload Your Balance\n            \n            \n              Amazon Currency Converter\n            \n        \n      \n        \n        \n          Let Us Help You\n        \n            \n              Amazon and COVID-19\n            \n            \n              Your Account\n            \n            \n              Your Orders\n            \n            \n              Shipping Rates & Policies\n            \n            \n              Returns & Replacements\n            \n            \n              Manage Your Content and Devices\n            \n            \n              Amazon Assistant\n            \n            \n              Help\n            \n        \n      \n  \n\n\n\n  \n    \n      \n        \n        \n        \n      \n\n    \n      \n\n  #icp-touch-link-language { display: none; }\n\n\n\n\n  English\n\n\n\n\n\n  #icp-touch-link-cop { display: none; }\n\n\n\n  $USD - U.S. Dollar\n\n\n\n#icp-touch-link-country { display: none; }\n\n\n  United States\n\n\n    \n  \n  \n  \n  \n    \n      \nAmazon MusicStream millionsof songs\nAmazon AdvertisingFind, attract, andengage customers\n6pmScore dealson fashion brands\nAbeBooksBooks, art& collectibles\nACX Audiobook PublishingMade Easy\nSell on AmazonStart a Selling Account\nAmazon BusinessEverything ForYour Business\n \n\nAmazonGlobalShip OrdersInternationally\nHome ServicesExperienced ProsHappiness Guarantee\nAmazon IgniteSell your originalDigital EducationalResources\nAmazon Web ServicesScalable CloudComputing Services\nAudibleListen to Books & OriginalAudio Performances\nBook DepositoryBooks With FreeDelivery Worldwide\nBox Office MojoFind MovieBox Office Data\n \n\nComiXologyThousands ofDigital Comics\nDPReviewDigitalPhotography\nFabricSewing, Quilting& Knitting\nGoodreadsBook reviews& recommendations\nIMDbMovies, TV& Celebrities\nIMDbProGet Info EntertainmentProfessionals Need\nKindle Direct PublishingIndie Digital & Print PublishingMade Easy\n\n \n\nPrime Video DirectVideo DistributionMade Easy\nShopbopDesignerFashion Brands\nWoot!Deals and Shenanigans\nZapposShoes &Clothing\nRingSmart HomeSecurity Systems\n\neero WiFiStream 4K Videoin Every Room\nBlinkSmart Securityfor Every Home\n\n \n\n \n\n \n\nNeighbors App  Real-Time Crime& Safety Alerts\n\nAmazon Subscription BoxesTop subscription boxes – right to your door\nPillPackPharmacy Simplified\n \n\n \n\n\n    \n  \n\n  \n\n  Conditions of Use Privacy Notice Your Ads Privacy Choices  © 1996-2023, Amazon.com, Inc. or its affiliates\n\n\n  \n\n(function(a,b){a.attachEvent?a.attachEvent(\"onload\",b):a.addEventListener&&a.addEventListener(\"load\",b,!1)})(window,function(){setTimeout(function(){var el=document.getElementById(\"sis_pixel_r2\");el&&(el.innerHTML='<iframe id=\"DAsis\" src=\"//s.amazon-adsystem.com/iu3?d=amazon.com&slot=navFooter&a2=01010375035a9f90bfa8f6f3d347bdd993013e4c52fa4a2aea8267174c26d6d48781&old_oo=0&ts=1689104642269&s=AWqiUGUsYdL4l37wA-Id9bgBG0OHYJ7llBBGz84nxJoU&gdpr_consent=&gdpr_consent_avl=&cb=1689104642269\" width=\"1\" height=\"1\" frameborder=\"0\" marginwidth=\"0\" marginheight=\"0\" scrolling=\"no\" tabindex=\"-1\" sandbox></iframe>')},300)});\n\n  \n\n\n\n\n\n(window.AmazonUIPageJS ? AmazonUIPageJS : P).when('afterLoad').execute(function() {\n(function(d,h,z){var w=d.ue||{},p=function(){},n=function(d){return function(b,g){g||(g=\"ERROR\");b=b&&b.stack&&b.message?b:JSON.stringify(b);d({logLevel:g,attribution:\"MSAVowelsJavascriptAssets\",message:b})}}(d.ueLogError||p),t=function(d){return function(b,g){d(\"MSAVowelsJavascriptAssets:\"+b,g)}}(w.count||p),u=function(d){return function(){try{return d.apply(this,arguments)}catch(b){n(b,\"FATAL\")}}},k=function(){var g=Array.prototype.slice.call(arguments,0);g[0]=u(g[0]);return d.setTimeout.apply(null,\ng)},v=function(){t(\"invoked\",1);var g=function(){try{var d=h.createElement(\"link\").relList.supports(\"preload\")}catch(H){d=!1}return function(b){var f=d?h.createElement(\"link\"):new Image;f.onerror=f.onload=u(function(){f&&f.parentElement&&f.parentElement.removeChild(f)});d?(f.rel=\"preload\",f.as=\"image\",f.referrerPolicy=\"strict-origin-when-cross-origin\",f.href=b,h.head.appendChild(f)):(f.style.display=\"none\",f.referrerPolicy=\"strict-origin-when-cross-origin\",f.src=b,h.documentElement.appendChild(f))}}(),\nb=function(b){b={events:[{data:b}]};b=JSON.stringify(b);var g=p(d.location.href),f=\"https://unagi-na.amazon.com\";g&&g.toLowerCase().endsWith(\"amazon.cn\")&&(f=\"https://unagi.amazon.cn\");g=f+\"/1/events/com.amazon.Vowels.ClientMetrics\";navigator&&navigator.sendBeacon?navigator.sendBeacon(g,b):fetch(g,{method:\"POST\",body:b}).catch(function(b){n(b,\"WARN\")})},p=function(b){return(b=b.match(/^https?:\\/\\/([^\\/?#]+)(?:[\\/?#]|$)/i))&&b[1]};k(g,0,\"https://redirect.prod.experiment.routing.cloudfront.aws.a2z.com/x.png\");\nk(function(){P.when(\"3p-promise\").execute(function(g){function p(){for(var a=new URLSearchParams,c=0;c<arguments.length;c++){var b=arguments[c],l;for(l in b)a.set(l,b[l])}return a.toString()}function f(){for(var a={},c=0;c<arguments.length;c++){var b=arguments[c],l;for(l in b)a[l]=b[l]}return a}function k(a,c){var A=a.name,l=a.startTime||0,e=a.connectEnd||0,x=a.connectStart||0,d=a.domainLookupEnd||0,g=a.domainLookupStart||0,m=a.requestStart||0,k=a.responseEnd||0,h=a.responseStart||0,p=a.secureConnectionStart||\n0,t=a.transferSize||0,n=a.duration||0;0>=n&&0<l&&0<k&&(n=k-l);var q={src:A,sy:c+\".2023-05-05\"};0<=A.indexOf(\"/images/G/01/msa/vowels/metrics\")?q.l=r(n):(0<t&&(q.siz=t),0<h-m&&0<h&&0<m&&(q.ttf=r(h-m)),0<k-h&&0<k&&0<h&&(q.con=r(k-h)),0<n&&(q.dur=r(n)));0<d-g&&0<d&&0<g&&(q.dns=r(d-g));0<e-x&&0<e&&0<x&&(q.tcp=r(e-x));0<e-p&&0<e&&0<p&&(q.tls=r(e-p));\"serverTiming\"in a&&a.serverTiming.slice(0,15).forEach(function(c){var a=\"st_\"+c.name,e=c.description.substring(0,Math.min(64,c.description.length));q[a]=\nencodeURIComponent(c.duration+\";\"+e)});a=f(B,q);b(a)}function u(a){var c=f(B,{src:a,error:1}),b=m.now();return fetch(a).then(function(l){var e=m.now();var d=l.headers.get(\"x-cache\")||\"\";d=0<=d.indexOf(\"cloudfront\")?\"c\":0<=d.indexOf(\"akamai\")?\"a\":0<=d.indexOf(\"fastly\")?\"f\":\"u\";if(!l.ok)throw c.l=r(e-b),c.status=l.status,c.sy=d+\".2023-05-05\",v(d,c),c;l={name:a,duration:e-b};e=m.getEntriesByName(a);k(1===e.length?e[0]:l,d);return{cdn:d,url:a}},function(a){c.message=a.message;c.sy=\"u.2023-05-05\";v(\"u\",\nc);throw a;})}function v(a,c){try{var d=new URL(c.src)}catch(e){throw e;}var l=c.status;\"u\"===a?w(d,l).then(function(a){a.forEach(function(a){c.sy=a+\".2023-05-05\";b(c)})}):b(c)}function w(a,c){var b=[];a=[y(a,\"f\"),y(a,\"c\"),y(a,\"a\")];return g.all(a.map(D)).then(function(a){a.forEach(function(a){\"fulfilled\"===a.status?c&&a.value.statusCode===c&&b.push(a.value.cdn):c===z&&b.push(a.reason)});return 0<b.length?b:[\"u\"]})}function D(a){return a.then(function(a){return{value:a,status:\"fulfilled\"}},function(a){return{reason:a,\nstatus:\"rejected\"}})}function y(a,c){a.hostname=E[c];return fetch(a).then(function(a){return{cdn:c,statusCode:a.status}}).catch(function(a){n(a,\"WARN\");throw c;})}function F(){for(var a=new RegExp(\"^https://(.*.(images|ssl-images|media)-amazon.(?:com|cn)|\"+d.location.hostname+\")/images/\",\"i\"),c={},b=0,g=\"\",e,f,h=m.getEntriesByType(\"resource\"),k=h.length-1;0<=k;k--)e=h[k],0<e.transferSize&&e.transferSize>=e.encodedBodySize&&(e=a.exec(String(h[k].name)))&&3===e.length&&(e=e[1],f=c[e]=(c[e]||0)+1,f>\nb&&(g=e,b=f));return g}var C=navigator.userAgent,r=Math.round,m=d.performance,B={s:h.domain,u:d.location.pathname,tz:C},E={a:\"a.media-amazon.com\",c:\"dk9ps7goqoeef.cloudfront.net\",f:\"f.media-amazon.com\"},G=d.URLSearchParams&&d.fetch;m&&\"function\"===typeof m.getEntriesByName&&\"function\"===typeof m.getEntriesByType&&\"function\"===typeof m.now&&0>C.toLowerCase().indexOf(\"firefox\")&&G?function(){var a=F()||\"m.media-amazon.com\";u(\"https://\"+a+\"/images/G/01/msa/vowels/metrics.jpg?\"+p({time:+new Date,rand:r(1E6*\nMath.random()).toString()})).then(function(){var c=\"STID\"+r(1E6*Math.random()).toString()+\"-\"+ +new Date;return u(\"https://\"+a+\"/images/G/01/msa/vowels/metrics._\"+c+\"_.jpg\")}).then(function(c){c=c.cdn;t(\"cdn:\"+c,1);var b=m.getEntriesByType(\"resource\");if(b===z||0>=b.length)c=0;else{var d=\"https://\"+a+\"/images/\",e,g=0;for(e=0;e<b.length&&3>g;e++){var f=b[e];var h=f.name;0<f.transferSize&&f.transferSize>=f.encodedBodySize&&0===h.indexOf(d)&&!(0<h.indexOf(\"/images/G/01/msa/vowels/metrics\"))&&(k(f,c),\ng++)}c=g}t(\"resourceCount\",c)},function(a){t(\"resourceError\",1);a instanceof TypeError&&a.message&&a.message.includes(\"Failed to fetch\")?n(a,\"WARN\"):n(a,\"ERROR\")})}():t(\"unsupportedBrowser\",1)})},4E3)};\"loading\"!==h.readyState?k(v,1E3):d.addEventListener&&d.addEventListener(\"DOMContentLoaded\",function(){k(v,1E3)});t(\"registered\",1)})(window,document);\n});\n\n\n\n\n\n\n\nwindow.ue_ibe = (window.ue_ibe || 0) + 1;\nif (window.ue_ibe === 1) {\n(function(e,c){function h(b,a){f.push([b,a])}function g(b,a){if(b){var c=e.head||e.getElementsByTagName(\"head\")[0]||e.documentElement,d=e.createElement(\"script\");d.async=\"async\";d.src=b;d.setAttribute(\"crossorigin\",\"anonymous\");a&&a.onerror&&(d.onerror=a.onerror);a&&a.onload&&(d.onload=a.onload);c.insertBefore(d,c.firstChild)}}function k(){ue.uels=g;for(var b=0;b<f.length;b++){var a=f[b];g(a[0],a[1])}ue.deffered=1}var f=[];c.ue&&(ue.uels=h,c.ue.attach&&c.ue.attach(\"load\",k))})(document,window);\n\n\nif (window.ue && window.ue.uels) {\n        var cel_widgets = [ { \"c\":\"celwidget\" },{ \"s\":\"#nav-swmslot > div\", \"id_gen\":function(elem, index){ return 'nav_sitewide_msg'; } } ];\n\n                ue.uels(\"https://images-na.ssl-images-amazon.com/images/I/31bJewCvY-L.js\");\n}\nvar ue_mbl=ue_csm.ue.exec(function(h,a){function s(c){b=c||{};a.AMZNPerformance=b;b.transition=b.transition||{};b.timing=b.timing||{};if(a.csa){var d;b.timing.transitionStart&&(d=b.timing.transitionStart);b.timing.processStart&&(d=b.timing.processStart);d&&(csa(\"PageTiming\")(\"mark\",\"nativeTransitionStart\",d),csa(\"PageTiming\")(\"mark\",\"transitionStart\",d))}h.ue.exec(t,\"csm-android-check\")()&&b.tags instanceof Array&&(c=-1!=b.tags.indexOf(\"usesAppStartTime\")||b.transition.type?!b.transition.type&&-1<\nb.tags.indexOf(\"usesAppStartTime\")?\"warm-start\":void 0:\"view-transition\",c&&(b.transition.type=c));n=null;\"reload\"===e._nt&&h.ue_orct||\"intrapage-transition\"===e._nt?u(b):\"undefined\"===typeof e._nt&&f&&f.timing&&f.timing.navigationStart&&a.history&&\"function\"===typeof a.History&&\"object\"===typeof a.history&&a.history.length&&1!=a.history.length&&(b.timing.transitionStart=f.timing.navigationStart);p&&e.ssw(q,\"\"+(b.timing.transitionStart||n||\"\"));c=b.transition;d=e._nt?e._nt:void 0;c.subType=d;a.ue&&\na.ue.tag&&a.ue.tag(\"has-AMZNPerformance\");e.isl&&a.uex&&a.uex(\"at\",\"csm-timing\");v()}function w(c){a.ue&&a.ue.count&&a.ue.count(\"csm-cordova-plugin-failed\",1)}function t(){return a.cordova&&a.cordova.platformId&&\"android\"==a.cordova.platformId}function u(){if(p){var c=e.ssw(q),a=function(){},x=e.count||a,a=e.tag||a,k=b.timing.transitionStart,g=c&&!c.e&&c.val;n=c=g?+c.val:null;k&&g&&k>c?(x(\"csm.jumpStart.mtsDiff\",k-c||0),a(\"csm-rld-mts-gt\")):k&&g?a(\"csm-rld-mts-leq\"):g?k||a(\"csm-rld-mts-no-new\"):a(\"csm-rld-mts-no-old\")}f&&\nf.timing&&f.timing.navigationStart?b.timing.transitionStart=f.timing.navigationStart:delete b.timing.transitionStart}function v(){try{a.P.register(\"AMZNPerformance\",function(){return b})}catch(c){}}function r(){if(!b)return\"\";ue_mbl.cnt=null;var c=b.timing,d=b.transition,d=[\"mts\",l(c.transitionStart),\"mps\",l(c.processStart),\"mtt\",d.type,\"mtst\",d.subType,\"mtlt\",d.launchType];a.ue&&a.ue.tag&&(c.fr_ovr&&a.ue.tag(\"fr_ovr\"),c.fcp_ovr&&a.ue.tag(\"fcp_ovr\"),d.push(\"fr_ovr\",l(c.fr_ovr),\"fcp_ovr\",l(c.fcp_ovr)));\nfor(var c=\"\",e=0;e<d.length;e+=2){var f=d[e],g=d[e+1];\"undefined\"!==typeof g&&(c+=\"&\"+f+\"=\"+g)}return c}function l(a){if(\"undefined\"!==typeof a&&\"undefined\"!==typeof m)return a-m}function y(a,d){b&&(m=d,b.timing.transitionStart=a,b.transition.type=\"view-transition\",b.transition.subType=\"ajax-transition\",b.transition.launchType=\"normal\",ue_mbl.cnt=r)}var e=h.ue||{},m=h.ue_t0,q=\"csm-last-mts\",p=1===h.ue_sswmts,n,f=a.performance,b;if(a.P&&a.P.when&&a.P.register)return 1===a.ue_fnt&&(m=a.aPageStart||\nh.ue_t0),a.P.when(\"CSMPlugin\").execute(function(a){a.buildAMZNPerformance&&a.buildAMZNPerformance({successCallback:s,failCallback:w})}),{cnt:r,ajax:y}},\"mobile-timing\")(ue_csm,ue_csm.window);\n\n(function(d){d._uess=function(){var a=\"\";screen&&screen.width&&screen.height&&(a+=\"&sw=\"+screen.width+\"&sh=\"+screen.height);var b=function(a){var b=document.documentElement[\"client\"+a];return\"CSS1Compat\"===document.compatMode&&b||document.body[\"client\"+a]||b},c=b(\"Width\"),b=b(\"Height\");c&&b&&(a+=\"&vw=\"+c+\"&vh=\"+b);return a}})(ue_csm);\n\n(function(a){function c(a){d(\"log\",a)}var d=csa(\"Errors\",{producerId:\"csa\",logOptions:{ent:\"all\"}});a.ue_err.buffer&&d&&(a.ue_err.buffer.forEach(c),a.ue_err.buffer.push=c);var b=document.ue_backdetect;b&&b.ue_back&&a.ue&&(a.ue.bfini=b.ue_back.value);a.uet&&a.uet(\"be\");a.onLdEnd&&(window.addEventListener?window.addEventListener(\"load\",a.onLdEnd,!1):window.attachEvent&&window.attachEvent(\"onload\",a.onLdEnd));a.ueh&&a.ueh(0,window,\"load\",a.onLd,1);a.ue&&a.ue.tag&&(a.ue_furl?(b=a.ue_furl.replace(/\\./g,\n\"-\"),a.ue.tag(b)):a.ue.tag(\"nofls\"))})(ue_csm);\n\n(function(g,h){function d(a,d){var b={};if(!e||!f)try{var c=h.sessionStorage;c?a&&(\"undefined\"!==typeof d?c.setItem(a,d):b.val=c.getItem(a)):f=1}catch(g){e=1}e&&(b.e=1);return b}var b=g.ue||{},a=\"\",f,e,c,a=d(\"csmtid\");f?a=\"NA\":a.e?a=\"ET\":(a=a.val,a||(a=b.oid||\"NI\",d(\"csmtid\",a)),c=d(b.oid),c.e||(c.val=c.val||0,d(b.oid,c.val+1)),b.ssw=d);b.tabid=a})(ue_csm,ue_csm.window);\n\nue_csm.ue.exec(function(e,f){var a=e.ue||{},b=a._wlo,d;if(a.ssw){d=a.ssw(\"CSM_previousURL\").val;var c=f.location,b=b?b:c&&c.href?c.href.split(\"#\")[0]:void 0;c=(b||\"\")===a.ssw(\"CSM_previousURL\").val;!c&&b&&a.ssw(\"CSM_previousURL\",b);d=c?\"reload\":d?\"intrapage-transition\":\"first-view\"}else d=\"unknown\";a._nt=d},\"NavTypeModule\")(ue_csm,window);\nue_csm.ue.exec(function(c,a){function g(a){a.run(function(e){d.tag(\"csm-feature-\"+a.name+\":\"+e);d.isl&&c.uex(\"at\")})}if(a.addEventListener)for(var d=c.ue||{},f=[{name:\"touch-enabled\",run:function(b){var e=function(){a.removeEventListener(\"touchstart\",c,!0);a.removeEventListener(\"mousemove\",d,!0)},c=function(){b(\"true\");e()},d=function(){b(\"false\");e()};a.addEventListener(\"touchstart\",c,!0);a.addEventListener(\"mousemove\",d,!0)}}],b=0;b<f.length;b++)g(f[b])},\"csm-features\")(ue_csm,window);\n\n\n(function(a,e){function c(a){d(\"recordCounter\",a.c,a.v)}var b=e.images,d=csa(\"Metrics\",{producerId:\"csa\"});b&&b.length&&a.ue.count(\"totalImages\",b.length);a.ue.cv.buffer&&d&&(a.ue.cv.buffer.forEach(c),a.ue.cv.buffer.push=c)})(ue_csm,document);\n(function(b){function c(){var d=[];a.log&&a.log.isStub&&a.log.replay(function(a){e(d,a)});a.clog&&a.clog.isStub&&a.clog.replay(function(a){e(d,a)});d.length&&(a._flhs+=1,n(d),p(d))}function g(){a.log&&a.log.isStub&&(a.onflush&&a.onflush.replay&&a.onflush.replay(function(a){a[0]()}),a.onunload&&a.onunload.replay&&a.onunload.replay(function(a){a[0]()}),c())}function e(d,b){var c=b[1],f=b[0],e={};a._lpn[c]=(a._lpn[c]||0)+1;e[c]=f;d.push(e)}function n(b){q&&(a._lpn.csm=(a._lpn.csm||0)+1,b.push({csm:{k:\"chk\",\nf:a._flhs,l:a._lpn,s:\"inln\"}}))}function p(a){if(h)a=k(a),b.navigator.sendBeacon(l,a);else{a=k(a);var c=new b[f];c.open(\"POST\",l,!0);c.setRequestHeader&&c.setRequestHeader(\"Content-type\",\"text/plain\");c.send(a)}}function k(a){return JSON.stringify({rid:b.ue_id,sid:b.ue_sid,mid:b.ue_mid,mkt:b.ue_mkt,sn:b.ue_sn,reqs:a})}var f=\"XMLHttpRequest\",q=1===b.ue_ddq,a=b.ue,r=b[f]&&\"withCredentials\"in new b[f],h=b.navigator&&b.navigator.sendBeacon,l=\"//\"+b.ue_furl+\"/1/batch/1/OE/\",m=b.ue_fci_ft||5E3;a&&(r||h)&&\n(a._flhs=a._flhs||0,a._lpn=a._lpn||{},a.attach&&(a.attach(\"beforeunload\",a.exec(g,\"fcli-bfu\")),a.attach(\"pagehide\",a.exec(g,\"fcli-ph\"))),m&&b.setTimeout(a.exec(c,\"fcli-t\"),m),a._ffci=a.exec(c))})(window);\n\n\n(function(k,c){function l(a,b){return a.filter(function(a){return a.initiatorType==b})}function f(a,c){if(b.t[a]){var g=b.t[a]-b._t0,e=c.filter(function(a){return 0!==a.responseEnd&&m(a)<g}),f=l(e,\"script\"),h=l(e,\"link\"),k=l(e,\"img\"),n=e.map(function(a){return a.name.split(\"/\")[2]}).filter(function(a,b,c){return a&&c.lastIndexOf(a)==b}),q=e.filter(function(a){return a.duration<p}),s=g-Math.max.apply(null,e.map(m))<r|0;\"af\"==a&&(b._afjs=f.length);return a+\":\"+[e[d],f[d],h[d],k[d],n[d],q[d],s].join(\"-\")}}\nfunction m(a){return a.responseEnd-(b._t0-c.timing.navigationStart)}function n(){var a=c[h](\"resource\"),d=f(\"cf\",a),g=f(\"af\",a),a=f(\"ld\",a);delete b._rt;b._ld=b.t.ld-b._t0;b._art&&b._art();return[d,g,a].join(\"_\")}var p=20,r=50,d=\"length\",b=k.ue,h=\"getEntriesByType\";b._rre=m;b._rt=c&&c.timing&&c[h]&&n})(ue_csm,window.performance);\n\n\n(function(c,d){var b=c.ue,a=d.navigator;b&&b.tag&&a&&(a=a.connection||a.mozConnection||a.webkitConnection)&&a.type&&b.tag(\"netInfo:\"+a.type)})(ue_csm,window);\n\n\n(function(c,d){function h(a,b){for(var c=[],d=0;d<a.length;d++){var e=a[d],f=b.encode(e);if(e[k]){var g=b.metaSep,e=e[k],l=b.metaPairSep,h=[],m=void 0;for(m in e)e.hasOwnProperty(m)&&h.push(m+\"=\"+e[m]);e=h.join(l);f+=g+e}c.push(f)}return c.join(b.resourceSep)}function s(a){var b=a[k]=a[k]||{};b[t]||(b[t]=c.ue_mid);b[u]||(b[u]=c.ue_sid);b[f]||(b[f]=c.ue_id);b.csm=1;a=\"//\"+c.ue_furl+\"/1/\"+a[v]+\"/1/OP/\"+a[w]+\"/\"+a[x]+\"/\"+h([a],y);if(n)try{n.call(d[p],a)}catch(g){c.ue.sbf=1,(new Image).src=a}else(new Image).src=\na}function q(){g&&g.isStub&&g.replay(function(a,b,c){a=a[0];b=a[k]=a[k]||{};b[f]=b[f]||c;s(a)});l.impression=s;g=null}if(!(1<c.ueinit)){var k=\"metadata\",x=\"impressionType\",v=\"foresterChannel\",w=\"programGroup\",t=\"marketplaceId\",u=\"session\",f=\"requestId\",p=\"navigator\",l=c.ue||{},n=d[p]&&d[p].sendBeacon,r=function(a,b,c,d){return{encode:d,resourceSep:a,metaSep:b,metaPairSep:c}},y=r(\"\",\"?\",\"&\",function(a){return h(a.impressionData,z)}),z=r(\"/\",\":\",\",\",function(a){return a.featureName+\":\"+h(a.resources,\nA)}),A=r(\",\",\"@\",\"|\",function(a){return a.id}),g=l.impression;n?q():(l.attach(\"load\",q),l.attach(\"beforeunload\",q));try{d.P&&d.P.register&&d.P.register(\"impression-client\",function(){})}catch(B){c.ueLogError(B,{logLevel:\"WARN\"})}}})(ue_csm,window);\n\n\n\nvar ue_pty = \"MASDetailPage\";\n\n\n\n\nvar ue_adb = 4;\nvar ue_adb_rtla = 1;\nue_csm.ue.exec(function(y,a){function t(){if(d&&f){var a;a:{try{a=d.getItem(g);break a}catch(c){}a=void 0}if(a)return b=a,!0}return!1}function u(){if(a.fetch)fetch(m).then(function(a){if(!a.ok)throw Error(a.statusText);return a.text?a.text():null}).then(function(b){b?(-1<b.indexOf(\"window.ue_adb_chk = 1\")&&(a.ue_adb_chk=1),n()):h()})[\"catch\"](h);else e.uels(m,{onerror:h,onload:n})}function h(){b=k;l();if(f)try{d.setItem(g,b)}catch(a){}}function n(){b=1===a.ue_adb_chk?p:k;l();if(f)try{d.setItem(g,\nb)}catch(c){}}function q(){a.ue_adb_rtla&&c&&0<c.ec&&!1===r&&(c.elh=null,ueLogError({m:\"Hit Info\",fromOnError:1},{logLevel:\"INFO\",adb:b}),r=!0)}function l(){e.tag(b);e.isl&&a.uex&&uex(\"at\",b);s&&s.updateCsmHit(\"adb\",b);c&&0<c.ec?q():a.ue_adb_rtla&&c&&(c.elh=q)}function v(){return b}if(a.ue_adb){a.ue_fadb=a.ue_fadb||10;var e=a.ue,k=\"adblk_yes\",p=\"adblk_no\",m=\"https://m.media-amazon.com/images/G/01/csm/showads.v2.js?ad_size=-ad-util-&adstype=-ad-sidebar-&advertiser=-ad-banner-\",b=\"adblk_unk\",d;a:{try{d=\na.localStorage;break a}catch(z){}d=void 0}var g=\"csm:adb\",c=a.ue_err,s=e.cookie,f=void 0!==a.localStorage,w=Math.random()>1-1/a.ue_fadb,r=!1,x=t();w||!x?u():l();a.ue_isAdb=v;a.ue_isAdb.unk=\"adblk_unk\";a.ue_isAdb.no=p;a.ue_isAdb.yes=k}},\"adb\")(document,window);\n\n\n\n\n(function(c,l,m){function h(a){if(a)try{if(a.id)return\"//*[@id='\"+a.id+\"']\";var b,d=1,e;for(e=a.previousSibling;e;e=e.previousSibling)e.nodeName===a.nodeName&&(d+=1);b=d;var c=a.nodeName;1!==b&&(c+=\"[\"+b+\"]\");a.parentNode&&(c=h(a.parentNode)+\"/\"+c);return c}catch(f){return\"DETACHED\"}}function f(a){if(a&&a.getAttribute)return a.getAttribute(k)?a.getAttribute(k):f(a.parentElement)}var k=\"data-cel-widget\",g=!1,d=[];(c.ue||{}).isBF=function(){try{var a=JSON.parse(localStorage[\"csm-bf\"]||\"[]\"),b=0<=a.indexOf(c.ue_id);\na.unshift(c.ue_id);a=a.slice(0,20);localStorage[\"csm-bf\"]=JSON.stringify(a);return b}catch(d){return!1}}();c.ue_utils={getXPath:h,getFirstAscendingWidget:function(a,b){c.ue_cel&&c.ue_fem?!0===g?b(f(a)):d.push({element:a,callback:b}):b()},notifyWidgetsLabeled:function(){if(!1===g){g=!0;for(var a=f,b=0;b<d.length;b++)if(d[b].hasOwnProperty(\"callback\")&&d[b].hasOwnProperty(\"element\")){var c=d[b].callback,e=d[b].element;\"function\"===typeof c&&\"function\"===typeof a&&c(a(e))}d=null}},extractStringValue:function(a){if(\"string\"===\ntypeof a)return a}}})(ue_csm,window,document);\n\n\n(function(a){a.ue_cel||(a.ue_cel=function(){function f(a,c){c?c.r=v:c={r:v,c:1};!ue_csm.ue_sclog&&c.clog&&d.clog?d.clog(a,c.ns||q,c):c.glog&&d.glog?d.glog(a,c.ns||q,c):d.log(a,c.ns||q,c)}function m(a,d){\"function\"===typeof g&&g(\"log\",{schemaId:s+\".RdCSI.1\",eventType:a,clientData:d},{ent:{page:[\"requestId\"]}})}function c(){var a=n.length;if(0<a){for(var c=[],b=0;b<a;b++){var F=n[b].api;F.ready()?(F.on({ts:d.d,ns:q}),e.push(n[b]),f({k:\"mso\",n:n[b].name,t:d.d()})):c.push(n[b])}n=c}}function h(){if(!h.executed){for(var a=\n0;a<e.length;a++)e[a].api.off&&e[a].api.off({ts:d.d,ns:q});A();f({k:\"eod\",t0:d.t0,t:d.d()},{c:1,il:1});h.executed=1;for(a=0;a<e.length;a++)n.push(e[a]);e=[];b(t);b(x)}}function A(a){f({k:\"hrt\",t:d.d()},{c:1,il:1,n:a});l=Math.min(w,r*l);y()}function y(){b(x);x=k(function(){A(!0)},l)}function u(){h.executed||A()}var p=a.window,k=p.setTimeout,b=p.clearTimeout,r=1.5,w=p.ue_cel_max_hrt||3E4,s=\"robotdetection\",n=[],e=[],q=a.ue_cel_ns||\"cel\",t,x,d=a.ue,E=a.uet,B=a.uex,v=d.rid,C=p.csa,g,l=p.ue_cel_hrt_int||\n3E3,z=p.requestAnimationFrame||function(a){a()};!a.ue_cel_lclia&&C&&(g=C(\"Events\",{producerId:s}));if(d.isBF)f({k:\"bft\",t:d.d()});else{\"function\"==typeof E&&E(\"bb\",\"csmCELLSframework\",{wb:1});k(c,0);d.onunload(h);if(d.onflush)d.onflush(u);t=k(h,6E5);y();\"function\"==typeof B&&B(\"ld\",\"csmCELLSframework\",{wb:1});return{registerModule:function(a,b){n.push({name:a,api:b});f({k:\"mrg\",n:a,t:d.d()});c()},reset:function(a){f({k:\"rst\",t0:d.t0,t:d.d()});n=n.concat(e);e=[];for(var r=n.length,g=0;g<r;g++)n[g].api.off(),\nn[g].api.reset();v=a||d.rid;c();b(t);t=k(h,6E5);h.executed=0},timeout:function(a,d){return k(function(){z(function(){h.executed||a()})},d)},log:f,csaEventLog:m,off:h}}}())})(ue_csm);\n(function(a){a.ue_pdm||!a.ue_cel||a.ue.isBF||(a.ue_pdm=function(){function f(){try{var d=b.screen;if(d){var c={w:d.width,aw:d.availWidth,h:d.height,ah:d.availHeight,cd:d.colorDepth,pd:d.pixelDepth};e&&e.w===c.w&&e.h===c.h&&e.aw===c.aw&&e.ah===c.ah&&e.pd===c.pd&&e.cd===c.cd||(e=c,e.t=s(),e.k=\"sci\",E(e),!C&&g&&l(\"sci\",{h:(e.h||\"0\")+\"\"}))}var k=r.body||{},h=r.documentElement||{},m={w:Math.max(k.scrollWidth||0,k.offsetWidth||0,h.clientWidth||0,h.scrollWidth||0,h.offsetWidth||0),h:Math.max(k.scrollHeight||\n0,k.offsetHeight||0,h.clientHeight||0,h.scrollHeight||0,h.offsetHeight||0)};q&&q.w===m.w&&q.h===m.h||(q=m,q.t=s(),q.k=\"doi\",E(q));w=a.ue_cel.timeout(f,n);x+=1}catch(p){b.ueLogError&&ueLogError(p,{attribution:\"csm-cel-page-module\",logLevel:\"WARN\"})}}function m(){u(\"ebl\",\"default\",!1)}function c(){u(\"efo\",\"default\",!0)}function h(){u(\"ebl\",\"app\",!1)}function A(){u(\"efo\",\"app\",!0)}function y(){b.setTimeout(function(){r[H]?u(\"ebl\",\"pageviz\",!1):u(\"efo\",\"pageviz\",!0)},0)}function u(a,d,c){t!==c&&(E({k:a,\nt:s(),s:d},{ff:!0===c?0:1}),!C&&g&&l(a,{t:(s()||\"0\")+\"\",s:d}));t=c}function p(){d.attach&&(z&&d.attach(D,y,r),I&&P.when(\"mash\").execute(function(a){a&&a.addEventListener&&(a.addEventListener(\"appPause\",h),a.addEventListener(\"appResume\",A))}),d.attach(\"blur\",m,b),d.attach(\"focus\",c,b))}function k(){d.detach&&(z&&d.detach(D,y,r),I&&P.when(\"mash\").execute(function(a){a&&a.removeEventListener&&(a.removeEventListener(\"appPause\",h),a.removeEventListener(\"appResume\",A))}),d.detach(\"blur\",m,b),d.detach(\"focus\",\nc,b))}var b=a.window,r=a.document,w,s,n,e,q,t=null,x=0,d=a.ue,E=a.ue_cel.log,B=a.uet,v=a.uex,C=a.ue_cel_lclia,g=b.csa,l=a.ue_cel.csaEventLog,z=!!d.pageViz,D=z&&d.pageViz.event,H=z&&d.pageViz.propHid,I=b.P&&b.P.when;\"function\"==typeof B&&B(\"bb\",\"csmCELLSpdm\",{wb:1});return{on:function(a){n=a.timespan||500;s=a.ts;p();a=b.location;E({k:\"pmd\",o:a.origin,p:a.pathname,t:s()});f();\"function\"==typeof v&&v(\"ld\",\"csmCELLSpdm\",{wb:1})},off:function(a){clearTimeout(w);k();d.count&&d.count(\"cel.PDM.TotalExecutions\",\nx)},ready:function(){return r.body&&a.ue_cel&&a.ue_cel.log},reset:function(){e=q=null}}}(),a.ue_cel&&a.ue_cel.registerModule(\"page module\",a.ue_pdm))})(ue_csm);\n(function(a){a.ue_vpm||!a.ue_cel||a.ue.isBF||(a.ue_vpm=function(){function f(){var a=y(),b={w:k.innerWidth,h:k.innerHeight,x:k.pageXOffset,y:k.pageYOffset};c&&c.w==b.w&&c.h==b.h&&c.x==b.x&&c.y==b.y||(b.t=a,b.k=\"vpi\",c=b,r(c,{clog:1}),!q&&t&&x(\"vpi\",{t:(c.t||\"0\")+\"\",h:(c.h||\"0\")+\"\",y:(c.y||\"0\")+\"\",w:(c.w||\"0\")+\"\",x:(c.x||\"0\")+\"\"}));h=0;u=y()-a;p+=1}function m(){h||(h=a.ue_cel.timeout(f,A))}var c,h,A,y,u=0,p=0,k=a.window,b=a.ue,r=a.ue_cel.log,w=a.uet,s=a.uex,n=b.attach,e=b.detach,q=a.ue_cel_lclia,t=\nk.csa,x=a.ue_cel.csaEventLog;\"function\"==typeof w&&w(\"bb\",\"csmCELLSvpm\",{wb:1});return{on:function(a){y=a.ts;A=a.timespan||100;f();n&&(n(\"scroll\",m),n(\"resize\",m));\"function\"==typeof s&&s(\"ld\",\"csmCELLSvpm\",{wb:1})},off:function(a){clearTimeout(h);e&&(e(\"scroll\",m),e(\"resize\",m));b.count&&(b.count(\"cel.VPI.TotalExecutions\",p),b.count(\"cel.VPI.TotalExecutionTime\",u),b.count(\"cel.VPI.AverageExecutionTime\",u/p))},ready:function(){return a.ue_cel&&a.ue_cel.log},reset:function(){c=void 0},getVpi:function(){return c}}}(),\na.ue_cel&&a.ue_cel.registerModule(\"viewport module\",a.ue_vpm))})(ue_csm);\n(function(a){if(!a.ue_fem&&a.ue_cel&&a.ue_utils){var f=a.ue||{},m=a.window,c=m.document;!f.isBF&&!a.ue_fem&&c.querySelector&&m.getComputedStyle&&[].forEach&&(a.ue_fem=function(){function h(a,d){return a>d?3>a-d:3>d-a}function A(a,d){var c=m.pageXOffset,b=m.pageYOffset,k;a:{try{if(a){var g=a.getBoundingClientRect(),e,r=0===a.offsetWidth&&0===a.offsetHeight;c:{for(var f=a.parentNode,w=g.left||0,n=g.top||0,p=g.width||0,q=g.height||0;f&&f!==document.body;){var l;d:{try{var s=void 0;if(f)var G=f.getBoundingClientRect(),\ns={x:G.left||0,y:G.top||0,w:G.width||0,h:G.height||0};else s=void 0;l=s;break d}catch(v){}l=void 0}var t=window.getComputedStyle(f),u=\"hidden\"===t.overflow,y=u||\"hidden\"===t.overflowX,z=u||\"hidden\"===t.overflowY,J=n+q-1<l.y+1||n+1>l.y+l.h-1;if((w+p-1<l.x+1||w+1>l.x+l.w-1)&&y||J&&z){e=!0;break c}f=f.parentNode}e=!1}k={x:g.left+c||0,y:g.top+b||0,w:g.width||0,h:g.height||0,d:(r||e)|0}}else k=void 0;break a}catch(A){}k=void 0}if(k&&!a.cel_b)a.cel_b=k,C({n:a.getAttribute(x),w:a.cel_b.w,h:a.cel_b.h,d:a.cel_b.d,\nx:a.cel_b.x,y:a.cel_b.y,t:d,k:\"ewi\",cl:a.className},{clog:1});else{if(c=k)c=a.cel_b,b=k,c=b.d===c.d&&1===b.d?!1:!(h(c.x,b.x)&&h(c.y,b.y)&&h(c.w,b.w)&&h(c.h,b.h)&&c.d===b.d);c&&(a.cel_b=k,C({n:a.getAttribute(x),w:a.cel_b.w,h:a.cel_b.h,d:a.cel_b.d,x:a.cel_b.x,y:a.cel_b.y,t:d,k:\"ewi\"},{clog:1}))}}function y(b,g){var h;h=b.c?c.getElementsByClassName(b.c):b.id?[c.getElementById(b.id)]:c.querySelectorAll(b.s);b.w=[];for(var f=0;f<h.length;f++){var e=h[f];if(e){if(!e.getAttribute(x)){var r=e.getAttribute(\"cel_widget_id\")||\n(b.id_gen||v)(e,f)||e.id;e.setAttribute(x,r)}b.w.push(e);k(Q,e,g)}}!1===B&&(E++,E===d.length&&(B=!0,a.ue_utils.notifyWidgetsLabeled()))}function u(a,c){g.contains(a)||C({n:a.getAttribute(x),t:c,k:\"ewd\"},{clog:1})}function p(a){K.length&&ue_cel.timeout(function(){if(q){for(var c=R(),d=!1;R()-c<e&&!d;){for(d=S;0<d--&&0<K.length;){var b=K.shift();T[b.type](b.elem,b.time)}d=0===K.length}U++;p(a)}},0)}function k(a,c,d){K.push({type:a,elem:c,time:d})}function b(a,c){for(var b=0;b<d.length;b++)for(var e=\nd[b].w||[],g=0;g<e.length;g++)k(a,e[g],c)}function r(){M||(M=a.ue_cel.timeout(function(){M=null;var c=t();b(W,c);for(var g=0;g<d.length;g++)k(X,d[g],c);0===d.length&&!1===B&&(B=!0,a.ue_utils.notifyWidgetsLabeled());p(c)},n))}function w(){M||N||(N=a.ue_cel.timeout(function(){N=null;var a=t();b(Q,a);p(a)},n))}function s(){return z&&D&&g&&g.contains&&g.getBoundingClientRect&&t}var n=50,e=4.5,q=!1,t,x=\"data-cel-widget\",d=[],E=0,B=!1,v=function(){},C=a.ue_cel.log,g,l,z,D,H=m.MutationObserver||m.WebKitMutationObserver||\nm.MozMutationObserver,I=!!H,F,G,O=\"DOMAttrModified\",L=\"DOMNodeInserted\",J=\"DOMNodeRemoved\",N,M,K=[],U=0,S=null,W=\"removedWidget\",X=\"updateWidgets\",Q=\"processWidget\",T,V=m.performance||{},R=V.now&&function(){return V.now()}||function(){return Date.now()};\"function\"==typeof uet&&uet(\"bb\",\"csmCELLSfem\",{wb:1});return{on:function(b){function e(){if(s()){T={removedWidget:u,updateWidgets:y,processWidget:A};if(I){var a={attributes:!0,subtree:!0};F=new H(w);G=new H(r);F.observe(g,a);G.observe(g,{childList:!0,\nsubtree:!0});G.observe(l,a)}else z.call(g,O,w),z.call(g,L,r),z.call(g,J,r),z.call(l,L,w),z.call(l,J,w);r()}}g=c.body;l=c.head;z=g.addEventListener;D=g.removeEventListener;t=b.ts;d=a.cel_widgets||[];S=b.bs||5;f.deffered?e():f.attach&&f.attach(\"load\",e);\"function\"==typeof uex&&uex(\"ld\",\"csmCELLSfem\",{wb:1});q=!0},off:function(){s()&&(G&&(G.disconnect(),G=null),F&&(F.disconnect(),F=null),D.call(g,O,w),D.call(g,L,r),D.call(g,J,r),D.call(l,L,w),D.call(l,J,w));f.count&&f.count(\"cel.widgets.batchesProcessed\",\nU);q=!1},ready:function(){return a.ue_cel&&a.ue_cel.log},reset:function(){d=a.cel_widgets||[]}}}(),a.ue_cel&&a.ue_fem&&a.ue_cel.registerModule(\"features module\",a.ue_fem))}})(ue_csm);\n(function(a){!a.ue_mcm&&a.ue_cel&&a.ue_utils&&!a.ue.isBF&&(a.ue_mcm=function(){function f(a,b){var h=a.srcElement||a.target||{},f={k:m,w:(b||{}).ow||(A.body||{}).scrollWidth,h:(b||{}).oh||(A.body||{}).scrollHeight,t:(b||{}).ots||c(),x:a.pageX,y:a.pageY,p:p.getXPath(h),n:h.nodeName};y&&\"function\"===typeof y.now&&a.timeStamp&&(f.dt=(b||{}).odt||y.now()-a.timeStamp,f.dt=parseFloat(f.dt.toFixed(2)));a.button&&(f.b=a.button);h.href&&(f.r=p.extractStringValue(h.href));h.id&&(f.i=h.id);h.className&&h.className.split&&\n(f.c=h.className.split(/\\s+/));u(f,{c:1})}var m=\"mcm\",c,h=a.window,A=h.document,y=h.performance,u=a.ue_cel.log,p=a.ue_utils;return{on:function(k){c=k.ts;a.ue_cel_stub&&a.ue_cel_stub.replayModule(m,f);h.addEventListener&&h.addEventListener(\"mousedown\",f,!0)},off:function(a){h.addEventListener&&h.removeEventListener(\"mousedown\",f,!0)},ready:function(){return a.ue_cel&&a.ue_cel.log},reset:function(){}}}(),a.ue_cel&&a.ue_cel.registerModule(\"mouse click module\",a.ue_mcm))})(ue_csm);\n(function(a){a.ue_mmm||!a.ue_cel||a.ue.isBF||(a.ue_mmm=function(f){function m(a,c){var b={x:a.pageX||a.x||0,y:a.pageY||a.y||0,t:p()};!c&&l&&(b.t-l.t<A||b.x==l.x&&b.y==l.y)||(l=b,v.push(b))}function c(){if(v.length){E=F.now();for(var a=0;a<v.length;a++){var c=v[a],b=a;z=v[g];D=c;var e=void 0;if(!(e=2>b)){e=void 0;a:if(v[b].t-v[b-1].t>h)e=0;else{for(e=g+1;e<b;e++){var f=z,k=D,l=v[e];H=(k.x-f.x)*(f.y-l.y)-(f.x-l.x)*(k.y-f.y);if(H*H/((k.x-f.x)*(k.x-f.x)+(k.y-f.y)*(k.y-f.y))>y){e=0;break a}}e=1}e=!e}(I=\ne)?g=b-1:C.pop();C.push(c)}B=F.now()-E;q=Math.min(q,B);t=Math.max(t,B);x=(x*d+B)/(d+1);d+=1;n({k:u,e:C,min:Math.floor(1E3*q),max:Math.floor(1E3*t),avg:Math.floor(1E3*x)},{c:1});v=[];C=[];g=0}}var h=100,A=20,y=25,u=\"mmm1\",p,k,b=a.window,r=b.document,w=b.setInterval,s=a.ue,n=a.ue_cel.log,e,q=1E3,t=0,x=0,d=0,E,B,v=[],C=[],g=0,l,z,D,H,I,F=f&&f.now&&f||Date.now&&Date||{now:function(){return(new Date).getTime()}};return{on:function(a){p=a.ts;k=a.ns;s.attach&&s.attach(\"mousemove\",m,r);e=w(c,3E3)},off:function(a){k&&\n(l&&m(l,!0),c());clearInterval(e);s.detach&&s.detach(\"mousemove\",m,r)},ready:function(){return a.ue_cel&&a.ue_cel.log},reset:function(){v=[];C=[];g=0;l=null}}}(window.performance),a.ue_cel&&a.ue_cel.registerModule(\"mouse move module\",a.ue_mmm))})(ue_csm);\n\n\n\nue_csm.ue.exec(function(b,c){var e=function(){},f=function(){return{send:function(b,d){if(d&&b){var a;if(c.XDomainRequest)a=new XDomainRequest,a.onerror=e,a.ontimeout=e,a.onprogress=e,a.onload=e,a.timeout=0;else if(c.XMLHttpRequest){if(a=new XMLHttpRequest,!(\"withCredentials\"in a))throw\"\";}else a=void 0;if(!a)throw\"\";a.open(\"POST\",b,!0);a.setRequestHeader&&a.setRequestHeader(\"Content-type\",\"text/plain\");a.send(d)}},isSupported:!0}}(),g=function(){return{send:function(c,d){if(c&&d)if(navigator.sendBeacon(c,\nd))b.ue_sbuimp&&b.ue&&b.ue.ssw&&b.ue.ssw(\"eelsts\",\"scs\");else throw\"\";},isSupported:!!navigator.sendBeacon&&!(c.cordova&&c.cordova.platformId&&\"ios\"==c.cordova.platformId)}}();b.ue._ajx=f;b.ue._sBcn=g},\"Transportation-clients\")(ue_csm,window);\nue_csm.ue.exec(function(b,k){function B(){for(var a=0;a<arguments.length;a++){var c=arguments[a];try{var g;if(c.isSupported){var f=u.buildPayload(l,e);g=c.send(K,f)}else throw dummyException;return g}catch(d){}}a={m:\"All supported clients failed\",attribution:\"CSMSushiClient_TRANSPORTATION_FAIL\",f:\"sushi-client.js\",logLevel:\"ERROR\"};C(a,k.ue_err_chan||\"jserr\");b.ue_err.buffer&&b.ue_err.buffer.push(a)}function m(){if(e.length){for(var a=0;a<n.length;a++)n[a]();B(d._sBcn||{},d._ajx||{});e=[];h={};l=\n{};v=w=r=x=0}}function L(){var a=new Date,c=function(a){return 10>a?\"0\"+a:a};return Date.prototype.toISOString?a.toISOString():a.getUTCFullYear()+\"-\"+c(a.getUTCMonth()+1)+\"-\"+c(a.getUTCDate())+\"T\"+c(a.getUTCHours())+\":\"+c(a.getUTCMinutes())+\":\"+c(a.getUTCSeconds())+\".\"+String((a.getUTCMilliseconds()/1E3).toFixed(3)).slice(2,5)+\"Z\"}function y(a){try{return JSON.stringify(a)}catch(c){}return null}function D(a,c,g,f){var q=!1;f=f||{};s++;if(s==E){var p={m:\"Max number of Sushi Logs exceeded\",f:\"sushi-client.js\",\nlogLevel:\"ERROR\",attribution:\"CSMSushiClient_MAX_CALLS\"};C(p,k.ue_err_chan||\"jserr\");b.ue_err.buffer&&b.ue_err.buffer.push(p)}if(p=!(s>=E))(p=a&&-1<a.constructor.toString().indexOf(\"Object\")&&c&&-1<c.constructor.toString().indexOf(\"String\")&&g&&-1<g.constructor.toString().indexOf(\"String\"))||M++;p&&(d.count&&d.count(\"Event:\"+g,1),a.producerId=a.producerId||c,a.schemaId=a.schemaId||g,a.timestamp=L(),c=Date.now?Date.now():+new Date,g=Math.random().toString().substring(2,12),a.messageId=b.ue_id+\"-\"+\nc+\"-\"+g,f&&!f.ssd&&(a.sessionId=a.sessionId||b.ue_sid,a.requestId=a.requestId||b.ue_id,a.obfuscatedMarketplaceId=a.obfuscatedMarketplaceId||b.ue_mid),(c=y(a))?(c=c.length,(e.length==N||r+c>O)&&m(),r+=c,a={data:u.compressEvent(a)},e.push(a),(f||{}).n?0===F?m():v||(v=k.setTimeout(m,F)):w||(w=k.setTimeout(m,P)),q=!0):q=!1);!q&&b.ue_int&&console.error(\"Invalid JS Nexus API call\");return q}function G(){if(!H){for(var a=0;a<z.length;a++)z[a]();for(a=0;a<n.length;a++)n[a]();e.length&&(b.ue_sbuimp&&b.ue&&\nb.ue.ssw&&(a=y({dct:l,evt:e}),b.ue.ssw(\"eeldata\",a),b.ue.ssw(\"eelsts\",\"unk\")),B(d._sBcn||{}));H=!0}}function I(a){z.push(a)}function J(a){n.push(a)}var E=1E3,N=499,O=524288,t=function(){},d=b.ue||{},C=d.log||t,Q=b.uex||t;(b.uet||t)(\"bb\",\"ue_sushi_v1\",{wb:1});var K=b.ue_surl||\"https://unagi-na.amazon.com/1/events/com.amazon.csm.nexusclient.gamma\",R=[\"messageId\",\"timestamp\"],A=\"#\",e=[],h={},l={},r=0,x=0,M=0,s=0,z=[],n=[],H=!1,v,w,F=void 0===b.ue_hpsi?1E3:b.ue_hpsi,P=void 0===b.ue_lpsi?1E4:b.ue_lpsi,\nu=function(){function a(a){h[a]=A+x++;l[h[a]]=a;return h[a]}function c(b){if(!(b instanceof Function)){if(b instanceof Array){for(var f=[],d=b.length,e=0;e<d;e++)f[e]=c(b[e]);return f}if(b instanceof Object){f={};for(d in b)b.hasOwnProperty(d)&&(f[h[d]?h[d]:a(d)]=-1===R.indexOf(d)?c(b[d]):b[d]);return f}return\"string\"===typeof b&&(b.length>(A+x).length||b.charAt(0)===A)?h[b]?h[b]:a(b):b}}return{compressEvent:c,buildPayload:function(){return y({cs:{dct:l},events:e})}}}();(function(){if(d.event&&d.event.isStub){if(b.ue_sbuimp&&\nb.ue&&b.ue.ssw){var a=b.ue.ssw(\"eelsts\").val;if(a&&\"unk\"===a&&(a=b.ue.ssw(\"eeldata\").val)){var c;a:{try{c=JSON.parse(a);break a}catch(g){}c=null}c&&c.evt instanceof Array&&c.dct instanceof Object&&(e=c.evt,l=c.dct,e&&l&&(m(),b.ue.ssw(\"eeldata\",\"{}\"),b.ue.ssw(\"eelsts\",\"scs\")))}}d.event.replay(function(a){a[3]=a[3]||{};a[3].n=1;D.apply(this,a)});d.onSushiUnload.replay(function(a){I(a[0])});d.onSushiFlush.replay(function(a){J(a[0])})}})();d.attach(\"beforeunload\",G);d.attach(\"pagehide\",G);d._cmps=u;d.event=\nD;d.event.reset=function(){s=0};d.onSushiUnload=I;d.onSushiFlush=J;try{k.P&&k.P.register&&k.P.register(\"sushi-client\",t)}catch(S){b.ueLogError(S,{logLevel:\"WARN\"})}Q(\"ld\",\"ue_sushi_v1\",{wb:1})},\"Nxs-JS-Client\")(ue_csm,window);\n\n\nue_csm.ue_unrt = 1500;\n(function(d,b,t){function u(a,g){var c=a.srcElement||a.target||{},b={k:v,t:g.t,dt:g.dt,x:a.pageX,y:a.pageY,p:e.getXPath(c),n:c.nodeName};a.button&&(b.b=a.button);c.type&&(b.ty=c.type);c.href&&(b.r=e.extractStringValue(c.href));c.id&&(b.i=c.id);c.className&&c.className.split&&(b.c=c.className.split(/\\s+/));h+=1;e.getFirstAscendingWidget(c,function(a){b.wd=a;d.ue.log(b,r)})}function w(a){if(!x(a.srcElement||a.target)){m+=1;n=!0;var g=f=d.ue.d(),c;p&&\"function\"===typeof p.now&&a.timeStamp&&(c=p.now()-\na.timeStamp,c=parseFloat(c.toFixed(2)));s=b.setTimeout(function(){u(a,{t:g,dt:c})},y)}}function z(a){if(a){var b=a.filter(A);a.length!==b.length&&(q=!0,k=d.ue.d(),n&&q&&(k&&f&&d.ue.log({k:B,t:f,m:Math.abs(k-f)},r),l(),q=!1,k=0))}}function A(a){if(!a)return!1;var b=\"characterData\"===a.type?a.target.parentElement:a.target;if(!b||!b.hasAttributes||!b.attributes)return!1;var c={\"class\":\"gw-clock gw-clock-aria s-item-container-height-auto feed-carousel using-mouse kfs-inner-container\".split(\" \"),id:[\"dealClock\",\n\"deal_expiry_timer\",\"timer\"],role:[\"timer\"]},d=!1;Object.keys(c).forEach(function(a){var e=b.attributes[a]?b.attributes[a].value:\"\";(c[a]||\"\").forEach(function(a){-1!==e.indexOf(a)&&(d=!0)})});return d}function x(a){if(!a)return!1;var b=(e.extractStringValue(a.nodeName)||\"\").toLowerCase(),c=(e.extractStringValue(a.type)||\"\").toLowerCase(),d=(e.extractStringValue(a.href)||\"\").toLowerCase();a=(e.extractStringValue(a.id)||\"\").toLowerCase();var f=\"checkbox color date datetime-local email file month number password radio range reset search tel text time url week\".split(\" \");\nif(-1!==[\"select\",\"textarea\",\"html\"].indexOf(b)||\"input\"===b&&-1!==f.indexOf(c)||\"a\"===b&&-1!==d.indexOf(\"http\")||-1!==[\"sitbreaderrightpageturner\",\"sitbreaderleftpageturner\",\"sitbreaderpagecontainer\"].indexOf(a))return!0}function l(){n=!1;f=0;b.clearTimeout(s)}function C(){b.ue.onunload(function(){ue.count(\"armored-cxguardrails.unresponsive-clicks.violations\",h);ue.count(\"armored-cxguardrails.unresponsive-clicks.violationRate\",h/m*100||0)})}if(b.MutationObserver&&b.addEventListener&&Object.keys&&\nd&&d.ue&&d.ue.log&&d.ue_unrt&&d.ue_utils){var y=d.ue_unrt,r=\"cel\",v=\"unr_mcm\",B=\"res_mcm\",p=b.performance,e=d.ue_utils,n=!1,f=0,s=0,q=!1,k=0,h=0,m=0;b.addEventListener&&(b.addEventListener(\"mousedown\",w,!0),b.addEventListener(\"beforeunload\",l,!0),b.addEventListener(\"visibilitychange\",l,!0),b.addEventListener(\"pagehide\",l,!0));b.ue&&b.ue.event&&b.ue.onSushiUnload&&b.ue.onunload&&C();(new MutationObserver(z)).observe(t,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}})(ue_csm,window,document);\n\n\nue_csm.ue.exec(function(g,e){if(e.ue_err){var f=\"\";e.ue_err.errorHandlers||(e.ue_err.errorHandlers=[]);e.ue_err.errorHandlers.push({name:\"fctx\",handler:function(a){if(!a.logLevel||\"FATAL\"===a.logLevel)if(f=g.getElementsByTagName(\"html\")[0].innerHTML){var b=f.indexOf(\"var ue_t0=ue_t0||+new Date();\");if(-1!==b){var b=f.substr(0,b).split(String.fromCharCode(10)),d=Math.max(b.length-10-1,0),b=b.slice(d,b.length-1);a.fcsmln=d+b.length+1;a.cinfo=a.cinfo||{};for(var c=0;c<b.length;c++)a.cinfo[d+c+1+\"\"]=\nb[c]}b=f.split(String.fromCharCode(10));a.cinfo=a.cinfo||{};if(!(a.f||void 0===a.l||a.l in a.cinfo))for(c=+a.l-1,d=Math.max(c-5,0),c=Math.min(c+5,b.length-1);d<=c;d++)a.cinfo[d+1+\"\"]=b[d]}}})}},\"fatals-context\")(document,window);\n\n\n(function(m,a){function c(k){function f(b){b&&\"string\"===typeof b&&(b=(b=b.match(/^(?:https?:)?\\/\\/(.*?)(\\/|$)/i))&&1<b.length?b[1]:null,b&&b&&(\"number\"===typeof e[b]?e[b]++:e[b]=1))}function d(b){var e=10,d=+new Date;b&&b.timeRemaining?e=b.timeRemaining():b={timeRemaining:function(){return Math.max(0,e-(+new Date-d))}};for(var c=a.performance.getEntries(),k=e;g<c.length&&k>n;)c[g].name&&f(c[g].name),g++,k=b.timeRemaining();g>=c.length?h(!0):l()}function h(b){if(!b){b=m.scripts;var c;if(b)for(var d=\n0;d<b.length;d++)(c=b[d].getAttribute(\"src\"))&&\"undefined\"!==c&&f(c)}0<Object.keys(e).length&&(p&&ue_csm.ue&&ue_csm.ue.event&&ue_csm.ue.event({domains:e,pageType:a.ue_pty||null,subPageType:a.ue_spty||null,pageTypeId:a.ue_pti||null},\"csm\",\"csm.CrossOriginDomains.2\"),a.ue_ext=e)}function l(){!0===k?d():a.requestIdleCallback?a.requestIdleCallback(d):a.requestAnimationFrame?a.requestAnimationFrame(d):a.setTimeout(d,100)}function c(){if(a.performance&&a.performance.getEntries){var b=a.performance.getEntries();\n!b||0>=b.length?h(!1):l()}else h(!1)}var e=a.ue_ext||{};a.ue_ext||c();return e}function q(){setTimeout(c,r)}var s=a.ue_dserr||!1,p=!0,n=1,r=2E3,g=0;a.ue_err&&s&&(a.ue_err.errorHandlers||(a.ue_err.errorHandlers=[]),a.ue_err.errorHandlers.push({name:\"ext\",handler:function(a){if(!a.logLevel||\"FATAL\"===a.logLevel){var f=c(!0),d=[],h;for(h in f){var f=h,g=f.match(/amazon(\\.com?)?\\.\\w{2,3}$/i);g&&1<g.length||-1!==f.indexOf(\"amazon-adsystem.com\")||-1!==f.indexOf(\"amazonpay.com\")||-1!==f.indexOf(\"cloudfront-labs.amazonaws.com\")||\nd.push(h)}a.ext=d}}}));a.ue&&a.ue.isl?c():a.ue&&ue.attach&&ue.attach(\"load\",q)})(document,window);\n\n\n\n\n\nvar ue_wtc_c = 3;\nue_csm.ue.exec(function(b,e){function l(){for(var a=0;a<f.length;a++)a:for(var d=s.replace(A,f[a])+g[f[a]]+t,c=arguments,b=0;b<c.length;b++)try{c[b].send(d);break a}catch(e){}g={};f=[];n=0;k=p}function u(){B?l(q):l(C,q)}function v(a,m,c){r++;if(r>w)d.count&&1==r-w&&(d.count(\"WeblabTriggerThresholdReached\",1),b.ue_int&&console.error(\"Number of max call reached. Data will no longer be send\"));else{var h=c||{};h&&-1<h.constructor.toString().indexOf(D)&&a&&-1<a.constructor.toString().indexOf(x)&&m&&-1<\nm.constructor.toString().indexOf(x)?(h=b.ue_id,c&&c.rid&&(h=c.rid),c=h,a=encodeURIComponent(\",wl=\"+a+\"/\"+m),2E3>a.length+p?(2E3<k+a.length&&u(),void 0===g[c]&&(g[c]=\"\",f.push(c)),g[c]+=a,k+=a.length,n||(n=e.setTimeout(u,E))):b.ue_int&&console.error(\"Invalid API call. The input provided is over 2000 chars.\")):d.count&&(d.count(\"WeblabTriggerImproperAPICall\",1),b.ue_int&&console.error(\"Invalid API call. The input provided does not match the API protocol i.e ue.trigger(String, String, Object).\"))}}function F(){d.trigger&&\nd.trigger.isStub&&d.trigger.replay(function(a){v.apply(this,a)})}function y(){z||(f.length&&l(q),z=!0)}var t=\":1234\",s=\"//\"+b.ue_furl+\"/1/remote-weblab-triggers/1/OE/\"+b.ue_mid+\":\"+b.ue_sid+\":PLCHLDR_RID$s:wl-client-id%3DCSMTriger\",A=\"PLCHLDR_RID\",E=b.wtt||1E4,p=s.length+t.length,w=b.mwtc||2E3,G=1===e.ue_wtc_c,B=3===e.ue_wtc_c,H=e.XMLHttpRequest&&\"withCredentials\"in new e.XMLHttpRequest,x=\"String\",D=\"Object\",d=b.ue,g={},f=[],k=p,n,z=!1,r=0,C=function(){return{send:function(a){if(H){var b=new e.XMLHttpRequest;\nb.open(\"GET\",a,!0);G&&(b.withCredentials=!0);b.send()}else throw\"\";}}}(),q=function(){return{send:function(a){(new Image).src=a}}}();e.encodeURIComponent&&(d.attach&&(d.attach(\"beforeunload\",y),d.attach(\"pagehide\",y)),F(),d.trigger=v)},\"client-wbl-trg\")(ue_csm,window);\n\n\n(function(k,d,h){function f(a,c,b){a&&a.indexOf&&0===a.indexOf(\"http\")&&0!==a.indexOf(\"https\")&&l(s,c,a,b)}function g(a,c,b){a&&a.indexOf&&(location.href.split(\"#\")[0]!=a&&null!==a&&\"undefined\"!==typeof a||l(t,c,a,b))}function l(a,c,b,e){m[b]||(e=u&&e?n(e):\"N/A\",d.ueLogError&&d.ueLogError({message:a+c+\" : \"+b,logLevel:v,stack:\"N/A\"},{attribution:e}),m[b]=1,p++)}function e(a,c){if(a&&c)for(var b=0;b<a.length;b++)try{c(a[b])}catch(d){}}function q(){return d.performance&&d.performance.getEntriesByType?\nd.performance.getEntriesByType(\"resource\"):[]}function n(a){if(a.id)return\"//*[@id='\"+a.id+\"']\";var c;c=1;var b;for(b=a.previousSibling;b;b=b.previousSibling)b.nodeName==a.nodeName&&(c+=1);b=a.nodeName;1!=c&&(b+=\"[\"+c+\"]\");a.parentNode&&(b=n(a.parentNode)+\"/\"+b);return b}function w(){var a=h.images;a&&a.length&&e(a,function(a){var b=a.getAttribute(\"src\");f(b,\"img\",a);g(b,\"img\",a)})}function x(){var a=h.scripts;a&&a.length&&e(a,function(a){var b=a.getAttribute(\"src\");f(b,\"script\",a);g(b,\"script\",a)})}\nfunction y(){var a=h.styleSheets;a&&a.length&&e(a,function(a){if(a=a.ownerNode){var b=a.getAttribute(\"href\");f(b,\"style\",a);g(b,\"style\",a)}})}function z(){if(A){var a=q();e(a,function(a){f(a.name,a.initiatorType)})}}function B(){e(q(),function(a){g(a.name,a.initiatorType)})}function r(){var a;a=d.location&&d.location.protocol?d.location.protocol:void 0;\"https:\"==a&&(z(),w(),x(),y(),B(),p<C&&setTimeout(r,D))}var s=\"[CSM] Insecure content detected \",t=\"[CSM] Ajax request to same page detected \",v=\"WARN\",\nm={},p=0,D=k.ue_nsip||1E3,C=5,A=1==k.ue_urt,u=!0;ue_csm.ue_disableNonSecure||(d.performance&&d.performance.setResourceTimingBufferSize&&d.performance.setResourceTimingBufferSize(300),r())})(ue_csm,window,document);\n\n\nvar ue_aa_a = \"C\";\nif (ue.trigger && (ue_aa_a === \"C\" || ue_aa_a === \"T1\")) {\n    ue.trigger(\"UEDATA_AA_SERVERSIDE_ASSIGNMENT_CLIENTSIDE_TRIGGER_190249\", ue_aa_a);\n}\n(function(f,b){function g(){try{b.PerformanceObserver&&\"function\"===typeof b.PerformanceObserver&&(a=new b.PerformanceObserver(function(b){c(b.getEntries())}),a.observe(d))}catch(h){k()}}function m(){for(var h=d.entryTypes,a=0;a<h.length;a++)c(b.performance.getEntriesByType(h[a]))}function c(a){if(a&&Array.isArray(a)){for(var c=0,e=0;e<a.length;e++){var d=l.indexOf(a[e].name);if(-1!==d){var g=Math.round(b.performance.timing.navigationStart+a[e].startTime);f.uet(n[d],void 0,void 0,g);c++}}l.length===\nc&&k()}}function k(){a&&a.disconnect&&\"function\"===typeof a.disconnect&&a.disconnect()}if(\"function\"===typeof f.uet&&b.performance&&\"object\"===typeof b.performance&&b.performance.getEntriesByType&&\"function\"===typeof b.performance.getEntriesByType&&b.performance.timing&&\"object\"===typeof b.performance.timing&&\"number\"===typeof b.performance.timing.navigationStart){var d={entryTypes:[\"paint\"]},l=[\"first-paint\",\"first-contentful-paint\"],n=[\"fp\",\"fcp\"],a;try{m(),g()}catch(p){f.ueLogError(p,{logLevel:\"ERROR\",\nattribution:\"performanceMetrics\"})}}})(ue_csm,window);\n\n\nif (window.csa) {\n    csa(\"Events\")(\"setEntity\", {\n        page:{pageType: \"MASDetailPage\", subPageType: \"\", pageTypeId: \"\"}\n    });\n}\ncsa.plugin(function(c){var m=\"transitionStart\",n=\"pageVisible\",e=\"PageTiming\",t=\"visibilitychange\",s=\"$latency.visible\",i=c.global,r=(i.performance||{}).timing,a=[\"navigationStart\",\"unloadEventStart\",\"unloadEventEnd\",\"redirectStart\",\"redirectEnd\",\"fetchStart\",\"domainLookupStart\",\"domainLookupEnd\",\"connectStart\",\"connectEnd\",\"secureConnectionStart\",\"requestStart\",\"responseStart\",\"responseEnd\",\"domLoading\",\"domInteractive\",\"domContentLoadedEventStart\",\"domContentLoadedEventEnd\",\"domComplete\",\"loadEventStart\",\"loadEventEnd\"],o=i.Math,u=o.max,l=o.floor,d=i.document||{},g=(r||{}).navigationStart,f=g,v=0,p=null;if(i.Object.keys&&[].forEach&&!c.config[\"KillSwitch.\"+e]){if(!r||null===g||g<=0||void 0===g)return c.error(\"Invalid navigation timing data: \"+g);p=new S({schemaId:\"<ns>.PageLatency.5\",producerId:\"csa\"}),\"boolean\"!=typeof d.hidden&&\"string\"!=typeof d.visibilityState||!d.removeEventListener?c.emit(s):h()?(c.emit(s),E(n,g)):c.on(d,t,function e(){h()&&(f=c.time(),d.removeEventListener(t,e),E(m,f),E(n,f),c.emit(s))}),c.once(\"$unload\",I),c.once(\"$load\",I),c.on(\"$pageTransition\",function(){f=c.time()}),c.register(e,{mark:E,instance:function(e){return new S(e)}})}function S(e){var i,r=null,a=e.ent||{page:[\"pageType\",\"subPageType\",\"requestId\"]},o=e.logger||c(\"Events\",{producerId:e.producerId});if(!e||!e.producerId||!e.schemaId)return c.error(\"The producer id and schema Id must be defined for PageLatencyInstance.\");function d(){return i||f}function n(){r=c.UUID()}this.mark=function(n,t){if(null!=n)return t=t||c.time(),n===m&&(i=t),c.once(s,function(){o(\"log\",{messageId:r,__merge:function(e){e.markers[n]=function(e,n){return u(0,n-(e||f))}(d(),t),e.markerTimestamps[n]=l(t)},markers:{},markerTimestamps:{},navigationStartTimestamp:d()?new Date(d()).toISOString():null,schemaId:e.schemaId},{ent:a})}),t},n(),c.on(\"$beforePageTransition\",n)}function E(e,n){e===m&&(f=n);var t=p.mark(e,n);c.emit(\"$timing:\"+e,t)}function I(){if(!v){for(var e=0;e<a.length;e++)r[a[e]]&&E(a[e],r[a[e]]);v=1}}function h(){return!d.hidden||\"visible\"===d.visibilityState}});csa.plugin(function(f){var u,c,l=\"length\",a=\"parentElement\",t=\"target\",i=\"getEntriesByName\",e=\"perf\",n=null,r=\"_csa_flt\",o=\"_csa_llt\",s=\"previousSibling\",d=\"_osrc\",g=\"_elt\",h=\"_eid\",m=10,p=5,v=15,y=100,E=f.global,S=f.timeout,b=E.Math,x=b.max,L=b.floor,O=b.ceil,_=E.document,w=E.performance||{},T=(w.timing||{}).navigationStart,I=Date.now,N=Object.values||(f.types||{}).ovl,k=f(\"PageTiming\"),B=f(\"SpeedIndexBuffers\"),Y=[],C=[],F=[],H=[],M=[],R=[],V=.1,W=.1,$=0,P=0,X=!0,D=0,J=0,j=1==f.config[\"SpeedIndex.ForceReplay\"],q=0,Q=1,U=0,z={},A=[],G=0,K={buffered:1};function Z(e){f.global.ue_csa_ss_tag||f.emit(\"$csmTag:\"+e,0,K)}function ee(){for(var e=I(),n=0;u;){if(0!==u[l]){if(!1!==u.h(u[0])&&u.shift(),n++,!j&&n%m==0&&I()-e>p)break}else u=u.n}$=0,u&&($||(!0===_.hidden?(j=1,ee()):f.timeout(ee,0)))}function ne(e,n,t,i,r){U=L(e),Y=n,C=t,F=i,R=r;var o=_.createTreeWalker(_.body,NodeFilter.SHOW_TEXT,null,null),a={w:E.innerWidth,h:E.innerHeight,x:E.pageXOffset,y:E.pageYOffset};_.body[g]=e,H.push({w:o,vp:a}),M.push({img:_.images,iter:0}),Y.h=te,(Y.n=C).h=ie,(C.n=F).h=re,(F.n=H).h=oe,(H.n=M).h=ae,(M.n=R).h=fe,u=Y,ee()}function te(e){e.m.forEach(function(e){for(var n=e;n&&(e===n||!n[r]||!n[o]);)n[r]||(n[r]=e[r]),n[o]||(n[o]=e[o]),n[g]=n[r]-T,n=n[s]})}function ie(e){e.m.forEach(function(e){var n=e[t];d in n||(n[d]=e.oldValue)})}function re(n){n.m.forEach(function(e){e[t][g]=n.t-T})}function oe(e){for(var n,t=e.vp,i=e.w,r=m;(n=i.nextNode())&&0<r;){r-=1;var o=(n[a]||{}).nodeName;\"SCRIPT\"!==o&&\"STYLE\"!==o&&\"NOSCRIPT\"!==o&&\"BODY\"!==o&&0!==(n.nodeValue||\"\").trim()[l]&&de(n[a],ue(n),t)}return!n}function ae(e){for(var n={w:E.innerWidth,h:E.innerHeight,x:E.pageXOffset,y:E.pageYOffset},t=m;e.iter<e.img[l]&&0<t;){var i,r=e.img[e.iter],o=se(r),a=o&&ue(o)||ue(r);o?(o[g]=a,i=le(o.querySelector('[aria-posinset=\"1\"] img')||r)||a,r=o):i=le(r)||a,J&&c<i&&(i=a),de(r,i,n),e.iter+=1,t-=1}return e.img[l]<=e.iter}function fe(e){var n=[],i=0,r=0,o=P,t=L(e.y/y),a=O((e.y+E.innerHeight)/y);A.slice(t,a).forEach(function(e){(e.elems||[]).forEach(function(e){e.lt in n||(n[e.lt]={}),e.id in n[e.lt]||(i+=(n[e.lt][e.id]=e).a)})}),Z(\"startVL\"),N(n).forEach(function(e){N(e).forEach(function(e){var n=1-r/i,t=x(e.lt,o);G+=n*(t-o),o=t,function(e,n){var t;for(;V<=1&&V-.01<=e;)ge(\"visuallyLoaded\"+(t=(100*V).toFixed(0)),n.lt),\"50\"!==t&&\"90\"!==t||f(\"Content\",{target:n.e})(\"mark\",\"visuallyLoaded\"+t,T+O(n.lt||0)),V+=W}((r+=e.a)/i,e)})}),Z(\"endVL\"),P=e.t-T,R[l]<=1&&(ge(\"speedIndex\",G),ge(\"visuallyLoaded0\",U)),X&&(X=!1,ge(\"atfSpeedIndex\",G))}function ue(e){for(var n=e[a],t=v;n&&0<t;){if(n[g]||0===n[g])return x(n[g],U);n=n.parentElement,t-=1}}function ce(e,n){if(e){if(!e.indexOf(\"data:\"))return ue(n);var t=w[i](e)||[];if(0<t[l])return x(O(t[0].responseEnd||0),U)}}function le(e){return ce(e[d],e)||ce(e.currentSrc,e)||ce(e.src,e)}function se(e){for(var n=10,t=e.parentElement;t&&0<n;){if(t.classList&&t.classList.contains(\"a-carousel-viewport\"))return t;t=t.parentElement,n-=1}return null}function de(e,n,t){if((n||0===n)&&!e[h]){var i=e.getBoundingClientRect(),r=i.width*i.height,o=i.width/2,a=Q++;if(0!=r&&!(o<i.right-t.w||i.right<o)){for(var f={e:e,lt:n,a:r,id:a},u=L((i.top+t.y)/y),c=O((i.top+t.y+i.height)/y),l=u;l<=c;l++)l in A||(A[l]={elems:[],lt:0}),A[l].elems.push(f);e[h]=a}}}function ge(e,n){k(\"mark\",e,T+O((z[e]=n)||0))}function he(e){q||(Z(\"browserQuite\"+e),B(\"getBuffers\",ne),q=1)}T&&N&&w[i]?(Z(e+\"Yes\"),B(\"registerListener\",function(){J&&(clearTimeout(D),D=S(he.bind(n,\"Mut\"),2500))}),f.once(\"$unload\",function(){j=1,he(\"Ud\")}),f.once(\"$load\",function(){J=1,c=I()-T,D=S(he.bind(n,\"Ld\"),2500)}),f.once(\"$timing:functional\",he.bind(n,\"Fn\")),B(\"replayModuleIsLive\"),f.register(\"SpeedIndex\",{getMarkers:function(e){e&&e(JSON.parse(JSON.stringify(z)))}})):Z(e+\"No\")});csa.plugin(function(e){var m=!!e.config[\"LCP.elementDedup\"],t=!1,n=e(\"PageTiming\"),r=e.global.PerformanceObserver,a=e.global.performance;function i(){return a.timing.navigationStart}function o(){t||function(o){var l=new r(function(e){var t=e.getEntries();if(0!==t.length){var n=t[t.length-1];if(m&&\"\"!==n.id&&n.element&&\"IMG\"===n.element.tagName){for(var r={},a=t[0],i=0;i<t.length;i++)t[i].id in r||(\"\"!==t[i].id&&(r[t[i].id]=!0),a.startTime<t[i].startTime&&(a=t[i]));n=a}l.disconnect(),o({startTime:n.startTime,renderTime:n.renderTime,loadTime:n.loadTime})}});try{l.observe({type:\"largest-contentful-paint\",buffered:!0})}catch(e){}}(function(e){e&&(t=!0,n(\"mark\",\"largestContentfulPaint\",Math.floor(e.startTime+i())),e.renderTime&&n(\"mark\",\"largestContentfulPaint.render\",Math.floor(e.renderTime+i())),e.loadTime&&n(\"mark\",\"largestContentfulPaint.load\",Math.floor(e.loadTime+i())))})}r&&a&&a.timing&&(e.once(\"$unload\",o),e.once(\"$load\",o),e.register(\"LargestContentfulPaint\",{}))});csa.plugin(function(r){var e=r(\"Metrics\",{producerId:\"csa\"}),n=r.global.PerformanceObserver;n&&(n=new n(function(r){var t=r.getEntries();if(0===t.length||!t[0].processingStart||!t[0].startTime)return;!function(r){r=r||0,n.disconnect(),0<=r?e(\"recordMetric\",\"firstInputDelay\",r):e(\"recordMetric\",\"firstInputDelay.invalid\",1)}(t[0].processingStart-t[0].startTime)}),function(){try{n.observe({type:\"first-input\",buffered:!0})}catch(r){}}())});csa.plugin(function(d){var e=\"Metrics\",g=0;function r(i){var c,t,e=i.producerId,r=i.logger,s=r||d(\"Events\",{producerId:e}),o=(i||{}).dimensions||{},u={},n=-1;if(!e&&!r)return d.error(\"Either a producer id or custom logger must be defined\");function a(){n!==g&&(c=d.UUID(),t=d.UUID(),u={},n=g)}this.recordMetric=function(r,n){var e=i.logOptions||{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}};e.debugMetric=i.debugMetric,a(),s(\"log\",{messageId:c,schemaId:i.schemaId||\"<ns>.Metric.3\",metrics:{},dimensions:o,__merge:function(e){e.metrics[r]=n}},e)},this.recordCounter=function(r,e){var n=i.logOptions||{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}};if(\"string\"!=typeof r||\"number\"!=typeof e||!isFinite(e))return d.error(\"Invalid type given for counter name or counter value: \"+r+\"/\"+e);a(),r in u||(u[r]={});var c=u[r];\"f\"in c||(c.f=e),c.c=(c.c||0)+1,c.s=(c.s||0)+e,c.l=e,s(\"log\",{messageId:t,schemaId:i.schemaId||\"<ns>.InternalCounters.2\",c:{},__merge:function(e){r in e.c||(e.c[r]={}),c.fs||(c.fs=1,e.c[r].f=c.f),1<c.c&&(e.c[r].s=c.s,e.c[r].l=c.l,e.c[r].c=c.c)}},n)}}d.config[\"KillSwitch.\"+e]||(new r({producerId:\"csa\"}).recordMetric(\"baselineMetricEvent\",1),d.on(\"$beforePageTransition\",function(){g++}),d.register(e,{instance:function(e){return new r(e||{})}}))});csa.plugin(function(t){var a,r=(t.global.performance||{}).timing,s=(r||{}).navigationStart||t.time();function e(){a=t.UUID()}function n(i){var r=(i=i||{}).producerId,e=i.logger,o=e||t(\"Events\",{producerId:r});if(!r&&!e)return t.error(\"Either a producer id or custom logger must be defined\");this.mark=function(e,r){var n=(void 0===r?t.time():r)-s;o(\"log\",{messageId:a,schemaId:i.schemaId||\"<ns>.Timer.1\",markers:{},__merge:function(r){r.markers[e]=n}},i.logOptions)}}r&&(e(),t.on(\"$beforePageTransition\",e),t.register(\"Timers\",{instance:function(r){return new n(r||{})}}))});csa.plugin(function(t){var e=\"takeRecords\",i=\"disconnect\",n=\"function\",o=t(\"Metrics\",{producerId:\"csa\"}),c=t(\"PageTiming\"),a=t.global,u=t.timeout,r=t.on,f=a.PerformanceObserver,m=0,l=!1,s=0,d=a.performance,h=a.document,v=null,y=!1,g=t.blank;function p(){l||(l=!0,clearTimeout(v),typeof f[e]===n&&f[e](),typeof f[i]===n&&f[i](),o(\"recordMetric\",\"documentCumulativeLayoutShift\",m),c(\"mark\",\"cumulativeLayoutShiftLastTimestamp\",Math.floor(s+d.timing.navigationStart)))}f&&d&&d.timing&&h&&(f=new f(function(t){v&&clearTimeout(v);t.getEntries().forEach(function(t){t.hadRecentInput||(m+=t.value,s<t.startTime&&(s=t.startTime))}),v=u(p,5e3)}),function(){try{f.observe({type:\"layout-shift\",buffered:!0}),v=u(p,5e3)}catch(t){}}(),g=r(h,\"click\",function(t){y||(y=!0,o(\"recordMetric\",\"documentCumulativeLayoutShiftToFirstInput\",m),g())}),r(h,\"visibilitychange\",function(){\"hidden\"===h.visibilityState&&p()}),t.once(\"$unload\",p))});csa.plugin(function(e){var t,n=e.global,r=n.PerformanceObserver,c=e(\"Metrics\",{producerId:\"csa\"}),o=0,i=0,a=-1,l=n.Math,f=l.max,u=l.ceil;if(r){t=new r(function(e){e.getEntries().forEach(function(e){var t=e.duration;o+=t,i+=t,a=f(t,a)})});try{t.observe({type:\"longtask\",buffered:!0})}catch(e){}t=new r(function(e){0<e.getEntries().length&&(i=0,a=-1)});try{t.observe({type:\"largest-contentful-paint\",buffered:!0})}catch(e){}e.on(\"$unload\",g),e.on(\"$beforePageTransition\",g)}function g(){c(\"recordMetric\",\"totalBlockingTime\",u(i||0)),c(\"recordMetric\",\"totalBlockingTimeInclLCP\",u(o||0)),c(\"recordMetric\",\"maxBlockingTime\",u(a||0)),i=o=0,a=-1}});csa.plugin(function(r){var e=\"CacheDetection\",o=\"csa-ctoken-\",n=r.store,t=r.deleteStored,c=r.config,a=c[e+\".RequestID\"],i=c[e+\".Callback\"],s=r.global,u=s.document||{},d=s.Date,f=r(\"Events\"),l=r(\"Events\",{producerId:\"csa\"});function p(e){try{var n=u.cookie.match(RegExp(\"(^| )\"+e+\"=([^;]+)\"));return n&&n[2].trim()}catch(e){}}!function(){var e=function(){var e=p(\"cdn-rid\");if(e)return{r:e,s:\"cdn\"}}()||function(){if(r.store(o+a))return{r:r.UUID().toUpperCase().replace(/-/g,\"\").slice(0,20),s:\"device\"}}()||{},n=e.r,c=e.s;if(!!n){var t=p(\"session-id\");!function(e,n,c,t){f(\"setEntity\",{page:{pageSource:\"cache\",requestId:e,cacheRequestId:a,cacheSource:t},session:{id:c}})}(n,0,t,c),\"device\"===c&&l(\"log\",{schemaId:\"<ns>.CacheImpression.1\"},{ent:\"all\"}),i&&i(n,t,c)}}(),n(o+a,d.now()+36e5),r.once(\"$load\",function(){var c=d.now();t(function(e,n){return 0==e.indexOf(o)&&parseInt(n)<c})})});csa.plugin(function(u){var i,t=\"Content\",e=\"MutationObserver\",n=\"addedNodes\",a=\"querySelectorAll\",f=\"matches\",o=\"getAttributeNames\",r=\"getAttribute\",s=\"dataset\",c=\"widget\",l=\"producerId\",d=\"slotId\",h=\"iSlotId\",g={ent:{element:1,page:[\"pageType\",\"subPageType\",\"requestId\"]}},p=5,m=u.config[t+\".BubbleUp.SearchDepth\"]||35,y=u.config[t+\".SearchPage\"]||0,v=\"csaC\",b=v+\"Id\",E=\"logRender\",w={},I=u.config,O=I[t+\".Selectors\"]||[],C=I[t+\".WhitelistedAttributes\"]||{href:1,class:1},N=I[t+\".EnableContentEntities\"],S=I[\"KillSwitch.ContentRendered\"],k=u.global,A=k.document||{},U=A.documentElement,L=k.HTMLElement,R={},_=[],j=function(t,e,n,i){var r=this,o=u(\"Events\",{producerId:t||\"csa\"});e.type=e.type||c,r.id=e.id,r.l=o,r.e=e,r.el=n,r.rt=i,r.dlo=g,r.op=W(n,\"csaOp\"),r.log=function(t,e){o(\"log\",t,e||g)},e.id&&o(\"setEntity\",{element:e})},x=j.prototype;function D(t){var e=(t=t||{}).element,n=t.target;return e?function(t,e){var n;n=t instanceof L?K(t)||Y(e[l],t,z,u.time()):R[t.id]||H(e[l],0,t,u.time());return n}(e,t):n?M(n):u.error(\"No element or target argument provided.\")}function M(t){var e=function(t){var e=null,n=0;for(;t&&n<m;){if(n++,P(t,b)){e=t;break}t=t.parentElement}return e}(t);return e?K(e):new j(\"csa\",{id:null},null,u.time())}function P(t,e){if(t&&t.dataset)return t.dataset[e]}function T(t,e,n){_.push({n:n,e:t,t:e}),B()}function q(){for(var t=u.time(),e=0;0<_.length;){var n=_.shift();if(w[n.n](n.e,n.t),++e%10==0&&u.time()-t>p)break}i=0,_.length&&B()}function B(){i=i||u.raf(q)}function X(t,e,n){return{n:t,e:e,t:n}}function Y(t,e,n,i){var r=u.UUID(),o={id:r},c=M(e);return e[s][b]=r,n(o,e),c&&c.id&&(o.parentId=c.id),H(t,e,o,i)}function $(t){return isNaN(t)?null:Math.round(t)}function H(t,e,n,i){N&&(n.schemaId=\"<ns>.ContentEntity.2\"),n.id=n.id||u.UUID();var r=new j(t,n,e,i);return function(t){return!S&&((t.op||{}).hasOwnProperty(E)||y)}(r)&&function(t,e){var n={},i=u.exec($);t.el&&(n=t.el.getBoundingClientRect()),t.log({schemaId:\"<ns>.ContentRender.2\",timestamp:e,width:i(n.width),height:i(n.height),positionX:i(n.left+k.pageXOffset),positionY:i(n.top+k.pageYOffset)})}(r,i),u.emit(\"$content.register\",r),R[n.id]=r}function K(t){return R[(t[s]||{})[b]]}function W(n,i){var r={};return o in(n=n||{})&&Object.keys(n[s]).forEach(function(t){if(!t.indexOf(i)&&i.length<t.length){var e=function(t){return(t[0]||\"\").toLowerCase()+t.slice(1)}(t.slice(i.length));r[e]=n[s][t]}}),r}function z(t,e){o in e&&(function(t,e){var n=W(t,v);Object.keys(n).forEach(function(t){e[t]=n[t]})}(e,t),d in t&&(t[h]=t[d]),function(e,n){(e[o]()||[]).forEach(function(t){t in C&&(n[t]=e[r](t))})}(e,t))}U&&A[a]&&k[e]&&(O.push({selector:\"*[data-csa-c-type]\",entity:z}),O.push({selector:\".celwidget\",entity:function(t,e){z(t,e),t[d]=t[d]||e[r](\"cel_widget_id\")||e.id,t.legacyId=e[r](\"cel_widget_id\")||e.id,t.type=t.type||c}}),w[1]=function(t,e){t.forEach(function(t){t[n]&&t[n].constructor&&\"NodeList\"===t[n].constructor.name&&Array.prototype.forEach.call(t[n],function(t){_.unshift(X(2,t,e))})})},w[2]=function(o,c){a in o&&f in o&&O.forEach(function(t){for(var e=t.selector,n=o[f](e),i=o[a](e),r=i.length-1;0<=r;r--)_.unshift(X(3,{e:i[r],s:t},c));n&&_.unshift(X(3,{e:o,s:t},c))})},w[3]=function(t,e){var n=t.e;K(n)||Y(\"csa\",n,t.s.entity,e)},w[4]=function(){u.register(t,{instance:D})},new k[e](function(t){T(t,u.time(),1)}).observe(U,{childList:!0,subtree:!0}),T(U,u.time(),2),T(null,u.time(),4),u.on(\"$content.export\",function(e){Object.keys(e).forEach(function(t){x[t]=e[t]})}))});csa.plugin(function(o){var i,t=\"ContentImpressions\",e=\"KillSwitch.\",n=\"IntersectionObserver\",r=\"getAttribute\",s=\"dataset\",c=\"intersectionRatio\",a=\"csaCId\",m=1e3,l=o.global,f=o.config,u=f[e+t],v=f[e+t+\".ContentViews\"],g=((l.performance||{}).timing||{}).navigationStart||o.time(),d={};function h(t){t&&(t.v=1,function(t){t.vt=o.time(),t.el.log({schemaId:\"<ns>.ContentView.3\",timeToViewed:t.vt-t.el.rt,pageFirstPaintToElementViewed:t.vt-g})}(t))}function I(t){t&&!t.it&&(t.i=o.time()-t.is>m,function(t){t.it=o.time(),t.el.log({schemaId:\"<ns>.ContentImpressed.2\",timeToImpressed:t.it-t.el.rt,pageFirstPaintToElementImpressed:t.it-g})}(t))}!u&&l[n]&&(i=new l[n](function(t){var n=o.time();t.forEach(function(t){var e=function(t){if(t&&t[r])return d[t[s][a]]}(t.target);if(e){o.emit(\"$content.intersection\",{meta:e.el,t:n,e:t});var i=t.intersectionRect;t.isIntersecting&&0<i.width&&0<i.height&&(v||e.v||h(e),.5<=t[c]&&!e.is&&(e.is=n,e.timer=o.timeout(function(){I(e)},m))),t[c]<.5&&!e.it&&e.timer&&(l.clearTimeout(e.timer),e.is=0,e.timer=0)}})},{threshold:[0,.5,.99]}),o.on(\"$content.register\",function(t){var e=t.el;e&&(d[t.id]={el:t,v:0,i:0,is:0,vt:0,it:0},i.observe(e))}))});csa.plugin(function(e){e.config[\"KillSwitch.ContentLatency\"]||e.emit(\"$content.export\",{mark:function(t,n){var o=this;o.t||(o.t=e(\"Timers\",{logger:o.l,schemaId:\"<ns>.ContentLatency.1\",logOptions:o.dlo})),o.t(\"mark\",t,n)}})});csa.plugin(function(t){function n(i,e,o){var c={};function r(t,n,e){t in c&&o<=n-c[t].s&&(function(n,e,i){if(!p)return;E(function(t){T(n,t),t.w[n][e]=a((t.w[n][e]||0)+i)})}(t,i,n-c[t].d),c[t].d=n),e||delete c[t]}this.update=function(t,n){n.isIntersecting&&e<=n.intersectionRatio?function(t,n){t in c||(c[t]={s:n,d:n})}(t,u()):r(t,u())},this.stopAll=function(t){var n=u();for(var e in c)r(e,n,t)},this.reset=function(){var t=u();for(var n in c)c[n].s=t,c[n].d=t}}var e=t.config,u=t.time,i=\"ContentInteractionsSummary\",o=e[i+\".FlushInterval\"]||5e3,c=e[i+\".FlushBackoff\"]||1.5,r=t.global,s=t.on,a=Math.floor,f=(r.document||{}).documentElement||{},l=((r.performance||{}).timing||{}).responseStart||t.time(),d=o,m=0,p=!0,v=t.UUID(),g=t(\"Events\",{producerId:\"csa\"}),w=new n(\"it0\",0,0),I=new n(\"it50\",.5,1e3),h=new n(\"it100\",.99,0),A={},b={};function $(){w.stopAll(!0),I.stopAll(!0),h.stopAll(!0),S()}function C(){w.reset(),I.reset(),h.reset(),S()}function S(){d&&(clearTimeout(m),m=t.timeout($,d),d*=c)}function U(n){E(function(t){T(n,t),t.w[n].mc=(t.w[n].mc||0)+1})}function E(t){g(\"log\",{messageId:v,schemaId:\"<ns>.ContentInteractionsSummary.1\",w:{},__merge:t},{ent:{page:[\"requestId\"]}})}function T(t,n){t in n.w||(n.w[t]={})}e[\"KillSwitch.\"+i]||(s(\"$content.intersection\",function(t){if(t&&t.meta&&t.e){var n=t.meta.id;if(n in A){var e=t.e.boundingClientRect||{};e.width<5||e.height<5||(w.update(n,t.e),I.update(n,t.e),h.update(n,t.e),!t.e.isIntersecting||n in b||(b[n]=1,function(n,e){E(function(t){T(n,t),t.w[n].ttfv=a(e)})}(n,u()-l)))}}}),s(\"$content.register\",function(t){(t.e||{}).slotId&&(A[t.id]={},function(e){E(function(t){var n=e.id;T(n,t),t.w[n].sid=(e.e||{}).slotId,t.w[n].cid=(e.e||{}).contentId})}(t))}),s(\"$beforePageTransition\",function(){$(),C(),v=t.UUID(),S()}),s(\"$beforeunload\",function(){w.stopAll(),I.stopAll(),h.stopAll(),d=null}),s(\"$visible\",function(t){t?C():($(),clearTimeout(m)),p=t},{buffered:1}),s(f,\"click\",function(t){for(var n=t.target,e=25;n&&0<e;){var i=(n.dataset||{}).csaCId;i&&U(i),n=n.parentElement,e-=1}},{capture:!0,passive:!0}),S())});csa.plugin(function(o){var t,n,i=\"normal\",s=\"reload\",e=\"history\",d=\"new-tab\",a=\"ajax\",r=1,c=2,u=\"lastActive\",l=\"lastInteraction\",f=\"used\",p=\"csa-tabbed-browsing\",g=\"visibilityState\",v={\"back-memory-cache\":1,\"tab-switch\":1,\"history-navigation-page-cache\":1},b=\"<ns>.TabbedBrowsing.2\",m=\"visible\",y=o.global,I=o(\"Events\",{producerId:\"csa\"}),h=y.location||{},T=y.document,w=y.JSON,z=((y.performance||{}).navigation||{}).type,P=o.store,S=o.on,k=o.storageSupport(),x=!1,A={},C={},O={},$={},j=!1,q=!1,B=!1;function E(i){try{return w.parse(P(p,void 0,{session:i})||\"{}\")||{}}catch(i){o.error('Could not parse storage value for key \"'+p+'\": '+i)}return{}}function J(i,t){P(p,w.stringify(t||{}),{session:i})}function N(i){var t=C.tid||i.id,n=A[u]||{};n.tid===t&&(n.pid=i.id),$={pid:i.id,tid:t,lastInteraction:C[l]||{},initialized:!0},O={lastActive:n,lastInteraction:A[l]||{},time:o.time()}}function D(i){var t=i===d,n=T.referrer,e=!(n&&n.length)||!~n.indexOf(h.origin||\"\"),a=t&&e,r={type:i,toTabId:$.tid,toPageId:$.pid,transitTime:o.time()-A.time||null};a||function(i,t,n){var e=i===s,a=t?A[u]||{}:C,r=A[l]||{},o=C[l]||{},d=t?r:o;n.fromTabId=a.tid,n.fromPageId=a.pid,e||!d.id||d[f]||(n.interactionId=d.id||null,r.id===d.id&&(r[f]=!0),o.id===d.id&&(o[f]=!0))}(i,t,r),I(\"log\",{navigation:r,schemaId:b},{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}})}function F(i){B=function(i){return i&&i in v}(i.transitionType),function(){A=E(!1),C=E(!0);var i=A[l],t=C[l],n=!1,e=!1;i&&t&&i.id===t.id&&i[f]!==t[f]&&(n=!i[f],e=!t[f],t[f]=i[f]=!0,n&&J(!1,A),e&&J(!0,C))}(),N(i),j=!0,function(i){var t,n;t=H(),n=K(),(t||n)&&N(i)}(i)}function G(){x&&!B?D(a):(x=!0,D(z===c||B?e:z===r?C.initialized?s:d:C.initialized?i:d))}function H(){return!(!j||!t)&&(C[l]={id:t.messageId,used:!(A[l]={id:t.messageId,used:!1})},!(t=null))}function K(){var i=!1;if(q=T[g]===m,j){var t=A[u]||{};i=function(i,t,n){var e=!1,a=i[u];return q?a&&a.tid===$.tid&&a[m]&&a.pid===n||(i[u]={visible:!0,pid:n,tid:t},e=!0):a&&a.tid===$.tid&&a[m]&&(e=!(a[m]=!1)),e}(A,C.tid||t.tid||$.tid,C.pid||t.pid||$.pid)}return i}k.local&&k.session&&w&&T&&g in T&&(n=function(){try{return y.self!==y.top}catch(i){return!0}}(),S(\"$pageChange\",function(i){n||(F(i),G(),J(!1,O),J(!0,$),C=$,A=O)},{buffered:1}),S(\"$content.interaction\",function(i){t=i,H()&&(J(!1,A),J(!0,C))}),S(T,\"visibilitychange\",function(){!n&&K()&&J(!1,A)},{capture:!1,passive:!0}))});csa.plugin(function(c){var e=c(\"Metrics\",{producerId:\"csa\"});c.on(c.global,\"pageshow\",function(c){c&&c.persisted&&e(\"recordMetric\",\"bfCache\",1)})});csa.plugin(function(n){var e,t,i,o,r,a,c,u,f,s,l,d,m,p,g,v,h=\"hasFocus\",b=\"$app.\",y=\"avail\",S=\"client\",T=\"document\",$=\"inner\",I=\"offset\",P=\"screen\",w=\"scroll\",D=\"Width\",E=\"Height\",F=y+D,O=y+E,q=S+D,x=S+E,z=$+D,C=$+E,H=I+D,K=I+E,M=w+D,W=w+E,X=n.config[\"KillSwitch.PageInteractionsSummary\"],Y=n(\"Events\",{producerId:\"csa\"}),j=1,k=n.global||{},A=n.time,B=n.on,G=n.once,J=k[T]||{},L=k[P]||{},N=k.Math||{},Q=N.abs,R=N.max,U=N.ceil,V=((k.performance||{}).timing||{}).responseStart,Z=function(){return J[h]()},_=1,nn=100,en={},tn=1;function on(){c=t=o=r=e,i=0,a=u=f=s=0,cn(),an()}function rn(){V&&!o&&(c=U((o=l)-V),tn=1)}function an(){u=U(R(u,m+v)),d&&(f=U(R(f,d+g))),tn=1}function cn(){l=A(),d=Q(k.pageXOffset||0),m=Q(k.pageYOffset||0),p=0<d||0<m,g=k[z]||0,v=k[C]||0}function un(){cn(),rn(),function(){var n=m-i;t&&!(t&&t<=l)||(n&&(++a,tn=1),i=m,n),t=l+nn}(),an()}function fn(){if(r){var n=U(A()-r);s+=n,r=e,tn=0<n}}function sn(){r=r||A()}function ln(n,e,t,i){e[n+D]=U(t||0),e[n+E]=U(i||0)}function dn(n){var e=n===en,t=Z();if(t||tn){if(!e){if(!j)return;j=0,t&&fn()}var i=function(){var n={},e=J.documentElement||{},t=J.body||{};return ln(\"availableScreen\",n,L[F],L[O]),ln(T,n,R(t[M]||0,t[H]||0,e[q]||0,e[M]||0,e[H]||0),R(t[W]||0,t[K]||0,e[x]||0,e[W]||0,e[K]||0)),ln(P,n,L.width,L.height),ln(\"viewport\",n,k[z],k[C]),n}(),o=function(){var n={scrollCounts:a,reachedDepth:u,horizontalScrollDistance:f,dwellTime:s};return\"number\"==typeof c&&(n.clientTimeToFirstScroll=c),n}();e?tn=0:(on(),V=A(),t&&(r=V)),Y(\"log\",{activity:o,dimensions:i,schemaId:\"<ns>.PageInteractionsSummary.1\"},{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}})}}function mn(){fn(),dn(en)}function pn(n,e){return function(){_=e,n()}}function gn(){Z=function(){return _},_&&!r&&(r=A())}\"function\"!=typeof J[h]||X||(on(),p&&rn(),B(k,w,un,{passive:!0}),B(k,\"blur\",mn),B(k,\"focus\",pn(sn,1)),G(b+\"android\",gn),G(b+\"ios\",gn),B(b+\"pause\",pn(mn,0)),B(b+\"resume\",pn(sn,1)),B(b+\"resign\",pn(mn,0)),B(b+\"active\",pn(sn,1)),Z()&&(r=V||A()),G(\"$beforeunload\",dn),B(\"$beforeunload\",dn),B(\"$document.hidden\",mn),B(\"$beforePageTransition\",dn),B(\"$afterPageTransition\",function(){tn=j=1}))});csa.plugin(function(e){var o,n,r=\"<ns>.Navigator.4\",a=e.global,i=a.navigator||{},d=i.connection||{},c=a.Math.round,t=e(\"Events\",{producerId:\"csa\"});function l(){o={network:{downlink:void 0,downlinkMax:void 0,rtt:void 0,type:void 0,effectiveType:void 0,saveData:void 0},language:void 0,doNotTrack:void 0,hardwareConcurrency:void 0,deviceMemory:void 0,cookieEnabled:void 0,webdriver:void 0},v(),o.language=i.language||null,o.doNotTrack=function(){switch(i.doNotTrack){case\"1\":return\"enabled\";case\"0\":return\"disabled\";case\"unspecified\":return i.doNotTrack;default:return null}}(),o.hardwareConcurrency=\"hardwareConcurrency\"in i?c(i.hardwareConcurrency||0):null,o.deviceMemory=\"deviceMemory\"in i?c(i.deviceMemory||0):null,o.cookieEnabled=\"cookieEnabled\"in i?i.cookieEnabled:null,o.webdriver=\"webdriver\"in i?i.webdriver:null}function u(){t(\"log\",{network:(n={},Object.keys(o.network).forEach(function(e){n[e]=o.network[e]+\"\"}),n),language:o.language,doNotTrack:o.doNotTrack,hardwareConcurrency:o.hardwareConcurrency,deviceMemory:o.deviceMemory,cookieEnabled:o.cookieEnabled,webdriver:o.webdriver,schemaId:r},{ent:{page:[\"pageType\",\"subPageType\",\"requestId\"]}})}function v(){!function(n){Object.keys(o.network).forEach(function(e){o.network[e]=n[e]})}({downlink:\"downlink\"in d?c(d.downlink||0):null,downlinkMax:\"downlinkMax\"in d?c(d.downlinkMax||0):null,rtt:\"rtt\"in d?(d.rtt||0).toFixed():null,type:d.type||null,effectiveType:d.effectiveType||null,saveData:\"saveData\"in d?d.saveData:null})}function k(){v(),u()}function w(){l(),u()}l(),u(),e.on(\"$afterPageTransition\",w),e.on(d,\"change\",k)});\n(function(t,z,C){var u=function(){var a,c=function(){return null!=a?a:a=function(){var a=[],c=\"unknown\",b={trident:0,gecko:0,presto:0,webkit:0,unknown:-1},d,e={},c=document.createElement(\"nadu\");for(d in c.style)if(c=(/^([A-Za-z][a-z]*)[A-Z]/.exec(d)||[])[1])c=c.toLowerCase(),c in e?e[c]++:e[c]=1;for(var n in e)a.push([n,e[n]]);a=a.sort(function(a,c){return c[1]-a[1]}).slice(0,10);for(d=0;d<a.length;d++)switch(a[d][0]){case \"moz\":b.gecko+=5;break;case \"ms\":b.trident+=5;break;case \"get\":b.webkit++;\nbreak;case \"webkit\":b.webkit+=5;break;case \"o\":b.presto+=2;break;case \"xv\":b.presto+=2}\"onhelp\"in window&&b.trident++;\"maxConnectionsPerServer\"in window&&b.trident++;try{void 0!==window.ActiveXObject.toString&&(b.trident+=5)}catch(r){}void 0!==function(){}.toSource&&(b.gecko+=5);var a=\"unknown\",q;for(q in b)b[q]>b[a]&&(a=q);return a}()},b=function(){return!!window.opera||!!window.opr&&!!window.opr.addons},d=function(){return!!document.documentMode},e=function(){return!d()&&\"undefined\"!==typeof CSS&&\nCSS.supports(\"(-ms-ime-align:auto)\")},n=function(){return\"webkit\"==c()},r=function(){return void 0!==z.chrome&&\"Opera Software ASA\"!=navigator.vendor&&void 0===navigator.msLaunchUri&&n()};return{isOpera:b,isIE:d,isEdge:e,isFirefox:function(){return\"undefined\"!==typeof InstallTrigger},isWebkit:n,isChrome:r,isSafari:function(){return!r()&&!e()&&!b()&&\"WebkitAppearance\"in document.documentElement.style}}}(),q=function(a,c,b,d){a.addEventListener?a.addEventListener(c,b,d):a.attachEvent&&a.attachEvent(\"on\"+\nc,b)},r=function(a,c,b,d){document.removeEventListener?a.removeEventListener(c,b,d||!1):document.detachEvent&&a.detachEvent(\"on\"+c,b)},H=function(a){var c;a=a.document;\"undefined\"!==typeof a.hidden?c=\"visibilitychange\":\"undefined\"!==typeof a.mozHidden?c=\"mozvisibilitychange\":\"undefined\"!==typeof a.msHidden?c=\"msvisibilitychange\":\"undefined\"!==typeof a.webkitHidden&&(c=\"webkitvisibilitychange\");return c},T=function(a,c){var b=H(a),d=a.document;b&&q(d,b,c,!1)},U=function(a){var c=window.addEventListener?\n\"addEventListener\":\"attachEvent\";(0,window[c])(\"attachEvent\"==c?\"onmessage\":\"message\",function(c){a(c[c.message?\"message\":\"data\"])},!1)},V=function(a,c){\"function\"===typeof a&&Math.random()<c/100&&a.call(this)},v=function(a){try{for(var c=Array.prototype.slice.call(arguments,1),b=0;b<c.length;b++){if(!a)return!0;var d=a[c[b]];if(null===d||void 0===d)return!0;a=d}return!1}catch(e){return!0}},A=function(a){try{if(!a)return a;for(var c=Array.prototype.slice.call(arguments,1),b,d=0;d<c.length;d++){b=\na[c[d]];if(!b)break;a=b}return b}catch(e){return null}},W=function(a,c){try{if(!a)return!1;for(var b=Array.prototype.slice.call(arguments,2),d=0;d<b.length;d++){var e=a[b[d]];if(null===e||void 0===e)return d===b.length-1?typeof e===c:!1;a=e}return typeof e===c}catch(n){return!1}},I=function(a){a&&document.body&&a.parentNode===document.body&&document.body.removeChild(a)},J=function(a,c,b){var d=window.document.createElement(\"IFRAME\");d.id=c;d.height=\"5px\";d.width=\"5px\";d.src=b?b:\"javascript:'1'\";d.style.position=\n\"absolute\";d.style.top=\"-10000px\";d.style.left=\"-10000px\";d.style.visibility=\"hidden\";d.style.opacity=0;window.document.body.appendChild(d);try{var e=d.contentDocument;if(e&&(e.open(),e.writeln(\"<!DOCTYPE html><html><head><title></title></head><body></body></html>\"),e.close(),a)){var r=e.createElement(\"script\");r&&(r.type=\"text/javascript\",r.text=a,e.body.appendChild(r))}}catch(q){n(q,\"JS exception while injecting iframe\")}return d},n=function(a,c){t.ueLogError(a,{logLevel:\"ERROR\",attribution:\"A9TQForensics\",\nmessage:c})},X=function(a,c,b){a={vfrd:1===c?\"8\":\"4\",dbg:a};\"object\"===typeof b?a.info=JSON.stringify(b):\"string\"===typeof b&&(a.info=b);return{server:window.location.hostname,fmp:a}};(function(a){function c(a,c,b){var d=\"chrm msie ffox sfri opra phnt slnm othr extr xpcm invs poev njs cjs rhn clik1 rfs uam clik stln mua nfo hlpx clkh mmh chrm1 chrm2 wgl srvr zdim nomime chrm3 otch ivm.tst ivm.clk mmh2 clkh2 achf nopl spfp4 uam1 lsph nmim1 slnm2 crtt spfp misp spfp1 spfp2 clik2 clik3 spfp3 estr\".split(\" \");\nF=a<d.length?d[a]:\"othr\";t.ue&&t.ue.event&&t.ue.event(X(F,c,b),\"a9_tq\",\"a9_tq.FraudMetrics.3\")}function b(){var c=!1,m=\"\",b=6,d=function(a,c){var f,m,b=!1;for(f in a)b=b||-1<c.indexOf(f);if(\"function\"===typeof Object.getOwnPropertyNames)for(f=Object.getOwnPropertyNames(a),m=0;m<f.length;m++)b=b||-1<c.indexOf(f[m]);if(\"function\"===typeof Object.keys)for(f=Object.keys(a),m=0;m<f.length;m++)b=b||-1<c.indexOf(f[m]);return b},k=function(a){try{return!!a.toString().match(/\\{\\s*\\[native code\\]\\s*\\}$/m)}catch(c){return!1}},\nl=0;\"undefined\"!==typeof _evaluate&&-1<_evaluate.toString().indexOf(\"browser.runScript\")&&l++;\"undefined\"!==typeof ArrayBuffer&&\"undefined\"!==typeof print&&k(ArrayBuffer)&&!k(print)&&l++;\"undefined\"!==typeof ABORT_ERR&&l++;try{\"undefined\"!==typeof browser&&\"undefined\"!==typeof browser._windowInScope&&\"undefined\"!==typeof browser._windowInScope._response&&l++}catch(Z){}3<=l&&(c=!0);k=[function(){if(!0===d(C,\"__webdriver_evaluate __selenium_evaluate __fxdriver_evaluate __driver_evaluate __webdriver_unwrapped __selenium_unwrapped __fxdriver_unwrapped __driver_unwrapped __webdriver_script_function __webdriver_script_func __webdriver_script_fn webdriver _Selenium_IDE_Recorder _selenium calledSelenium $cdc_asdjflasutopfhvcZLmcfl_ $chrome_asyncScriptInfo __$webdriverAsyncExecutor\".split(\" \")))return!0;\nvar c=function(c){return c.match(/\\$[a-z]dc_/)&&a.document[c]&&a.document[c].cache_},f;for(f in C)if(c(f))return m=f,!0;if(\"function\"===typeof Object.getOwnPropertyNames)for(var b=Object.getOwnPropertyNames(C),l=0;l<b.length;l++)if(c(b[l]))return m=f,!0;return!1},function(){return d(a,\"_phantom __nightmare _selenium callPhantom callSelenium _Selenium_IDE_Recorder webdriver __webdriverFunc domAutomation domAutomationController __lastWatirAlert __lastWatirConfirm __lastWatirPrompt _WEBDRIVER_ELEM_CACHE\".split(\" \"))||\n\"function\"===typeof a.cdc_adoQpoasnfa76pfcZLmcfl_Promise||\"function\"===typeof a.cdc_adoQpoasnfa76pfcZLmcfl_Array||\"function\"===typeof a.cdc_adoQpoasnfa76pfcZLmcfl_Symbol?!0:!1},function(){return a.webdriver||a.document.webdriver||a.document.documentElement.hasAttribute(\"webdriver\")||a.document.documentElement.hasAttribute(\"selenium\")||a.document.documentElement.hasAttribute(\"driver\")||navigator.webdriver||A(p,\"navigator\",\"webdriver\")||\"object\"===typeof a.$cdc_asdjflasutopfhvcZLmcfl_||\"object\"===typeof a.document.$cdc_asdjflasutopfhvcZLmcfl_||\nnull!=a.name&&-1<a.name.indexOf(\"driver\")?(m=null!=a.name?a.name:\"\",!0):!1},function(){return a.external&&\"function\"===typeof a.external.toString&&a.external.toString()&&-1!=a.external.toString().indexOf(\"Sequentum\")?(m=a.external.toString(),!0):!1},function(){for(var c in a){var f;a:{if((f=c)&&33<=f.length&&\"function\"===typeof a[f]){var b=a[f];if(/\\.*_Array$/.test(f)||/\\.*_Promise$/.test(f)||/\\.*_Symbol$/.test(f)||34===f.length&&\"resolve\"in b&&\"reject\"in b&&\"prototype\"in b||33===f.length&&\"isConcatSpreadable\"in\nb&&\"unscopables\"in b&&\"toStringTag\"in b&&\"match\"in b){f=!0;break a}}f=!1}if(f)return m=c,!0}return!1},function(){var a=!1;if(!u.isFirefox())return!1;var c;c=navigator.userAgent.match(/(firefox(?=\\/))\\/?\\s*(\\d+)/i)||[];c=3<=c.length?c[2]:null;if(!c)return!1;60<=c&&void 0===navigator.webdriver?a=!0:60>c&&\"webdriver\"in navigator&&(a=!0);return a?(b=43,m=c.toString(),!0):!1}];for(l=0;l<k.length;l++)if(k[l].call()){c=!0;break}return{isSel:c,isTest:!1,info:m,headlessCode:b}}function d(a){var b=new Date;\n!v(a,\"Function\",\"prototype\",\"toString\")&&W(b,\"function\",\"toLocaleString\")&&(a=a.Function.prototype.toString.call(b.toLocaleString))&&100<a.length&&0<=a.indexOf(\"this.getTime\")&&c(41)}function e(){var a=\"AddChannel AddDesktopComponent AddFavorite AddSearchProvider AddService AddToFavoritesBar AutoCompleteSaveForm AutoScan bubbleEvent ContentDiscoveryReset ImportExportFavorites InPrivateFilteringEnabled IsSearchProviderInstalled IsServiceInstalled IsSubscribed msActiveXFilteringEnabled msAddSiteMode msAddTrackingProtectionList msClearTile msEnableTileNotificationQueue msEnableTileNotificationQueueForSquare150x150 msEnableTileNotificationQueueForSquare310x310 msEnableTileNotificationQueueForWide310x150 msIsSiteMode msIsSiteModeFirstRun msPinnedSiteState msProvisionNetworks msRemoveScheduledTileNotification msReportSafeUrl msScheduledTileNotification msSiteModeActivate msSiteModeAddButtonStyle msSiteModeAddJumpListItem msSiteModeAddThumbBarButton msSiteModeClearBadge msSiteModeClearIconOverlay msSiteModeClearJumpList msSiteModeCreateJumpList msSiteModeRefreshBadge msSiteModeSetIconOverlay msSiteModeShowButtonStyle msSiteModeShowJumpList msSiteModeShowThumbBar msSiteModeUpdateThumbBarButton msStartPeriodicBadgeUpdate msStartPeriodicTileUpdate msStartPeriodicTileUpdateBatch msStopPeriodicBadgeUpdate msStopPeriodicTileUpdate msTrackingProtectionEnabled NavigateAndFind raiseEvent setContextMenu ShowBrowserUI menuArguments onvisibilitychange scrollbar selectableContent version visibility mssitepinned AddUrlAuthentication CloseRegPopup FeatureEnabled GetJsonWebData GetRegValue Log LogShellErrorsOnly OpenPopup ReadFile RunGutsScript SaveRegInfo SetAppMaximizeTimeToRestart SetAppMinimizeTimeToRestart SetAutoStart SetAutoStartMinimized SetMaxMemory ShareEventFromBrowser ShowPopup ShowRadar WriteFile WriteRegValue summonWalrus\".split(\" \"),\nb=-1,d,h;\"Microsoft Internet Explorer\"===navigator.appName?(d=navigator.userAgent,h=/MSIE ([0-9]{1,}[\\.0-9]{0,})/,null!==h.exec(d)&&(b=parseFloat(RegExp.$1))):\"Netscape\"===navigator.appName&&(d=navigator.userAgent,h=/Trident\\/.*rv:([0-9]{1,}[\\.0-9]{0,})/,null!==h.exec(d)&&(b=parseFloat(RegExp.$1)));if(-1===b&&null===navigator.userAgent.match(/Windows Phone/)&&window.external){for(d=b=0;d<a.length;d++)if(\"unknown\"===typeof window.external[a[d]]){b++;break}0<b&&c(17)}}function z(){var f=a.navigator.userAgent;\nif(f&&!/8.0 Safari|iPhone|iPad/.test(f)){var b={clearInterval:42,clearTimeout:41,eval:33,alert:34,prompt:35,scroll:35},d={clearInterval:46,clearTimeout:45,eval:37,alert:38,prompt:39,scroll:39},h=0;if(/Chrome/.test(f))d=b;else if(b=/Firefox/.test(f),f=/Safari/.test(f),!b&&!f)return;if(Function.prototype&&Function.prototype.toString)for(var k in d)\"function\"===typeof window[k]&&(f=Function.prototype.toString.call(window[k]))&&f.length!==d[k]&&(h+=1);3<=h&&(6<=h?c(40,0,h.toString()):c(40,1,h.toString()))}}\nfunction S(){var a=Math.random().toString(36).substr(2),b=null;U(function(d){try{var h=d.split(\" \");if(null!==b&&h[0]===a&&0<h[1].length){var k=JSON.parse(h[1]);for(d=0;d<k.length;d++)1==d&&\"R29vZ2xlIFN3aWZ0U2hhZGVy\"==k[d]&&c(27)}}catch(l){}});b=J('(function fg45s() {                     var payload = [];                     var canvas = document.createElement(\"canvas\");                     var gl = canvas.getContext(\"webgl\") || canvas.getContext(\"experimental-webgl\") || canvas.getContext(\"moz-webgl\");                     if (gl != null) {                         var debugInfo = gl.getExtension(\"WEBGL_debug_renderer_info\");                         if (debugInfo != null) {                             payload.push(btoa(gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL)));                             payload.push(btoa(gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)));                             parent.postMessage(window.frameElement.id + \" \" + JSON.stringify(payload), parent.location.origin);                         }                     }                 }             )();',\na);window.setTimeout(function(){try{b&&document.body&&b.parentNode===document.body&&document.body.removeChild(b),b=null}catch(a){n(a,\"JS exception while removing iframe\")}},5E3)}function L(){function b(){r(a,\"mousemove\",e);r(a,\"click\",d)}function d(){try{c(23),b(),r(a.document,l,h)}catch(m){n(m,\"JS exception - detectClickDuringTabInactive\")}}function e(){try{k||(k=!0,c(24),b(),r(a.document,l,h))}catch(d){n(d,\"JS exception - detectMouseMovementsDuringTabInactive\")}}function h(){try{var c;\"undefined\"!==\ntypeof document.hidden?c=\"hidden\":\"undefined\"!==typeof document.mozHidden?c=\"mozHidden\":\"undefined\"!==typeof document.msHidden?c=\"msHidden\":\"undefined\"!==typeof document.webkitHidden&&(c=\"webkitHidden\");!0!==document[c]===!1?(q(a,\"mousemove\",e,!1),q(a,\"click\",d,!1)):b()}catch(l){n(l,\"JS exception - handleVisibilityChangeDuringTabInactive\")}}var k=!1,l=H(a);T(a,h)}var M=function(){var a=window.navigator,c=a.vendor,b=\"undefined\"!==typeof window.opr,d=-1<a.userAgent.indexOf(\"Edg\"),a=/Chrome/.test(a.userAgent);\nreturn/Google Inc\\./.test(c)&&a&&!b&&!d},F=null,N=function(a){var b=[],d=(new Date).getTime(),h=!1,k=!1,l,e,D=function(){r(a,\"mousemove\",s);r(a,\"click\",g)},s=function(a){try{var f=(new Date).getTime();if(!(100>f-d)){b.push({x:a.clientX,y:a.clientY});if(50<b.length&&(b.shift(),!(50>b.length))){var l=b[0].x,g=b[49].x,k=b[0].y,h=b[49].y;a=h-k;for(var e=l-g,l=k*g-l*h,g=a/e*-1,s=b[49].ts-b[0].ts,k=!0,h=0;h<b.length;h++)if(0!=a*b[h].x+e*b[h].y+l){k=!1;break}!0==k&&(s={grdt:g,tmsp:s},D(),c(19,0,s))}d=f}}catch(B){n(B,\n\"JS exception - recordHoverPosition\")}},g=function(a){try{var d=a.clientX,f=a.clientY,l={hcc:b.length,cx:d,cy:f};if(0===b.length)D(),c(18,0,l);else if(null!=d&&null!=f){var g;l.hpos=b;var k=b[b.length-1];g=Math.sqrt(Math.pow(d-k.x,2)+Math.pow(f-k.y,2));100<g&&(l.hcc=b.length,l.cx=d,l.cy=f,l.dhp=g,D(),c(15,0,l))}}catch(h){n(h,\"JS exception - checkClick\")}},B=function(c){try{l=c.clientX,e=c.clientY,h=!0,r(a,\"mouseup\",B)}catch(b){n(b,\"JS exception - checkMouseUp\")}},p=function(){try{k=!0,r(a,\"mousedown\",\np)}catch(c){n(c,\"JS exception - checkMouseDown\")}},t=function(b){try{h||k||c(49);var d=b.clientX-l,g=b.clientY-e;0<d&&1>d&&0<g&&1>g&&c(50,0,{xDiff:d,yDiff:g});r(a,\"click\",t)}catch(m){n(m,\"JS exception - checkFirstClick\")}};q(a,\"mousemove\",s,!1);q(a,\"mouseup\",B,!1);q(a,\"mousedown\",p,!1);q(a,\"click\",t,!1);q(a,\"click\",g,!1)},O=function(){if(u.isFirefox()){var a=0;void 0!==window.onstorage&&a++;var b=document.createElement(\"div\");b.style.wordSpacing=\"22%\";\"22%\"===b.style.wordSpacing&&a++;\"function\"===\ntypeof b.getAttributeNames&&a++;2<a&&(void 0===window.onabsolutedeviceorientation||void 0===navigator.permissions)&&c(37,0,a)}},w=function(){return null===navigator.userAgent.match(/(iPad|iPhone|iPod|android|webOS)/i)},G=function(a,b){var d=a&&a.navigator;!d||!w()||d.mimeTypes&&0!=d.mimeTypes.length||(x?c(b,0,\"chrm\"):u.isFirefox()&&c(b,0,\"ff\"))},R=function(){var a=function(a,c){for(var b,d=\"\",f={},e={},s=0,g=0;g<c.length;g++)e[c[g]]=String.fromCharCode(126-g);var s=0,m;for(m in a)-1<c.indexOf(m)&&\n(f[m]=1,s++);d=d+s+\"!\";if(\"function\"===typeof Object.getOwnPropertyNames){b=Object.getOwnPropertyNames(a);for(g=s=0;g<b.length;g++)-1<c.indexOf(b[g])&&(f[b[g]]=1,s++);d=d+s+\"!\"}if(\"function\"===typeof Object.keys){b=Object.keys(a);for(g=s=0;g<b.length;g++)-1<c.indexOf(b[g])&&(f[b[g]]=1,s++);d=d+s+\"!\"}if(\"prototype\"in Object&&\"hasOwnProperty\"in Object.prototype)for(m in f)Object.prototype.hasOwnProperty.call(f,m)&&(d+=e[m]);else for(m in f)d+=e[m];return encodeURIComponent(d)},c=document.createElement(\"nadu\"),\na={w:a(window,\"SVGFESpotLightElement XMLHttpRequestEventTarget onratechange StereoPannerNode dolphinInfo VTTCue globalStorage WebKitCSSRegionRule MozSmsFilter MediaController mozInnerScreenX onwebkitwillrevealleft DOMMatrix GeckoActiveXObject MediaQueryListEvent PhoneNumberService ServiceWorkerContainer yandex vc2hxtaq9c NavigatorDeviceStorage HTMLHtmlElement ScreenOrientation MSGesture mozCancelRequestAnimationFrame GetSVGDocument MediaSource webkitMediaStream DeviceMotionEvent webkitPostMessage doNotTrack WebKitMediaKeyError HTMLCollection InstallTrigger StorageObsolete CustomEvent orientation XMLHttpRequest Worker showModelessDialog EventSource onmouseleave SVGAnimatedPathData TouchList TextTrackCue onanimationend HTMLBodyElement fluid MSFrameUITab Generator SecurityPolicyViolationEvent ClientRectList SmartCardEvent CSSSupportsRule mmbrowser\".split(\" \")),\nc:a(c.style,\"XvPhonemes MozTextAlignLast webkitFilter MozPerspective msTextSizeAdjust OAnimationFillMode borderImageSource MozOSXFontSmoothing border-inline-start-color MozOsxFontSmoothing columns touchAction scroll-snap-coordinate webkitAnimationFillMode webkitLineSnap webkitGridAutoColumns animationDuration isolation overflowWrap offsetRotation webkitShapeOutside MozOpacity position justifySelf borderRight webkitMatchNearestMailBlockquoteColor msImeAlign parentRule MozColumnFill cssText borderRightStyle textOverflow webkitGridRow webkitBackgroundComposite length -moz-column-fill enableBackground flex-basis\".split(\" \"))};\nt.ue&&t.ue.event&&(a={vfrd:\"0\",info:JSON.stringify(a)},t.ue.event({server:window.location.hostname,fmp:a},\"a9_tq\",\"a9_tq.FraudMetrics.3\"))},P=function(){var b=function(a){try{return\"function\"!==typeof a||v(p,\"Function\",\"prototype\",\"toString\")?null:p.Function.prototype.toString.call(a)}catch(b){return null}},d=function(a,c){try{if(\"function\"!==typeof Object.getOwnPropertyDescriptor)return null;var d=Object.getPrototypeOf(a);if(!d)return null;var e=Object.getOwnPropertyDescriptor(d,c);return e?b(e.get):\nnull}catch(g){return null}},e=function(a){var b=[28,161,141];!v(a,\"getDetails\",\"a\")&&\"s\"===a.getDetails.a&&0<=b.indexOf(a.getDetails.l)&&c(45,0,k)},h=function(){(function(){if(\"function\"===typeof Object.getOwnPropertyNames&&w()){var a=Object.getOwnPropertyNames(navigator);a&&1<a.length&&c(47,0,a.length.toString())}})();0<navigator.hardwareConcurrency&&!v(p,\"navigator\",\"hardwareConcurrency\")&&p.navigator.hardwareConcurrency!==navigator.hardwareConcurrency&&c(48,0,p.navigator.hardwareConcurrency.toString());\n(function(){var b=[];if(!v(p,\"navigator\")){p===a&&(b.push(\"iwin\"),c(51,0,b));for(var d=\"platform vendor maxTouchPoints userAgent deviceMemory webdriver hardwareConcurrency appVersion mimeTypes plugins languages\".split(\" \"),f=0;f<d.length;f++){var e=d[f],g;if(\"object\"===typeof navigator[e]){g=navigator[e];var h=p.navigator[e],k=!1;g||h?(g&&h?g.length!==h.length?k=!0:0<g.length&&0<h.length&&\"string\"===typeof g[0]&&g[0]!==h[0]&&(k=!0):k=!0,g=k):g=!1}else g=navigator[e],h=p.navigator[e],g=g||h?g!==h?\n!0:!1:!1;g&&b.push(e)}0<b.length&&(0<=b.indexOf(\"webdriver\")?c(51,0,b):c(39,1,b))}})()},k=function(a){if(!a)return null;for(var c={},e=0,h=0,g=0;g<a.length;g++)for(var k=a[g].obj,n=a[g].props,r=0;r<n.length;r++){var p=n[r];c[p]={};var q=A(k,n[r]);if(null===q||void 0===q)h+=1,c[p].a=\"m\",c[p].l=0;else if(q=\"function\"===typeof q?b(q):d(k,p))q&&!/\\[native code\\]/.test(q)?(c[p].a=\"s\",e+=1):c[p].a=\"p\",c[p].l=q.length}return{sig:c,sCount:e,mCount:h}}([{obj:A(a,\"chrome\",\"app\"),props:[\"getDetails\",\"getIsInstalled\",\n\"runningState\"]},{obj:a.chrome,props:[\"csi\",\"loadTimes\",\"runtime\"]},{obj:navigator,props:\"platform vendor userAgent mimeTypes plugins webdriver languages\".split(\" \")}]);k&&(e(k.sig),x&&w()&&3<=k.mCount&&(6<=k.mCount?c(46,0,k):c(46,1,k)),h())},Q=function(){var b=a.Document&&a.Document.prototype.evaluate;b&&(a.Document.prototype.evaluate=function(){try{var d=Error(\"test error\"),e=d.stack&&d.stack.toString();e&&e.match(/(puppeteer|phantomjs|apply.xpath)/)&&c(52,0,e);a.Document.prototype.evaluate=b;return b.apply(this,\narguments)}catch(h){return n(h,\"JS exception while overidding evaluate\"),a.Document.prototype.evaluate=b,b.apply(this,arguments)}})};try{v(navigator,\"userAgent\")&&c(20);var x=M(),y,p;(a.callPhantom||a._phantom||a.PhantomEmitter||a.__phantomas||/PhantomJS/.test(navigator.userAgent))&&c(5);a.Buffer&&c(12);a.emit&&c(13);a.spawn&&c(14);(null!=a.domAutomation||null!=a.domAutomationController||null!=a._WEBDRIVER_ELEM_CACHE||/HeadlessChrome/.test(navigator.userAgent)||\"\"===navigator.languages)&&c(0);if(u.isChrome()&&\na.webkitRequestFileSystem)a.webkitRequestFileSystem(a.TEMPORARY,1,function(){},function(){c(0)});else if(u.isSafari()&&a.localStorage){try{a.localStorage.setItem(\"__nadu\",\"\")}catch($){c(3)}a.localStorage.removeItem(\"__nadu\")}G(a,30);u.isWebkit()&&x&&w()&&(void 0===a.chrome&&c(0),a.chrome&&a.chrome.app&&!1!==a.chrome.app.isInstalled&&void 0!==navigator.languages&&c(31));a.external&&\"function\"===typeof a.external.toString&&a.external.toString()&&-1<a.external.toString().indexOf(\"RuntimeObject\")&&c(8);\na.FirefoxInterfaces&&\"function\"===typeof a.FirefoxInterfaces&&a.FirefoxInterfaces(\"wdICoordinate\",\"wdIMouse\",\"wdIStatus\")&&c(2);a.XPCOMUtils&&c(9);(a.Components&&(a.Components.interfaces&&a.Components.interfaces.nsICommandProcessor||a.Components.wdICoordinate||a.Components.wdIMouse||a.Components.wdIStatus||a.Components.classes)||a.netscape&&a.netscape.security&&a.netscape.security.privilegemanager)&&c(8);a.isExternalUrlSafeForNavigation&&c(1);!a.opera||null===a.opera._browserjsran||0!==a.opera._browserjsran&&\n!1!==a.opera._browserjsran||c(4);a.screen&&(1>=a.screen.availHeight||1>=a.screen.availWidth||1>=a.screen.height||1>=a.screen.width||0>=a.screen.devicePixelRatio)&&c(10);var E=window.setInterval(function(){try{var a=b();a.isSel&&(c(a.headlessCode,!0===a.isTest?1:0,a.info),window.clearInterval(E),I(y))}catch(d){window.clearInterval(E),n(d,\"JS exception - detectSelenium\")}},1E3);window.setTimeout(function(){try{window.clearInterval(E),I(y)}catch(a){n(a,\"JS exception - clearInterval for detectSelenium\")}},\n1E4);var K=a.PointerEvent;a.PointerEvent=function(){c(11);if(void 0!==K){var a=Array.prototype.slice.call(arguments);return new K(a)}return null};e();N(a);L();S();0!==a.outerHeight&&0!==a.outerWidth||c(29);O();!w()||navigator.plugins&&0!=navigator.plugins.length||(x?c(38,0,\"chrm\"):u.isFirefox()&&c(38,0,\"ff\"));V(R,10);x&&w()&&a.chrome&&!a.chrome.csi&&!a.chrome.loadTimes&&c(25);z();y=J(null,Math.random().toString(36).substr(2));p=v(y,\"contentWindow\")?a:y.contentWindow;d(p);G(p,42);0===A(navigator,\"connection\",\n\"rtt\")&&c(44);P();Q()}catch(Y){n(Y,\"JS exception - \")}})(z)})(ue_csm,window,document);\n\n\n\nue.exec(function(d,c){function g(e,c){e&&ue.tag(e+c);return!!e}function n(){for(var e=RegExp(\"^https://(.*\\.(images|ssl-images|media)-amazon\\.com|\"+c.location.hostname+\")/images/\",\"i\"),d={},h=0,k=c.performance.getEntriesByType(\"resource\"),l=!1,b,a,m,f=0;f<k.length;f++)if(a=k[f],0<a.transferSize&&a.transferSize>=a.encodedBodySize&&(b=e.exec(String(a.name)))&&3===b.length){a:{b=a.serverTiming||[];for(a=0;a<b.length;a++)if(\"provider\"===b[a].name){b=b[a].description;break a}b=void 0}b&&(l||(l=g(b,\"_cdn_fr\")),\na=d[b]=(d[b]||0)+1,a>h&&(m=b,h=a))}g(m,\"_cdn_mp\")}d.ue&&\"function\"===typeof d.ue.tag&&c.performance&&c.location&&n()},\"cdnTagging\")(ue_csm,window);\n\n\n}\n!function(n){function r(r){var o=n([1,function(n){b(n).t[S(n)]=w(n)},2,function(n){m(n).t[S(n)]=w(n)},3,function(n){n.u=w(n)},4,function(n){var r=w(n),t=w(n),n=w(n);d(n)||(n[t]=r)},10,function(n){b(n).o.push(w(n))},12,function(n){for(var r=g(n);0<r--;)n.i.push(I(n))},29,function(n){n.u=!w(n)},42,function(){},43,function(n){for(var r=g(n);0<r--;)b(n).t.push(n.v.pop())},45,a(!0),44,a(!1),48,v(0,y),49,v(1,y),50,v(2,y),51,v(-1,y),52,v(0,_),53,v(1,_),54,v(2,_),55,v(-1,_),58,function(n){A(n,F(n))},59,l(!0),60,l(!1),64,function(n){var r=F(n),t=h(n,b(n).l);n.u=t,A(n,r)},65,function(n){var r=g(n),t=F(n),u=h(n,b(n).l);b(n).t[r]=u,A(n,t)}]),i=n([16,s(\"+\"),17,s(\"-\"),18,s(\"*\"),19,s(\"/\"),20,Math.pow,21,s(\"%\"),22,s(\"&\"),23,s(\"|\"),24,s(\"^\"),25,s(\"<<\"),26,s(\">>\"),27,s(\"&&\"),28,s(\"||\"),30,s(\">\"),32,s(\">=\"),31,s(\"<\"),33,s(\"<=\"),34,s(\"==\"),35,s(\"===\"),36,s(\"!=\"),37,s(\"!==\"),38,s(\" in \"),39,function(n,r){return\"__rx_cls\"in n?n.__rx_cls===r.__rx_ref:n instanceof r}]),t=n([10,C,11,null,14,!0,15,!1]),u=n([1,function(n){return n.u},17,g,18,function(n){return k(n,Int32Array,4)},19,function(n){return k(n,Float32Array,4)},12,I,13,function(n){return n.i[g(n)]},20,function(){return[]},21,function(n){for(var r=g(n),t=[];0<r--;)t.unshift(w(n));return t},22,function(){return{}},23,function(n){for(var r=g(n)/2,t={};0<r--;){var u=w(n);t[w(n)]=u}return t},32,function(n){return b(n).t[g(n)]},33,function(n){return m(n).t[g(n)]},48,function(n){var r=w(n),n=w(n);return d(n)?n:(\"function\"==typeof(r=n[r])&&(r._=n),r)},51,function(n){var r=w(n),t=0;return d(r)?r:function(){return{value:r[t],done:!(t++<r.length)}}},50,function(n){return b(n).o.pop()},52,function(n){return typeof w(n)}]);function f(n){for(;0<(r=n).h.length&&b(r).l<r.p.length;)c(S(n),n);var r}function c(n,r){var t,u;return n in i?(t=w(r),u=w(r),r.u=i[n](u,t)):n in o?o[n](r):V(\"e2:\"+n+\":\"+b(r).l),r.u}function e(n,r){return{m:n,l:n,t:[],o:[],A:r}}function n(n){for(var r={},t=0;t<n.length;t+=2)r[n[t]]=n[t+1];return r}function a(u){return function(n){var r=u?w(n):C,t=n.h.pop();n.u=t.A?t.t[0]:r,n.v=[],p(n,b(n).m)}}function v(u,o){return function(n){var r=w(n),t=u;for(-1===u&&(t=g(n));0<t--;)n.v.push(w(n));n.u=C,r&&o(r,n)}}function l(u){return function(n){var r=w(n),t=F(n);(u&&r||!r&&!u)&&A(n,t)}}function s(n){return Function(\"a\",\"b\",\"return a\"+n+\"b\")}function _(n,r){if(n.__rx_ref&&n.S===r){var t=e(n.__rx_ref,!0);t.t.push({__rx_cls:n.__rx_ref}),r.h.push(t),p(r,t.m)}else if(\"function\"==typeof n){r.v.reverse();t=Function.prototype.bind.apply(n,[null].concat(r.v.splice(0)));try{r.u=new t}catch(n){}}else V(\"e5:\"+n+\":\"+b(r).l)}function y(n,r){if(n.__rx_ref&&n.S===r){var t=e(n.__rx_ref);t.t.push(n._||this),r.h.push(t),p(r,t.m)}else if(\"function\"==typeof n){r.v.reverse();try{r.u=n.apply(n._||this,r.v.splice(0))}catch(n){}}else V(\"e4:\"+n)}function w(n){var r=S(n);return 0<(128&r)?c(127&r,n):r in t?t[r]:r in u?u[r](n):void V(\"e3:\"+r)}function h(t,u){var n=P(function(){var n=e(u),r=n.t;return r.push(this),r.push.apply(r,arguments),t.h.push(n),p(t,n.m),f(t),t.u});return n.__rx_ref=u,n.S=t,n}function d(n){return(n===C||null===n)&&(r&&V(\"e10\"+n),1)}function p(n,r){n.k=r%127+37}function b(n){return n.h[n.h.length-1]}function m(n){return n.h[0]}function x(n){return b(n).l}function A(n,r){b(n).l+=r}function S(n){return n.p[b(n).l++]^n.k}function k(n,r,t){for(var u=x(n),o=n.p.slice(u,u+t),i=0;i<o.length;i++)o[i]=o[i]^n.k;u=new r(new Uint8Array(o).buffer);return A(n,t),u[0]}function F(n){return k(n,Int16Array,2)}function g(n){for(var r,t=0,u=0,o=x(n);t|=(127&(r=n.p[o+u]^n.k))<<7*u,u+=1,0<(128&r););return A(n,u),t}function I(n){for(var r=g(n),t=\"\";0<r--;)t+=String.fromCharCode(S(n));return t}function P(n){return function(){try{return n.apply(this,arguments)}catch(n){V(n)}}}function V(n){if(r)throw Error(n)}this.execute=P(function(n,r){var t,u;return 82!==n[0]&&88!==n[1]?V(\"e1\"):(n=n,t=3,(u=e(0)).t[0]=(r=r)||{},u.l=t,p(r={p:n,u:0,h:[u],v:[],i:[],k:0},0),f(t=r),t)})}var C;(n=\"undefined\"==typeof window?n:window).RXVM=r,n.$RX=new r}(window);\n!function(n){for(var o=\"undefined\"==typeof window?n:window,r=0,i=\"sendBeacon\",n=\"addEventListener\",f=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\".split(\"\"),u=[],t=o.rx||{},e=o.navigator||{},c=t.c||{},d=c.rxp||\"/rd/uedata\",a=c.fi||5e3,w={},s={},v=[],x=0,h=0;h<64;h++)u[f[h]]=h;function l(n,o){return function(){try{return n.apply(this,arguments)}catch(n){y(n.message||n,n)}}}function y(n,o){n=(\"\"+(n||\"\")).substring(0,100),v.push(r),v.push(n.length);for(var i=0;i<n.length;i++)v.push(n.charCodeAt(i));if(c.DEBUG)throw o||n;b()}function g(n,o){n in w||(w[n]=o,(s[n]||[]).forEach(function(n){n(o)}))}function m(n){for(var o=0,i=0,r=\"\",t=0;t<n.length;t+=1)for(i+=8,o=o<<8|n[t];6<=i;)r+=f[o>>i-6],o&=255>>8-(i-=6);return 0<i&&(r+=f[o<<6-i]),r}function p(n){for(var o=0,i=0,r=[],t=0;t<n.length&&\"=\"!==n[t];t+=1)for(i+=6,o=o<<6|u[n[t]];8<=i;)r.push(o>>i-8),o&=255>>8-(i-=8);return r}function b(){x||(setTimeout(l(A),a),x=1)}function A(){x=0,rx.ep&&rx.ep(v,B),v=[]}function B(n){n=m(new Uint8Array(n));n=d+\"?rid=\"+rx.rid+\"&sid=\"+rx.sid+\"&rx=\"+n;i in e?e[i](n):(new Image).src=n}function E(n){g(\"unload\",n),A()}(o.rx=t).err=y,t.r=l(function(n,o){o=l(o),n in s||(s[n]=[]),s[n].push(o),n in w&&o()}),t.e=l(g),t.exec=l,t.p=l(function(n,o){v.push(255&n),v=v.concat(o),b()}),t.ex64=l(function(n){n=p(n),o.$RX&&$RX.execute(n,o)}),t.e64=l(m),t.d64=l(p),n in o&&(o[n](\"load\",l(function(n){g(\"load\",n)})),o[n](\"beforeunload\",l(E)),o[n](\"pagehide\",l(E)))}(window);\nrx.ex64(\"UlgBKTUnV10vcExLUR1kV1dEXCNJQEtCUU0hUU1ASy9KS0ZKSFVJQFFALUZESUlHREZOJlZAUSNWUEdRSUAiQEtGV1xVUSFLREhAImRgdghmZ2YjQUxCQFZRInZtZAgXEBMhQF1ARiZXTEEmVkxBJCQVKCUFJSQnuTMVKSRGBSQkJrkVKSNGV1xVUUoFJRUpLUhWZldcVVFKBSVkI2MlbkRwdUhEZEV1SEdlRERHRERGVEV52nVIR2VEZUZhRXR1SU8mLSQ3BiohIAQxZURlRk9EQXdlRmVHREbVVERlRn+UumhlR2QiYiWkjbMpr46CjIKPvr+CjK+Or42ijrWgj7Mpr46Ci6iPi6+Ngoqvjs+cjxMICTQ8GTkJNT9LXEpMVU0ZORWFjou9gouvjqKOo2QtniVwWWfHxnpfxnpeWlt3a2tXWDU0LGtXXx86Lz56W0hKs1xaUVpqa1dePTc0NClrV18WOi8zeltpR1prV1k8L2tWW3pbWlhabmtWWnpby2tWWXtaSl9aX1pua1dQDjI1L2hpGikpOiJ6W05ae1hYa1ddOS49PT4pWlFabmtWWnpbaVFaamtWXXtfaWlrVl17X0pfe1poa1ZTa1ZcelhYe196X0xdSttaVll6XldZMi1WUVZSUVppelx7WWl3ZCzFJbCZqrqdu5qampqqup27mZqZmqmrlpCrlpy6mLualpeRmtvXm2tBc3BMSSktMC8yNAslOXBNR2FDRVVCTEckJSMyOTA0TUhPV0RRwEFNQk1KTUlgQUxDMiE3SkEASUBRe3h+WntXW35KQXJhR3JybUGRmqm6nKmpmpiaqauWkKuWnLqYu5mWl5Ga24ybAigbGSUsWkVASkwJKDg5OCkrLCgECCyRmqm6nKmpmp+ap6uXnt/e2c7cupm+m6qrl5j69/erl5zL6fT28uj+upuOmbufu5iRmp+pl5/ExP7pupq3FBUoKAUkBS0vJCEXKSdAVQUkGb4VKCoFJBUoKwUkMSUUFSgoBSQFLBckFSgqBSQVKCsFJA==\");\nrx.ex64(\"UlgBKS8sUkBHQVdMU0BXI2pHT0BGUSFOQFxWIkxLQUBdakMhQUpLQCBTRElQQCBkV1dEXCN2XEhHSkkgdVdKXVwnV10kJDQ1JCc0JCQmuTMVKSxLRFNMQkRRSlcFJSQhuTMVKS1BSkZQSEBLUQUlZCA2JbK1BQWolJLo9Pnh7+rx//DsuZhkIyglGh2slhEwPTAAPTARM2QiGCVrcHFMQ3FMQGBBYEFAQHJAcWFAQENxTERAfeJOcUxFQFhBcHFMQmFDTUUiJSIeYlBBQH1AQ0FsT3uUvmxOZC2JJaizso+Aso+Do4KjgoODsYOyooODgLKPh4O+IY2yj4aDCoKzso+BooCOhN3D8PDj+6KTgoOZgyePhKKAmSGyj4SjgrKigKOCg76DgIKvjLOyj4GigI6F3dH77+Dt7qKTgoOZgyePhaKAmSGyj4WjgrKigKOCg76DgIKvjLOyj4GigI6E3dLw7fr7opOCg5mDJ4+KooCZIbKPiqOCsqKAo4KDvoOAgq+MuOR9r41kLAUlHhmokhU0OD9XVVhYZFxVWkBbWZIVNDg8a0RcVVpAW1lkL+ElcmloVFU7Kj05LD0dND01PTYseVxUXjs5Ni45K1lZWWloVFI/PSwbNzYsPSAseFlUXS89Oj80WVpZZMV4WlpYdVZpaFRUPz0sHSAsPTYrMTc2eFpUQQ8dGh8UBzw9Oi0/Byo9Njw9Kj0qBzE2PjdbaFRPDRYVGQsTHRwHCh0WHB0KHQoHDx0aHxRZUllpaFRUPz0sCDkqOTU9LD0qeFpqWVtZZPtSeFtaWHVWaWhVW3hbVFMLLzE+LAswOTw9KnhJWFl1WWQuBiWLjAKRrarIz8/E0+nEyMbJ1YChka2qztTVxNPpxMjGydWAoWQpDiVjZOp5RUMgJycsOx4gLT0haEl5RUIqJSAsJz0eIC09IdVfeUVNKyYtMGhNJCgwLQUpBS4FLwUsBS0FIgUjBSBkK0Ilpo2NnYyNjp2MsBO8gIrg6eLr+OStgayOtoy9vICI6fTp77yBha2MvKyOrYG8jY2PjbAvhqyPj4yNj4KwrI+GjI2NGxWsjp2NrI2NjhydjayOtjlzvryAjfy8gYWtjJmOrI2tjq2NoBcVKSRXFSgsBSUFKykhSUpEQQ==\");\n/* ◬ */\n\n\n\n\n\n    <img height=\"1\" width=\"1\" style='display:none;visibility:hidden;' src='//fls-na.amazon.com/1/batch/1/OP/ATVPDKIKX0DER:134-1570085-9450222:K1JQ9VEP43RRWKW775V5$uedata=s:%2Frd%2Fuedata%3Fnoscript%26id%3DK1JQ9VEP43RRWKW775V5:0' alt=\"\"/>\n\n\nwindow.ue && ue.count && ue.count('CSMLibrarySize', 92747)\n\n\n\n\n\n     ✍操作台（点此拖动，左上角调整大小）  \n              ● 已选中1个元素，您可以:\n                  确认采集    取消选择  Path: /html/body  \n\n\n  \n    \n  \n  \n    \n\n  Close menu\n\n\n\n    \n      \n    \n  \n    \n      Hello, sign in\n    \n  \n\n\n\n    \n      \n\n\ndigital content & devicesAmazon MusicKindle E-readers & BooksAmazon Appstoreshop by departmentElectronicsComputersSmart HomeArts & CraftsAutomotiveBabyBeauty and personal careWomen's FashionMen's FashionGirls' FashionBoys' FashionHealth and HouseholdHome and KitchenIndustrial and ScientificLuggageMovies & TelevisionPet suppliesSoftwareSports and OutdoorsTools & Home ImprovementToys and GamesVideo Gamessee allsee lessprograms & featuresGift CardsShop By InterestAmazon LiveInternational ShoppingAmazon Second Chancesee allsee lesshelp & settingsYour AccountEnglishUnited StatesCustomer ServiceSign in\n\n\n    \n    Back to top\n  \n\n"}], "unique_index": "43aibjbtha4ljypan6w", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}