{"id": 85, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "5/28/2023, 1:41:36 AM", "version": "0.3.1", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "参数1_图片页面标题", "desc": "", "type": "string", "exampleValue": "京东全球版-专业的综合网上购物商城"}, {"id": 1, "name": "参数2_图片页面网址", "desc": "", "type": "string", "exampleValue": "https://global.jd.com/"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 4, "contentType": 6, "relative": false, "name": "参数1_图片页面标题", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/a[1]/img[1]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/a[1]/img[1]", "//img[contains(., '')]"], "exampleValues": [{"num": 0, "value": "京东全球版-专业的综合网上购物商城"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 5, "relative": false, "name": "参数2_图片页面网址", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[4]/div[1]/div[1]/a[1]/img[1]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[4]/div[1]/div[1]/a[1]/img[1]", "//img[contains(., '')]"], "exampleValues": [{"num": 0, "value": "https://global.jd.com/"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}