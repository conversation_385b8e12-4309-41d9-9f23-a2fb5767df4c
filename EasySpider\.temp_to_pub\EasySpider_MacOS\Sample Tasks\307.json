{"id": 307, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "2023-12-23 11:16:19", "update_time": "2023-12-23 11:32:41", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "dataWriteMode": 1, "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "browser": "chrome", "removeDuplicate": 0, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "Field[\"参数\"]TTTeval(\"self.a\")TTTJS(\"return new Date().getMonth()+1\")TTTJS(\"return new Date()\")", "value": "Field[\"参数\"]TTTeval(\"self.a\")TTTJS(\"return new Date().getMonth()+1\")TTTJS(\"return new Date()\")"}, {"id": 2, "name": "loopText_2", "nodeId": 4, "nodeName": "循环输入文字", "desc": "要输入的文本/网址,多行以\\n分开", "type": "text", "exampleValue": "JS(\"function getCurrentMonth() {var now = new Date(); var month = now.getMonth() + 1; return month;} return getCurrentMonth()\")/2023\nJS(\"function getCurrentMonth() {var now = new Date(); var month = now.getMonth() + 1; return month;} return getCurrentMonth()\")/2024\nJS(\"function getCurrentMonth() {var now = new Date(); var month = now.getMonth() + 1; return month;} return getCurrentMonth()\")/2025", "value": "JS(\"function getCurrentMonth() {var now = new Date(); var month = now.getMonth() + 1; return month;} return getCurrentMonth()\")/2023\nJS(\"function getCurrentMonth() {var now = new Date(); var month = now.getMonth() + 1; return month;} return getCurrentMonth()\")/2024\nJS(\"function getCurrentMonth() {var now = new Date(); var month = now.getMonth() + 1; return month;} return getCurrentMonth()\")/2025"}], "outputParameters": [{"id": 0, "name": "执行Python代码", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 1, "name": "参数", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "手机"}, {"id": 2, "name": "参数2_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://shouji.jd.com/"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 3, 6, 2, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 4, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "value": "Field[\"参数\"]TTTeval(\"self.a\")TTTJS(\"return new Date().getMonth()+1\")TTTJS(\"return new Date()\")", "index": 0, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": 2, "index": 3, "parentId": 0, "type": 0, "option": 5, "title": "执行Python代码", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 5, "code": "self.a = 222", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"id": 5, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环输入文字", "sequence": [5], "isInLoop": false, "position": 4, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 3, "pathList": "", "textList": "JS(\"function getCurrentMonth() {var now = new Date(); var month = now.getMonth() + 1; return month;} return getCurrentMonth()\")/2023\nJS(\"function getCurrentMonth() {var now = new Date(); var month = now.getMonth() + 1; return month;} return getCurrentMonth()\")/2024\nJS(\"function getCurrentMonth() {var now = new Date(); var month = now.getMonth() + 1; return month;} return getCurrentMonth()\")/2025", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": 7, "index": 5, "parentId": 5, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "value": "result 1JS(\"return 22\")", "index": 0, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": 3, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [7], "isInLoop": false, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '手机')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/a[last()-1]"]}}, {"id": 6, "index": 7, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "手机"}], "unique_index": "tain0ppyd1rlqhi7699", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "https://shouji.jd.com/"}], "unique_index": "tain0ppyd1rlqhi7699", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}]}}]}