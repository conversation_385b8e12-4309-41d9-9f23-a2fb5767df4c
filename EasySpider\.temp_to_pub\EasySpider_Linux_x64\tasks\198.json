{"id": 198, "name": "Cross Origin Local Storage", "url": "https://space.bilibili.com/291929894/video?tid=0&pn=1&keyword=&order=pubdate", "links": "https://space.bilibili.com/291929894/video?tid=0&pn=1&keyword=&order=pubdate", "create_time": "7/23/2023, 8:01:53 PM", "update_time": "7/23/2023, 8:05:25 PM", "version": "0.5.0", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "https://space.bilibili.com/291929894/video?tid=0&pn=1&keyword=&order=pubdate", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://space.bilibili.com/291929894/video?tid=0&pn=1&keyword=&order=pubdate", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://space.bilibili.com/291929894/video?tid=0&pn=1&keyword=&order=pubdate"}, {"id": 1, "name": "loopTimes_循环_1", "nodeId": 2, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数1_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "07:33"}, {"id": 1, "name": "参数2_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//www.bilibili.com/video/BV1pg411a748/"}, {"id": 2, "name": "参数3_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//i0.hdslb.com/bfs/archive/1e0d9307f1ca5024141ec58676e18e4d3b2a7068.jpg@320w_200h_1c_!web-space-upload-video.webp"}, {"id": 3, "name": "参数4_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "新加坡国立大学NUS数据科学学院IDS博士生招生宣传（每周二四六晚8-10点直播答疑，同时答疑NUS计算机和EE博士申请，平日直播间为日常生活分享）"}, {"id": 4, "name": "参数5_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//www.bilibili.com/video/BV1pg411a748/"}, {"id": 5, "name": "参数6_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "599"}, {"id": 6, "name": "参数7_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "2022-10-16"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 6, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://space.bilibili.com/291929894/video?tid=0&pn=1&keyword=&order=pubdate", "links": "https://space.bilibili.com/291929894/video?tid=0&pn=1&keyword=&order=pubdate", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4, 3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"be-pager-next\")]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[2]/div[4]/div[1]/div[1]/ul[3]/li[4]", "//li[contains(., '下一页')]", "//LI[@class='be-pager-next']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div/div/div/ul/li"]}}, {"id": 4, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 4, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[2]/div[4]/div[1]/div[1]/ul[3]/li[4]", "//li[contains(., '下一页')]", "//LI[@class='be-pager-next']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div/div/div/ul/li"], "loopType": 0}}, {"id": 3, "index": 4, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[4]/div[1]/div[1]/div[2]/div[4]/div[1]/div[1]/ul[2]/li", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[2]/div[4]/div[1]/div[1]/ul[2]/li[1]", "//li[contains(., '07:33新加坡国立')]", "//LI[@class='small-item fakeDanmu-item']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div/div/div/ul[last()-1]/li[last()-15]"]}}, {"id": 5, "index": 5, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '07:33')]", "//A[@class='cover']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div/div/div/ul[last()-1]/li[last()-15]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "07:33"}], "unique_index": "/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '07:33')]", "//A[@class='cover']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div/div/div/ul[last()-1]/li[last()-15]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "//www.bilibili.com/video/BV1pg411a748/"}], "unique_index": "/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数3_图片地址", "desc": "", "relativeXPath": "/a[1]/div[1]/picture[1]/img[1]", "allXPaths": ["/a[1]/div[1]/picture[1]/img[1]", "//img[contains(., '')]", "//IMG[@alt='新加坡国立大学NUS数据科学学院IDS博士生招生宣传（每周二四六晚8-10点直播答疑，同时答疑NUS计算机和EE博士申请，平日直播间为日常生活分享）']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div/div/div/ul[last()-1]/li[last()-15]/a[last()-1]/div/picture/img"], "exampleValues": [{"num": 0, "value": "//i0.hdslb.com/bfs/archive/1e0d9307f1ca5024141ec58676e18e4d3b2a7068.jpg@320w_200h_1c_!web-space-upload-video.webp"}], "unique_index": "/a[1]/div[1]/picture[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 1}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数4_链接文本", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '新加坡国立大学NUS')]", "//A[@class='title']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div/div/div/ul[last()-1]/li[last()-15]/a"], "exampleValues": [{"num": 0, "value": "新加坡国立大学NUS数据科学学院IDS博士生招生宣传（每周二四六晚8-10点直播答疑，同时答疑NUS计算机和EE博士申请，平日直播间为日常生活分享）"}], "unique_index": "/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数5_链接地址", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '新加坡国立大学NUS')]", "//A[@class='title']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div/div/div/ul[last()-1]/li[last()-15]/a"], "exampleValues": [{"num": 0, "value": "//www.bilibili.com/video/BV1pg411a748/"}], "unique_index": "/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数6_文本", "desc": "", "relativeXPath": "/div[1]/span[1]/span[1]", "allXPaths": ["/div[1]/span[1]/span[1]", "//span[contains(., '599')]", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div/div/div/ul[last()-1]/li[last()-15]/div/span[last()-1]/span"], "exampleValues": [{"num": 0, "value": "599"}], "unique_index": "/div[1]/span[1]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数7_文本", "desc": "", "relativeXPath": "/div[1]/span[2]", "allXPaths": ["/div[1]/span[2]", "//span[contains(., '')]", "//SPAN[@class='time']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div/div/div/ul[last()-1]/li[last()-15]/div/span"], "exampleValues": [{"num": 0, "value": "2022-10-16"}], "unique_index": "/div[1]/span[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}