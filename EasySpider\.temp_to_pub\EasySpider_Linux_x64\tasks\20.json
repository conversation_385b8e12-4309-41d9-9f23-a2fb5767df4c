{"id": 20, "name": "Bilibili粉丝", "url": "https://space.bilibili.com/291929894/fans/fans", "links": "https://space.bilibili.com/291929894/fans/fans", "containJudge": false, "desc": "https://space.bilibili.com/291929894/fans/fans", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "Open Page", "value": "https://space.bilibili.com/291929894/fans/fans", "desc": "List of URLs to be collected, separated by \\n for multiple lines", "type": "string", "exampleValue": "https://space.bilibili.com/291929894/fans/fans"}, {"id": 1, "name": "loopTimes_Loop_1", "nodeId": 2, "nodeName": "Loop", "desc": "Number of loop executions, 0 means unlimited loops (until element not found)", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "对方答复5"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "url": "https://space.bilibili.com/291929894/fans/fans", "links": "https://space.bilibili.com/291929894/fans/fans", "scrollType": 0, "scrollCount": 0}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "Loop", "sequence": [4], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//a[contains(text(),\"下一页\")]", "wait": 0, "scrollType": 0, "scrollCount": 0, "loopType": 0, "pathList": "", "textList": "", "exitCount": 0, "historyWait": 2}}, {"id": -1, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "<PERSON>lick Element", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"page-follows\"]/div[1]/div[2]/div[2]/div[2]/ul[2]/li[7]", "wait": 1, "scrollType": 0, "scrollCount": 0, "params": [], "loopType": 0}}, {"id": 3, "index": 4, "parentId": 2, "type": 1, "option": 8, "title": "Loop", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[4]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/ul[1]/li/div[2]/a[1]/span[1]", "wait": 0, "scrollType": 0, "scrollCount": 0, "loopType": 1, "pathList": "", "textList": "", "exitCount": 0, "historyWait": 2}}, {"id": 4, "index": 5, "parentId": 3, "type": 0, "option": 3, "title": "Extract Data", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "", "exampleValues": [{"num": 0, "value": "对方答复5"}], "default": ""}], "loopType": 1}}]}