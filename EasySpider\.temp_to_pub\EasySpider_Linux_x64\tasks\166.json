{"id": 166, "name": "（无限滚动翻页子元素测试）京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://search.jd.com/Search?keyword=iphone&qrst=1&wq=iphone&ev=exbrand_Apple%5E&pvid=437ed6ea5f6445b3b05704232c701074&page=98&s=121&click=0", "create_time": "7/13/2023, 3:24:51 PM", "update_time": "7/13/2023, 3:25:05 PM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://search.jd.com/Search?keyword=iphone&qrst=1&wq=iphone&ev=exbrand_Apple%5E&pvid=437ed6ea5f6445b3b05704232c701074&page=98&s=121&click=0", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://search.jd.com/Search?keyword=iphone&qrst=1&wq=iphone&ev=exbrand_Apple%5E&pvid=437ed6ea5f6445b3b05704232c701074&page=98&s=121&click=0"}, {"id": 1, "name": "loopTimes_循环_1", "nodeId": 6, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数40_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "iPhone"}, {"id": 1, "name": "参数47_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "剩余9天22时10分"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 6], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://search.jd.com/Search?keyword=iphone&qrst=1&wq=iphone&ev=exbrand_Apple%5E&pvid=437ed6ea5f6445b3b05704232c701074&page=98&s=121&click=0", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "iPhone", "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": -1, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-btn\"]/i[1]", "iframe": false, "wait": 8, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": "3", "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/button[1]/i[1]", "//i[contains(., '')]", "/html/body/div[last()-6]/div/div[last()-2]/div/button/i"]}}, {"id": 3, "index": 4, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li/div[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='gl-i-wrap']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div"]}}, {"id": 5, "index": 5, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数40_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/em[1]/font[3]", "allXPaths": ["/div[4]/a[1]/em[1]/font[3]", "//font[contains(., 'iPhone')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-24]/div/div[last()-5]/a/em/font"], "exampleValues": [{"num": 5, "value": "iPhone"}], "unique_index": "/div[4]/a[1]/em[1]/font[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数47_文本", "desc": "", "relativeXPath": "//a/em[1]", "allXPaths": ["/div[10]/em[1]", "//em[contains(., '剩余9天22时10分')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-19]/div/div/em"], "exampleValues": [{"num": 10, "value": "剩余9天22时10分"}], "unique_index": "/div[10]/em[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 2, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4, 7], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"pn-next\")]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[3]/div[1]/span[1]/a[9]", "//a[contains(., '下一页>')]", "//A[@class='pn-next']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div/div/span[last()-1]/a"]}}, {"id": 4, "index": 7, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "//*[contains(@class, \"pn-next\")]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": "3", "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[3]/div[1]/span[1]/a[9]", "//a[contains(., '下一页>')]", "//A[@class='pn-next']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div/div/span[last()-1]/a"], "loopType": 0}}]}