{"id": 88, "name": "选中子元素（测试C）", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "5/31/2023, 9:04:57 PM", "version": "0.3.2", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "/"}, {"id": 1, "name": "参数2_链接文本", "desc": "", "type": "string", "exampleValue": "手机"}, {"id": 2, "name": "参数3_链接地址", "desc": "", "type": "string", "exampleValue": "https://shouji.jd.com/"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '/手机/数码')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG ']"]}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/span[1]", "allXPaths": ["/span[1]", "//span[contains(., '/')]", "//SPAN[@class='LeftSide_cate_menu_line__vzQu9 LeftSide_fore0__r2Yrl']"], "exampleValues": [{"num": 0, "value": "/"}, {"num": 1, "value": "/"}, {"num": 2, "value": "/"}, {"num": 3, "value": "/"}, {"num": 4, "value": "/"}, {"num": 5, "value": "/"}, {"num": 6, "value": "/"}, {"num": 7, "value": "/"}, {"num": 8, "value": "/"}, {"num": 9, "value": "/"}, {"num": 10, "value": "/"}, {"num": 11, "value": "/"}, {"num": 12, "value": "/"}], "unique_index": "/span[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数2_链接文本", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '手机')]"], "exampleValues": [{"num": 0, "value": "手机"}, {"num": 1, "value": "家用电器"}, {"num": 2, "value": "电脑"}, {"num": 3, "value": "家纺"}, {"num": 4, "value": "家具"}, {"num": 5, "value": "内衣"}, {"num": 6, "value": "箱包"}, {"num": 7, "value": "运动"}, {"num": 8, "value": "汽车用品"}, {"num": 9, "value": "母婴"}, {"num": 10, "value": "玩具乐器"}, {"num": 11, "value": "家庭清洁"}, {"num": 12, "value": "图书"}], "unique_index": "/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数3_链接地址", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '手机')]"], "exampleValues": [{"num": 0, "value": "https://shouji.jd.com/"}, {"num": 1, "value": "https://search.jd.com/Search?keyword=%E5%B0%8F%E5%AE%B6%E7%94%B5&enc=utf-8&wq=%E5%B0%8F%E5%AE%B6%E7%94%B5&pvid=261a350161304c979fa0e7ce95c05671"}, {"num": 2, "value": "https://diannao.jd.com/"}, {"num": 3, "value": "https://channel.jd.com/jf.html"}, {"num": 4, "value": "https://channel.jd.com/furniture.html"}, {"num": 5, "value": "https://channel.jd.com/underwear.html"}, {"num": 6, "value": "https://channel.jd.com/bag.html"}, {"num": 7, "value": "https://phat.jd.com/10-109.html"}, {"num": 8, "value": "https://che.jd.com/"}, {"num": 9, "value": "https://search.jd.com/Search?keyword=%E6%AF%8D%E5%A9%B4&enc=utf-8&qrst=1&rt=1&stop=1&vt=2&wq=%E6%AF%8D%E5%A9%B4&stock=1&gp=2&click=1"}, {"num": 10, "value": "https://toy.jd.com/"}, {"num": 11, "value": "https://channel.jd.com/beauty.html"}, {"num": 12, "value": "https://book.jd.com/"}], "unique_index": "/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}