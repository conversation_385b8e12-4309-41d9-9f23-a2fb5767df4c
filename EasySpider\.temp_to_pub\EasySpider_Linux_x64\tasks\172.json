{"id": 172, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "7/14/2023, 2:22:19 AM", "update_time": "7/14/2023, 3:26:51 AM", "version": "0.3.6", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "containJudge": true, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 1, "name": "自定义操作2", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 2, "name": "自定义操作3", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 3, "name": "参数2_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "今日推荐"}, {"id": 4, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 5, "name": "CONTINUE", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 6, "name": "自定义操作4", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 7, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 23, 4, 5, 9], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "5", "code": "self.ta = 1\nself.tb = 1\nprint(self.ta)\nself.ta += \"PPP\"\nself.browser.find_element(By.CSS_SELECTOR, \"body\").send_keys(Keys.END)", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": -1, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 4, "index": 4, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作2", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "6", "code": "self.ta", "waitTime": 0, "recordASField": 1, "paraType": "text"}}, {"id": 5, "index": 5, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作3", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "6", "code": "self.browser.find_element(By.CSS_SELECTOR, \"body\").text", "waitTime": 0, "recordASField": 1, "paraType": "text"}}, {"id": -1, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [7], "isInLoop": false, "position": 4, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '/手机/数码')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG ']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]"]}}, {"id": -1, "index": 7, "parentId": 5, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "/手机/数码"}], "unique_index": "ih7gv6hqtblk1hz79l", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 12, "index": 8, "parentId": 9, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": false, "name": "参数2_链接文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[Field[\"自定义操作4\"]]/div[1]/ul[1]/li[2]/a[1]", "allXPaths": ["/html/body/div[4]/div[1]/ul[1]/li[2]/a[1]", "//a[contains(., '今日推荐')]", "/html/body/div[last()-6]/div/ul/li/a"], "exampleValues": [{"num": 0, "value": "今日推荐"}], "unique_index": "goewg2jg36vlk1hzq4j", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 6, "index": 9, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [10, 11], "isInLoop": false, "position": 5, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": "7", "pathList": "", "textList": "", "code": "self.tb < 20", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 7, "index": 10, "parentId": 6, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 1, "codeMode": "5", "code": "self.ta += 1\nself.tb += 1\nprint(\"TATB\", ' is', self.ta, self.tb)", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 8, "index": 11, "parentId": 6, "type": 2, "option": 9, "title": "判断条件", "sequence": [12, 13], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": 9, "parentId": 8, "index": 12, "type": 3, "option": 10, "title": "self.ta < 10", "sequence": [22, 8], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "8", "value": "", "code": "self.ta < 10", "waitTime": 0}, "position": 0}, {"id": 10, "parentId": 8, "index": 13, "type": 3, "option": 10, "title": "条件分支", "sequence": [14], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 13, "index": 14, "parentId": 10, "type": 0, "option": 5, "title": "CONTINUE", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "4", "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": -1, "index": 15, "parentId": 5, "type": 2, "option": 9, "title": "判断条件", "sequence": [16], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 7, "index": 16, "type": 3, "option": 10, "title": "tb > 20", "sequence": [21], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "8", "value": "", "code": "self.tb > 20", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 8, "index": 17, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 18, "parentId": 9, "type": 2, "option": 9, "title": "自定义操作", "sequence": [19, 20], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 13, "index": 19, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 13, "index": 20, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 21, "parentId": 9, "type": 0, "option": 5, "title": "BREAK", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "3", "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 11, "index": 22, "parentId": 9, "type": 0, "option": 5, "title": "自定义操作4", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "6", "code": "self.ta", "waitTime": 0, "recordASField": 1, "paraType": "text"}}, {"id": 3, "index": 23, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "0", "code": "123", "waitTime": 0, "recordASField": 0, "paraType": "text"}}]}