{"id": 9, "name": "Electronics, Cars, Fashion, Collectibles & More | eBay", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "create_time": "5/25/2023, 9:26:34 PM", "version": "0.3.1", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "Open Page", "value": "https://www.ebay.com", "desc": "List of URLs to be collected, separated by \\n for multiple lines", "type": "string", "exampleValue": "https://www.ebay.com"}, {"id": 1, "name": "inputText_1", "nodeName": "Input Text", "nodeId": 2, "desc": "The text to be entered, such as 'computer' at eBay search box", "type": "string", "exampleValue": "iphone", "value": "iphone"}], "outputParameters": [{"id": 0, "name": "para1_page_title", "desc": "", "type": "string", "exampleValue": "iphone for sale | eBay"}, {"id": 1, "name": "para2_page_url", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/sch/i.html?_from=R40&_trksid=p2380057.m570.l1313&_nkw=iphone&_sacat=0"}, {"id": 2, "name": "para3_background_image_address", "desc": "", "type": "string", "exampleValue": "https://i.ebayimg.com/00/z/3RcAAOSwJ~pkQaT7/$_58.jpg"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 5, 6, 2, 3, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 4, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "Input Text", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"gh-ac\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "iphone", "allXPaths": ["/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[5]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/div[1]/input[1]", "//input[contains(., '')]", "id(\"gh-ac\")", "//INPUT[@class='gh-tb ui-autocomplete-input ui-autocomplete-loading']", "//INPUT[@name='_nkw']"]}}, {"id": 5, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "<PERSON>lick Element", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"gh-btn\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[5]/form[1]/table[1]/tbody[1]/tr[1]/td[3]/input[1]", "//input[contains(., '')]", "id(\"gh-btn\")", "//INPUT[@class='btn btn-prim gh-spr']"]}}, {"id": 6, "index": 4, "parentId": 0, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": false, "position": 5, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 6, "relative": false, "name": "para1_page_title", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": ["/html/body", "//body[contains(., '\"use stric')]", "//BODY[@class='s-page no-touch skin-large srp--list-view srp-shopping-list-v1 gh-flex']"], "exampleValues": [{"num": 0, "value": "iphone for sale | eBay"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 5, "relative": false, "name": "para2_page_url", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": ["/html/body", "//body[contains(., '\"use stric')]", "//BODY[@class='s-page no-touch skin-large srp--list-view srp-shopping-list-v1 gh-flex']"], "exampleValues": [{"num": 0, "value": "https://www.ebay.com/sch/i.html?_from=R40&_trksid=p2380057.m570.l1313&_nkw=iphone&_sacat=0"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 4, "relative": false, "name": "para3_background_image_address", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[5]/div[4]/div[1]/div[1]/a[1]/div[1]/div[2]/div[1]", "allXPaths": ["/html/body/div[5]/div[4]/div[1]/div[1]/a[1]/div[1]/div[2]/div[1]", "//div[contains(., '')]", "//DIV[@class='srp-1p__image']"], "exampleValues": [{"num": 0, "value": "https://i.ebayimg.com/00/z/3RcAAOSwJ~pkQaT7/$_58.jpg"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 2, "index": 5, "parentId": 0, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 6, "relative": false, "name": "para1_page_title", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": ["/html/body", "//body[contains(., '\"use stric')]", "//BODY[@class='s-page no-touch skin-large srp--list-view srp-shopping-list-v1 gh-flex']"], "exampleValues": [{"num": 0, "value": "iphone for sale | eBay"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 5, "relative": false, "name": "para2_page_url", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": ["/html/body", "//body[contains(., '\"use stric')]", "//BODY[@class='s-page no-touch skin-large srp--list-view srp-shopping-list-v1 gh-flex']"], "exampleValues": [{"num": 0, "value": "https://www.ebay.com/sch/i.html?_from=R40&_trksid=p2380057.m570.l1313&_nkw=iphone&_sacat=0"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 4, "relative": false, "name": "para3_background_image_address", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[5]/div[4]/div[1]/div[1]/a[1]/div[1]/div[2]/div[1]", "allXPaths": ["/html/body/div[5]/div[4]/div[1]/div[1]/a[1]/div[1]/div[2]/div[1]", "//div[contains(., '')]", "//DIV[@class='srp-1p__image']"], "exampleValues": [{"num": 0, "value": "https://i.ebayimg.com/00/z/3RcAAOSwJ~pkQaT7/$_58.jpg"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 3, "index": 6, "parentId": 0, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 7, "relative": false, "name": "para1_page_title", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": ["/html/body", "//body[contains(., '\"use stric')]", "//BODY[@class='s-page no-touch skin-large srp--list-view srp-shopping-list-v1 gh-flex']"], "exampleValues": [{"num": 0, "value": "iphone for sale | eBay"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 5, "relative": false, "name": "para2_page_url", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": ["/html/body", "//body[contains(., '\"use stric')]", "//BODY[@class='s-page no-touch skin-large srp--list-view srp-shopping-list-v1 gh-flex']"], "exampleValues": [{"num": 0, "value": "https://www.ebay.com/sch/i.html?_from=R40&_trksid=p2380057.m570.l1313&_nkw=iphone&_sacat=0"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 4, "relative": false, "name": "para3_background_image_address", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[5]/div[4]/div[1]/div[1]/a[1]/div[1]/div[2]/div[1]", "allXPaths": ["/html/body/div[5]/div[4]/div[1]/div[1]/a[1]/div[1]/div[2]/div[1]", "//div[contains(., '')]", "//DIV[@class='srp-1p__image']"], "exampleValues": [{"num": 0, "value": "https://i.ebayimg.com/00/z/3RcAAOSwJ~pkQaT7/$_58.jpg"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}