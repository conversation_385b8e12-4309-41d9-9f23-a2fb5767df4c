{"id": 136, "name": "Dynamic Iframe", "url": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://localhost:8074/taskGrid/test_pages/select.html", "links": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://localhost:8074/taskGrid/test_pages/select.html", "create_time": "7/5/2023, 7:39:11 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": true, "desc": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://localhost:8074/taskGrid/test_pages/select.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://localhost:8074/taskGrid/test_pages/select.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://localhost:8074/taskGrid/test_pages/select.html"}], "outputParameters": [{"id": 0, "name": "自定义参数_1", "desc": "", "type": "string", "exampleValue": "自定义字段"}, {"id": 1, "name": "自定义参数_0", "desc": "", "type": "string", "exampleValue": "自定义字段"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 3, 2, 8, 13, 23], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://localhost:8074/taskGrid/test_pages/select.html", "links": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://localhost:8074/taskGrid/test_pages/select.html", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 3, "index": 2, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"cars\"]", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": ["/html/body/form[1]/select[1]", "//select[contains(., '')]", "id(\"cars\")", "/html/body/form/select"], "optionMode": "1", "optionValue": "2"}}, {"id": 2, "index": 3, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "document.body.style.background = \"black\"", "waitTime": 0, "recordASField": 0}}, {"id": -1, "index": 4, "parentId": 0, "type": 2, "option": 9, "title": "判断条件", "sequence": [5, 6], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 3, "index": 5, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "5", "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 3, "index": 6, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 7, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 4, "index": 8, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "document.body.style.background = \"blue\"", "waitTime": 0, "recordASField": 0}}, {"id": -1, "index": 9, "parentId": 0, "type": 2, "option": 9, "title": "判断条件", "sequence": [10], "isInLoop": false, "position": 5, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 6, "index": 10, "type": 3, "option": 10, "title": "条件分支", "sequence": [12], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 3, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "5", "value": "", "code": "return document.body.style.background == \"blue\"", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 5, "index": 11, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 12, "parentId": 9, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "自定义参数_0", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "iframe": false}]}}, {"id": 5, "index": 13, "parentId": 0, "type": 2, "option": 9, "title": "判断条件", "sequence": [14], "isInLoop": false, "position": 4, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": 7, "parentId": 5, "index": 14, "type": 3, "option": 10, "title": "条件分支", "sequence": [22], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": true, "wait": 4, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "5", "value": "", "code": "return document.body.style.background == \"blue\"", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 6, "index": 15, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 16, "parentId": 7, "type": 2, "option": 9, "title": "提取数据", "sequence": [17, 18], "isInLoop": false, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 9, "index": 17, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 9, "index": 18, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 19, "parentId": 7, "type": 2, "option": 9, "title": "提取数据", "sequence": [20, 21], "isInLoop": false, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 9, "index": 20, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 9, "index": 21, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 8, "index": 22, "parentId": 7, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "自定义参数_1", "desc": "", "extractType": 0, "relativeXPath": "//body", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "iframe": true}]}}, {"id": 6, "index": 23, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 5, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 3, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "自定义参数_0", "desc": "", "extractType": 0, "relativeXPath": "//h1", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}