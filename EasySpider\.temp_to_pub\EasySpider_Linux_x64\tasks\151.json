{"id": 151, "name": "手机号码生成器：生产中国_香港_台湾_韩国_日本_美国电话号码-陈沩亮博客", "url": "https://www.chenweiliang.com/cwl-1992.html", "links": "https://www.chenweiliang.com/cwl-1992.html", "create_time": "7/7/2023, 7:58:46 PM", "version": "0.3.5", "saveThreshold": 1, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "mysql", "saveName": "手机号", "containJudge": true, "desc": "https://www.chenweiliang.com/cwl-1992.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.chenweiliang.com/cwl-1992.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.chenweiliang.com/cwl-1992.html"}, {"id": 1, "name": "loopTimes_循环_1", "nodeId": 2, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "手机号", "desc": "", "type": "text", "recordASField": "0", "exampleValue": "18851329001"}, {"id": 1, "name": "时间戳", "desc": "", "type": "text", "recordASField": "0", "exampleValue": "自定义字段"}, {"id": 2, "name": "自定义参数_2", "desc": "", "type": "text", "recordASField": "0", "exampleValue": "自定义字段"}, {"id": 3, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": "1", "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.chenweiliang.com/cwl-1992.html", "links": "https://www.chenweiliang.com/cwl-1992.html", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [9, 3, 4, 5], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"phone2\")]/fieldset[1]/div[2]/font[2]/font[1]/input[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[2]/div[1]/div[1]/main[1]/div[2]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/p[3]/div[1]/div[2]/fieldset[1]/div[2]/font[2]/font[1]/input[1]", "//input[contains(., '')]", "//INPUT[@class='button']", "/html/body/div[last()-4]/div[last()-1]/div/div/main/div/div/section[last()-1]/div[last()-1]/div/div[last()-1]/div/div/div/div/p[last()-11]/div/div[last()-5]/fieldset/div/font/font/input"]}}, {"id": 4, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "//*[contains(@class, \"phone2\")]/fieldset[1]/div[2]/font[2]/font[1]/input[1]", "iframe": false, "wait": 0.2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[2]/div[1]/div[1]/main[1]/div[2]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/p[3]/div[1]/div[2]/fieldset[1]/div[2]/font[2]/font[1]/input[1]", "//input[contains(., '')]", "//INPUT[@class='button']", "/html/body/div[last()-4]/div[last()-1]/div/div/main/div/div/section[last()-1]/div[last()-1]/div/div[last()-1]/div/div/div/div/p[last()-11]/div/div[last()-5]/fieldset/div/font/font/input"], "loopType": 0}}, {"id": 5, "index": 4, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 3, "contentType": 0, "relative": false, "name": "手机号", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[1]/div[2]/div[1]/div[1]/main[1]/div[2]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/p[3]/div[1]/div[2]/fieldset[1]/div[2]/input[1]", "allXPaths": ["/html/body/div[1]/div[2]/div[1]/div[1]/main[1]/div[2]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/p[3]/div[1]/div[2]/fieldset[1]/div[2]/input[1]", "//input[contains(., '')]", "id(\"chinamobiles\")", "//INPUT[@class='button']", "/html/body/div[last()-4]/div[last()-1]/div/div/main/div/div/section[last()-1]/div[last()-1]/div/div[last()-1]/div/div/div/div/p[last()-11]/div/div[last()-5]/fieldset/div/input"], "exampleValues": [{"num": 0, "value": "18851329001"}], "unique_index": "4clp5hiyw9iljsdpsql", "iframe": false, "default": "", "paraType": "text", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "recordASField": "1"}, {"nodeType": 0, "contentType": 9, "relative": false, "name": "时间戳", "desc": "", "extractType": 0, "relativeXPath": "//body", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "return new Date().toString()", "paraType": "text", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "recordASField": "0"}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "自定义参数_2", "desc": "", "extractType": 0, "relativeXPath": "//body", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "paraType": "text", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "recordASField": "0"}}, {"id": 6, "index": 5, "parentId": 2, "type": 2, "option": 9, "title": "判断条件", "sequence": [6], "isInLoop": true, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": 7, "parentId": 6, "index": 6, "type": 3, "option": 10, "title": "条件分支", "sequence": [8], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "5", "value": "", "code": "return document.evaluate(\"/html/body/div[1]/div[2]/div[1]/div[1]/main[1]/div[2]/div[1]/section[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/p[3]/div[1]/div[2]/fieldset[1]/div[2]/input[1]\", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.value.includes(\"73\")\n", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 5, "index": 7, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 8, "index": 8, "parentId": 7, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": "3", "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 3, "index": 9, "parentId": 2, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "", "waitTime": 0, "recordASField": "1", "paraType": "text"}}]}