{"id": 127, "name": "百度地图", "url": "https://map.baidu.com", "links": "https://map.baidu.com", "create_time": "2023/7/12 20:59:16", "update_time": "7/14/2023, 8:13:34 AM", "version": "0.3.6", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "test.xlsx", "startFromExit": 0, "containJudge": false, "desc": "https://map.baidu.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://map.baidu.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://map.baidu.com"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "北京市", "value": "北京市"}, {"id": 2, "name": "inputText_2", "nodeName": "输入文字", "nodeId": 4, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "门窗", "value": "门窗"}, {"id": 3, "name": "loopTimes_循环点击下一页_3", "nodeId": 7, "nodeName": "循环点击下一页", "desc": "循环循环点击下一页执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数8_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "鑫隆门窗"}, {"id": 1, "name": "参数11_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "电话:13717842988"}, {"id": 2, "name": "参数10_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "北京市朝阳区红霞路13号"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 4, 5, 6, 7], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://map.baidu.com", "links": "https://map.baidu.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"sole-input\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "北京市", "allXPaths": ["/html/body/div[1]/div[2]/div[1]/div[1]/div[1]/input[1]", "//input[contains(., '')]", "id(\"sole-input\")", "//INPUT[@class='searchbox-content-common']", "//INPUT[@name='word']", "/html/body/div[last()-5]/div[last()-5]/div/div[last()-1]/div/input"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-button\"]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[2]/div[1]/button[1]", "//button[contains(., '')]", "id(\"search-button\")", "/html/body/div[last()-7]/div[last()-5]/div/button"]}}, {"id": 4, "index": 4, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"sole-input\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "门窗", "allXPaths": ["/html/body/div[1]/div[2]/div[1]/div[1]/div[1]/input[1]", "//input[contains(., '')]", "id(\"sole-input\")", "//INPUT[@class='searchbox-content-common']", "//INPUT[@name='word']", "/html/body/div[last()-7]/div[last()-5]/div/div[last()-1]/div/input"]}}, {"id": 5, "index": 5, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-button\"]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[2]/div[1]/button[1]", "//button[contains(., '')]", "id(\"search-button\")", "/html/body/div[last()-8]/div[last()-5]/div/button"]}}, {"id": 6, "index": 6, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 5, "parameters": {"history": 6, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"leadDownloadCard\"]/div[1]/div[1]/div[3]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[2]/ul[2]/li[1]/div[1]/div[2]/div[1]/div[1]/div[3]", "//div[contains(., '×')]", "//DIV[@class='close-btn-download-banner']", "/html/body/div[last()-8]/div[last()-5]/ul[last()-1]/li/div/div/div/div/div"]}}, {"id": 7, "index": 7, "parentId": 0, "type": 1, "option": 8, "title": "循环点击下一页", "sequence": [9, 8], "isInLoop": false, "position": 6, "parameters": {"history": 6, "tabIndex": -1, "useLoop": false, "xpath": "//a[contains(., '下一页')]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[2]/ul[2]/li[1]/div[1]/div[1]/div[6]/p[1]/span[5]/a[1]", "//a[contains(., '下一页>')]", "/html/body/div[last()-8]/div[last()-5]/ul[last()-1]/li/div/div[last()-1]/div[last()-2]/p/span/a"]}}, {"id": 9, "index": 8, "parentId": 7, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 6, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[2]/ul[2]/li[1]/div[1]/div[1]/div[6]/p[1]/span[5]/a[1]", "//a[contains(., '下一页>')]", "/html/body/div[last()-8]/div[last()-5]/ul[last()-1]/li/div/div[last()-1]/div[last()-2]/p/span/a"], "loopType": 0}}, {"id": 8, "index": 9, "parentId": 7, "type": 1, "option": 8, "title": "循环", "sequence": [10], "isInLoop": true, "position": 0, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div/div[2]/ul[2]/li[1]/div[1]/div[1]/ul/li", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[2]/ul[2]/li[1]/div[1]/div[1]/ul[1]/li[1]", "//li[contains(., '')]", "//LI[@class='search-item base-item']", "/html/body/div[last()-8]/div[last()-5]/ul[last()-1]/li/div/div[last()-1]/ul/li[last()-9]"]}}, {"id": 10, "index": 10, "parentId": 8, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数8_链接文本", "desc": "", "relativeXPath": "/div[1]/div[3]/div[1]/span[1]/a[1]", "allXPaths": ["/div[1]/div[3]/div[1]/span[1]/a[1]", "//a[contains(., '鑫隆门窗')]", "//A[@class='n-blue']", "/html/body/div[last()-8]/div[last()-5]/ul[last()-1]/li/div/div[last()-1]/ul/li[last()-9]/div[last()-1]/div/div[last()-3]/span[last()-1]/a"], "exampleValues": [{"num": 0, "value": "鑫隆门窗"}], "unique_index": "/div[1]/div[3]/div[1]/span[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数11_文本", "desc": "", "relativeXPath": "/div[1]/div[3]/div[3]", "allXPaths": ["/div[1]/div[3]/div[3]", "//div[contains(., '')]", "//DIV[@class='row tel']", "/html/body/div[last()-8]/div[last()-5]/ul[last()-1]/li/div/div[last()-1]/ul/li[last()-9]/div[last()-1]/div/div[last()-1]"], "exampleValues": [{"num": 0, "value": "电话:13717842988"}], "unique_index": "/div[1]/div[3]/div[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/div[1]/div[3]/div[2]/span[1]", "allXPaths": ["/div[1]/div[3]/div[2]/span[1]", "//span[contains(., '北京市朝阳区红霞路1')]", "//SPAN[@class='n-grey']", "/html/body/div[last()-8]/div[last()-5]/ul[last()-1]/li/div/div[last()-1]/ul/li[last()-9]/div[last()-1]/div/div[last()-2]/span"], "exampleValues": [{"num": 0, "value": "北京市朝阳区红霞路13号"}], "unique_index": "/div[1]/div[3]/div[2]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}