{"id": 86, "name": "选中子元素（测试A）", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "5/31/2023, 9:05:12 PM", "version": "0.3.2", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "参数5_链接文本", "desc": "", "type": "string", "exampleValue": "数码"}, {"id": 1, "name": "参数6_链接地址", "desc": "", "type": "string", "exampleValue": "https://shuma.jd.com/"}, {"id": 2, "name": "参数7_文本", "desc": "", "type": "string", "exampleValue": "/"}, {"id": 3, "name": "参数8_链接文本", "desc": "", "type": "string", "exampleValue": "厨具"}, {"id": 4, "name": "参数9_链接地址", "desc": "", "type": "string", "exampleValue": "https://channel.jd.com/kitchenware.html"}, {"id": 5, "name": "参数10_文本", "desc": "", "type": "string", "exampleValue": "/"}, {"id": 6, "name": "参数11_链接文本", "desc": "", "type": "string", "exampleValue": "工业品"}, {"id": 7, "name": "参数12_链接地址", "desc": "", "type": "string", "exampleValue": "https://pro.jd.com/mall/active/2u2DR1dUiK34csAE3DqmcG8aXvUK/index.html"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '/手机/数码')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG ']"]}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数5_链接文本", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '数码')]"], "exampleValues": [{"num": 0, "value": "数码"}, {"num": 2, "value": "办公"}, {"num": 3, "value": "家居"}, {"num": 4, "value": "家装"}, {"num": 5, "value": "男装"}, {"num": 6, "value": "钟表"}, {"num": 7, "value": "户外"}, {"num": 8, "value": "车载电器"}, {"num": 9, "value": "洗护喂养"}, {"num": 10, "value": "宠物生活"}, {"num": 11, "value": "个人护理"}, {"num": 12, "value": "童书"}], "unique_index": "/a[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数6_链接地址", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '数码')]"], "exampleValues": [{"num": 0, "value": "https://shuma.jd.com/"}, {"num": 2, "value": "https://bg.jd.com/"}, {"num": 3, "value": "https://channel.jd.com/home.html"}, {"num": 4, "value": "https://jzjc.jd.com/"}, {"num": 5, "value": "https://channel.jd.com/1315-1342.html"}, {"num": 6, "value": "https://channel.jd.com/watch.html"}, {"num": 7, "value": "https://channel.jd.com/outdoor.html"}, {"num": 8, "value": "https://list.jd.com/list.html?cat=6728,6740&page=1&delivery_glb=1&stock=1&sort=sort_totalsales15_desc&trans=1&JL=4_7_0#J_main"}, {"num": 9, "value": "https://channel.jd.com/feed.html"}, {"num": 10, "value": "https://channel.jd.com/pet.html"}, {"num": 11, "value": "https://lady.jd.com/"}, {"num": 12, "value": "https://book.jd.com/children.html"}], "unique_index": "/a[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数7_文本", "desc": "", "relativeXPath": "/span[3]", "allXPaths": ["/span[3]", "//span[contains(., '/')]", "//SPAN[@class='LeftSide_cate_menu_line__vzQu9 undefined']"], "exampleValues": [{"num": 3, "value": "/"}, {"num": 4, "value": "/"}, {"num": 5, "value": "/"}, {"num": 6, "value": "/"}, {"num": 7, "value": "/"}, {"num": 11, "value": "/"}, {"num": 12, "value": "/"}], "unique_index": "/span[3]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数8_链接文本", "desc": "", "relativeXPath": "/a[3]", "allXPaths": ["/a[3]", "//a[contains(., '厨具')]"], "exampleValues": [{"num": 3, "value": "厨具"}, {"num": 4, "value": "灯具"}, {"num": 5, "value": "女装"}, {"num": 6, "value": "珠宝"}, {"num": 7, "value": "男鞋"}, {"num": 11, "value": "计生情趣"}, {"num": 12, "value": "文学"}], "unique_index": "/a[3]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数9_链接地址", "desc": "", "relativeXPath": "/a[3]", "allXPaths": ["/a[3]", "//a[contains(., '厨具')]"], "exampleValues": [{"num": 3, "value": "https://channel.jd.com/kitchenware.html"}, {"num": 4, "value": "https://channel.jd.com/9855-9856.html"}, {"num": 5, "value": "https://channel.jd.com/women.html"}, {"num": 6, "value": "https://channel.jd.com/jewellery.html"}, {"num": 7, "value": "https://channel.jd.com/mensshoes.html"}, {"num": 11, "value": "https://channel.jd.com/9192-9196.html"}, {"num": 12, "value": "https://channel.jd.com/p_wenxuezongheguan.html"}], "unique_index": "/a[3]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/span[4]", "allXPaths": ["/span[4]", "//span[contains(., '/')]", "//SPAN[@class='LeftSide_cate_menu_line__vzQu9 undefined']"], "exampleValues": [{"num": 4, "value": "/"}, {"num": 5, "value": "/"}, {"num": 6, "value": "/"}], "unique_index": "/span[4]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数11_链接文本", "desc": "", "relativeXPath": "/a[4]", "allXPaths": ["/a[4]", "//a[contains(., '工业品')]"], "exampleValues": [{"num": 4, "value": "工业品"}, {"num": 5, "value": "童装"}, {"num": 6, "value": "女鞋"}], "unique_index": "/a[4]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数12_链接地址", "desc": "", "relativeXPath": "/a[4]", "allXPaths": ["/a[4]", "//a[contains(., '工业品')]"], "exampleValues": [{"num": 4, "value": "https://pro.jd.com/mall/active/2u2DR1dUiK34csAE3DqmcG8aXvUK/index.html"}, {"num": 5, "value": "https://list.jd.com/list.html?cat=1319,11842"}, {"num": 6, "value": "https://channel.jd.com/womensshoes.html"}], "unique_index": "/a[4]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}