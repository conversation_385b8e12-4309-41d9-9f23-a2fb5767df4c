{"id": 241, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "", "update_time": "12/9/2023, 3:58:29 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": true, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "iPhone", "value": "iPhone"}, {"id": 2, "name": "loopTimes_循环点击单个元素_2", "nodeId": 8, "nodeName": "循环点击单个元素", "desc": "循环循环点击单个元素执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 1, "name": "参数1_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\n\t\t\t\t        \t"}, {"id": 2, "name": "参数2_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//item.jd.com/10085330892348.html"}, {"id": 3, "name": "参数3_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//img13.360buyimg.com/n7/jfs/t1/97175/23/37553/79167/65041ffcF98d45532/44eae3ee6717d3f8.jpg"}, {"id": 4, "name": "参数4_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "<"}, {"id": 5, "name": "参数5_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ">"}, {"id": 6, "name": "参数6_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 7, "name": "参数7_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 8, "name": "参数8_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//img13.360buyimg.com/n7/jfs/t1/97175/23/37553/79167/65041ffcF98d45532/44eae3ee6717d3f8.jpg"}, {"id": 9, "name": "参数9_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "￥"}, {"id": 10, "name": "参数10_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1459.00"}, {"id": 11, "name": "参数11_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t苹果苹果（Apple）airpods3三代苹果无线蓝牙耳机3代 抗汗抗水适用iPhone/iPad/AppleWatch AirPods 3 （闪电充电盒） 官方标配\n\t\t\t\t\t\t\t\t抗汗抗水\n\t\t\t\t\t\t\t"}, {"id": 12, "name": "参数12_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//item.jd.com/10085330892348.html"}, {"id": 13, "name": "参数13_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "苹果苹果（Apple）airpods3三代苹果无线蓝牙耳机3代抗汗抗水适用/iPad/AppleWatchAirPods3（闪电充电盒）官方标配"}, {"id": 14, "name": "参数14_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "iPhone"}, {"id": 15, "name": "参数15_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "抗汗抗水"}, {"id": 16, "name": "参数16_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "条评价"}, {"id": 17, "name": "参数17_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1"}, {"id": 18, "name": "参数18_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//item.jd.com/10085330892348.html#comment"}, {"id": 19, "name": "参数19_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "关注"}, {"id": 20, "name": "参数20_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 21, "name": "参数21_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "链族旗舰店"}, {"id": 22, "name": "参数22_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//mall.jd.com/index-13031422.html?from=pc"}, {"id": 23, "name": "参数23_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "免邮"}, {"id": 24, "name": "参数24_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "满1000-300"}, {"id": 25, "name": "参数25_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "广告"}, {"id": 26, "name": "参数26_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//misc.360buyimg.com/lib/img/e/blank.gif"}, {"id": 27, "name": "参数27_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 28, "name": "参数28_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 29, "name": "参数29_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//img12.360buyimg.com/n7/jfs/t1/230258/20/6646/37916/6571768dFeae15df0/c87121a1ecece93e.jpg"}, {"id": 30, "name": "参数30_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//m.360buyimg.com/cc/jfs/t1/113659/27/28361/2962/62ecb1f0E6c5fc50c/b914680e87a2c8e9.png"}, {"id": 31, "name": "参数31_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 32, "name": "参数32_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 33, "name": "参数33_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//img12.360buyimg.com/n7/jfs/t1/127754/34/40128/10766/6502a43dF456f293b/7bd7eaa173baf036.jpg"}, {"id": 34, "name": "参数34_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 35, "name": "参数35_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 36, "name": "参数36_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//img13.360buyimg.com/n7/jfs/t1/136795/21/36503/11651/6502a504Fa0ecacb3/7b0d6a723504e6ec.jpg"}, {"id": 37, "name": "参数37_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 38, "name": "参数38_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 39, "name": "参数39_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//img14.360buyimg.com/n7/jfs/t1/101872/39/43691/49939/65029815Fc5088551/e9d060aad302fdc5.jpg"}, {"id": 40, "name": "参数40_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 41, "name": "参数41_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 42, "name": "参数42_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//img11.360buyimg.com/n7/jfs/t1/124606/38/37429/11910/6502a5ddF3edcbe59/9128859296c1fed7.jpg"}, {"id": 43, "name": "参数43_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 44, "name": "参数44_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 45, "name": "参数45_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//img14.360buyimg.com/n7/jfs/t1/166659/11/41600/12197/650281c0F5e02cc5b/f34896441a1b59cb.jpg"}, {"id": 46, "name": "参数46_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 47, "name": "参数47_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 48, "name": "参数48_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 49, "name": "参数49_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 50, "name": "参数50_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 51, "name": "参数51_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 52, "name": "参数52_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 53, "name": "参数53_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 54, "name": "参数54_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 55, "name": "参数55_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1件9折"}, {"id": 56, "name": "参数56_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "海外预定"}, {"id": 57, "name": "参数57_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "iPhone"}, {"id": 58, "name": "参数58_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//m.360buyimg.com/cc/jfs/t1/197706/29/27826/4384/63314bc6E21b4c3e9/8b4d5fc44efc5b3f.png"}, {"id": 59, "name": "参数59_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//m.360buyimg.com/cc/jfs/t1/208534/35/31238/1841/64d370c9F1c347119/9a4c369c569b8866.png"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 4, 8], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "value": "iPhone", "index": 0, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-btn\"]/i[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/button[1]/i[1]", "//i[contains(., '')]", "/html/body/div[last()-6]/div/div[last()-2]/div/button/i"]}}, {"id": 4, "index": 4, "parentId": 0, "type": 2, "option": 9, "title": "判断条件", "sequence": [5], "isInLoop": false, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": 6, "parentId": 4, "index": 5, "type": 3, "option": 10, "title": "条件分支1", "sequence": [7], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": "1", "value": "密码登录", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 4, "index": 6, "type": 3, "option": 10, "title": "条件分支2", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 9, "index": 7, "parentId": 6, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": "7", "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 5, "index": 8, "parentId": 0, "type": 1, "option": 8, "title": "循环点击单个元素", "sequence": [10, 9], "isInLoop": false, "position": 4, "parameters": {"history": 6, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"pn-next\")]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[3]/div[1]/div[1]/div[1]/div[5]/div[1]/span[1]/a[9]", "//a[contains(., '下一页>')]", "//A[@class='pn-next']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div/div/span[last()-1]/a"]}}, {"id": 8, "index": 9, "parentId": 5, "type": 0, "option": 2, "title": "点击下一页>", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 6, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[5]/div[3]/div[1]/div[1]/div[1]/div[5]/div[1]/span[1]/a[9]", "//a[contains(., '下一页>')]", "//A[@class='pn-next']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div/div/span[last()-1]/a"], "loopType": 0}}, {"id": 7, "index": 10, "parentId": 5, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [11], "isInLoop": true, "position": 0, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div/div/div/div/div/div/ul[1]/li/div[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[3]/div[1]/div[1]/div[1]/div[4]/ul[1]/li[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='gl-i-wrap']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div"]}}, {"id": 10, "index": 11, "parentId": 7, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/div[1]/a[1]", "allXPaths": ["/div[1]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-8]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t\n\t\t\t\t        \t"}], "unique_index": "/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/div[1]/a[1]", "allXPaths": ["/div[1]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-8]/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10085330892348.html"}], "unique_index": "/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数3_图片地址", "desc": "", "relativeXPath": "/div[1]/a[1]/img[1]", "allXPaths": ["/div[1]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-8]/a/img"], "exampleValues": [{"num": 0, "value": "//img13.360buyimg.com/n7/jfs/t1/97175/23/37553/79167/65041ffcF98d45532/44eae3ee6717d3f8.jpg"}], "unique_index": "/div[1]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/div[2]/span[1]", "allXPaths": ["/div[2]/span[1]", "//span[contains(., '<')]", "//SPAN[@class='ps-prev']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-7]/span[last()-1]"], "exampleValues": [{"num": 0, "value": "<"}], "unique_index": "/div[2]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/div[2]/span[2]", "allXPaths": ["/div[2]/span[2]", "//span[contains(., '>')]", "//SPAN[@class='ps-next']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-7]/span"], "exampleValues": [{"num": 0, "value": ">"}], "unique_index": "/div[2]/span[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数6_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[1]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[1]/a[1]", "//a[contains(., '')]", "//A[@class='curr']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数7_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[1]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[1]/a[1]", "//a[contains(., '')]", "//A[@class='curr']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数8_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[1]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[1]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-7]/div/ul/li/a/img"], "exampleValues": [{"num": 0, "value": "//img13.360buyimg.com/n7/jfs/t1/97175/23/37553/79167/65041ffcF98d45532/44eae3ee6717d3f8.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[1]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/div[3]/strong[1]/em[1]", "allXPaths": ["/div[3]/strong[1]/em[1]", "//em[contains(., '￥')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-6]/strong/em"], "exampleValues": [{"num": 0, "value": "￥"}], "unique_index": "/div[3]/strong[1]/em[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/div[3]/strong[1]/i[1]", "allXPaths": ["/div[3]/strong[1]/i[1]", "//i[contains(., '1459.00')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-6]/strong/i"], "exampleValues": [{"num": 0, "value": "1459.00"}], "unique_index": "/div[3]/strong[1]/i[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数11_链接文本", "desc": "", "relativeXPath": "/div[4]/a[1]", "allXPaths": ["/div[4]/a[1]", "//a[contains(., '苹')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-5]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t苹果苹果（Apple）airpods3三代苹果无线蓝牙耳机3代 抗汗抗水适用iPhone/iPad/AppleWatch AirPods 3 （闪电充电盒） 官方标配\n\t\t\t\t\t\t\t\t抗汗抗水\n\t\t\t\t\t\t\t"}], "unique_index": "/div[4]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数12_链接地址", "desc": "", "relativeXPath": "/div[4]/a[1]", "allXPaths": ["/div[4]/a[1]", "//a[contains(., '苹')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-5]/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10085330892348.html"}], "unique_index": "/div[4]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数13_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/em[1]", "allXPaths": ["/div[4]/a[1]/em[1]", "//em[contains(., '苹果苹果（Apple')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-5]/a/em"], "exampleValues": [{"num": 0, "value": "苹果苹果（Apple）airpods3三代苹果无线蓝牙耳机3代抗汗抗水适用/iPad/AppleWatchAirPods3（闪电充电盒）官方标配"}], "unique_index": "/div[4]/a[1]/em[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数14_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/em[1]/font[1]", "allXPaths": ["/div[4]/a[1]/em[1]/font[1]", "//font[contains(., 'iPhone')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-5]/a/em/font"], "exampleValues": [{"num": 0, "value": "iPhone"}], "unique_index": "/div[4]/a[1]/em[1]/font[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数15_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/i[1]", "allXPaths": ["/div[4]/a[1]/i[1]", "//i[contains(., '抗汗抗水')]", "id(\"J_AD_10085330892348\")", "//I[@class='promo-words']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-5]/a/i"], "exampleValues": [{"num": 0, "value": "抗汗抗水"}], "unique_index": "/div[4]/a[1]/i[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数16_文本", "desc": "", "relativeXPath": "/div[5]/strong[1]", "allXPaths": ["/div[5]/strong[1]", "//strong[contains(., '1条评价')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-4]/strong"], "exampleValues": [{"num": 0, "value": "条评价"}], "unique_index": "/div[5]/strong[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数17_链接文本", "desc": "", "relativeXPath": "/div[5]/strong[1]/a[1]", "allXPaths": ["/div[5]/strong[1]/a[1]", "//a[contains(., '1')]", "id(\"J_comment_10085330892348\")", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-4]/strong/a"], "exampleValues": [{"num": 0, "value": "1"}], "unique_index": "/div[5]/strong[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数18_链接地址", "desc": "", "relativeXPath": "/div[5]/strong[1]/a[1]", "allXPaths": ["/div[5]/strong[1]/a[1]", "//a[contains(., '1')]", "id(\"J_comment_10085330892348\")", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-4]/strong/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10085330892348.html#comment"}], "unique_index": "/div[5]/strong[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数19_链接文本", "desc": "", "relativeXPath": "/div[6]/a[1]", "allXPaths": ["/div[6]/a[1]", "//a[contains(., '关注')]", "//A[@class='J_focus']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-3]/a"], "exampleValues": [{"num": 0, "value": "关注"}], "unique_index": "/div[6]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数20_链接地址", "desc": "", "relativeXPath": "/div[6]/a[1]", "allXPaths": ["/div[6]/a[1]", "//a[contains(., '关注')]", "//A[@class='J_focus']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-3]/a"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[6]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数21_链接文本", "desc": "", "relativeXPath": "/div[7]/span[1]/a[1]", "allXPaths": ["/div[7]/span[1]/a[1]", "//a[contains(., '链族旗舰店')]", "//A[@class='curr-shop hd-shopname']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-2]/span/a"], "exampleValues": [{"num": 0, "value": "链族旗舰店"}], "unique_index": "/div[7]/span[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数22_链接地址", "desc": "", "relativeXPath": "/div[7]/span[1]/a[1]", "allXPaths": ["/div[7]/span[1]/a[1]", "//a[contains(., '链族旗舰店')]", "//A[@class='curr-shop hd-shopname']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-2]/span/a"], "exampleValues": [{"num": 0, "value": "//mall.jd.com/index-13031422.html?from=pc"}], "unique_index": "/div[7]/span[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数23_文本", "desc": "", "relativeXPath": "/div[8]/i[1]", "allXPaths": ["/div[8]/i[1]", "//i[contains(., '免邮')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-1]/i[last()-1]"], "exampleValues": [{"num": 0, "value": "免邮"}], "unique_index": "/div[8]/i[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数24_文本", "desc": "", "relativeXPath": "/div[8]/i[2]", "allXPaths": ["/div[8]/i[2]", "//i[contains(., '满1000-300')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-4]/div/div[last()-1]/i"], "exampleValues": [{"num": 0, "value": "满1000-300"}], "unique_index": "/div[8]/i[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数25_文本", "desc": "", "relativeXPath": "/span[1]", "allXPaths": ["/span[1]", "//span[contains(., '广告')]", "//SPAN[@class='p-promo-flag']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/span"], "exampleValues": [{"num": 1, "value": "广告"}], "unique_index": "/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数26_图片地址", "desc": "", "relativeXPath": "/img[1]", "allXPaths": ["/img[1]", "//img[contains(., '')]", "//IMG[@class='err-poster']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/img"], "exampleValues": [{"num": 1, "value": "//misc.360buyimg.com/lib/img/e/blank.gif"}], "unique_index": "/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数27_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[2]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[2]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 2, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数28_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[2]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[2]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 2, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数29_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[2]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[2]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div[last()-7]/div/ul/li/a/img"], "exampleValues": [{"num": 2, "value": "//img12.360buyimg.com/n7/jfs/t1/230258/20/6646/37916/6571768dFeae15df0/c87121a1ecece93e.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[2]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数30_图片地址", "desc": "", "relativeXPath": "/div[4]/a[1]/em[1]/img[1]", "allXPaths": ["/div[4]/a[1]/em[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='p-tag3']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-27]/div/div[last()-5]/a/em/img"], "exampleValues": [{"num": 3, "value": "//m.360buyimg.com/cc/jfs/t1/113659/27/28361/2962/62ecb1f0E6c5fc50c/b914680e87a2c8e9.png"}], "unique_index": "/div[4]/a[1]/em[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数31_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[3]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[3]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-7]/a"], "exampleValues": [{"num": 4, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数32_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[3]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[3]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-7]/a"], "exampleValues": [{"num": 4, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数33_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[3]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[3]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-7]/a/img"], "exampleValues": [{"num": 4, "value": "//img12.360buyimg.com/n7/jfs/t1/127754/34/40128/10766/6502a43dF456f293b/7bd7eaa173baf036.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[3]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数34_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[4]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[4]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-6]/a"], "exampleValues": [{"num": 4, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[4]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数35_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[4]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[4]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-6]/a"], "exampleValues": [{"num": 4, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[4]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数36_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[4]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[4]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-6]/a/img"], "exampleValues": [{"num": 4, "value": "//img13.360buyimg.com/n7/jfs/t1/136795/21/36503/11651/6502a504Fa0ecacb3/7b0d6a723504e6ec.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[4]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数37_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[5]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[5]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-5]/a"], "exampleValues": [{"num": 4, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[5]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数38_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[5]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[5]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-5]/a"], "exampleValues": [{"num": 4, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[5]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数39_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[5]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[5]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-5]/a/img"], "exampleValues": [{"num": 4, "value": "//img14.360buyimg.com/n7/jfs/t1/101872/39/43691/49939/65029815Fc5088551/e9d060aad302fdc5.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[5]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数40_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[6]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[6]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-4]/a"], "exampleValues": [{"num": 4, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[6]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数41_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[6]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[6]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-4]/a"], "exampleValues": [{"num": 4, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[6]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数42_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[6]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[6]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-4]/a/img"], "exampleValues": [{"num": 4, "value": "//img11.360buyimg.com/n7/jfs/t1/124606/38/37429/11910/6502a5ddF3edcbe59/9128859296c1fed7.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[6]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数43_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[7]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[7]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-3]/a"], "exampleValues": [{"num": 4, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[7]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数44_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[7]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[7]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-3]/a"], "exampleValues": [{"num": 4, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[7]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数45_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[7]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[7]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-3]/a/img"], "exampleValues": [{"num": 4, "value": "//img14.360buyimg.com/n7/jfs/t1/166659/11/41600/12197/650281c0F5e02cc5b/f34896441a1b59cb.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[7]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数46_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[8]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[8]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-2]/a"], "exampleValues": [{"num": 4, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[8]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数47_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[8]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[8]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-2]/a"], "exampleValues": [{"num": 4, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[8]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数48_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[8]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[8]/a[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='err-product']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-2]/a/img"], "exampleValues": [{"num": 4, "value": ""}], "unique_index": "/div[2]/div[1]/ul[1]/li[8]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数49_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[9]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[9]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-1]/a"], "exampleValues": [{"num": 4, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[9]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数50_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[9]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[9]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-1]/a"], "exampleValues": [{"num": 4, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[9]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数51_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[9]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[9]/a[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='err-product']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li[last()-1]/a/img"], "exampleValues": [{"num": 4, "value": ""}], "unique_index": "/div[2]/div[1]/ul[1]/li[9]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数52_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[10]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 4, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[10]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数53_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[10]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 4, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[10]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数54_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[10]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[10]/a[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='err-product']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li/a/img"], "exampleValues": [{"num": 4, "value": ""}], "unique_index": "/div[2]/div[1]/ul[1]/li[10]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数55_文本", "desc": "", "relativeXPath": "/div[8]/i[3]", "allXPaths": ["/div[8]/i[3]", "//i[contains(., '1件9折')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-1]/i"], "exampleValues": [{"num": 4, "value": "1件9折"}], "unique_index": "/div[8]/i[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数56_文本", "desc": "", "relativeXPath": "/div[9]", "allXPaths": ["/div[9]", "//div[contains(., '海外预定')]", "//DIV[@class='p-stock']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-24]/div/div"], "exampleValues": [{"num": 6, "value": "海外预定"}], "unique_index": "/div[9]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数57_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/em[1]/font[2]", "allXPaths": ["/div[4]/a[1]/em[1]/font[2]", "//font[contains(., 'iPhone')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-23]/div/div[last()-5]/a/em/font"], "exampleValues": [{"num": 7, "value": "iPhone"}], "unique_index": "/div[4]/a[1]/em[1]/font[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数58_图片地址", "desc": "", "relativeXPath": "/div[7]/img[1]", "allXPaths": ["/div[7]/img[1]", "//img[contains(., '')]", "//IMG[@class='shop-tag fl']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-21]/div/div[last()-2]/img"], "exampleValues": [{"num": 9, "value": "//m.360buyimg.com/cc/jfs/t1/197706/29/27826/4384/63314bc6E21b4c3e9/8b4d5fc44efc5b3f.png"}], "unique_index": "/div[7]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数59_图片地址", "desc": "", "relativeXPath": "/div[8]/img[1]", "allXPaths": ["/div[8]/img[1]", "//img[contains(., '')]", "//IMG[@class='goods-icons-img J-picon-tips']", "/html/body/div[last()-4]/div[last()-7]/div/div[last()-1]/div/div[last()-2]/ul/li[last()-10]/div/div[last()-1]/img"], "exampleValues": [{"num": 20, "value": "//m.360buyimg.com/cc/jfs/t1/208534/35/31238/1841/64d370c9F1c347119/9a4c369c569b8866.png"}], "unique_index": "/div[8]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}