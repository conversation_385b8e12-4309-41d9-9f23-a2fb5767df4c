{"id": 213, "name": "<PERSON><PERSON>", "url": "http://localhost:63342/EasySpider_portal/test_pages/alert_test.html?_ijt=316rkefpc1dbqphp9lcl1ik7hb&_ij_reload=RELOAD_ON_SAVE", "links": "http://localhost:63342/EasySpider_portal/test_pages/alert_test.html?_ijt=316rkefpc1dbqphp9lcl1ik7hb&_ij_reload=RELOAD_ON_SAVE", "create_time": "12/5/2023, 12:56:22 AM", "update_time": "12/5/2023, 12:56:22 AM", "version": "0.6.0", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "http://localhost:63342/EasySpider_portal/test_pages/alert_test.html?_ijt=316rkefpc1dbqphp9lcl1ik7hb&_ij_reload=RELOAD_ON_SAVE", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://localhost:63342/EasySpider_portal/test_pages/alert_test.html?_ijt=316rkefpc1dbqphp9lcl1ik7hb&_ij_reload=RELOAD_ON_SAVE", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "http://localhost:63342/EasySpider_portal/test_pages/alert_test.html?_ijt=316rkefpc1dbqphp9lcl1ik7hb&_ij_reload=RELOAD_ON_SAVE"}], "outputParameters": [{"id": 0, "name": "参数2_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\nTEST\n\n\n\n    alert(\"test\");\n\n\n(function() {\n  var ws = new WebSocket('ws://' + window.location.host + \n             '/jb-server-page?reloadMode=RELOAD_ON_SAVE&'+\n             'referrer=' + encodeURIComponent(window.location.pathname));\n  ws.onmessage = function (msg) {\n      if (msg.data === 'reload') {\n          window.location.reload();\n      }\n      if (msg.data.startsWith('update-css ')) {\n          var messageId = msg.data.substring(11);\n          var links = document.getElementsByTagName('link');\n          for (var i = 0; i < links.length; i++) {\n              var link = links[i];\n              if (link.rel !== 'stylesheet') continue;\n              var clonedLink = link.cloneNode(true);\n              var newHref = link.href.replace(/(&|\\?)jbUpdateLinksId=\\d+/, \"$1jbUpdateLinksId=\" + messageId);\n              if (newHref !== link.href) {\n                clonedLink.href = newHref;\n              }\n              else {\n                var indexOfQuest = newHref.indexOf('?');\n                if (indexOfQuest >= 0) {\n                  // to support ?foo#hash \n                  clonedLink.href = newHref.substring(0, indexOfQuest + 1) + 'jbUpdateLinksId=' + messageId + '&' + \n                                    newHref.substring(indexOfQuest + 1);\n                }\n                else {\n                  clonedLink.href += '?' + 'jbUpdateLinksId=' + messageId;\n                }\n              }\n              link.replaceWith(clonedLink);\n          }\n      }\n  };\n})();\n\n     ✖ ✍操作台（点此拖动，左上角调整大小）  \n              ● 已选中1个元素，您可以:\n                  确认采集    取消选择  Path: /html/body  \n"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 3], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "http://localhost:63342/EasySpider_portal/test_pages/alert_test.html?_ijt=316rkefpc1dbqphp9lcl1ik7hb&_ij_reload=RELOAD_ON_SAVE", "links": "http://localhost:63342/EasySpider_portal/test_pages/alert_test.html?_ijt=316rkefpc1dbqphp9lcl1ik7hb&_ij_reload=RELOAD_ON_SAVE", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": []}}, {"id": 2, "index": 3, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数2_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": ["/html/body", "//body[contains(., 'TEST')]", "/html/body"], "exampleValues": [{"num": 0, "value": "\n\nTEST\n\n\n\n    alert(\"test\");\n\n\n(function() {\n  var ws = new WebSocket('ws://' + window.location.host + \n             '/jb-server-page?reloadMode=RELOAD_ON_SAVE&'+\n             'referrer=' + encodeURIComponent(window.location.pathname));\n  ws.onmessage = function (msg) {\n      if (msg.data === 'reload') {\n          window.location.reload();\n      }\n      if (msg.data.startsWith('update-css ')) {\n          var messageId = msg.data.substring(11);\n          var links = document.getElementsByTagName('link');\n          for (var i = 0; i < links.length; i++) {\n              var link = links[i];\n              if (link.rel !== 'stylesheet') continue;\n              var clonedLink = link.cloneNode(true);\n              var newHref = link.href.replace(/(&|\\?)jbUpdateLinksId=\\d+/, \"$1jbUpdateLinksId=\" + messageId);\n              if (newHref !== link.href) {\n                clonedLink.href = newHref;\n              }\n              else {\n                var indexOfQuest = newHref.indexOf('?');\n                if (indexOfQuest >= 0) {\n                  // to support ?foo#hash \n                  clonedLink.href = newHref.substring(0, indexOfQuest + 1) + 'jbUpdateLinksId=' + messageId + '&' + \n                                    newHref.substring(indexOfQuest + 1);\n                }\n                else {\n                  clonedLink.href += '?' + 'jbUpdateLinksId=' + messageId;\n                }\n              }\n              link.replaceWith(clonedLink);\n          }\n      }\n  };\n})();\n\n     ✖ ✍操作台（点此拖动，左上角调整大小）  \n              ● 已选中1个元素，您可以:\n                  确认采集    取消选择  Path: /html/body  \n"}], "unique_index": "x3n1i38g6ulpr5l38y", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}