{"id": 298, "name": "ZSXQ", "url": "https://t.zsxq.com/15aUTk4Oa", "links": "https://wx.zsxq.com/dweb2/index/files", "create_time": "12/17/2023, 12:12:12 PM", "update_time": "12/17/2023, 12:48:48 PM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "dataWriteMode": 1, "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "browser": "chrome", "desc": "https://t.zsxq.com/15aUTk4Oa", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://wx.zsxq.com/dweb2/index/files", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://wx.zsxq.com/dweb2/index/files"}], "outputParameters": [{"id": 0, "name": "执行JavaScript", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 6], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://t.zsxq.com/15aUTk4Oa", "links": "https://wx.zsxq.com/dweb2/index/files", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0}}, {"id": -1, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击2023程序...", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 15, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"main-content-container\")]/app-topic[3]/div[1]/div[1]/div[1]/app-talk-content[1]/div[1]/app-file-gallery[1]/div[1]/div[2]/div[2]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/app-root[1]/app-index[1]/div[1]/app-topic-flow[1]/div[1]/app-main-content[1]/div[1]/app-topic[3]/div[1]/div[1]/div[1]/app-talk-content[1]/div[1]/app-file-gallery[1]/div[1]/div[2]/div[2]", "//div[contains(., '2023程序员人群洞')]", "//DIV[@class='file-name']", "/html/body/app-root/app-index/div/app-topic-flow/div/app-main-content/div/app-topic[last()-17]/div/div/div[last()-1]/app-talk-content/div/app-file-gallery/div/div/div"]}}, {"id": 5, "index": 4, "parentId": 2, "type": 0, "option": 5, "title": "执行JavaScript", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 0, "code": "document.elementFromPoint(20,20).click();", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"id": -1, "index": 5, "parentId": 0, "type": 1, "option": 8, "title": "循环 - 不固定元素列表", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "/html/body/app-root[1]/app-index[1]/div[1]/app-all-topic-files[1]/div[1]/div[2]/app-joined-group-file[1]/div[1]/div[2]/div", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 2, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "循环点击每个元素", "sequence": [7, 8, 4], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/app-root[1]/app-index[1]/div[1]/app-all-topic-files[1]/div[1]/div[2]/app-joined-group-file[1]/div[1]/div[2]/div", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 3, "index": 7, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 1, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ""}}, {"id": 4, "index": 8, "parentId": 2, "type": 0, "option": 2, "title": "点击下载", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"download\")]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/app-root[1]/app-index[1]/div[1]/app-all-topic-files[1]/div[1]/div[2]/app-joined-group-file[1]/div[1]/app-file-preview[1]/div[1]/div[1]/div[4]/div[5]/div[1]", "//div[contains(., '下载')]", "//DIV[@class='download']", "/html/body/app-root/app-index/div/app-all-topic-files/div/div/app-joined-group-file/div/app-file-preview/div/div/div[last()-2]/div/div"]}}]}