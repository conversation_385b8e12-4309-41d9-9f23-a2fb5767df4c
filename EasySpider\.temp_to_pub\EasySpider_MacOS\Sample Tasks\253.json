{"id": 253, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "12/11/2023, 6:52:17 AM", "update_time": "12/11/2023, 6:52:17 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "123", "value": "123"}, {"id": 2, "name": "loopTimes_循环点击单个元素_2", "nodeId": 6, "nodeName": "循环点击单个元素", "desc": "循环循环点击单个元素执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数1_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\n\t\t\t\t        \t"}, {"id": 1, "name": "参数2_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//item.jd.com/10092848337443.html"}, {"id": 2, "name": "参数3_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//img13.360buyimg.com/n7/jfs/t1/205525/1/33065/90897/656d32caFeffa6b86/99acb81df6dd3d67.jpg"}, {"id": 3, "name": "参数4_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "￥"}, {"id": 4, "name": "参数5_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "59.46"}, {"id": 5, "name": "参数6_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t全新速发全新现货二全套123全集当官是一门技术活黄晓阳官 高品质完整版 二号首长全套三本\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t"}, {"id": 6, "name": "参数7_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//item.jd.com/10092848337443.html"}, {"id": 7, "name": "参数8_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "全新速发全新现货二全套全集当官是一门技术活黄晓阳官高品质完整版二号首长全套三本"}, {"id": 8, "name": "参数9_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "123"}, {"id": 9, "name": "参数10_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "条评价"}, {"id": 10, "name": "参数11_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "0"}, {"id": 11, "name": "参数12_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//item.jd.com/10092848337443.html#comment"}, {"id": 12, "name": "参数13_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "银磊小说专营店"}, {"id": 13, "name": "参数14_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//mall.jd.com/index-13208509.html?from=pc"}, {"id": 14, "name": "参数15_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "免邮"}, {"id": 15, "name": "参数16_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "券10-3"}, {"id": 16, "name": "参数17_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1件9折"}, {"id": 17, "name": "参数18_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "对比"}, {"id": 18, "name": "参数19_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 19, "name": "参数20_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "关注"}, {"id": 20, "name": "参数21_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 21, "name": "参数22_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "加入购物车"}, {"id": 22, "name": "参数23_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//cart.jd.com/gate.action?pid=10092848337443&pcount=1&ptype=1"}, {"id": 23, "name": "参数24_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "0-2、3-6岁亲子绘本书单，超细致分龄指南！掌握0-6岁黄金关键期，让宝宝自然爱上英语启蒙。爸爸妈妈不要怕！跟着读就对了。搜【图书企业购】享特权，团购专线400-026-0000"}, {"id": 24, "name": "参数25_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "..."}, {"id": 25, "name": "参数26_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//m.360buyimg.com/cc/jfs/t1/113659/27/28361/2962/62ecb1f0E6c5fc50c/b914680e87a2c8e9.png"}, {"id": 26, "name": "参数27_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//m.360buyimg.com/cc/jfs/t1/197706/29/27826/4384/63314bc6E21b4c3e9/8b4d5fc44efc5b3f.png"}, {"id": 27, "name": "参数28_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "领券9日晚8享200减50"}, {"id": 28, "name": "参数29_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "12.8-12.12"}, {"id": 29, "name": "参数30_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "券99-5"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 6], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "value": "123", "index": 0, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-btn\"]/i[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/button[1]/i[1]", "//i[contains(., '')]", "/html/body/div[last()-6]/div/div[last()-2]/div/button/i"]}}, {"id": 5, "index": 4, "parentId": 4, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li/div[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='gl-i-wrap']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div"]}}, {"id": 7, "index": 5, "parentId": 5, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/div[1]/a[1]", "allXPaths": ["/div[1]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t\n\t\t\t\t        \t"}], "unique_index": "/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/div[1]/a[1]", "allXPaths": ["/div[1]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10092848337443.html"}], "unique_index": "/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数3_图片地址", "desc": "", "relativeXPath": "/div[1]/a[1]/img[1]", "allXPaths": ["/div[1]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/a/img"], "exampleValues": [{"num": 0, "value": "//img13.360buyimg.com/n7/jfs/t1/205525/1/33065/90897/656d32caFeffa6b86/99acb81df6dd3d67.jpg"}], "unique_index": "/div[1]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/div[2]/strong[1]/em[1]", "allXPaths": ["/div[2]/strong[1]/em[1]", "//em[contains(., '￥')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-6]/strong/em"], "exampleValues": [{"num": 0, "value": "￥"}], "unique_index": "/div[2]/strong[1]/em[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/div[2]/strong[1]/i[1]", "allXPaths": ["/div[2]/strong[1]/i[1]", "//i[contains(., '59.46')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-6]/strong/i"], "exampleValues": [{"num": 0, "value": "59.46"}], "unique_index": "/div[2]/strong[1]/i[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数6_链接文本", "desc": "", "relativeXPath": "/div[3]/a[1]", "allXPaths": ["/div[3]/a[1]", "//a[contains(., '全')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t全新速发全新现货二全套123全集当官是一门技术活黄晓阳官 高品质完整版 二号首长全套三本\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t"}], "unique_index": "/div[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数7_链接地址", "desc": "", "relativeXPath": "/div[3]/a[1]", "allXPaths": ["/div[3]/a[1]", "//a[contains(., '全')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10092848337443.html"}], "unique_index": "/div[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数8_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]", "allXPaths": ["/div[3]/a[1]/em[1]", "//em[contains(., '全新速发全新现货二全')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a/em"], "exampleValues": [{"num": 0, "value": "全新速发全新现货二全套全集当官是一门技术活黄晓阳官高品质完整版二号首长全套三本"}], "unique_index": "/div[3]/a[1]/em[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/font[1]", "allXPaths": ["/div[3]/a[1]/em[1]/font[1]", "//font[contains(., '123')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a/em/font"], "exampleValues": [{"num": 0, "value": "123"}], "unique_index": "/div[3]/a[1]/em[1]/font[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/div[4]/strong[1]", "allXPaths": ["/div[4]/strong[1]", "//strong[contains(., '0条评价')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong"], "exampleValues": [{"num": 0, "value": "条评价"}], "unique_index": "/div[4]/strong[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数11_链接文本", "desc": "", "relativeXPath": "/div[4]/strong[1]/a[1]", "allXPaths": ["/div[4]/strong[1]/a[1]", "//a[contains(., '0')]", "id(\"J_comment_10092848337443\")", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong/a"], "exampleValues": [{"num": 0, "value": "0"}], "unique_index": "/div[4]/strong[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数12_链接地址", "desc": "", "relativeXPath": "/div[4]/strong[1]/a[1]", "allXPaths": ["/div[4]/strong[1]/a[1]", "//a[contains(., '0')]", "id(\"J_comment_10092848337443\")", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10092848337443.html#comment"}], "unique_index": "/div[4]/strong[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数13_链接文本", "desc": "", "relativeXPath": "/div[5]/span[1]/a[1]", "allXPaths": ["/div[5]/span[1]/a[1]", "//a[contains(., '银磊小说专营店')]", "//A[@class='curr-shop hd-shopname']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-3]/span/a"], "exampleValues": [{"num": 0, "value": "银磊小说专营店"}], "unique_index": "/div[5]/span[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数14_链接地址", "desc": "", "relativeXPath": "/div[5]/span[1]/a[1]", "allXPaths": ["/div[5]/span[1]/a[1]", "//a[contains(., '银磊小说专营店')]", "//A[@class='curr-shop hd-shopname']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-3]/span/a"], "exampleValues": [{"num": 0, "value": "//mall.jd.com/index-13208509.html?from=pc"}], "unique_index": "/div[5]/span[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数15_文本", "desc": "", "relativeXPath": "/div[6]/i[1]", "allXPaths": ["/div[6]/i[1]", "//i[contains(., '免邮')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-2]/i[last()-2]"], "exampleValues": [{"num": 0, "value": "免邮"}], "unique_index": "/div[6]/i[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数16_文本", "desc": "", "relativeXPath": "/div[6]/i[2]", "allXPaths": ["/div[6]/i[2]", "//i[contains(., '券10-3')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-2]/i[last()-1]"], "exampleValues": [{"num": 0, "value": "券10-3"}], "unique_index": "/div[6]/i[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数17_文本", "desc": "", "relativeXPath": "/div[6]/i[3]", "allXPaths": ["/div[6]/i[3]", "//i[contains(., '1件9折')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-2]/i"], "exampleValues": [{"num": 0, "value": "1件9折"}], "unique_index": "/div[6]/i[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数18_链接文本", "desc": "", "relativeXPath": "/div[7]/a[1]", "allXPaths": ["/div[7]/a[1]", "//a[contains(., '对比')]", "//A[@class='p-o-btn contrast J_contrast contrast-hide']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-2]"], "exampleValues": [{"num": 0, "value": "对比"}], "unique_index": "/div[7]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数19_链接地址", "desc": "", "relativeXPath": "/div[7]/a[1]", "allXPaths": ["/div[7]/a[1]", "//a[contains(., '对比')]", "//A[@class='p-o-btn contrast J_contrast contrast-hide']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-2]"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[7]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数20_链接文本", "desc": "", "relativeXPath": "/div[7]/a[2]", "allXPaths": ["/div[7]/a[2]", "//a[contains(., '关注')]", "//A[@class='p-o-btn focus  J_focus']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "关注"}], "unique_index": "/div[7]/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数21_链接地址", "desc": "", "relativeXPath": "/div[7]/a[2]", "allXPaths": ["/div[7]/a[2]", "//a[contains(., '关注')]", "//A[@class='p-o-btn focus  J_focus']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[7]/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数22_链接文本", "desc": "", "relativeXPath": "/div[7]/a[3]", "allXPaths": ["/div[7]/a[3]", "//a[contains(., '加入购物车')]", "//A[@class='p-o-btn addcart']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a"], "exampleValues": [{"num": 0, "value": "加入购物车"}], "unique_index": "/div[7]/a[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数23_链接地址", "desc": "", "relativeXPath": "/div[7]/a[3]", "allXPaths": ["/div[7]/a[3]", "//a[contains(., '加入购物车')]", "//A[@class='p-o-btn addcart']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a"], "exampleValues": [{"num": 0, "value": "//cart.jd.com/gate.action?pid=10092848337443&pcount=1&ptype=1"}], "unique_index": "/div[7]/a[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数24_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/i[1]", "allXPaths": ["/div[3]/a[1]/i[1]", "//i[contains(., '0-2、3-6岁亲子')]", "id(\"J_AD_11797923\")", "//I[@class='promo-words']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div[last()-5]/a/i"], "exampleValues": [{"num": 1, "value": "0-2、3-6岁亲子绘本书单，超细致分龄指南！掌握0-6岁黄金关键期，让宝宝自然爱上英语启蒙。爸爸妈妈不要怕！跟着读就对了。搜【图书企业购】享特权，团购专线400-026-0000"}], "unique_index": "/div[3]/a[1]/i[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数25_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/font[2]", "allXPaths": ["/div[3]/a[1]/em[1]/font[2]", "//font[contains(., '...')]", "//FONT[@class='dot']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-27]/div/div[last()-5]/a/em/font"], "exampleValues": [{"num": 2, "value": "..."}], "unique_index": "/div[3]/a[1]/em[1]/font[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数26_图片地址", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/img[1]", "allXPaths": ["/div[3]/a[1]/em[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='p-tag3']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-25]/div/div[last()-5]/a/em/img"], "exampleValues": [{"num": 4, "value": "//m.360buyimg.com/cc/jfs/t1/113659/27/28361/2962/62ecb1f0E6c5fc50c/b914680e87a2c8e9.png"}], "unique_index": "/div[3]/a[1]/em[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数27_图片地址", "desc": "", "relativeXPath": "/div[5]/img[1]", "allXPaths": ["/div[5]/img[1]", "//img[contains(., '')]", "//IMG[@class='shop-tag fl']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-22]/div/div[last()-3]/img"], "exampleValues": [{"num": 7, "value": "//m.360buyimg.com/cc/jfs/t1/197706/29/27826/4384/63314bc6E21b4c3e9/8b4d5fc44efc5b3f.png"}], "unique_index": "/div[5]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数28_文本", "desc": "", "relativeXPath": "/div[1]/a[1]/div[1]/div[1]", "allXPaths": ["/div[1]/a[1]/div[1]/div[1]", "//div[contains(., '领券9日晚8享200')]", "//DIV[@class='sign-title ac']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-19]/div/div[last()-7]/a/div/div"], "exampleValues": [{"num": 10, "value": "领券9日晚8享200减50"}], "unique_index": "/div[1]/a[1]/div[1]/div[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数29_文本", "desc": "", "relativeXPath": "/div[1]/a[1]/div[1]/div[1]/span[1]", "allXPaths": ["/div[1]/a[1]/div[1]/div[1]/span[1]", "//span[contains(., '12.8-12.12')]", "//SPAN[@class='sign-date']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-19]/div/div[last()-7]/a/div/div/span"], "exampleValues": [{"num": 10, "value": "12.8-12.12"}], "unique_index": "/div[1]/a[1]/div[1]/div[1]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数30_文本", "desc": "", "relativeXPath": "/div[6]/i[4]", "allXPaths": ["/div[6]/i[4]", "//i[contains(., '券99-5')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li/div/div[last()-2]/i"], "exampleValues": [{"num": 29, "value": "券99-5"}], "unique_index": "/div[6]/i[4]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 4, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "循环点击单个元素", "sequence": [4, 7], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"pn-next\")]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[4]/div[1]/span[1]/a[9]", "//a[contains(., '下一页>')]", "//A[@class='pn-next']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div/div/span[last()-1]/a"]}}, {"id": 6, "index": 7, "parentId": 4, "type": 0, "option": 2, "title": "点击下一页>", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[4]/div[1]/span[1]/a[9]", "//a[contains(., '下一页>')]", "//A[@class='pn-next']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div/div/span[last()-1]/a"], "loopType": 0}}]}