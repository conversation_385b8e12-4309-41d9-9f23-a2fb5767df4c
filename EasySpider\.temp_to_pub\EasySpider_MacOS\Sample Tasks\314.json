{"id": 314, "name": "多字段案例", "url": "https://easyspider.cn/test_pages/unfixed_elements.html", "links": "https://easyspider.cn/test_pages/unfixed_elements.html\nhttps://easyspider.cn/test_pages/unfixed_elements2.html", "create_time": "2023-12-29 19:34:59", "update_time": "2023-12-30 16:04:30", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "dataWriteMode": 1, "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": true, "browser": "chrome", "removeDuplicate": 0, "desc": "", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://easyspider.cn/test_pages/unfixed_elements.html\nhttps://easyspider.cn/test_pages/unfixed_elements2.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://easyspider.cn/test_pages/unfixed_elements.html\nhttps://easyspider.cn/test_pages/unfixed_elements2.html"}], "outputParameters": [{"id": 0, "name": "品牌", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}, {"id": 1, "name": "代工厂", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}, {"id": 2, "name": "价格", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}, {"id": 3, "name": "特点", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}, {"id": 4, "name": "生成新行", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 5, "name": "清空字段值", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 28, 44, 43], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.avito.ru/moskva/telefony/mobilnye_telefony/apple-ASgBAgICAkS0wA3OqzmwwQ2I_Dc", "links": "https://easyspider.cn/test_pages/unfixed_elements.html\nhttps://easyspider.cn/test_pages/unfixed_elements2.html", "maxWaitTime": 30, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.avito.ru/moskva/telefony/mobilnye_telefony/apple-ASgBAgICAkS0wA3OqzmwwQ2I_Dc", "links": "https://www.avito.ru/moskva/telefony/mobilnye_telefony/apple-ASgBAgICAkS0wA3OqzmwwQ2I_Dc", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环 产品标题", "sequence": [4, 11], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[3]/div[2]/div[1]/div[1]/div[1]/div[2]/div[6]/p[1]", "//p[contains(., 'В наличии')]", "//P[@class='styles-module-root-_KFFt styles-module-size_s-awPvv styles-module-size_s_compensated-Wo8uc styles-module-size_s-_P6ZA styles-module-ellipsis-LKWy3 stylesMarningNormal-module-root-OSCNq stylesMarningNormal-module-paragraph-s-_c6vD styles-module-noAccent-nZxz7 styles-module-root_bottom-XgXHc styles-module-margin-bottom_6-nU1Wp']", "/html/body/div[last()-9]/div/div[last()-1]/div/div[last()-4]/div/div/div[last()-2]/div/div[last()-49]/div/div/div[last()-1]/div[last()-2]/p"]}}, {"id": -1, "index": 4, "parentId": 2, "type": 0, "option": 2, "title": "点击产品标题", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 1, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[3]/div[2]/div[1]/div[1]/div[1]/div[2]/div[6]/p[1]", "//p[contains(., 'В наличии')]", "//P[@class='styles-module-root-_KFFt styles-module-size_s-awPvv styles-module-size_s_compensated-Wo8uc styles-module-size_s-_P6ZA styles-module-ellipsis-LKWy3 stylesMarningNormal-module-root-OSCNq stylesMarningNormal-module-paragraph-s-_c6vD styles-module-noAccent-nZxz7 styles-module-root_bottom-XgXHc styles-module-margin-bottom_6-nU1Wp']", "/html/body/div[last()-9]/div/div[last()-1]/div/div[last()-4]/div/div/div[last()-2]/div/div[last()-49]/div/div/div[last()-1]/div[last()-2]/p"]}}, {"id": -1, "index": 5, "parentId": 3, "type": 1, "option": 8, "title": "循环点击", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[4]/nav[1]/ul[1]/li[9]/a[1]/svg[1]", "//svg[contains(., '')]", "//svg[@class='[object SVGAnimatedString]']", "/html/body/div[last()-9]/div/div[last()-1]/div/div[last()-4]/div/div/div[last()-1]/nav/ul/li/a/svg"]}}, {"id": -1, "index": 6, "parentId": 3, "type": 0, "option": 12, "title": "点击产品标题", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": -1, "index": 7, "parentId": 3, "type": 0, "option": 12, "title": "点击产品标题", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": -1, "index": 8, "parentId": 3, "type": 1, "option": 8, "title": "循环点击", "sequence": [10], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[4]/nav[1]/ul[1]/li[9]/a[1]/svg[1]", "//svg[contains(., '')]", "//svg[@class='[object SVGAnimatedString]']", "/html/body/div[last()-9]/div/div[last()-1]/div/div[last()-4]/div/div/div[last()-1]/nav/ul/li/a/svg"]}}, {"id": -1, "index": 9, "parentId": 5, "type": 1, "option": 8, "title": "循环 - 单个元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0}}, {"id": -1, "index": 10, "parentId": 5, "type": 0, "option": 12, "title": "循环点击", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": -1, "index": 11, "parentId": 2, "type": 1, "option": 8, "title": "循环点击单个元素", "sequence": [13, 12], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"styles-module-listItem_arrow_next-AdI_R\")]/a[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[4]/nav[1]/ul[1]/li[9]/a[1]", "//a[contains(., '')]", "//A[@class='styles-module-item-kF45w styles-module-item_arrow-sxBqe styles-module-item_size_s-Tvz95 styles-module-item_link-_bV2N']", "/html/body/div[last()-9]/div/div[last()-1]/div/div[last()-4]/div/div/div[last()-1]/nav/ul/li/a"]}}, {"id": -1, "index": 12, "parentId": 5, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[4]/nav[1]/ul[1]/li[last()]/a[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[4]/nav[1]/ul[1]/li[9]/a[1]", "//a[contains(., '')]", "//A[@class='styles-module-item-kF45w styles-module-item_arrow-sxBqe styles-module-item_size_s-Tvz95 styles-module-item_link-_bV2N']", "/html/body/div[last()-9]/div/div[last()-1]/div/div[last()-4]/div/div/div[last()-1]/nav/ul/li/a"]}}, {"id": -1, "index": 13, "parentId": 5, "type": 1, "option": 8, "title": "循环点击每个元素", "sequence": [14], "isInLoop": true, "position": 0, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[3]/div[2]/div/div[1]/div[1]/div[2]/div/p[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ""}}, {"id": -1, "index": 14, "parentId": 7, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 7, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 1, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ""}}, {"id": -1, "index": 15, "parentId": 0, "type": 1, "option": 8, "title": "循环点击每个元素", "sequence": [16], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[2]/div[1]/div[2]/div[3]/div[3]/div[3]/div[2]/div/div[1]/div[1]/div[2]/div/p[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ""}}, {"id": -1, "index": 16, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 1, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ""}}, {"id": -1, "index": 17, "parentId": 0, "type": 1, "option": 8, "title": "循环 - 不固定元素列表", "sequence": [27, 18, 19, 20], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[3]/div[2]/div/div[1]/div[1]/div[2]/div/p[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ""}}, {"id": -1, "index": 18, "parentId": 2, "type": 0, "option": 2, "title": "点击标题", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[3]/div[2]/div/div[1]/div[1]/div[2]/div/p[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 1, "maxWaitTime": 30, "params": [], "alertHandleType": 0, "allXPaths": ""}}, {"id": -1, "index": 19, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 4, "contentType": 4, "relative": false, "name": "图片地址", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/img[1]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/img[1]", "//img[contains(., '')]", "//IMG[@class='desktop-1ky5g7j']", "//IMG[@alt='iPhone 12 Pro, 128 ГБ']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div[last()-1]/div[last()-1]/div[last()-3]/div[last()-3]/div/div/div/div/div[last()-1]/img"], "exampleValues": [{"num": 0, "value": "https://00.img.avito.st/image/1/1.1-Tecra4ew3o27kI_AWTu8TQeQtg0_kFqNZ5D27bcwdo.Taj3KsYDq5gpIxqFx1gxVaZd-QSoPvutg-jYxgBAgEU"}], "unique_index": "o8ebxy70mtlqp1mpku", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 1, "splitLine": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "标题", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[1]/div[1]/div[1]/h1[1]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[1]/div[1]/div[1]/h1[1]", "//h1[contains(., 'iPhone 12')]", "//H1[@class='styles-module-root-TWVKW styles-module-root-_KFFt styles-module-size_xxxl-A2qfi styles-module-size_xxxl-_bK04 stylesMarningNormal-module-root-OSCNq stylesMarningNormal-module-header-3xl-k0ckc']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div[last()-1]/div[last()-2]/div/div[last()-2]/h1"], "exampleValues": [{"num": 0, "value": "iPhone 12 Pro, 128 ГБ"}], "unique_index": "8151oy1e97plqp0y72l", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 1, "relative": false, "name": "售价", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/span[1]/span[1]/span[1]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/span[1]/span[1]/span[1]", "//span[contains(., '47 990 ₽')]", "//SPAN[@class='styles-module-size_xxxl-A2qfi']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div/div[last()-1]/div[last()-1]/div/div[last()-1]/div/div/div/div/div/span/span/span[last()-1]"], "exampleValues": [{"num": 0, "value": "47990"}], "unique_index": "", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "评论数", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[2]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/span[3]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[2]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/span[3]", "//span[contains(., '1 837 отзы')]", "//SPAN[@class='styles-module-size_s-awPvv']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div/div[last()-1]/div[last()-1]/div/div/div/div/div/div/div[last()-1]/div[last()-1]/div[last()-1]/div[last()-2]/div/span"], "exampleValues": [{"num": 0, "value": "1 837 отзывов"}], "unique_index": "u0jdus1du1elqp0y72l", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "评论级别", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[2]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/span[1]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[2]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/span[1]", "//span[contains(., '5,0')]", "//SPAN[@class='style-seller-info-rating-score-C0y96']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div/div[last()-1]/div[last()-1]/div/div/div/div/div/div/div[last()-1]/div[last()-1]/div[last()-1]/div[last()-2]/div/span[last()-2]"], "exampleValues": [{"num": 0, "value": "5,0"}], "unique_index": "rj241lmpz2elqp0y72l", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "上架时间", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[2]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[1]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[2]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[3]/div[1]", "//div[contains(., 'На Авито c')]", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div/div[last()-1]/div[last()-1]/div/div/div/div/div/div/div[last()-1]/div[last()-1]/div[last()-1]/div/div"], "exampleValues": [{"num": 0, "value": "На Авито c мая 2020 "}], "unique_index": "cut9yumrmhblqp0y72l", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 1, "relative": false, "name": "成色", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[4]/div[1]/ul[1]/li[1]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[4]/div[1]/ul[1]/li[1]", "//li[contains(., 'СостояниеН')]", "//LI[@class='params-paramsList__item-appQw']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div[last()-1]/div[last()-1]/div[last()-3]/div/div/ul/li[last()-4]"], "exampleValues": [{"num": 0, "value": "Новое"}], "unique_index": "", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 1, "relative": false, "name": "存储空间", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[4]/div[1]/ul[1]/li[4]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[4]/div[1]/ul[1]/li[4]", "//li[contains(., 'Встроенная')]", "//LI[@class='params-paramsList__item-appQw']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div[last()-1]/div[last()-1]/div[last()-3]/div/div/ul/li[last()-1]"], "exampleValues": [{"num": 0, "value": "128ГБ"}], "unique_index": "", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 1, "relative": false, "name": "品牌", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[4]/div[1]/ul[1]/li[2]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[4]/div[1]/ul[1]/li[2]", "//li[contains(., 'Производит')]", "//LI[@class='params-paramsList__item-appQw']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div[last()-1]/div[last()-1]/div[last()-3]/div/div/ul/li[last()-3]"], "exampleValues": [{"num": 0, "value": "Apple"}], "unique_index": "/li[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 1, "relative": false, "name": "颜色", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[4]/div[1]/ul[1]/li[5]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[4]/div[1]/ul[1]/li[5]", "//li[contains(., 'Цвет: Сини')]", "//LI[@class='params-paramsList__item-appQw']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div[last()-1]/div[last()-1]/div[last()-3]/div/div/ul/li"], "exampleValues": [{"num": 0, "value": "Синий"}], "unique_index": "", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 1, "contentType": 0, "relative": false, "name": "型号", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[4]/div[1]/ul[1]/li[3]/p[1]/a[1]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[4]/div[1]/ul[1]/li[3]/p[1]/a[1]", "//a[contains(., 'iPhone 12')]", "//A[@class='styles-module-root-QmppR styles-module-root_noVisited-aFA10']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div[last()-1]/div[last()-1]/div[last()-3]/div/div/ul/li[last()-2]/p/a"], "exampleValues": [{"num": 0, "value": "iPhone 12 Pro"}], "unique_index": "svn196058vjlqp0y72l", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "描述", "desc": "", "relativeXPath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[2]/div[1]/div[2]", "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[2]/div[1]/div[2]", "//div[contains(., '❗Дpузья, п')]", "//DIV[@class='style-item-description-html-qCwUL']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div[last()-1]/div[last()-1]/div[last()-2]/div/div"], "exampleValues": [{"num": 0, "value": "❗Дpузья, прeжде чем звoнить и писaть, ознакомьтеcь, пожaлуйста, с опиcаниeм объявлeния! Пoдapoк внимaтельным!  📱 iРhоne 12 Рro 128 Gb Вlue • Oригинaл • Зaпeчатанный • Полный рабочий функциoнал • RЕF (Rеfurbished), прoвеpен по чек-листу Тeлeфон выглядит абсолютно кaк нoвый, отлично пoдoйдет в качестве подaрка!  ⭐️ Почему выбирают нaс • Нам мoжнo довeрять, работаем прозрачно и честно с 2017 года • Высокий сервис и обслуживание, более 1000 благодарных отзывов • Большой ассортимент и широкий выбор аксессуаров • Поддержка после покупки  📦 Доставка • По Москве и МО без предоплаты — от 3х часов • СДЕК, ВохВеrry и Почта России по 100% предоплате — от 2х дней • Авито Доставка (безопасная сделка)  🏢 Самовывоз • Офис в центре Москвы, м.Савеловская • Можно распаковать и проверить устройство на месте ❗️Просьба ставить нужную модель в резерв за час  💵 Способы оплаты • Наличными • Переводом (1.5% комиссия банка) • Картой (от 4% кассовый сбор)  ⚙️ Гарантия • 7 дней на проверку и обмен • 1 год на ремонт по талону  🤝 Сотрудничество • Постоянным клиентам скидки • Опт от 5 шт.  ⚠️ Остерегайтесь недобросовестных продавцов, многие продают восстановленные под видом новых! 🎁 Напиши нам и получи промо-код на бесплатный подарок!  🩵 Приятных покупок! ❗️Количество подарков ограничено, промокод и наличие уточняйте у менеджеров."}], "unique_index": "sejmihvis6nlqp0y72l", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}]}}, {"id": -1, "index": 20, "parentId": 2, "type": 1, "option": 8, "title": "循环 - 下一页", "sequence": [21], "isInLoop": true, "position": 3, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"styles-module-listItem_arrow_next-AdI_R\")]/a[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[2]/div[1]/div[2]/div[3]/div[3]/div[4]/nav[1]/ul[1]/li[9]/a[1]", "//a[contains(., '')]", "//A[@class='styles-module-item-kF45w styles-module-item_arrow-sxBqe styles-module-item_size_s-Tvz95 styles-module-item_link-_bV2N']", "/html/body/div[last()-6]/div/div[last()-1]/div/div[last()-4]/div/div/div[last()-1]/nav/ul/li/a"]}}, {"id": -1, "index": 21, "parentId": 6, "type": 0, "option": 2, "title": "点击下一页", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 30, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[2]/div[1]/div[2]/div[3]/div[3]/div[4]/nav[1]/ul[1]/li[9]/a[1]", "//a[contains(., '')]", "//A[@class='styles-module-item-kF45w styles-module-item_arrow-sxBqe styles-module-item_size_s-Tvz95 styles-module-item_link-_bV2N']", "/html/body/div[last()-6]/div/div[last()-1]/div/div[last()-4]/div/div/div[last()-1]/nav/ul/li/a"]}}, {"id": -1, "index": 22, "parentId": 5, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"help-button-image-DI1Hb\")]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[4]/div[1]/div[2]/div[1]/div[4]/div[1]/ul[1]/li[1]/span[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='help-button-image-DI1Hb']", "/html/body/div[last()-10]/div/div[last()-1]/div[last()-1]/div/div[last()-6]/div/div[last()-1]/div[last()-1]/div[last()-3]/div/div/ul/li[last()-4]/span/img"]}}, {"id": -1, "index": 23, "parentId": 0, "type": 1, "option": 8, "title": "循环点击每个元素", "sequence": [24], "isInLoop": false, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[3]/div[2]/div/div[1]/div[1]/div[2]/div/p[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ""}}, {"id": -1, "index": 24, "parentId": 3, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 1, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ""}}, {"id": -1, "index": 25, "parentId": 3, "type": 1, "option": 8, "title": "循环点击每个元素", "sequence": [26], "isInLoop": true, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[3]/div[1]/div[2]/div[3]/div[3]/div[3]/div[2]/div/div[1]/div[1]/div[2]/div/p[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ""}}, {"id": -1, "index": 26, "parentId": 8, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 1, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ""}}, {"id": -1, "index": 27, "parentId": 2, "type": 0, "option": 5, "title": "获得Python表达式值", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 6, "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"id": 2, "index": 28, "parentId": 0, "type": 1, "option": 8, "title": "循环 - 不固定元素列表", "sequence": [29], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "//div", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0}}, {"id": 5, "index": 29, "parentId": 2, "type": 2, "option": 9, "title": "判断条件 - 从左往右依次判断", "sequence": [30, 31, 32, 33], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": 6, "parentId": 5, "index": 30, "type": 3, "option": 10, "title": "当前循环项包含文本 - 品牌", "sequence": [35], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 3, "value": "品牌", "code": "", "waitTime": 0}, "position": 0}, {"id": 7, "parentId": 5, "index": 31, "type": 3, "option": 10, "title": "当前循环项包含文本 - 代工厂", "sequence": [36], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 3, "value": "代工厂", "code": "", "waitTime": 0}, "position": 1}, {"index": 32, "id": 8, "parentId": 5, "type": 3, "option": 10, "title": "当前循环项包含文本 - 价格", "sequence": [37], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 3, "value": "价格", "code": "", "waitTime": 0}, "position": 2}, {"index": 33, "id": 9, "parentId": 5, "type": 3, "option": 10, "title": "当前循环项包含文本 - 特点", "sequence": [38], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 3, "value": "特点", "code": "", "waitTime": 0}, "position": 3}, {"index": 34, "id": -1, "parentId": 3, "type": 3, "option": 10, "title": "当前循环项包含文本 - 活动", "sequence": [39], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 3, "value": "", "code": "", "waitTime": 0}, "position": 4}, {"id": 10, "index": 35, "parentId": 6, "type": 0, "option": 3, "title": "提取数据 - 品牌", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": false, "newLine": false, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "品牌", "desc": "", "iframe": false, "extractType": 0, "relativeXPath": "//span[2]", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text", "splitLine": 0}]}}, {"id": 11, "index": 36, "parentId": 7, "type": 0, "option": 3, "title": "提取数据 - 代工厂", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": false, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "代工厂", "desc": "", "iframe": false, "extractType": 0, "relativeXPath": "//span[2]", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text", "splitLine": 0}]}}, {"id": 12, "index": 37, "parentId": 8, "type": 0, "option": 3, "title": "提取数据 - 价格", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": false, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "价格", "desc": "", "iframe": false, "extractType": 0, "relativeXPath": "//span[2]", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text", "splitLine": 0}]}}, {"id": 13, "index": 38, "parentId": 9, "type": 0, "option": 3, "title": "提取数据 - 特点", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": false, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "特点", "desc": "", "iframe": false, "extractType": 0, "relativeXPath": "//span[2]", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text", "splitLine": 0}]}}, {"id": -1, "index": 39, "parentId": 8, "type": 0, "option": 3, "title": "提取数据 - 品牌", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": []}}, {"id": -1, "index": 40, "parentId": 0, "type": 0, "option": 5, "title": "执行JavaScript", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 0, "code": "return \"\"", "waitTime": 0, "recordASField": 1, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"id": -1, "index": 41, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "结束标记", "desc": "", "iframe": false, "extractType": 0, "relativeXPath": "//body", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text", "splitLine": 0}]}}, {"id": -1, "index": 42, "parentId": 0, "type": 0, "option": 5, "title": "生成新行", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 11, "code": "\"页面已完成采集\"", "waitTime": 0, "recordASField": 1, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"id": 4, "index": 43, "parentId": 0, "type": 0, "option": 5, "title": "生成新行", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 11, "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"id": 3, "index": 44, "parentId": 0, "type": 0, "option": 5, "title": "清空字段值", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 10, "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}]}