{"id": 206, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "", "update_time": "8/10/2023, 6:46:45 PM", "version": "0.5.0", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 1, "name": "自定义操作2", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 2, "name": "自定义参数_1", "desc": "", "type": "text", "recordASField": 0, "exampleValue": "自定义值"}, {"id": 3, "name": "自定义参数_2", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 4, 5, 2, 8], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 4, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [6], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 3, "parentId": 4, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "//a[Field[\"自定义操作2\"]]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 1}}, {"id": 2, "index": 4, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "codeMode": "5", "code": "self.t = 2", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 3, "index": 5, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作2", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "codeMode": "6", "code": "self.t", "waitTime": 0, "recordASField": 1, "paraType": "text"}}, {"id": 6, "index": 6, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": false, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "自定义参数_1", "desc": "", "extractType": 0, "relativeXPath": "//a[Field[\"自定义操作2\"]]", "recordASField": 0, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}]}}, {"id": -1, "index": 7, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 5, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": []}}, {"id": 5, "index": 8, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": true, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "自定义参数_2", "desc": "", "extractType": 0, "relativeXPath": "//body", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}]}}]}