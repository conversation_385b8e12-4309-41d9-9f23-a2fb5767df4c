{"id": 143, "name": "中国知网", "url": "https://chn.oversea.cnki.net/index/", "links": "https://chn.oversea.cnki.net/index/", "create_time": "7/6/2023, 4:50:52 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "csv", "containJudge": false, "desc": "https://chn.oversea.cnki.net/index/", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://chn.oversea.cnki.net/index/", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://chn.oversea.cnki.net/index/"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://chn.oversea.cnki.net/index/", "links": "https://chn.oversea.cnki.net/index/", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}]}