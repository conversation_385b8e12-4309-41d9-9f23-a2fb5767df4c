{"id": 114, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "7/4/2023, 2:30:54 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "参数1_图片地址", "desc": "", "type": "string", "exampleValue": "//m.360buyimg.com/babel/jfs/t1/160456/7/37206/196421/649c09faFeab01f59/cc5ea5b81653b3a5.png"}, {"id": 1, "name": "参数2_图片地址", "desc": "", "type": "string", "exampleValue": "//m.360buyimg.com/babel/jfs/t1/170360/26/33432/1027/63a97f92Ea57bb36e/0ce5842c29b3c233.png"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div/div[1]/div[1]/a[1]/img[1]", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-1]/div/div[last()-1]/div/div[last()-1]/div/div[last()-8]/div/div/a/img"]}}, {"id": 4, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 4, "contentType": 0, "relative": true, "name": "参数1_图片地址", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "//m.360buyimg.com/babel/jfs/t1/160456/7/37206/196421/649c09faFeab01f59/cc5ea5b81653b3a5.png"}], "unique_index": "qxx8j1eagpljn723fu", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 1}], "loopType": 1}}, {"id": 3, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[6]/ul[1]/li/a[1]/img[1]", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[6]/ul[1]/li[1]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div/div/div[last()-1]/ul/li[last()-4]/a/img"]}}, {"id": 5, "index": 5, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 7, "relative": true, "name": "参数2_图片地址", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "//m.360buyimg.com/babel/jfs/t1/170360/26/33432/1027/63a97f92Ea57bb36e/0ce5842c29b3c233.png"}], "unique_index": "vynho6ujxoljn72a9h", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 1}]}}]}