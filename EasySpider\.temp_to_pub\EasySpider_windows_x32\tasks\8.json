{"id": 8, "name": "Electronics, Cars, Fashion, Collectibles & More | eBay", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "create_time": "5/25/2023, 9:22:13 PM", "version": "0.3.1", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "Open Page", "value": "https://www.ebay.com", "desc": "List of URLs to be collected, separated by \\n for multiple lines", "type": "string", "exampleValue": "https://www.ebay.com"}], "outputParameters": [{"id": 0, "name": "para1_text", "desc": "", "type": "string", "exampleValue": ""}, {"id": 1, "name": "para2_text", "desc": "", "type": "string", "exampleValue": ""}, {"id": 2, "name": "para3_text", "desc": "", "type": "string", "exampleValue": ""}, {"id": 3, "name": "para4_text", "desc": "", "type": "string", "exampleValue": ""}, {"id": 4, "name": "para5_text", "desc": "", "type": "string", "exampleValue": ""}, {"id": 5, "name": "para6_text", "desc": "", "type": "string", "exampleValue": "Jordan 4 Retro Thunder 2023"}, {"id": 6, "name": "para7_text", "desc": "", "type": "string", "exampleValue": "Jordan 4 Retro Thunder 2023"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "Loop", "sequence": [3, 4], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "/html/body/div[6]/div[5]/ul[1]/li/a[1]/div[1]/div[1]/div[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[6]/div[5]/ul[1]/li[1]/a[1]/div[1]/div[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='hl-image__background']"]}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 7, "relative": true, "name": "para1_text", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": ""}, {"num": 1, "value": ""}, {"num": 2, "value": ""}, {"num": 3, "value": ""}, {"num": 4, "value": ""}, {"num": 5, "value": ""}, {"num": 6, "value": ""}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para2_text", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[6]/div[6]/ul[1]/li[1]/a[1]/div[1]/div[1]/div[1]", "allXPaths": ["/html/body/div[6]/div[6]/ul[1]/li[1]/a[1]/div[1]/div[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='hl-image__background']"], "exampleValues": [{"num": 0, "value": ""}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para3_text", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[6]/div[6]/ul[1]/li[1]/a[1]/div[1]/div[1]/div[1]", "allXPaths": ["/html/body/div[6]/div[6]/ul[1]/li[1]/a[1]/div[1]/div[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='hl-image__background']"], "exampleValues": [{"num": 0, "value": ""}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para4_text", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[6]/div[6]/ul[1]/li[1]/a[1]/div[1]/div[1]/div[1]", "allXPaths": ["/html/body/div[6]/div[6]/ul[1]/li[1]/a[1]/div[1]/div[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='hl-image__background']"], "exampleValues": [{"num": 0, "value": ""}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 4, "index": 4, "parentId": 2, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 7, "relative": false, "name": "para5_text", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[6]/div[6]/ul[1]/li[1]/a[1]/div[1]/div[1]/div[1]", "allXPaths": ["/html/body/div[6]/div[6]/ul[1]/li[1]/a[1]/div[1]/div[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='hl-image__background']"], "exampleValues": [{"num": 0, "value": ""}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para6_text", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[6]/div[5]/ul[1]/li[1]/a[1]/div[2]", "allXPaths": ["/html/body/div[6]/div[5]/ul[1]/li[1]/a[1]/div[2]", "//div[contains(., 'Jordan 4 R')]", "//DIV[@class='hl-popular-destinations-name']"], "exampleValues": [{"num": 0, "value": "Jordan 4 Retro Thunder 2023"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para7_text", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[6]/div[5]/ul[1]/li[1]/a[1]/div[2]", "allXPaths": ["/html/body/div[6]/div[5]/ul[1]/li[1]/a[1]/div[2]", "//div[contains(., 'Jordan 4 R')]", "//DIV[@class='hl-popular-destinations-name']"], "exampleValues": [{"num": 0, "value": "Jordan 4 Retro Thunder 2023"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}