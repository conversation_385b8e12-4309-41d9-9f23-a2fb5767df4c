{"id": 243, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "12/9/2023, 4:33:28 AM", "update_time": "12/9/2023, 4:42:05 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "参数4_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "注册"}, {"id": 1, "name": "参数6_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "HI～欢迎来到京东!登录|注册"}, {"id": 2, "name": "参数7_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "数码"}, {"id": 3, "name": "参数8_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://shuma.jd.com/"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 3, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 3, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 1, "contentType": 0, "relative": false, "name": "参数4_链接文本", "desc": "", "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/span[1]/a[2]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/span[1]/a[2]", "//a[contains(., '注册')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div/div/div[last()-6]/div/span/a"], "exampleValues": [{"num": 0, "value": "注册"}], "unique_index": "n1xhxb3wzgilpx33o2f", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数6_文本", "desc": "", "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]", "//div[contains(., 'HI～欢迎来到京东!')]", "//DIV[@class='user_info']", "/html/body/div[last()-5]/div/div[last()-4]/div/div/div/div[last()-6]/div"], "exampleValues": [{"num": 0, "value": "HI～欢迎来到京东!登录|注册"}], "unique_index": "mivesswt8dalpx33o2f", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 2, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [4], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div/a", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": "1", "pathList": "//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[1]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[2]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[3]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[3]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[4]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[4]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[4]/a[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[5]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[5]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[5]/a[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[5]/a[4]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[6]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[6]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[6]/a[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[6]/a[4]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[7]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[7]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[7]/a[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[7]/a[4]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[8]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[8]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[8]/a[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[9]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[9]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[10]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[10]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[11]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[11]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[12]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[12]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[12]/a[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[13]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[13]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[13]/a[3]", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 4, "index": 4, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数7_链接文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "数码"}], "unique_index": "6fkg1hrxhswlpx33tya", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数8_链接地址", "desc": "", "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "https://shuma.jd.com/"}], "unique_index": "6fkg1hrxhswlpx33tya", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 2}}]}