{"id": 228, "name": "[2312.02977] Exploring the nonclassical dynamics of the \"classical'' <PERSON><PERSON><PERSON><PERSON><PERSON> equation", "url": "https://arxiv.org/abs/2312.02977", "links": "https://arxiv.org/abs/2312.02977", "create_time": "12/7/2023, 2:44:58 AM", "update_time": "2024-01-05 22:08:46", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 3, "environment": 1, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "TTT", "dataWriteMode": 3, "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "browser": "chrome", "removeDuplicate": 0, "desc": "https://arxiv.org/abs/2312.02977", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://arxiv.org/abs/2312.02977", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://arxiv.org/abs/2312.02977"}, {"id": 1, "name": "loopTimes_1", "nodeId": 5, "nodeName": "循环 - 单个元素", "desc": "循环循环 - 单个元素执行的次数（0代表无限循环）", "type": "int", "exampleValue": 10, "value": 10}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 5], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://arxiv.org/abs/2312.02977", "links": "https://arxiv.org/abs/2312.02977", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 3, "index": 2, "parentId": 2, "type": 0, "option": 2, "title": "点击Download PDF", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"download-pdf\")]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[2]/main[1]/div[1]/div[1]/div[2]/div[1]/ul[1]/li[1]/a[1]", "//a[contains(., 'Download P')]", "//A[@class='abs-button download-pdf']", "/html/body/div[last()-3]/main/div/div/div[last()-2]/div[last()-5]/ul/li[last()-2]/a"]}}, {"id": -1, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击Download PDF", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"download-pdf\")]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[2]/main[1]/div[1]/div[1]/div[2]/div[1]/ul[1]/li[1]/a[1]", "//a[contains(., 'Download P')]", "//A[@class='abs-button download-pdf']", "/html/body/div[last()-3]/main/div/div/div[last()-2]/div[last()-5]/ul/li[last()-2]/a"]}}, {"id": -1, "index": 4, "parentId": 0, "type": 0, "option": 2, "title": "点击Download PDF", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"download-pdf\")]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[2]/main[1]/div[1]/div[1]/div[2]/div[1]/ul[1]/li[1]/a[1]", "//a[contains(., 'Download P')]", "//A[@class='abs-button download-pdf']", "/html/body/div[last()-3]/main/div/div/div[last()-2]/div[last()-5]/ul/li[last()-2]/a"]}}, {"id": 2, "index": 5, "parentId": 0, "type": 1, "option": 8, "title": "循环 - 单个元素", "sequence": [2], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "//body", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 10, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0}}]}