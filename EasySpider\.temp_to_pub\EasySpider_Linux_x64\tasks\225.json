{"id": 225, "name": "【软科排名】-中国最好学科排名|最权威的大学学科|高校学科排名", "url": "https://www.shanghairanking.cn/rankings/bcsr/2023", "links": "https://www.shanghairanking.cn/rankings/bcsr/2023", "create_time": "12/7/2023, 1:12:10 AM", "update_time": "12/7/2023, 1:12:10 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://www.shanghairanking.cn/rankings/bcsr/2023", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.shanghairanking.cn/rankings/bcsr/2023", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.shanghairanking.cn/rankings/bcsr/2023"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.shanghairanking.cn/rankings/bcsr/2023", "links": "https://www.shanghairanking.cn/rankings/bcsr/2023", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环点击每个元素", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 2, "pathList": "//*[@id=\"01\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"04\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"04\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"04\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"05\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"05\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"05\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"06\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"06\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"06\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[10]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[11]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[12]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[13]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[14]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[10]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[11]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[12]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[13]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[14]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[15]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[16]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[17]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[18]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[19]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[20]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[21]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[22]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[23]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[24]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[25]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[26]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[27]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[28]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[29]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[30]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[31]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[32]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[33]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[34]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[35]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[36]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[37]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[38]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[10]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[5]/a[1]/span[2]", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 7, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": "", "loopType": 2}}]}