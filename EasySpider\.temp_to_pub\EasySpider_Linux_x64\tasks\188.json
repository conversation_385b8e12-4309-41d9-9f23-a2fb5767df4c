{"id": 188, "name": "Bilibili取关", "url": "https://space.bilibili.com/3494372528097534/fans/follow", "links": "https://space.bilibili.com/3494372528097534/fans/follow", "create_time": "7/18/2023, 12:40:07 AM", "update_time": "7/18/2023, 1:31:00 AM", "version": "0.3.6", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": true, "desc": "https://space.bilibili.com/3494372528097534/fans/follow", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://space.bilibili.com/3494372528097534/fans/follow", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://space.bilibili.com/3494372528097534/fans/follow"}, {"id": 1, "name": "loopTimes_循环_1", "nodeId": 2, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 1, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 2, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://space.bilibili.com/3494372528097534/fans/follow", "links": "https://space.bilibili.com/3494372528097534/fans/follow", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [13, 4, 3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"be-pager-next\")]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/ul[2]/li[7]", "//li[contains(., '下一页')]", "//LI[@class='be-pager-next']", "/html/body/div[last()-5]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul/li"]}}, {"id": 5, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/ul[2]/li[7]", "//li[contains(., '下一页')]", "//LI[@class='be-pager-next']", "/html/body/div[last()-5]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul/li"], "loopType": 0}}, {"id": 4, "index": 4, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [18, 14, 6, 5], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//div[contains(@class, 'fans-action')]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 9, "index": 5, "parentId": 4, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "//li[@class='be-dropdown-item'][2]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 1}}, {"id": 8, "index": 6, "parentId": 4, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[1]/div[2]/div[2]/div[2]/ul[1]/li[1]/div[3]/div[1]/div[1]", "//div[contains(., '已关注')]", "//DIV[@class='be-dropdown fans-action-btn fans-action-follow']", "/html/body/div[last()-4]/div[last()-1]/div/div/div/div[last()-4]/div/div/ul[last()-1]/li[last()-19]/div/div/div[last()-1]"]}}, {"id": -1, "index": 7, "parentId": 2, "type": 0, "option": "5", "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "codeMode": "5", "code": "self.a = 0", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": -1, "index": 8, "parentId": 4, "type": 2, "option": "9", "title": "判断条件", "sequence": [9], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": -1, "parentId": 6, "index": 9, "type": 3, "option": 10, "title": "条件分支", "sequence": [11], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": "8", "value": "", "code": "self.a %2 == 1", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 6, "index": 10, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 11, "parentId": 11, "type": 0, "option": "5", "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "codeMode": "4", "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": -1, "index": 12, "parentId": 4, "type": 0, "option": "5", "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "codeMode": "5", "code": "self.a += 1", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 3, "index": 13, "parentId": 2, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "codeMode": "5", "code": "self.a = 0", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 7, "index": 14, "parentId": 4, "type": 2, "option": 9, "title": "判断条件", "sequence": [15], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": 10, "parentId": 7, "index": 15, "type": 3, "option": 10, "title": "条件分支", "sequence": [17], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": "8", "value": "", "code": "self.a %2 == 0", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 7, "index": 16, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 11, "index": 17, "parentId": 10, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "codeMode": "4", "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 6, "index": 18, "parentId": 4, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "codeMode": "5", "code": "self.a += 1\nprint(\"a: \", self.a)", "waitTime": 0, "recordASField": 0, "paraType": "text"}}]}