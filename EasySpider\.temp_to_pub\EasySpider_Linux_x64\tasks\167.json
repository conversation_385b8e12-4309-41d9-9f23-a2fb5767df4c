{"id": 167, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "7/13/2023, 3:42:11 PM", "update_time": "7/13/2023, 3:52:01 PM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 5, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "Field[\"参数4_链接文本\"]", "value": "Field[\"参数4_链接文本\"]"}], "outputParameters": [{"id": 0, "name": "参数4_链接文本", "desc": "", "type": "text", "recordASField": 0, "exampleValue": "平板電腦"}, {"id": 1, "name": "参数2_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n        \n        \n          \n          \n            \n            \n              \n            \n            \n          \n        \n        \n          \n             ;0\n            我的购物车\n          \n          \n        \n        平板電腦爆款耳機手機數據線年貨節\n        \n        领券中心今日推荐\n      "}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 7], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [6], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '/手机/数码')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG ']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]"]}}, {"id": 4, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": false, "name": "参数4_链接文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[4]/div[1]/div[4]/a[1]", "allXPaths": ["/html/body/div[4]/div[1]/div[4]/a[1]", "//a[contains(., '平板電腦')]", "/html/body/div[last()-6]/div/div/a[last()-4]"], "exampleValues": [{"num": 0, "value": "平板電腦"}], "unique_index": "xjgrb8ksxwlk0u8zxj", "iframe": false, "default": "", "paraType": "text", "recordASField": 0, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": -1, "index": 4, "parentId": 2, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": ""}}, {"id": 5, "index": 5, "parentId": 2, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "Field[\"参数4_链接文本\"]", "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": -1, "index": 6, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]", "//div[contains(., 'HI～欢迎来到京东!')]", "//DIV[@class='welcome']", "/html/body/div[last()-5]/div/div[last()-4]/div/div/div/div[last()-6]/div/div"], "exampleValues": [{"num": 0, "value": "HI～欢迎来到京东!"}], "unique_index": "n3to5b3vx7lk0uimvc", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 2, "index": 7, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [8, 3, 5], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '/手机/数码')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG ']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]"]}}, {"id": 3, "index": 8, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数2_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[4]/div[1]", "allXPaths": ["/html/body/div[4]/div[1]", "//div[contains(., '')]", "//DIV[@class='w']", "/html/body/div[last()-6]/div"], "exampleValues": [{"num": 0, "value": "\n        \n        \n          \n          \n            \n            \n              \n            \n            \n          \n        \n        \n          \n             ;0\n            我的购物车\n          \n          \n        \n        平板電腦爆款耳機手機數據線年貨節\n        \n        领券中心今日推荐\n      "}], "unique_index": "208wnq8u1unlk0uni2b", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}