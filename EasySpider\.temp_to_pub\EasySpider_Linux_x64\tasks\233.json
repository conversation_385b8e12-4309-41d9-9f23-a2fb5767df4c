{"id": 233, "name": "Dynamic Iframe", "url": "https://easyspider.cn/test_pages/iframe.html", "links": "https://easyspider.cn/test_pages/iframe.html", "create_time": "12/8/2023, 5:20:59 AM", "update_time": "12/8/2023, 7:21:28 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "earth", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": true, "desc": "https://easyspider.cn/test_pages/iframe.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://easyspider.cn/test_pages/iframe.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://easyspider.cn/test_pages/iframe.html"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "12", "value": "12"}, {"id": 2, "name": "loopTimes_循环点击单个元素_2", "nodeId": 4, "nodeName": "循环点击单个元素", "desc": "循环循环点击单个元素执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}, {"id": 3, "name": "loopTimes_循环_3", "nodeId": 8, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 99, "value": 99}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "double", "recordASField": 1, "exampleValue": "3.7"}, {"id": 1, "name": "参数2_文本", "desc": "", "type": "datetime", "recordASField": 1, "exampleValue": "2023-11-3003:44:10"}, {"id": 2, "name": "参数3_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "37.53"}, {"id": 3, "name": "参数4_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "112.47"}, {"id": 4, "name": "参数5_文本", "desc": "", "type": "int", "recordASField": 1, "exampleValue": "28"}, {"id": 5, "name": "参数6_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "山西太原市清徐县"}, {"id": 6, "name": "参数7_链接地址", "desc": "", "type": "mediumText", "recordASField": 1, "exampleValue": "https://news.ceic.ac.cn/CC20231130034410.html"}, {"id": 7, "name": "参数8_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "震级(M)"}, {"id": 8, "name": "参数9_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "发震时刻(UTC+8)"}, {"id": 9, "name": "参数10_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "纬度(°)"}, {"id": 10, "name": "参数11_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "经度(°)"}, {"id": 11, "name": "参数12_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "深度(千米)"}, {"id": 12, "name": "参数13_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "参考位置"}, {"id": 13, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 14, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 8, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://easyspider.cn/test_pages/iframe.html", "links": "https://easyspider.cn/test_pages/iframe.html", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"weidu1\"]", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "value": "12", "index": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[2]/input[1]", "//input[contains(., '')]", "id(\"weidu1\")", "//INPUT[@class='span1']", "//INPUT[@name='weidu1']", "/html/body/div[last()-3]/div[last()-1]/div/div/div[last()-1]/form/div[last()-3]/input[last()-1]"]}}, {"id": 10, "index": 3, "parentId": 8, "type": 0, "option": 2, "title": "点击查询", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search\"]", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 1, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[5]/a[1]", "//a[contains(., '查询')]", "id(\"search\")", "//A[@class='check']", "/html/body/div[last()-3]/div[last()-1]/div/div/div[last()-1]/form/div/a"]}}, {"id": 4, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环点击单个元素", "sequence": [6, 5], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"pagination\")]/ul[1]/li[last()-1]/a[1]", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '»')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div/div/div/ul/li[last()-1]/a"]}}, {"id": 7, "index": 5, "parentId": 4, "type": 0, "option": 2, "title": "点击»", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 1, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '»')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div/div/div/ul/li[last()-1]/a"], "loopType": 0}}, {"id": 6, "index": 6, "parentId": 4, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [7], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr[1]", "//tr[contains(., '震级(M)发震时刻(')]", "//TR[@class='speed-tr-h1']", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]"]}}, {"id": 9, "index": 7, "parentId": 6, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/td[1]", "allXPaths": ["/td[1]", "//td[contains(., '3.7')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td[last()-5]"], "exampleValues": [{"num": 0, "value": "3.7"}], "unique_index": "/td[1]", "iframe": true, "default": "", "paraType": "double", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/td[2]", "allXPaths": ["/td[2]", "//td[contains(., '2023-11-30')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td[last()-4]"], "exampleValues": [{"num": 0, "value": "2023-11-3003:44:10"}], "unique_index": "/td[2]", "iframe": true, "default": "", "paraType": "datetime", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数3_文本", "desc": "", "relativeXPath": "/td[3]", "allXPaths": ["/td[3]", "//td[contains(., '37.53')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td[last()-3]"], "exampleValues": [{"num": 0, "value": "37.53"}], "unique_index": "/td[3]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/td[4]", "allXPaths": ["/td[4]", "//td[contains(., '112.47')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td[last()-2]"], "exampleValues": [{"num": 0, "value": "112.47"}], "unique_index": "/td[4]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/td[5]", "allXPaths": ["/td[5]", "//td[contains(., '28')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td[last()-1]"], "exampleValues": [{"num": 0, "value": "28"}], "unique_index": "/td[5]", "iframe": true, "default": "", "paraType": "int", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数6_链接文本", "desc": "", "relativeXPath": "/td[6]/a[1]", "allXPaths": ["/td[6]/a[1]", "//a[contains(., '山西太原市清徐县')]", "id(\"cid\")", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td/a"], "exampleValues": [{"num": 0, "value": "山西太原市清徐县"}], "unique_index": "/td[6]/a[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数7_链接地址", "desc": "", "relativeXPath": "/td[6]/a[1]", "allXPaths": ["/td[6]/a[1]", "//a[contains(., '山西太原市清徐县')]", "id(\"cid\")", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td/a"], "exampleValues": [{"num": 0, "value": "https://news.ceic.ac.cn/CC20231130034410.html"}], "unique_index": "/td[6]/a[1]", "iframe": true, "default": "", "paraType": "mediumText", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数8_文本", "desc": "", "relativeXPath": "/th[1]", "allXPaths": ["/th[1]", "//th[contains(., '震级(M)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-5]"], "exampleValues": [{"num": 1, "value": "震级(M)"}], "unique_index": "/th[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/th[2]", "allXPaths": ["/th[2]", "//th[contains(., '发震时刻(UTC+8')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-4]"], "exampleValues": [{"num": 1, "value": "发震时刻(UTC+8)"}], "unique_index": "/th[2]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/th[3]", "allXPaths": ["/th[3]", "//th[contains(., '纬度(°)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-3]"], "exampleValues": [{"num": 1, "value": "纬度(°)"}], "unique_index": "/th[3]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数11_文本", "desc": "", "relativeXPath": "/th[4]", "allXPaths": ["/th[4]", "//th[contains(., '经度(°)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-2]"], "exampleValues": [{"num": 1, "value": "经度(°)"}], "unique_index": "/th[4]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数12_文本", "desc": "", "relativeXPath": "/th[5]", "allXPaths": ["/th[5]", "//th[contains(., '深度(千米)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-1]"], "exampleValues": [{"num": 1, "value": "深度(千米)"}], "unique_index": "/th[5]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数13_文本", "desc": "", "relativeXPath": "/th[6]", "allXPaths": ["/th[6]", "//th[contains(., '参考位置')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th"], "exampleValues": [{"num": 1, "value": "参考位置"}], "unique_index": "/th[6]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 3, "index": 8, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [9], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "//body", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 99, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 5, "index": 9, "parentId": 3, "type": 2, "option": 9, "title": "判断条件", "sequence": [10], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": 8, "parentId": 5, "index": 10, "type": 3, "option": 10, "title": "条件分支1", "sequence": [3, 13, 12], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": "2", "value": "//*[@id=\"search1\"]", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 5, "index": 11, "type": 3, "option": 10, "title": "条件分支2", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 12, "index": 12, "parentId": 8, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": "3", "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 11, "index": 13, "parentId": 8, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": "6", "code": "print(\"EXIT LOOP\")", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"index": 14, "id": -1, "parentId": 5, "type": 3, "option": 10, "title": "条件分支2", "sequence": [15], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 15, "parentId": 9, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": "5", "code": "print(\"Cannot Find element, retrying……\")", "waitTime": 0, "recordASField": 0, "paraType": "text"}}]}