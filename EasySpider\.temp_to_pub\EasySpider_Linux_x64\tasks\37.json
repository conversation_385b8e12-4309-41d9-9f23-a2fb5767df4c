{"id": 37, "name": "Ebay T", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.ebay.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.ebay.com"}], "outputParameters": [{"id": 0, "name": "参数1_页面标题", "desc": "", "type": "string", "exampleValue": "Electronics, Cars, Fashion, Collectibles & More | eBay"}, {"id": 1, "name": "参数2_页面URL", "desc": "", "type": "string", "exampleValue": "https://www.ebay.com/"}, {"id": 2, "name": "自定义操作系统", "desc": "自定义操作返回的数据", "type": "string", "exampleValue": ""}, {"id": 3, "name": "自定义操作JS", "desc": "自定义操作返回的数据", "type": "string", "exampleValue": ""}, {"id": 4, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 5, 6, 7], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 2, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "scrollType": 0, "scrollCount": 0}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "params": [{"nodeType": 0, "contentType": 6, "relative": false, "name": "参数1_页面标题", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[6]/div[5]/ul[1]/li[5]/a[1]/div[2]", "exampleValues": [{"num": 0, "value": "Electronics, Cars, Fashion, Collectibles & More | eBay"}], "default": ""}, {"nodeType": 0, "contentType": 5, "relative": false, "name": "参数2_页面URL", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[6]/div[5]/ul[1]/li[5]/a[1]/div[2]", "exampleValues": [{"num": 0, "value": "https://www.ebay.com/"}]}]}}, {"id": -1, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[6]/div[5]/ul[1]/li/a[1]/div[1]/div[1]", "wait": 0, "scrollType": 0, "scrollCount": 0, "loopType": 1, "pathList": "", "textList": "", "exitCount": 0, "historyWait": 2}}, {"id": -1, "index": 4, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "params": [{"nodeType": 0, "contentType": 4, "relative": true, "name": "参数3_文本", "desc": "", "extractType": 0, "relativeXPath": "", "exampleValues": [{"num": 0, "value": ""}, {"num": 1, "value": ""}, {"num": 2, "value": ""}, {"num": 3, "value": ""}, {"num": 4, "value": ""}, {"num": 5, "value": ""}, {"num": 6, "value": ""}], "default": ""}], "loopType": 1}}, {"id": 3, "index": 5, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作系统", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "codeMode": "1", "code": "python D:/test.py", "waitTime": 0, "recordASField": true}}, {"id": 4, "index": 6, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作JS", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "codeMode": 0, "code": "return document.getElementsByClassName(\"clk\").length == 1", "waitTime": 10, "recordASField": true}}, {"id": 5, "index": 7, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [8], "isInLoop": false, "position": 4, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div/ul[1]/li/a[1]/div[1]/div[1]", "wait": 0, "scrollType": 0, "scrollCount": 0, "loopType": 1, "pathList": "", "textList": "", "exitCount": 0, "historyWait": 2}}, {"id": 6, "index": 8, "parentId": 5, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "params": [{"nodeType": 0, "contentType": 4, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "exampleValues": [{"num": 0, "value": ""}, {"num": 1, "value": ""}, {"num": 2, "value": ""}, {"num": 3, "value": ""}, {"num": 4, "value": ""}, {"num": 5, "value": ""}, {"num": 6, "value": ""}], "default": ""}]}}, {"id": -1, "index": 9, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "value": ""}}, {"id": -1, "index": 10, "parentId": 5, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "value": ""}}, {"id": -1, "index": 11, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "codeMode": 0, "code": "", "waitTime": 0, "recordASField": true}}, {"id": -1, "index": 12, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作1", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "codeMode": 0, "code": "", "waitTime": 0, "recordASField": true}}]}