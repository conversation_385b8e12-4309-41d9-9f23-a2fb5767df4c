Official Site: https://www.easyspider.net

Welcome to promote this software to other friends and star our Github Repository!

This version is for MacOS, can be used on all Chips, including Intel (such as Corel i7) and Arm (such as M1). Support on MacOS 11.x and above.

If your MacOS version is 10.x and below, please download EasySpider V0.2.0.

The software's open-source code repository on GitHub: https://github.com/NaiboWang/EasySpider

Official documentation can be found at: https://github.com/NaiboWang/EasySpider/wiki

Video Tutorial: https://youtube.com/playlist?list=PL0kEFEkWrT7mt9MUlEBV2DTo1QsaanUTp

You can import tasks from other machines by simply opening the EasySpider software in this directory, right-clicking "Show Package Contents", and then placing the .json files from the tasks folder in the /Users/<USER>/Library/Application Support/EasySpider/tasks folder of the other machine. Similarly, execution ID files can be imported by copying the .json files from the execution_instances folder. Please note that the .json files in both folders only support names greater than 0.

You can quickly navigate to the tasks folder using the following commands:

cd /Users/<USER>/Library/Application\ Support/EasySpider/tasks
open .

If you need to press p one the keyboard to pause and continue the execution of the task, you need to grant the program keyboard monitoring permission.
