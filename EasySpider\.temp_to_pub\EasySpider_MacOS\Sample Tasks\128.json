{"id": 128, "name": "Dynamic Iframe - jd", "url": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://jd.com", "links": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://jd.com", "create_time": "7/5/2023, 5:11:04 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://jd.com"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 18], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 3, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://jd.com", "links": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[10]/a[2]", "iframe": true, "wait": 0.4, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[10]/a[2]", "//a[contains(., '洗护喂养')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-3]/a"]}}, {"id": -1, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4], "isInLoop": false, "position": 2, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 4, "parentId": 3, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "iframe": true, "wait": 0.1, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": "", "loopType": 1}}, {"id": -1, "index": 5, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": true, "wait": 1, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "iPhone", "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": -1, "index": 6, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-btn\"]/i[1]", "iframe": true, "wait": 10, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/button[1]/i[1]", "//i[contains(., '')]", "/html/body/div[last()-6]/div/div[last()-2]/div/button/i"]}}, {"id": -1, "index": 7, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 5, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "iPhone", "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": -1, "index": 8, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 5, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-btn\"]/i[1]", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/button[1]/i[1]", "//i[contains(., '')]", "/html/body/div[last()-6]/div/div[last()-2]/div/button/i"]}}, {"id": -1, "index": 9, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-link\"]/i[1]", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/a[1]/i[1]", "//i[contains(., '')]", "/html/body/div[last()-6]/div/div[last()-2]/div/a/i"]}}, {"id": -1, "index": 10, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "i", "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": -1, "index": 11, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-btn\"]/i[1]", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/button[1]/i[1]", "//i[contains(., '')]", "/html/body/div[last()-6]/div/div[last()-2]/div/button/i"]}}, {"id": -1, "index": 12, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "1", "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-7]/div/div[last()-2]/div/input"]}}, {"id": -1, "index": 13, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-btn\"]/i[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/button[1]/i[1]", "//i[contains(., '')]", "/html/body/div[last()-7]/div/div[last()-2]/div/button/i"]}}, {"id": -1, "index": 14, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "1", "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": -1, "index": 15, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-btn\"]/i[1]", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": "2", "scrollCount": 2, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/button[1]/i[1]", "//i[contains(., '')]", "/html/body/div[last()-6]/div/div[last()-2]/div/button/i"]}}, {"id": -1, "index": 16, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [17], "isInLoop": false, "position": 1, "parameters": {"history": 6, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li/div[1]", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='gl-i-wrap']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div"]}}, {"id": -1, "index": 17, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 6, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/div[1]/a[1]", "allXPaths": ["/div[1]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t\n\t\t\t\t        \t"}], "unique_index": "/div[1]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/div[1]/a[1]", "allXPaths": ["/div[1]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10067439099522.html"}], "unique_index": "/div[1]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数3_图片地址", "desc": "", "relativeXPath": "/div[1]/a[1]/img[1]", "allXPaths": ["/div[1]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/a/img"], "exampleValues": [{"num": 0, "value": "//img12.360buyimg.com/n7/jfs/t1/109789/26/27069/100701/64a29c89F4b5d42ee/268b40ebc376eba1.jpg.avif"}], "unique_index": "/div[1]/a[1]/img[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/div[2]/strong[1]/em[1]", "allXPaths": ["/div[2]/strong[1]/em[1]", "//em[contains(., '￥')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-6]/strong/em"], "exampleValues": [{"num": 0, "value": "￥"}], "unique_index": "/div[2]/strong[1]/em[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/div[2]/strong[1]/i[1]", "allXPaths": ["/div[2]/strong[1]/i[1]", "//i[contains(., '2799.00')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-6]/strong/i"], "exampleValues": [{"num": 0, "value": "2799.00"}], "unique_index": "/div[2]/strong[1]/i[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数6_链接文本", "desc": "", "relativeXPath": "/div[3]/a[1]", "allXPaths": ["/div[3]/a[1]", "//a[contains(., '格')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t格力（GREE）空调 云佳 大1匹新一级能效 变频冷暖 自清洁 卧室壁挂式空调挂机 KFR-26GW/NhGd1B(含管) 顶(皓雪白\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t"}], "unique_index": "/div[3]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数7_链接地址", "desc": "", "relativeXPath": "/div[3]/a[1]", "allXPaths": ["/div[3]/a[1]", "//a[contains(., '格')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10067439099522.html"}], "unique_index": "/div[3]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数8_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]", "allXPaths": ["/div[3]/a[1]/em[1]", "//em[contains(., '格力（GREE）空调')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a/em"], "exampleValues": [{"num": 0, "value": "格力（GREE）空调云佳大匹新一级能效变频冷暖自清洁卧室壁挂式空调挂机KFR-26GW/NhGdB(含管)顶(皓雪白"}], "unique_index": "/div[3]/a[1]/em[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/font[1]", "allXPaths": ["/div[3]/a[1]/em[1]/font[1]", "//font[contains(., '1')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a/em/font[last()-1]"], "exampleValues": [{"num": 0, "value": "1"}], "unique_index": "/div[3]/a[1]/em[1]/font[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/font[2]", "allXPaths": ["/div[3]/a[1]/em[1]/font[2]", "//font[contains(., '1')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a/em/font"], "exampleValues": [{"num": 0, "value": "1"}], "unique_index": "/div[3]/a[1]/em[1]/font[2]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数11_文本", "desc": "", "relativeXPath": "/div[4]/strong[1]", "allXPaths": ["/div[4]/strong[1]", "//strong[contains(., '2万+条评价')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong"], "exampleValues": [{"num": 0, "value": "条评价"}], "unique_index": "/div[4]/strong[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数12_链接文本", "desc": "", "relativeXPath": "/div[4]/strong[1]/a[1]", "allXPaths": ["/div[4]/strong[1]/a[1]", "//a[contains(., '2万+')]", "id(\"J_comment_10067439099522\")", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong/a"], "exampleValues": [{"num": 0, "value": "2万+"}], "unique_index": "/div[4]/strong[1]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数13_链接地址", "desc": "", "relativeXPath": "/div[4]/strong[1]/a[1]", "allXPaths": ["/div[4]/strong[1]/a[1]", "//a[contains(., '2万+')]", "id(\"J_comment_10067439099522\")", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10067439099522.html#comment"}], "unique_index": "/div[4]/strong[1]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数14_链接文本", "desc": "", "relativeXPath": "/div[5]/span[1]/a[1]", "allXPaths": ["/div[5]/span[1]/a[1]", "//a[contains(., '格力官方旗舰店')]", "//A[@class='curr-shop hd-shopname']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-3]/span/a"], "exampleValues": [{"num": 0, "value": "格力官方旗舰店"}], "unique_index": "/div[5]/span[1]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数15_链接地址", "desc": "", "relativeXPath": "/div[5]/span[1]/a[1]", "allXPaths": ["/div[5]/span[1]/a[1]", "//a[contains(., '格力官方旗舰店')]", "//A[@class='curr-shop hd-shopname']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-3]/span/a"], "exampleValues": [{"num": 0, "value": "//mall.jd.com/index-708522.html?from=pc"}], "unique_index": "/div[5]/span[1]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数16_链接文本", "desc": "", "relativeXPath": "/div[7]/a[1]", "allXPaths": ["/div[7]/a[1]", "//a[contains(., '对比')]", "//A[@class='p-o-btn contrast J_contrast contrast']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-2]"], "exampleValues": [{"num": 0, "value": "对比"}], "unique_index": "/div[7]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数17_链接地址", "desc": "", "relativeXPath": "/div[7]/a[1]", "allXPaths": ["/div[7]/a[1]", "//a[contains(., '对比')]", "//A[@class='p-o-btn contrast J_contrast contrast']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-2]"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[7]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数18_链接文本", "desc": "", "relativeXPath": "/div[7]/a[2]", "allXPaths": ["/div[7]/a[2]", "//a[contains(., '关注')]", "//A[@class='p-o-btn focus  J_focus']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "关注"}], "unique_index": "/div[7]/a[2]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数19_链接地址", "desc": "", "relativeXPath": "/div[7]/a[2]", "allXPaths": ["/div[7]/a[2]", "//a[contains(., '关注')]", "//A[@class='p-o-btn focus  J_focus']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[7]/a[2]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数20_链接文本", "desc": "", "relativeXPath": "/div[7]/a[3]", "allXPaths": ["/div[7]/a[3]", "//a[contains(., '加入购物车')]", "//A[@class='p-o-btn addcart']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a"], "exampleValues": [{"num": 0, "value": "加入购物车"}], "unique_index": "/div[7]/a[3]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数21_链接地址", "desc": "", "relativeXPath": "/div[7]/a[3]", "allXPaths": ["/div[7]/a[3]", "//a[contains(., '加入购物车')]", "//A[@class='p-o-btn addcart']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a"], "exampleValues": [{"num": 0, "value": "//cart.jd.com/gate.action?pid=10067439099522&pcount=1&ptype=1"}], "unique_index": "/div[7]/a[3]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数22_文本", "desc": "", "relativeXPath": "/span[1]", "allXPaths": ["/span[1]", "//span[contains(., '广告')]", "//SPAN[@class='p-promo-flag']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/span"], "exampleValues": [{"num": 0, "value": "广告"}], "unique_index": "/span[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数23_图片地址", "desc": "", "relativeXPath": "/img[1]", "allXPaths": ["/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/img"], "exampleValues": [{"num": 0, "value": ""}], "unique_index": "/img[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数24_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/i[1]", "allXPaths": ["/div[3]/a[1]/i[1]", "//i[contains(., '【自营】【365天只')]", "id(\"J_AD_100050943080\")", "//I[@class='promo-words']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div[last()-5]/a/i"], "exampleValues": [{"num": 1, "value": "【自营】【365天只换不修】2.4英寸大屏，大字体大声音，锌合金边框、来电报名字读短信、9个亲情号码、一键双灯手电筒、超长待机、精准定位、移动/电信/联通4G"}], "unique_index": "/div[3]/a[1]/i[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数25_文本", "desc": "", "relativeXPath": "/div[6]/i[1]", "allXPaths": ["/div[6]/i[1]", "//i[contains(., '自营')]", "//I[@class='goods-icons J-picon-tips J-picon-fix']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div[last()-2]/i[last()-2]"], "exampleValues": [{"num": 1, "value": "自营"}], "unique_index": "/div[6]/i[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数26_文本", "desc": "", "relativeXPath": "/div[6]/i[2]", "allXPaths": ["/div[6]/i[2]", "//i[contains(., '放心购')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div[last()-2]/i[last()-1]"], "exampleValues": [{"num": 1, "value": "放心购"}], "unique_index": "/div[6]/i[2]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数27_文本", "desc": "", "relativeXPath": "/div[6]/i[3]", "allXPaths": ["/div[6]/i[3]", "//i[contains(., '新品')]", "//I[@class='goods-icons3 J-picon-tips J-picon-fix']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div[last()-2]/i"], "exampleValues": [{"num": 1, "value": "新品"}], "unique_index": "/div[6]/i[3]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数28_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/font[3]", "allXPaths": ["/div[3]/a[1]/em[1]/font[3]", "//font[contains(., '1')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-25]/div/div[last()-6]/a/em/font[last()-3]"], "exampleValues": [{"num": 4, "value": "1"}], "unique_index": "/div[3]/a[1]/em[1]/font[3]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数29_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/font[4]", "allXPaths": ["/div[3]/a[1]/em[1]/font[4]", "//font[contains(., '1')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-25]/div/div[last()-6]/a/em/font[last()-2]"], "exampleValues": [{"num": 4, "value": "1"}], "unique_index": "/div[3]/a[1]/em[1]/font[4]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数30_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/font[5]", "allXPaths": ["/div[3]/a[1]/em[1]/font[5]", "//font[contains(., '1')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-25]/div/div[last()-6]/a/em/font[last()-1]"], "exampleValues": [{"num": 4, "value": "1"}], "unique_index": "/div[3]/a[1]/em[1]/font[5]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数31_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/font[6]", "allXPaths": ["/div[3]/a[1]/em[1]/font[6]", "//font[contains(., '1')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-25]/div/div[last()-6]/a/em/font"], "exampleValues": [{"num": 4, "value": "1"}], "unique_index": "/div[3]/a[1]/em[1]/font[6]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数32_文本", "desc": "", "relativeXPath": "/div[9]/span[1]", "allXPaths": ["/div[9]/span[1]", "//span[contains(., '抢购中')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-25]/div/div/span"], "exampleValues": [{"num": 4, "value": "抢购中"}], "unique_index": "/div[9]/span[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数33_文本", "desc": "", "relativeXPath": "/div[9]/em[1]", "allXPaths": ["/div[9]/em[1]", "//em[contains(., '剩余19时05分44')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-25]/div/div/em"], "exampleValues": [{"num": 4, "value": "剩余19时05分44秒"}], "unique_index": "/div[9]/em[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数34_文本", "desc": "", "relativeXPath": "/div[6]/i[4]", "allXPaths": ["/div[6]/i[4]", "//i[contains(., '券200-20')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-20]/div/div[last()-2]/i"], "exampleValues": [{"num": 9, "value": "券200-20"}], "unique_index": "/div[6]/i[4]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数35_链接文本", "desc": "", "relativeXPath": "/div[4]/a[1]", "allXPaths": ["/div[4]/a[1]", "//a[contains(., '去看二手')]", "//A[@class='spu-link']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-16]/div/div[last()-4]/a"], "exampleValues": [{"num": 13, "value": "去看二手"}], "unique_index": "/div[4]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数36_链接地址", "desc": "", "relativeXPath": "/div[4]/a[1]", "allXPaths": ["/div[4]/a[1]", "//a[contains(., '去看二手')]", "//A[@class='spu-link']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-16]/div/div[last()-4]/a"], "exampleValues": [{"num": 13, "value": "//paipai.jd.com/pc/list.html?pid=100049916305"}], "unique_index": "/div[4]/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数37_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/font[7]", "allXPaths": ["/div[3]/a[1]/em[1]/font[7]", "//font[contains(., '1')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-15]/div/div[last()-6]/a/em/font"], "exampleValues": [{"num": 14, "value": "1"}], "unique_index": "/div[3]/a[1]/em[1]/font[7]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数38_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/span[1]", "allXPaths": ["/div[3]/a[1]/em[1]/span[1]", "//span[contains(., '京品电脑')]", "//SPAN[@class='p-tag']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-14]/div/div[last()-6]/a/em/span"], "exampleValues": [{"num": 15, "value": "京品电脑"}], "unique_index": "/div[3]/a[1]/em[1]/span[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数39_文本", "desc": "", "relativeXPath": "/div[8]", "allXPaths": ["/div[8]", "//div[contains(., '海外')]", "//DIV[@class='p-stock']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-10]/div/div[last()-1]"], "exampleValues": [{"num": 19, "value": "海外无货"}], "unique_index": "/div[8]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 2, "index": 18, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [19], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div/a", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 3, "index": 19, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div/a", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ""}}]}