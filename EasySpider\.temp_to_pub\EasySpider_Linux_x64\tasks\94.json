{"id": 94, "name": "采集超链接 最新汽车资讯_资讯_懂车帝", "url": "https://www.dongchedi.com/news", "links": "https://www.dongchedi.com/news", "create_time": "5/31/2023, 11:20:35 PM", "version": "0.3.2", "containJudge": false, "desc": "https://www.dongchedi.com/news", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.dongchedi.com/news", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.dongchedi.com/news"}, {"id": 1, "name": "loopTimes_循环点击下一页_1", "nodeId": 2, "nodeName": "循环点击下一页", "desc": "循环循环点击下一页执行的次数（0代表无限循环）", "type": "int", "exampleValue": 5, "value": 5}], "outputParameters": [{"id": 0, "name": "参数1_链接文本", "desc": "", "type": "string", "exampleValue": "搭载理想魔毯空气悬架，理想L7如何？"}, {"id": 1, "name": "参数2_链接地址", "desc": "", "type": "string", "exampleValue": "/article/7239351600723034685"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 5, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.dongchedi.com/news", "links": "https://www.dongchedi.com/news", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环点击下一页", "sequence": [6, 3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"tw-mb-40\")]/ul[1]/li[last()]/a[1]/span[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 5, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[2]/div[2]/div[1]/div[2]/ul[1]/li[8]/a[1]/span[1]", "//span[contains(., '')]", "//SPAN[@class='jsx-2727778437 tw-inline-block tw-px-6 tw-h-28 tw-text-14 tw-leading-28 tw-text-center tw-text-black tw-cursor-pointer']"]}}, {"id": 4, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击下一页", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "//*[contains(@class, \"tw-mb-40\")]/ul[1]/li[8]/a[1]/span[1]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[1]/div[2]/div[2]/div[1]/div[2]/ul[1]/li[8]/a[1]/span[1]", "//span[contains(., '')]", "//SPAN[@class='jsx-2727778437 tw-inline-block tw-px-6 tw-h-28 tw-text-14 tw-leading-28 tw-text-center tw-text-black tw-cursor-pointer']"], "loopType": 0}}, {"id": -1, "index": 4, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div/div[1]/h3[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/h3[1]", "//h3[contains(., '搭载理想魔毯空气悬架')]", "//H3[@class='tw-leading-22 tw-text-16 tw-font-medium tw-text-common-black tw-text-justify line-2 g-active-link-text tw-mt-36']"]}}, {"id": -1, "index": 5, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "搭载理想魔毯空气悬架，理想L7如何？"}, {"num": 1, "value": "关注 | 鸿海集团与英伟达合作研发自动驾驶，同步布局“CityGPT”"}, {"num": 2, "value": "特写 | 造车9年，贾跃亭 “为梦想窒息”踩了多少坑？"}, {"num": 3, "value": "性能车迷的狂欢！领克03++来了，大男孩的六一礼物"}, {"num": 4, "value": "2023道奇挑战者，外观和内饰细节"}, {"num": 5, "value": "欧美需求迫切，充电桩：需求与供给共振，全球充电桩建设加速"}, {"num": 6, "value": "赵长江放话：腾势N7下个月交付，9月破万！"}, {"num": 7, "value": "迪拜街头的跑车GT RS、TTRS、M3、宝马XM、GPowerM3"}, {"num": 8, "value": "吉利亮出13.87万起售价，比亚迪宋拿什么和银河L7比？"}, {"num": 9, "value": "贾跃亭发布会造了哪些新词？快来学学"}, {"num": 10, "value": "采用悬浮式双联屏设计，理想L7如何？"}, {"num": 11, "value": "采用悬浮式双联屏设计，理想L7如何？"}, {"num": 12, "value": "每天车闻：长城汽车品牌公关总经理果铁夫离职"}, {"num": 13, "value": "采用悬浮式双联屏设计，理想L7如何？"}, {"num": 14, "value": "不是合资不行，而是国产拿出了真本事，揭开理想L7月销过万的秘密"}, {"num": 15, "value": "采用悬浮式双联屏设计，理想L7如何？"}, {"num": 16, "value": "“宋”标识消失？比亚迪宋PLUS DM-i由内到外海洋味十足！"}, {"num": 17, "value": "2023奥迪Q5 内部和外部细节"}, {"num": 18, "value": "采用悬浮式双联屏设计，理想L7如何？"}, {"num": 19, "value": "比亚迪元Pro，续航超400km，顶配不到12万！"}, {"num": 20, "value": "8万你能买到啥样的SUV？颜值、配置都在线的哈弗赤兔了解一下"}, {"num": 21, "value": "英飞凌推出新型汽车功率模块HybridPACK Drive G2"}, {"num": 22, "value": "魏牌蓝山DHT-PHEV，功力扎实，续航靠谱！"}, {"num": 23, "value": "新手买车必看 关于新车年检那点事"}, {"num": 24, "value": "全新英仕派官图曝光，外观内饰大变样，还能反超雅阁？"}, {"num": 25, "value": "新能源车解读：哈弗枭龙，外观设计很时尚，我们一起了解下"}, {"num": 26, "value": "年轻人买轿跑看看这款，2.0T+9AT，掀背造型，无框车门，名爵MG7"}, {"num": 27, "value": "或对标Taycan，3秒内破百，路特斯四座轿跑Type 133曝光！"}, {"num": 28, "value": "外观张扬个性，内饰温馨，搭载2.0T发动机，MG7实力解析"}, {"num": 29, "value": "CLTC综合续航里程达1315公里，理想L7值得一试吗？"}, {"num": 30, "value": "何时换刹车片！讲一些概念！"}, {"num": 31, "value": "2023款马自达2官图发布，个性外观更讨人喜爱，为何不被看好？"}, {"num": 32, "value": "CLTC综合续航里程达1315公里，理想L7值得一试吗？"}, {"num": 33, "value": "地表最强会计贾老板化身带货大哥，FF 91三款车型，超豪版剑指宾利"}, {"num": 34, "value": "雅科仕车灯升级方案#汽车灯光升级 #激光大灯 #led双光透镜"}, {"num": 35, "value": "探店实拍：2023款哈弗枭龙，外观设计很时尚，我们一起了解下"}, {"num": 36, "value": "订单超160万，9月初步生产，特斯拉Cybertruck内饰做调整！"}, {"num": 37, "value": "奥迪A8：低调的旗舰，55TFSI最高降24.62万，实力如何？"}, {"num": 38, "value": "2023丰田红杉TRD外观和内饰细节"}, {"num": 39, "value": "2023路虎揽胜运动版，外观和内饰细节"}, {"num": 40, "value": "可靠又忠实的全新汉兰达 果然不会让你失望"}, {"num": 41, "value": "2023梅赛德斯迈巴赫GLS600内饰和外观细节"}, {"num": 42, "value": "搭载海神发动机，玛莎拉蒂MC20静态展示"}, {"num": 43, "value": "张国宇在昆仑天路上的拉力驾控 2023环塔拉力赛SS9 #现场实拍"}, {"num": 44, "value": "打响品牌第一炮 吉利银河L7正式上市"}, {"num": 45, "value": "邀请才能购买 全新揽胜运动版SV发布"}, {"num": 46, "value": "油电同价！新款比亚迪元Pro上市，9.58万元起售"}, {"num": 47, "value": "配1.5T+7DCT动力系统，博越L 年轻人的第一台SUV"}], "unique_index": "09w0shu1i6qhlibuiybj", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 3, "index": 6, "parentId": 2, "type": 1, "option": 8, "title": "循环采集超链接", "sequence": [7], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div/div[1]/h3[1]/a[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/h3[1]/a[1]", "//a[contains(., '搭载理想魔毯空气悬架')]", "//A[@class='jsx-1095092229']"]}}, {"id": 5, "index": 7, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "搭载理想魔毯空气悬架，理想L7如何？"}, {"num": 1, "value": "关注 | 鸿海集团与英伟达合作研发自动驾驶，同步布局“CityGPT”"}, {"num": 2, "value": "特写 | 造车9年，贾跃亭 “为梦想窒息”踩了多少坑？"}, {"num": 3, "value": "性能车迷的狂欢！领克03++来了，大男孩的六一礼物"}, {"num": 4, "value": "2023道奇挑战者，外观和内饰细节"}, {"num": 5, "value": "欧美需求迫切，充电桩：需求与供给共振，全球充电桩建设加速"}, {"num": 6, "value": "赵长江放话：腾势N7下个月交付，9月破万！"}, {"num": 7, "value": "迪拜街头的跑车GT RS、TTRS、M3、宝马XM、GPowerM3"}, {"num": 8, "value": "吉利亮出13.87万起售价，比亚迪宋拿什么和银河L7比？"}, {"num": 9, "value": "贾跃亭发布会造了哪些新词？快来学学"}, {"num": 10, "value": "采用悬浮式双联屏设计，理想L7如何？"}, {"num": 11, "value": "采用悬浮式双联屏设计，理想L7如何？"}, {"num": 12, "value": "每天车闻：长城汽车品牌公关总经理果铁夫离职"}, {"num": 13, "value": "采用悬浮式双联屏设计，理想L7如何？"}, {"num": 14, "value": "不是合资不行，而是国产拿出了真本事，揭开理想L7月销过万的秘密"}, {"num": 15, "value": "采用悬浮式双联屏设计，理想L7如何？"}, {"num": 16, "value": "“宋”标识消失？比亚迪宋PLUS DM-i由内到外海洋味十足！"}, {"num": 17, "value": "2023奥迪Q5 内部和外部细节"}, {"num": 18, "value": "采用悬浮式双联屏设计，理想L7如何？"}, {"num": 19, "value": "比亚迪元Pro，续航超400km，顶配不到12万！"}, {"num": 20, "value": "8万你能买到啥样的SUV？颜值、配置都在线的哈弗赤兔了解一下"}, {"num": 21, "value": "英飞凌推出新型汽车功率模块HybridPACK Drive G2"}, {"num": 22, "value": "魏牌蓝山DHT-PHEV，功力扎实，续航靠谱！"}, {"num": 23, "value": "新手买车必看 关于新车年检那点事"}, {"num": 24, "value": "全新英仕派官图曝光，外观内饰大变样，还能反超雅阁？"}, {"num": 25, "value": "新能源车解读：哈弗枭龙，外观设计很时尚，我们一起了解下"}, {"num": 26, "value": "年轻人买轿跑看看这款，2.0T+9AT，掀背造型，无框车门，名爵MG7"}, {"num": 27, "value": "或对标Taycan，3秒内破百，路特斯四座轿跑Type 133曝光！"}, {"num": 28, "value": "外观张扬个性，内饰温馨，搭载2.0T发动机，MG7实力解析"}, {"num": 29, "value": "CLTC综合续航里程达1315公里，理想L7值得一试吗？"}, {"num": 30, "value": "何时换刹车片！讲一些概念！"}, {"num": 31, "value": "2023款马自达2官图发布，个性外观更讨人喜爱，为何不被看好？"}, {"num": 32, "value": "CLTC综合续航里程达1315公里，理想L7值得一试吗？"}, {"num": 33, "value": "地表最强会计贾老板化身带货大哥，FF 91三款车型，超豪版剑指宾利"}, {"num": 34, "value": "雅科仕车灯升级方案#汽车灯光升级 #激光大灯 #led双光透镜"}, {"num": 35, "value": "探店实拍：2023款哈弗枭龙，外观设计很时尚，我们一起了解下"}, {"num": 36, "value": "订单超160万，9月初步生产，特斯拉Cybertruck内饰做调整！"}, {"num": 37, "value": "奥迪A8：低调的旗舰，55TFSI最高降24.62万，实力如何？"}, {"num": 38, "value": "2023丰田红杉TRD外观和内饰细节"}, {"num": 39, "value": "2023路虎揽胜运动版，外观和内饰细节"}, {"num": 40, "value": "可靠又忠实的全新汉兰达 果然不会让你失望"}, {"num": 41, "value": "2023梅赛德斯迈巴赫GLS600内饰和外观细节"}, {"num": 42, "value": "搭载海神发动机，玛莎拉蒂MC20静态展示"}, {"num": 43, "value": "张国宇在昆仑天路上的拉力驾控 2023环塔拉力赛SS9 #现场实拍"}, {"num": 44, "value": "打响品牌第一炮 吉利银河L7正式上市"}, {"num": 45, "value": "邀请才能购买 全新揽胜运动版SV发布"}, {"num": 46, "value": "油电同价！新款比亚迪元Pro上市，9.58万元起售"}, {"num": 47, "value": "配1.5T+7DCT动力系统，博越L 年轻人的第一台SUV"}], "unique_index": "mq4wp38yy3libujx5l", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "/article/7239351600723034685"}, {"num": 1, "value": "/article/7239190275762143759"}, {"num": 2, "value": "/article/7239243337478210083"}, {"num": 3, "value": "/article/7239351123465880124"}, {"num": 4, "value": "/video/7239351149319422519"}, {"num": 5, "value": "/article/7239347840563593785"}, {"num": 6, "value": "/article/7239350618027606587"}, {"num": 7, "value": "/video/7239350556396159547"}, {"num": 8, "value": "/video/7239350544463528482"}, {"num": 9, "value": "/article/7239350477736165903"}, {"num": 10, "value": "/article/7239350430403314236"}, {"num": 11, "value": "/article/7239350242003436087"}, {"num": 12, "value": "/article/7239350169022726693"}, {"num": 13, "value": "/article/7239350112911524389"}, {"num": 14, "value": "/article/7239350034389975589"}, {"num": 15, "value": "/article/7239349952873628215"}, {"num": 16, "value": "/article/7239349941448049207"}, {"num": 17, "value": "/video/7239349804126929467"}, {"num": 18, "value": "/article/7239349600614695484"}, {"num": 19, "value": "/article/7239349525557723706"}, {"num": 20, "value": "/video/7239349464715461132"}, {"num": 21, "value": "/article/7239349097495757347"}, {"num": 22, "value": "/video/7239348973697008165"}, {"num": 23, "value": "/article/7239348738178023997"}, {"num": 24, "value": "/article/7239348733501047351"}, {"num": 25, "value": "/video/7239348726589588026"}, {"num": 26, "value": "/article/7239348603435909692"}, {"num": 27, "value": "/article/7239348561648632375"}, {"num": 28, "value": "/article/7239348349961306682"}, {"num": 29, "value": "/article/7239348226103968293"}, {"num": 30, "value": "/video/7239348239516992033"}, {"num": 31, "value": "/article/7239348159682888250"}, {"num": 32, "value": "/article/7239348021199520315"}, {"num": 33, "value": "/article/7239336338443469367"}, {"num": 34, "value": "/video/7239347918296384037"}, {"num": 35, "value": "/video/7239347888512795197"}, {"num": 36, "value": "/article/7239347762084332088"}, {"num": 37, "value": "/article/7239347659751359033"}, {"num": 38, "value": "/video/7239347684740432443"}, {"num": 39, "value": "/video/7239347642323436093"}, {"num": 40, "value": "/article/7239347545674121789"}, {"num": 41, "value": "/video/7239347395262153275"}, {"num": 42, "value": "/video/7239347384483119673"}, {"num": 43, "value": "/video/7239347296012665376"}, {"num": 44, "value": "/article/7239347163661566520"}, {"num": 45, "value": "/article/7239346432161956410"}, {"num": 46, "value": "/article/7239347064935563787"}, {"num": 47, "value": "/article/7239346787894067773"}], "unique_index": "mq4wp38yy3libujx5l", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}