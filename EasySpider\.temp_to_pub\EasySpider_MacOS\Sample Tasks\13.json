{"id": 13, "name": "Electronics, Cars, Fashion, Collectibles & More | eBay", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "create_time": "5/25/2023, 10:30:20 PM", "version": "0.3.1", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "Open Page", "value": "https://www.ebay.com", "desc": "List of URLs to be collected, separated by \\n for multiple lines", "type": "string", "exampleValue": "https://www.ebay.com"}, {"id": 1, "name": "inputText_1", "nodeName": "Input Text", "nodeId": 2, "desc": "The text to be entered, such as 'computer' at eBay search box", "type": "string", "exampleValue": "iPhone", "value": "iPhone"}, {"id": 2, "name": "loopTimes_Loop_2", "nodeId": 4, "nodeName": "Loop", "desc": "Number of loop executions, 0 means unlimited loops (until element not found)", "type": "int", "exampleValue": 5, "value": 5}], "outputParameters": [{"id": 0, "name": "para1_text", "desc": "", "type": "string", "exampleValue": "Apple IPhone 1st Generation-8GB-Black Unlocked 2G A1203 (GSM) veryGOOD condition"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "Input Text", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"gh-ac\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "iPhone", "allXPaths": ["/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[5]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/div[1]/input[1]", "//input[contains(., '')]", "id(\"gh-ac\")", "//INPUT[@class='gh-tb ui-autocomplete-input ui-autocomplete-loading']", "//INPUT[@name='_nkw']"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "<PERSON>lick Element", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"gh-btn\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[5]/form[1]/table[1]/tbody[1]/tr[1]/td[3]/input[1]", "//input[contains(., '')]", "id(\"gh-btn\")", "//INPUT[@class='btn btn-prim gh-spr']"]}}, {"id": 4, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "Loop", "sequence": [6, 5], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"pagination__next\")]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": "2", "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 5, "historyWait": 2, "breakMode": "1", "breakCode": "return window.innerHeight > 500", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[7]/div[4]/div[2]/div[1]/div[2]/ul[1]/li[64]/div[2]/span[1]/span[1]/nav[1]/a[1]", "//a[contains(., '')]", "//A[@class='pagination__next icon-link']"]}}, {"id": 6, "index": 5, "parentId": 4, "type": 0, "option": 2, "title": "<PERSON>lick Element", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": true, "xpath": "//*[contains(@class, \"pagination__next\")]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[7]/div[4]/div[2]/div[1]/div[2]/ul[1]/li[64]/div[2]/span[1]/span[1]/nav[1]/a[1]", "//a[contains(., '')]", "//A[@class='pagination__next icon-link']"], "loopType": 0}}, {"id": 5, "index": 6, "parentId": 4, "type": 1, "option": 8, "title": "Loop", "sequence": [7], "isInLoop": true, "position": 0, "parameters": {"history": 6, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div/div[4]/div[2]/div[1]/div[2]/ul[1]/li/div[1]/div[2]/a[1]/div[1]/span[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[7]/div[4]/div[2]/div[1]/div[2]/ul[1]/li[2]/div[1]/div[2]/a[1]/div[1]/span[1]", "//span[contains(., 'Apple IPho')]"]}}, {"id": 7, "index": 7, "parentId": 5, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 6, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "para1_text", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "Apple IPhone 1st Generation-8GB-Black Unlocked 2G A1203 (GSM) veryGOOD condition"}, {"num": 1, "value": "Apple iPhone XS Max - 64 GB - Space Grey (Unlocked) A2101 (GSM) (AU Stock)"}, {"num": 2, "value": "Full Cover TEMPERED Glass For iPhone 11 12 13 PRO XS MAX XR 7 8 Screen Protector"}, {"num": 3, "value": "Apple iPhone SE smartphone A1723 32GB Space Grey Aus stock Unlocked"}, {"num": 4, "value": "original Apple iPhone 1st Generation 16GB unlocked 2G GSM work good IOS3"}, {"num": 5, "value": "Apple iPhone SE (2016) 1st Gen 32GB | Telstra Locked | Good Battery"}, {"num": 6, "value": "Apple iPhone 11 Pro Max Unlocked Smartphone, 64GB, 256GB, 512GB SYDNEY STOCK"}, {"num": 7, "value": "Apple iPhone 11 Pro A2215 256GB Factory Unlocked Single sim Very Good condition"}, {"num": 8, "value": "iPhone 12 Pro 128gb Unlocked Used Excellent"}, {"num": 9, "value": "📱 IOS6 Apple iPhone 5 16GB Unlocked Black white gold blue Perfect appearance 📱"}, {"num": 10, "value": "Apple iPhone 2G Generation - 8GB - Silver"}, {"num": 11, "value": "Apple iPhone XS - 64GB - Space Gray - Unlocked - Excellent Condition"}, {"num": 12, "value": "Apple iPhone SE 1st Gen 32GB (Unlocked) IOS Smartphone + 12 Months Warranty"}, {"num": 13, "value": "Apple iPhone 13 Pro 128GB Unlocked Good Condition"}, {"num": 14, "value": "Apple iPhone 14 Pro Max -1TB- Gold. Physical SIM tray, Dual Sim - Fast Ship✈️"}, {"num": 15, "value": "Apple iPhone 11 Unlocked Various Colors 64GB 128GB 256GB Smartphone Used"}, {"num": 16, "value": "New&Sealed Apple iPhone 4-8GB 16GB 32GB-Black White UNlocked (AT&T) A1332 (GSM)"}, {"num": 17, "value": "Apple iPhone 6s Plus - 64GB - Space Gray (SaskTel) A1687 (CDMA + GSM) (CA)"}, {"num": 18, "value": "Apple iPhone X Unlocked OLED Smartphone 5,8\", 64GB, 256GB, 512GB, SYDNEY STOCK"}, {"num": 19, "value": "New ListingApple iPhone 7 Plus - 32GB - Black A1661 (CDMA + GSM) - IC LOCKED"}, {"num": 20, "value": "New ListingApple iPhone 6s - 128GB - Silver (Unlocked) A1549 (CDMA + GSM)"}, {"num": 21, "value": "Apple iPhone 8 Plus A1864 (10,2) 256GB Smartphone | Verizon | Space Gray | 5.5\""}, {"num": 22, "value": "New ListingApple iPhone 8 A1863 (10,1) 64GB 4.7\" Smartphone | Verizon | Rose Gold"}, {"num": 23, "value": "New ListingApple iPhone 6s Plus (A1687) - Cracked Screen, No Power On (For Parts/Repair)"}, {"num": 24, "value": "100% Factory Original iPhone 5 Sealed box Unlocked Apple Cell phone 64GB "}, {"num": 25, "value": "🔥 Apple iPhone 6s 128GB - Space Gray (Unlocked) A1688/A1633 smartphone sealed"}, {"num": 26, "value": "Apple A1532 iPhone 5c 16GB (Verizon) White Smartphone (A4170)"}, {"num": 27, "value": "📱 Apple iPhone 4S 8/16/32GB - Unlocked Black white Grade A+ Condition 📱 IOS 9"}, {"num": 28, "value": "iPhone 13 128GB Midnight ---Apple Warranty + Seller's Warranty"}, {"num": 29, "value": "iOS 2.0  Apple iPhone 3G 2nd Generation - 16GB - White (Unlocked) A1241 (GSM)"}, {"num": 30, "value": "Apple iPhone 4s - 64GB - Black (Unlocked) A1387 (CDMA + GSM) (FACTORY SEAL) RARE"}, {"num": 31, "value": "Apple iPhone 14 Pro A16 A2890* Factory Unlocked New Sealed Dual-Sim - FedEX"}, {"num": 32, "value": "Apple iPhone 3GS - 8GB - Black (AT&T) A1303 (GSM) Fast Ship Excellent Used"}, {"num": 33, "value": "Apple iPhone 11 Pro Max 256GB Space Gray Unlocked Excellent Condition"}, {"num": 34, "value": "New&Sealed Apple iPhone 4 32GB A1332 Unlocked White/Black Smart Phone"}, {"num": 35, "value": "original Apple iPhone 2rd Generation 8GB unlocked 2G GSM network work good"}, {"num": 36, "value": "Apple iPhone XS ,XS Max Unlocked Various Colors 64GB 256GB 512GB Smartphone good"}, {"num": 37, "value": "Apple iPhone 7 - 128GB - Gold (T-Mobile) A1778 (GSM)"}, {"num": 38, "value": "Apple iPhone X Unlocked Various Colors 64GB 256GB Smartphone Used"}, {"num": 39, "value": "Case For iPhone 14 Pro Max 14 Pro 14 Plus 14 Shockproof Silicone Cover"}, {"num": 40, "value": "New ListingApple iPhone 14 Pro Max - NEW"}, {"num": 41, "value": "Apple iPhone 8 A1863 (10,1) 4.7\" Smartphone | Verizon | Silver | 64GB"}, {"num": 42, "value": "Apple iPhone 6S, Unlocked Smartphone, Silver, Gold, Grey - SYDNEY STOCK"}, {"num": 43, "value": "📱 Apple iPhone 5 - 16GB - black (Unlocked) IOS6 Sealed transportation 📱"}, {"num": 44, "value": "AT&T FACTORY UNLOCK SERVICE Factory ATT Unlock Service only Clean iPhone"}, {"num": 45, "value": "Apple iPhone SE 2nd 64GB Xfinity"}, {"num": 46, "value": "NEW Apple iPhone 7 - 128GB - Black (Unlocked) GSM IOS Smartphone  sealed package"}, {"num": 47, "value": "Apple iPhone 3G 16GB USA version"}, {"num": 48, "value": "Original Unlocked Apple iPhone 5 - 16GB, 32GB, 64GB White&Black 3 Years Warranty"}, {"num": 49, "value": "HOT Original Apple iPhone 5S 16GB/32GB/64GB 3 Colors Unlocked IOS GPS smartphone"}, {"num": 50, "value": "Apple iPhone 8 Plus - 64GB - Space Gray (Unlocked) A1897 (GSM)"}, {"num": 51, "value": "Apple iPhone 5 - 32GB - Black White  (Unlocked) A1428 (GSM)  IOS Smartphone"}, {"num": 52, "value": "Apple iPhone 1st Generation   - 4GB 8GB 16GB  - Black (Unlocked) A1203 (GSM)"}, {"num": 53, "value": "Apple iPhone 12 Pro Max - 128GB - Pacific Blue Used For Parts/Repair"}, {"num": 54, "value": "Apple iPhone 3GS - 32GB - Black (Unlocked) A1303 (GSM)"}, {"num": 55, "value": "New&Sealed Apple iPhone 4S 8GB 16GB 32GB 64GB unlocked (GSM CDMA) 3G IOS 6"}, {"num": 56, "value": "📱 Apple iPhone 4 8/16/32GB - Unlocked Used Full function mobile phone IOS7 📱"}, {"num": 57, "value": "📱 Apple iPhone 5 16/32/64GB - Unlocked Used Full function mobile phone IOS10 📱"}, {"num": 58, "value": "Full Protection Case For iPhone 14 11 12 13 PRO MAX X XR XS 7 8 Shockproof Cover"}, {"num": 59, "value": "Apple iPhone XR 64GB Black Unlocked Good Condition"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}