欢迎将软件宣传给更多需要的朋友和Star我们的Github仓库！

官方网址: https://www.easyspider.cn

支持MacOS系统，包括Intel芯片和Arm芯片，如酷睿i7和M1芯片，最低MacOS系统版本为11.x。

10.x版本MacOS请下载v0.2.0版本使用。

软件开源代码Github库地址：https://github.com/NaiboWang/EasySpider

官方文档地址：https://github.com/NaiboWang/EasySpider/wiki

视频教程：https://www.bilibili.com/video/BV1th411A7ey/

可以从其他机器导入任务，只需要把其他机器的tasks文件夹里的.json文件放入/Users/<USER>/Library/Application Support/EasySpider/tasks文件夹里即可。同理执行号文件可以通过复制execution_instances文件夹中的.json文件来导入。注意，两个文件夹里的.json文件只支持命名为大于0的数字。

可通过以下命令快速进入tasks文件夹：

cd /Users/<USER>/Library/Application\ Support/EasySpider/tasks
open .

如果需要按p键暂停和继续任务的执行,需要赋予程序键盘监控权限。
