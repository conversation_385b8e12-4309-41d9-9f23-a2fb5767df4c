{"id": 157, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "2023/7/9 10:41:47", "update_time": "7/11/2023, 4:22:21 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "mysql", "saveName": "current_time", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}, {"id": 1, "name": "loopTimes_循环_1", "nodeId": 6, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}, {"id": 2, "name": "inputText_2", "nodeName": "输入文字", "nodeId": 7, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "", "value": ""}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/"}, {"id": 1, "name": "参数2_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "手机"}, {"id": 2, "name": "参数3_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://shouji.jd.com/"}, {"id": 3, "name": "参数4_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/"}, {"id": 4, "name": "参数5_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "数码"}, {"id": 5, "name": "参数6_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://shuma.jd.com/"}, {"id": 6, "name": "参数7_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/"}, {"id": 7, "name": "参数8_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "厨具"}, {"id": 8, "name": "参数9_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://channel.jd.com/kitchenware.html"}, {"id": 9, "name": "参数10_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/"}, {"id": 10, "name": "参数11_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "工业品"}, {"id": 11, "name": "参数12_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://pro.jd.com/mall/active/2u2DR1dUiK34csAE3DqmcG8aXvUK/index.html"}, {"id": 12, "name": "参数13_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//m.360buyimg.com/babel/s1125x600_jfs/t1/156011/19/36990/85599/646c850aF5e22eaa0/87641bfb5cf707ba.jpg!q70.dpg"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 6, 7, 3], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 4, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环提取数据", "sequence": [4], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '/手机/数码')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG ']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]"]}}, {"id": 5, "index": 4, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/span[1]", "allXPaths": ["/span[1]", "//span[contains(., '/')]", "//SPAN[@class='LeftSide_cate_menu_line__vzQu9 LeftSide_fore0__r2Yrl']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/span[last()-1]"], "exampleValues": [{"num": 0, "value": "/"}], "unique_index": "/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数2_链接文本", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '手机')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "手机"}], "unique_index": "/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数3_链接地址", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '手机')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "https://shouji.jd.com/"}], "unique_index": "/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/span[2]", "allXPaths": ["/span[2]", "//span[contains(., '/')]", "//SPAN[@class='LeftSide_cate_menu_line__vzQu9 undefined']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/span"], "exampleValues": [{"num": 0, "value": "/"}], "unique_index": "/span[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数5_链接文本", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '数码')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/a"], "exampleValues": [{"num": 0, "value": "数码"}], "unique_index": "/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数6_链接地址", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '数码')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/a"], "exampleValues": [{"num": 0, "value": "https://shuma.jd.com/"}], "unique_index": "/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数7_文本", "desc": "", "relativeXPath": "/span[3]", "allXPaths": ["/span[3]", "//span[contains(., '/')]", "//SPAN[@class='LeftSide_cate_menu_line__vzQu9 undefined']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-9]/span"], "exampleValues": [{"num": 3, "value": "/"}], "unique_index": "/span[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数8_链接文本", "desc": "", "relativeXPath": "/a[3]", "allXPaths": ["/a[3]", "//a[contains(., '厨具')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-9]/a"], "exampleValues": [{"num": 3, "value": "厨具"}], "unique_index": "/a[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数9_链接地址", "desc": "", "relativeXPath": "/a[3]", "allXPaths": ["/a[3]", "//a[contains(., '厨具')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-9]/a"], "exampleValues": [{"num": 3, "value": "https://channel.jd.com/kitchenware.html"}], "unique_index": "/a[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/span[4]", "allXPaths": ["/span[4]", "//span[contains(., '/')]", "//SPAN[@class='LeftSide_cate_menu_line__vzQu9 undefined']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-8]/span"], "exampleValues": [{"num": 4, "value": "/"}], "unique_index": "/span[4]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数11_链接文本", "desc": "", "relativeXPath": "/a[4]", "allXPaths": ["/a[4]", "//a[contains(., '工业品')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-8]/a"], "exampleValues": [{"num": 4, "value": "工业品"}], "unique_index": "/a[4]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数12_链接地址", "desc": "", "relativeXPath": "/a[4]", "allXPaths": ["/a[4]", "//a[contains(., '工业品')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-8]/a"], "exampleValues": [{"num": 4, "value": "https://pro.jd.com/mall/active/2u2DR1dUiK34csAE3DqmcG8aXvUK/index.html"}], "unique_index": "/a[4]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 0, "relative": false, "name": "参数13_图片地址", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[1]/div[1]/a[1]/img[1]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[1]/div[1]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-1]/div/div[last()-1]/div/div[last()-1]/div/div[last()-6]/div/div/a/img"], "exampleValues": [{"num": 0, "value": "//m.360buyimg.com/babel/s1125x600_jfs/t1/156011/19/36990/85599/646c850aF5e22eaa0/87641bfb5cf707ba.jpg!q70.dpg"}], "unique_index": "65z1z1niylfljutw14e", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": -1, "index": 5, "parentId": 0, "type": 1, "option": 8, "title": "循环123动afsadf  大森 ", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 2, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 3, "index": 7, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": ""}}]}