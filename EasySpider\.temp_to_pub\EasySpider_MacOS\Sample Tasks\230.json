{"id": 230, "name": "多层嵌套iframe示例 - 主页面", "url": "https://easyspider.cn/test_pages/nested_iframe.html", "links": "https://easyspider.cn/test_pages/nested_iframe.html", "create_time": "12/8/2023, 3:48:34 AM", "update_time": "12/8/2023, 5:06:34 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://easyspider.cn/test_pages/nested_iframe.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://easyspider.cn/test_pages/nested_iframe.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://easyspider.cn/test_pages/nested_iframe.html"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "这是主页面"}, {"id": 1, "name": "参数2_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "这是第一层嵌套页面"}, {"id": 2, "name": "参数3_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "这是第二层嵌套页面"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://easyspider.cn/test_pages/nested_iframe.html", "links": "https://easyspider.cn/test_pages/nested_iframe.html", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h1[1]", "allXPaths": ["/html/body/h1[1]", "//h1[contains(., '这是主页面')]", "/html/body/h1"], "exampleValues": [{"num": 0, "value": "这是主页面"}], "unique_index": "c61bxpv7felpvm1u1j", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数2_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h2[1]", "allXPaths": ["/html/body/h2[1]", "//h2[contains(., '这是第一层嵌套页面')]", "/html/body/h2"], "exampleValues": [{"num": 0, "value": "这是第一层嵌套页面"}], "unique_index": "io61iajn9h7lpvm1wbp", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数3_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h3[1]", "allXPaths": ["/html/body/h3[1]", "//h3[contains(., '这是第二层嵌套页面')]", "/html/body/h3"], "exampleValues": [{"num": 0, "value": "这是第二层嵌套页面"}], "unique_index": "3jhqkc33etglpvm1z9v", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击这是主页面", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/h1[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/h1[1]", "//h1[contains(., '这是主页面')]", "/html/body/h1"]}}, {"id": 4, "index": 4, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h3[1]", "allXPaths": ["/html/body/h3[1]", "//h3[contains(., '这是第二层嵌套页面')]", "/html/body/h3"], "exampleValues": [{"num": 0, "value": "这是第二层嵌套页面"}], "unique_index": "ksdtgc5d4rlpvn5ihd", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数2_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h2[1]", "allXPaths": ["/html/body/h2[1]", "//h2[contains(., '这是第一层嵌套页面')]", "/html/body/h2"], "exampleValues": [{"num": 0, "value": "这是第一层嵌套页面"}], "unique_index": "p3fjzfrj7e8lpvn5ozn", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数3_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h1[1]", "allXPaths": ["/html/body/h1[1]", "//h1[contains(., '这是主页面')]", "/html/body/h1"], "exampleValues": [{"num": 0, "value": "这是主页面"}], "unique_index": "ixtq9u2yf5rlpvn5sny", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}