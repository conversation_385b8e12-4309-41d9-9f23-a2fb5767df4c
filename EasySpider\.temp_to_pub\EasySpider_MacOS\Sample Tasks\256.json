{"id": 256, "name": "", "url": "https://easyspider.cn/test_pages/empty.html", "links": "https://easyspider.cn/test_pages/empty.html", "create_time": "12/12/2023, 12:51:13 AM", "update_time": "12/12/2023, 1:42:26 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": true, "desc": "https://easyspider.cn/test_pages/empty.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://easyspider.cn/test_pages/empty.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://easyspider.cn/test_pages/empty.html"}, {"id": 1, "name": "loopText_1", "nodeId": 8, "nodeName": "循环", "desc": "要输入的文本/网址,多行以\\n分开", "type": "text", "exampleValue": "1\n1\n1\n1\n1\n1\n1\n1\n1\n1\n1\n1\n", "value": "1\n1\n1\n1\n1\n1\n1\n1\n1\n1\n1\n1\n"}], "outputParameters": [{"id": 0, "name": "删除Body节点", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 1, "name": "自定义参数_1", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}, {"id": 2, "name": "刷新页面", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 3, "name": "执行Python代码", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 8], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://easyspider.cn/test_pages/empty.html", "links": "https://easyspider.cn/test_pages/empty.html", "maxWaitTime": 10, "scrollType": 3, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 5, "title": "删除Body节点", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 1, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 0, "code": "document.body.parentNode.removeChild(document.body);", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"id": 8, "index": 3, "parentId": 6, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 5, "relative": false, "name": "自定义参数_1", "desc": "", "iframe": false, "extractType": 0, "relativeXPath": "//body", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}]}}, {"id": 4, "index": 4, "parentId": 3, "type": 2, "option": 9, "title": "判断条件", "sequence": [5, 6], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": 6, "parentId": 4, "index": 5, "type": 3, "option": 10, "title": "条件分支1 - 页面有内容", "sequence": [3], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "value": "//body", "code": "", "waitTime": 0, "class": "2"}, "position": 0}, {"id": 7, "parentId": 4, "index": 6, "type": 3, "option": 10, "title": "条件分支2 - 页面没内容", "sequence": [7], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 9, "index": 7, "parentId": 7, "type": 0, "option": 5, "title": "刷新页面", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 0, "code": "location.reload()", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"id": 3, "index": 8, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4, 9], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 3, "pathList": "", "textList": "1\n1\n1\n1\n1\n1\n1\n1\n1\n1\n1\n1\n", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 5, "index": 9, "parentId": 3, "type": 0, "option": 5, "title": "执行Python代码", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 5, "code": "print(\"2\")", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}]}