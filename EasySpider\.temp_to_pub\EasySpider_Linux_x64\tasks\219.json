{"id": 219, "name": "Index of /files/", "url": "https://www.easyspider.cn/files", "links": "https://www.easyspider.cn/files", "create_time": "12/5/2023, 3:49:19 AM", "update_time": "12/7/2023, 2:43:02 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 1, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "t", "containJudge": false, "desc": "https://www.easyspider.cn/files", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.easyspider.cn/files", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.easyspider.cn/files"}], "outputParameters": [{"id": 0, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.easyspider.cn/files", "links": "https://www.easyspider.cn/files", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击V0.5.0/", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/pre[1]/a[7]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/pre[1]/a[7]", "//a[contains(., 'V0.5.0/')]", "/html/body/pre/a"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击EasySpider_0....", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/pre[1]/a[4]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/pre[1]/a[4]", "//a[contains(., 'EasySpider')]", "/html/body/pre/a[last()-1]"]}}, {"id": 4, "index": 4, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": "7", "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}]}