{"id": 68, "name": "<select>: The HTML Select element - basic_select - code sample", "url": "https://live-samples.mdn.mozilla.net/en-US/docs/Web/HTML/Element/select/_sample_.basic_select.html", "links": "https://live-samples.mdn.mozilla.net/en-US/docs/Web/HTML/Element/select/_sample_.basic_select.html", "create_time": "5/24/2023, 3:36:40 AM", "version": "0.3.0", "containJudge": false, "desc": "https://live-samples.mdn.mozilla.net/en-US/docs/Web/HTML/Element/select/_sample_.basic_select.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://live-samples.mdn.mozilla.net/en-US/docs/Web/HTML/Element/select/_sample_.basic_select.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://live-samples.mdn.mozilla.net/en-US/docs/Web/HTML/Element/select/_sample_.basic_select.html"}], "outputParameters": [{"id": 0, "name": "参数1_选择的选项值", "desc": "", "type": "string", "exampleValue": "second"}, {"id": 1, "name": "参数2_选择的选项文本", "desc": "", "type": "string", "exampleValue": "Second Value"}, {"id": 2, "name": "参数3_文本", "desc": "", "type": "string", "exampleValue": "\n  First Value\n  Second Value\n  Third Value\n"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 4, 5, 6, 7, 3], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://live-samples.mdn.mozilla.net/en-US/docs/Web/HTML/Element/select/_sample_.basic_select.html", "links": "https://live-samples.mdn.mozilla.net/en-US/docs/Web/HTML/Element/select/_sample_.basic_select.html", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/select[1]", "allXPaths": ["/html/body/select[1]", "//select[contains(., 'First V')]", "//SELECT[@name='choice']"], "exampleValues": [{"num": 0, "value": "\n  First Value\n  Second Value\n  Third Value\n"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 6, "index": 3, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 5, "parameters": {"history": 3, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 10, "relative": false, "name": "参数1_选择的选项值", "desc": "", "extractType": 0, "relativeXPath": "/html/body/select[1]", "allXPaths": ["/html/body/select[1]", "//select[contains(., 'First V')]", "//SELECT[@name='choice']"], "exampleValues": [{"num": 0, "value": "second"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 11, "relative": false, "name": "参数2_选择的选项文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/select[1]", "allXPaths": ["/html/body/select[1]", "//select[contains(., 'First V')]", "//SELECT[@name='choice']"], "exampleValues": [{"num": 0, "value": "Second Value"}], "default": "", "beforeJS": "arguments[0].value=\"first\"", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数3_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/select[1]", "allXPaths": ["/html/body/select[1]", "//select[contains(., 'First V')]", "//SELECT[@name='choice']"], "exampleValues": [{"num": 0, "value": "\n  First Value\n  Second Value\n  Third Value\n"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 2, "index": 4, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/select[1]", "wait": 3, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": ["/html/body/select[1]", "//select[contains(., 'First V')]", "//SELECT[@name='choice']"], "optionMode": "0", "optionValue": "Third Value"}}, {"id": 3, "index": 5, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/select[1]", "wait": 3, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": ["/html/body/select[1]", "//select[contains(., 'First V')]", "//SELECT[@name='choice']"], "optionMode": "1", "optionValue": "0"}}, {"id": 4, "index": 6, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/select[1]", "wait": 3, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": ["/html/body/select[1]", "//select[contains(., 'First V')]", "//SELECT[@name='choice']"], "optionMode": "2", "optionValue": "second"}}, {"id": 5, "index": 7, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/select[1]", "wait": 3, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": ["/html/body/select[1]", "//select[contains(., 'First V')]", "//SELECT[@name='choice']"], "optionMode": "3", "optionValue": "Third Value45645"}}, {"id": -1, "index": 8, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/select[1]", "wait": 3, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": ["/html/body/select[1]", "//select[contains(., 'First V')]", "//SELECT[@name='choice']"], "optionMode": "0", "optionValue": "Third Value"}}]}