{"id": 300, "name": "你永远难以忘记的pool party：用windows线程池的新进程注入技术 - 先知社区", "url": "https://xz.aliyun.com/t/13184", "links": "https://xz.aliyun.com/t/13184", "create_time": "12/19/2023, 8:18:48 PM", "update_time": "12/19/2023, 8:18:52 PM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "dataWriteMode": 1, "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "browser": "chrome", "removeDuplicate": 0, "desc": "https://xz.aliyun.com/t/13184", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://xz.aliyun.com/t/13184", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://xz.aliyun.com/t/13184"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "原文链接：Process Injection Using Windows Thread Pools | Safebreach"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://xz.aliyun.com/t/13184", "links": "https://xz.aliyun.com/t/13184", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/p", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ["/html/body/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/p[1]", "//p[contains(., '作者：Alon Le')]", "/html/body/div[last()-3]/div/div[last()-1]/div[last()-3]/div/div/div[last()-2]/p[last()-98]"]}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "原文链接：Process Injection Using Windows Thread Pools | Safebreach"}], "unique_index": "omov61ihv2rlqcba4hj", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}