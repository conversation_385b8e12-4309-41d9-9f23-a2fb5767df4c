{"id": 190, "name": "GPT", "url": "https://gpt.naibo.wang", "links": "https://gpt.naibo.wang", "create_time": "7/18/2023, 2:22:36 AM", "update_time": "7/18/2023, 2:22:36 AM", "version": "0.3.6", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "https://gpt.naibo.wang", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://gpt.naibo.wang", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://gpt.naibo.wang"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 3, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "给一段算法的代码", "value": "给一段算法的代码"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "给一段算法的代码  你没有解释你希望提供什么类型的代码- 比如设计的目标，使用的语言等。这里提供一个简单的 Python 算法，该算法用于查找列表中的最大元素：\ndef find_max(input_list):\n    if len(input_list) == 0:\n        return None\n    max_value = input_list[0]\n    for i in range(1, len(input_list)):\n        if input_list[i] > max_value:\n            max_value = input_list[i]\n    return max_value\n\nif __name__ == \"__main__\":\n    test_list = [12, 3, 45, 27]\n    max_value = find_max(test_list)\n    print(\"Maximum value in the list is\", max_value)\n \n\n当你运行这个代码，它将会输出列表 `[12, 3, 45,   "}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 4, 5], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://gpt.naibo.wang", "links": "https://gpt.naibo.wang", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"lg\")]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/gradio-app[1]/div[1]/div[1]/div[1]/div[1]/button[1]", "//button[contains(., 'Login')]", "//BUTTON[@class='lg primary svelte-1ipelgc']", "/html/body/gradio-app/div/div/div/div/button"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"component-10\"]/label[1]/textarea[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "value": "给一段算法的代码", "index": 0, "allXPaths": ["/html/body/gradio-app[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/label[1]/textarea[1]", "//textarea[contains(., '')]", "//TEXTAREA[@class='scroll-hide svelte-1pie7s6']", "/html/body/gradio-app/div/div/div/div/div/div/div/div[last()-3]/div/div/div[last()-3]/div/div/label/textarea"]}}, {"id": 4, "index": 4, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"component-13\"]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": "3", "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/gradio-app[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[3]/div[1]/div[2]/button[1]", "//button[contains(., 'Send')]", "id(\"component-13\")", "//BUTTON[@class='lg primary svelte-1ipelgc']", "/html/body/gradio-app/div/div/div/div/div/div/div/div[last()-3]/div/div/div[last()-2]/button"]}}, {"id": 5, "index": 5, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/gradio-app[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]", "allXPaths": ["/html/body/gradio-app[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]", "//div[contains(., '给一段算法的代码')]", "//DIV[@class='message-wrap svelte-18telvq']", "/html/body/gradio-app/div/div/div/div/div/div/div[last()-1]/div/div/div"], "exampleValues": [{"num": 0, "value": "给一段算法的代码  你没有解释你希望提供什么类型的代码- 比如设计的目标，使用的语言等。这里提供一个简单的 Python 算法，该算法用于查找列表中的最大元素：\ndef find_max(input_list):\n    if len(input_list) == 0:\n        return None\n    max_value = input_list[0]\n    for i in range(1, len(input_list)):\n        if input_list[i] > max_value:\n            max_value = input_list[i]\n    return max_value\n\nif __name__ == \"__main__\":\n    test_list = [12, 3, 45, 27]\n    max_value = find_max(test_list)\n    print(\"Maximum value in the list is\", max_value)\n \n\n当你运行这个代码，它将会输出列表 `[12, 3, 45,   "}], "unique_index": "bchbjubuyetlk76zuuf", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}