## 版本信息 ｜ Version Information
**EasySpider版本 ｜ EasySpider Version**:  
**系统版本（架构） ｜ System Version (Architecture)**:  
**浏览器版本 ｜ Browser Version**:  
**安装方式 ｜ Installation method**:

## 问题描述 ｜ Issue Description


## 如何复现 ｜ Steps to Reproduce

## 示例任务文件 ｜ Example Task File

Windows和Linux版本的软件设计的任务文件在软件目录下的`tasks`文件夹中，文件名为任务列表中`任务的ID号.json`；MacOS系统的任务文件目录请运行下面的命令打开tasks文件夹：

The task file designed for the Windows and Linux versions of the software is in the `tasks` folder in the software directory, and the file name is `the ID number of the task.json` in the task list; the task file directory of the MacOS system is opened by running the following command:

```bash
cd /Users/<USER>/Library/Application\ Support/EasySpider/tasks
open .
```

请将任务文件直接以文件的方式粘贴到这里，不要截图和打开复制里面的内容。

Please paste the task file directly as a file here, do not take screenshots and open to copy the content.