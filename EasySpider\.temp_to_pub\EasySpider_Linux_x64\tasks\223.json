{"id": 223, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "", "update_time": "12/7/2023, 12:43:42 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 3], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击手机", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[1]/a[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '手机')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/a[last()-1]"]}}, {"id": 2, "index": 3, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": "5", "code": "from selenium.common.exceptions import NoSuchElementException, ElementClickInterceptedException\ntry:\n    # 使用XPath定位元素并点击\n    element = self.browser.find_element(By.XPATH, \"//*[contains(@class, 'LeftSide_menu_list__qX123CeM')]/div[1]/a[1]\")\n    element.click()\n    print(\"点击成功\")\nexcept ElementClickInterceptedException:\n    # 如果元素被遮挡，点击失败\n    print(\"元素被遮挡，无法点击\")\nexcept Exception as e:\n    # 打印其他异常\n    print(\"发生了一个异常:\", e)\nfinally:\n    print(\"finally\")", "waitTime": 0, "recordASField": 0, "paraType": "text"}}]}