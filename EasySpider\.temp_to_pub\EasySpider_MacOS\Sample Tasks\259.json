{"id": 259, "name": "", "url": "https://mall.espic.com.cn/mall-view/", "links": "https://mall.espic.com.cn/mall-view/", "create_time": "12/12/2023, 5:26:03 PM", "update_time": "12/12/2023, 5:26:03 PM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://mall.espic.com.cn/mall-view/", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://mall.espic.com.cn/mall-view/", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://mall.espic.com.cn/mall-view/"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://mall.espic.com.cn/mall-view/", "links": "https://mall.espic.com.cn/mall-view/", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}]}