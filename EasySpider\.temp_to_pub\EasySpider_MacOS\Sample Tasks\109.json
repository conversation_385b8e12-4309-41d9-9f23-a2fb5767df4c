{"id": 109, "name": "Logo Design Jobs for July 2023 | Freelancer", "url": "https://www.freelancer.com/jobs/logo-design", "links": "https://www.freelancer.com/jobs/logo-design", "create_time": "7/2/2023, 11:36:40 AM", "version": "0.3.3", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "https://www.freelancer.com/jobs/logo-design", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.freelancer.com/jobs/logo-design", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.freelancer.com/jobs/logo-design"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 2, "waitType": "1", "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.freelancer.com/jobs/logo-design", "links": "https://www.freelancer.com/jobs/logo-design", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//a[@data-pagination-next-button]\n", "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/main[1]/section[1]/div[4]/div[1]/div[2]/div[1]/div[1]/div[4]/ul[1]/li[6]/a[1]", "//a[contains(., '')]", "//A[@class='btn Pagination-link']", "/html/body/div[last()-7]/main/section/div[last()-2]/div/div/div[last()-1]/div[last()-1]/div/ul/li[last()-1]/a"]}}]}