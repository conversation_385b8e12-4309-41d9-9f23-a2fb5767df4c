{"id": 251, "name": "Select · Bootstrap v5.3", "url": "https://getbootstrap.com/docs/5.3/forms/select/", "links": "https://getbootstrap.com/docs/5.3/forms/select/", "create_time": "12/10/2023, 2:29:23 PM", "update_time": "12/10/2023, 2:29:23 PM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://getbootstrap.com/docs/5.3/forms/select/", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://getbootstrap.com/docs/5.3/forms/select/", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://getbootstrap.com/docs/5.3/forms/select/"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 5, 6], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://getbootstrap.com/docs/5.3/forms/select/", "links": "https://getbootstrap.com/docs/5.3/forms/select/", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"bd-content\")]/div[1]/div[1]/select[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "optionMode": 0, "optionValue": "One", "index": 0, "allXPaths": ["/html/body/div[2]/main[1]/div[3]/div[1]/div[1]/select[1]", "//select[contains(., 'Open th')]", "//SELECT[@class='form-select']", "/html/body/div[last()-4]/main/div/div[last()-5]/div[last()-2]/select"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"bd-content\")]/div[1]/div[1]/select[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "optionMode": "1", "optionValue": "2", "index": 0, "allXPaths": ["/html/body/div[2]/main[1]/div[3]/div[1]/div[1]/select[1]", "//select[contains(., 'Open th')]", "//SELECT[@class='form-select']", "/html/body/div[last()-4]/main/div/div[last()-5]/div[last()-2]/select"]}}, {"id": -1, "index": 4, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"bd-content\")]/div[1]/div[1]/select[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "optionMode": "2", "optionValue": "1", "index": 0, "allXPaths": ["/html/body/div[2]/main[1]/div[3]/div[1]/div[1]/select[1]", "//select[contains(., 'Open th')]", "//SELECT[@class='form-select']", "/html/body/div[last()-4]/main/div/div[last()-5]/div[last()-2]/select"]}}, {"id": 4, "index": 5, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"bd-content\")]/div[1]/div[1]/select[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "optionMode": "2", "optionValue": "1", "index": 0, "allXPaths": ["/html/body/div[2]/main[1]/div[3]/div[1]/div[1]/select[1]", "//select[contains(., 'Open th')]", "//SELECT[@class='form-select']", "/html/body/div[last()-4]/main/div/div[last()-5]/div[last()-2]/select"]}}, {"id": 5, "index": 6, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 4, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"bd-content\")]/div[1]/div[1]/select[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "optionMode": "3", "optionValue": "Three", "index": 0, "allXPaths": ["/html/body/div[2]/main[1]/div[3]/div[1]/div[1]/select[1]", "//select[contains(., 'Open th')]", "//SELECT[@class='form-select']", "/html/body/div[last()-4]/main/div/div[last()-5]/div[last()-2]/select"]}}]}