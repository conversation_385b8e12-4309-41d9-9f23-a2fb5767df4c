{"id": 163, "name": "Just a moment...", "url": "https://portal.ustraveldocs.com/scheduleappointment", "links": "https://portal.ustraveldocs.com/scheduleappointment", "create_time": "", "update_time": "7/12/2023, 5:31:34 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 1, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "containJudge": false, "desc": "https://portal.ustraveldocs.com/scheduleappointment", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://portal.ustraveldocs.com/scheduleappointment", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://portal.ustraveldocs.com/scheduleappointment"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "在本网站所支付的所有费用均不予退还。请确保您已付款，并获得了收据号码。"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 20, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://portal.ustraveldocs.com/scheduleappointment", "links": "https://portal.ustraveldocs.com/scheduleappointment", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[1]/form[1]/span[1]/div[1]/ul[1]/li", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/form[1]/span[1]/div[1]/ul[1]/li[1]", "//li[contains(., '在本网站所支付的所有')]", "/html/body/div[last()-5]/div/div/form/span/div/ul/li[last()-4]"]}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "在本网站所支付的所有费用均不予退还。请确保您已付款，并获得了收据号码。"}], "unique_index": "mzdtihya6jljyt3ewb", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}