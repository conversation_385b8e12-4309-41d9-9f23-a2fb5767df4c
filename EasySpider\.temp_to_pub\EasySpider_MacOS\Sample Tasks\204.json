{"id": 204, "name": "官方视频-锤子商城", "url": "https://www.smartisan.com/pr/videos/r2-id", "links": "https://www.smartisan.com/pr/videos/r2-id", "create_time": "8/1/2023, 7:44:06 PM", "update_time": "8/1/2023, 8:06:22 PM", "version": "0.5.0", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "https://www.smartisan.com/pr/videos/r2-id", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.smartisan.com/pr/videos/r2-id", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.smartisan.com/pr/videos/r2-id"}], "outputParameters": [{"id": 0, "name": "自定义参数_1", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.smartisan.com/pr/videos/r2-id", "links": "https://www.smartisan.com/pr/videos/r2-id", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": [{"nodeType": 0, "contentType": 9, "relative": false, "name": "自定义参数_1", "desc": "", "extractType": 0, "relativeXPath": "//video[@class=\"vjs-tech\"]", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "return arguments[0].getAttribute(\"src\")", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}]}}]}