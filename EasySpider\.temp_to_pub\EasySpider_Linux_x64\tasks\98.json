{"id": 98, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "6/7/2023, 11:55:54 PM", "version": "0.3.2", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.jd.com"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 4, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "string", "exampleValue": "男Field[\"商品类别\"]女", "value": "男Field[\"商品类别\"]女"}], "outputParameters": [{"id": 0, "name": "参数3_链接地址", "desc": "", "type": "string", "exampleValue": "https://shouji.jd.com/"}, {"id": 1, "name": "参数4_文本", "desc": "", "type": "string", "exampleValue": "/"}, {"id": 2, "name": "商品类别", "desc": "", "type": "string", "exampleValue": "数码"}, {"id": 3, "name": "参数6_链接地址", "desc": "", "type": "string", "exampleValue": "https://shuma.jd.com/"}, {"id": 4, "name": "参数14_文本", "desc": "", "type": "string", "exampleValue": "7919.00"}, {"id": 5, "name": "参数17_文本", "desc": "", "type": "string", "exampleValue": "Apple14ProMax(A2896)256GB暗紫色支持移动联通电信5G双卡双待手机【大王卡】"}, {"id": 6, "name": "参数21_链接文本", "desc": "", "type": "string", "exampleValue": "10万+"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 4, 5, 6], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '/手机/数码')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG ']"]}}, {"id": 6, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 2, "contentType": 0, "relative": true, "name": "参数3_链接地址", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '手机')]"], "exampleValues": [{"num": 0, "value": "https://shouji.jd.com/"}, {"num": 1, "value": "https://search.jd.com/Search?keyword=%E5%B0%8F%E5%AE%B6%E7%94%B5&enc=utf-8&wq=%E5%B0%8F%E5%AE%B6%E7%94%B5&pvid=261a350161304c979fa0e7ce95c05671"}, {"num": 2, "value": "https://diannao.jd.com/"}, {"num": 3, "value": "https://channel.jd.com/jf.html"}, {"num": 4, "value": "https://channel.jd.com/furniture.html"}, {"num": 5, "value": "https://channel.jd.com/underwear.html"}, {"num": 6, "value": "https://channel.jd.com/bag.html"}, {"num": 7, "value": "https://phat.jd.com/10-109.html"}, {"num": 8, "value": "https://che.jd.com/"}, {"num": 9, "value": "https://search.jd.com/Search?keyword=%E6%AF%8D%E5%A9%B4&enc=utf-8&qrst=1&rt=1&stop=1&vt=2&wq=%E6%AF%8D%E5%A9%B4&stock=1&gp=2&click=1"}, {"num": 10, "value": "https://toy.jd.com/"}, {"num": 11, "value": "https://channel.jd.com/beauty.html"}, {"num": 12, "value": "https://book.jd.com/"}], "unique_index": "/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/span[2]", "allXPaths": ["/span[2]", "//span[contains(., '/')]", "//SPAN[@class='LeftSide_cate_menu_line__vzQu9 undefined']"], "exampleValues": [{"num": 0, "value": "/"}, {"num": 2, "value": "/"}, {"num": 3, "value": "/"}, {"num": 4, "value": "/"}, {"num": 5, "value": "/"}, {"num": 6, "value": "/"}, {"num": 7, "value": "/"}, {"num": 8, "value": "/"}, {"num": 9, "value": "/"}, {"num": 10, "value": "/"}, {"num": 11, "value": "/"}, {"num": 12, "value": "/"}], "unique_index": "/span[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "商品类别", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '数码')]"], "exampleValues": [{"num": 0, "value": "数码"}, {"num": 2, "value": "办公"}, {"num": 3, "value": "家居"}, {"num": 4, "value": "家装"}, {"num": 5, "value": "男装"}, {"num": 6, "value": "钟表"}, {"num": 7, "value": "户外"}, {"num": 8, "value": "车载电器"}, {"num": 9, "value": "洗护喂养"}, {"num": 10, "value": "宠物生活"}, {"num": 11, "value": "个人护理"}, {"num": 12, "value": "童书"}], "unique_index": "/a[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数6_链接地址", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '数码')]"], "exampleValues": [{"num": 0, "value": "https://shuma.jd.com/"}, {"num": 2, "value": "https://bg.jd.com/"}, {"num": 3, "value": "https://channel.jd.com/home.html"}, {"num": 4, "value": "https://jzjc.jd.com/"}, {"num": 5, "value": "https://channel.jd.com/1315-1342.html"}, {"num": 6, "value": "https://channel.jd.com/watch.html"}, {"num": 7, "value": "https://channel.jd.com/outdoor.html"}, {"num": 8, "value": "https://list.jd.com/list.html?cat=6728,6740&page=1&delivery_glb=1&stock=1&sort=sort_totalsales15_desc&trans=1&JL=4_7_0#J_main"}, {"num": 9, "value": "https://channel.jd.com/feed.html"}, {"num": 10, "value": "https://channel.jd.com/pet.html"}, {"num": 11, "value": "https://lady.jd.com/"}, {"num": 12, "value": "https://book.jd.com/children.html"}], "unique_index": "/a[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 3, "index": 4, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "男Field[\"商品类别\"]女", "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']"]}}, {"id": 4, "index": 5, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-btn\"]/i[1]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/button[1]/i[1]", "//i[contains(., '')]"]}}, {"id": 5, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [7], "isInLoop": false, "position": 4, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li/div[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='gl-i-wrap']"]}}, {"id": 7, "index": 7, "parentId": 5, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数14_文本", "desc": "", "relativeXPath": "/div[3]/strong[1]/i[1]", "allXPaths": ["/div[3]/strong[1]/i[1]", "//i[contains(., '7919.00')]"], "exampleValues": [{"num": 0, "value": "7919.00"}, {"num": 1, "value": "4299.00"}, {"num": 2, "value": "6299.00"}, {"num": 3, "value": "4099.00"}, {"num": 4, "value": "8699.00"}, {"num": 5, "value": "11699.00"}, {"num": 6, "value": "7999.00"}, {"num": 7, "value": "7899.00"}, {"num": 8, "value": "3888.00"}, {"num": 9, "value": "2229.00"}, {"num": 10, "value": "1929.00"}, {"num": 11, "value": "7899.00"}, {"num": 12, "value": "2639.00"}, {"num": 13, "value": "6999.00"}, {"num": 14, "value": "6299.00"}, {"num": 15, "value": "4699.00"}, {"num": 16, "value": "3658.00"}, {"num": 17, "value": "3688.00"}, {"num": 18, "value": "3399.00"}, {"num": 19, "value": "5559.00"}, {"num": 20, "value": "3399.00"}, {"num": 21, "value": "4859.00"}, {"num": 22, "value": "6069.00"}, {"num": 23, "value": "12499.00"}, {"num": 24, "value": "5399.00"}, {"num": 25, "value": "5699.00"}, {"num": 26, "value": "3288.00"}, {"num": 27, "value": "4099.00"}, {"num": 28, "value": "4738.00"}, {"num": 29, "value": "5399.00"}], "unique_index": "/div[3]/strong[1]/i[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数17_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/em[1]", "allXPaths": ["/div[4]/a[1]/em[1]", "//em[contains(., 'Apple iPho')]"], "exampleValues": [{"num": 0, "value": "Apple14ProMax(A2896)256GB暗紫色支持移动联通电信5G双卡双待手机【大王卡】"}, {"num": 1, "value": "激活未使用Apple苹果1313(A2634)5G国行二手手机午夜黑128G全网通【电池效率100%】准新"}, {"num": 2, "value": "Apple14(A2884)256GB星光色支持移动联通电信5G双卡双待手机【大王卡】"}, {"num": 3, "value": "激活未使用Apple13(A2634)国行二手苹果手机原装5G全网通午夜黑128G全网通99成新"}, {"num": 4, "value": "Apple14Pro(A2892)256GB深空黑色支持移动联通电信5G双卡双待手机【大王卡】"}, {"num": 5, "value": "Apple14ProMax512GB暗紫色A2896手机支持移动联通电信5GMQ8G3CH/A【企业客户专享】"}, {"num": 6, "value": "Apple14Pro128GB银色A2892手机支持移动联通电信5GMPXY3CH/A【店铺专享】"}, {"num": 7, "value": "Apple14Pro(A2892)128GB银色支持移动联通电信5G双卡双待手机【大王卡】"}, {"num": 8, "value": "Apple/苹果12proMax双卡双待5G苹果12全网通12Pro苹果手机12_双卡5G_6.1寸_紫色官方标配_5G双卡普通64GB"}, {"num": 9, "value": "苹果XSMax双卡双待苹果XR全网通xs苹果手机x_X_[黑色]5.8寸官方标配_普通64G"}, {"num": 10, "value": "Apple/苹果XR双卡双待苹果X全网通苹果手机x_X_[白色]5.8寸官方标配_普通64GB"}, {"num": 11, "value": "Apple14Pro(A2892)128GB金色支持移动联通电信5G双卡双待手机【大王卡】"}, {"num": 12, "value": "苹果XSMax双卡双待苹果XR全网通xs苹果手机x_X_[白色]5.8寸官方标配_普通256G"}, {"num": 13, "value": "激活未使用Apple苹果13ProMax13ProMax全网通二手5G手机远峰蓝色256G全网通【电池效率100%】准新"}, {"num": 14, "value": "激活未使用Apple苹果13Pro13Pro5G国行二手5G手机远峰蓝色256G全网通【电池效率100%】准新"}, {"num": 15, "value": "激活未使用苹果13Apple13(A2634)二手5G手机国行双卡双待准新星光色256G全网通【电池效率100%】"}, {"num": 16, "value": "2022新款Apple苹果14全系列美版有锁支持移动联通电信不能插卡ESIM14蓝色128GB美版有锁"}, {"num": 17, "value": "Apple/苹果11proMax双卡双待苹果11全网通11Pro苹果手机苹果_11_6.1寸_白色官方标配_普通256GB"}, {"num": 18, "value": "Apple苹果1321款（美版有锁）苹果13美版单卡全新未激活手机智能手机13黑色128GB美版有锁单卡"}, {"num": 19, "value": "Apple13(A2634)256GB蓝色支持移动联通电信5G双卡双待手机"}, {"num": 20, "value": "Apple苹果13promax美版有锁带卡贴全新机13promax支持移动联通电信卡贴机13黑色128G美版有锁"}, {"num": 21, "value": "Apple/苹果13proMax双卡双待5G苹果13全网通13Pro苹果手机苹果13【6.1寸】双卡_黑色苹果13【6.1寸】官方标配_5G双卡普通128G"}, {"num": 22, "value": "Apple14Plus(A2888)128GB黄色支持移动联通电信5G双卡双待手机"}, {"num": 23, "value": "Apple14Pro1TB金色A2892手机支持移动联通电信5GMQ2R3CH/A【店铺专享】"}, {"num": 24, "value": "Apple苹果14Pro苹果14Promax（美版有锁）原版不支持插卡14Pro紫色128G美版【eSIM】不能插卡"}, {"num": 25, "value": "Apple苹果14Pro苹果14Promax（美版有锁）不支持插卡分期免息14Promax紫色128G美版【90天碎屏】"}, {"num": 26, "value": "Apple/苹果11双卡双待苹果11全网通苹果手机苹果_11_6.1寸_黑色官方标配_普通128G"}, {"num": 27, "value": "激活未使用苹果13Apple13(A2634)二手5G手机国行双卡双待星光色128G全网通【电池效率】99成新"}, {"num": 28, "value": "Apple/苹果12proMax双卡双待5G苹果12通12Pro苹果手机12_双卡5G_6.1寸_红色官方标配_5G双卡普通256G"}, {"num": 29, "value": "Apple14128GB午夜色A2884手机支持移动联通电信5GMPU93CH/A【企业客户专享】"}], "unique_index": "/div[4]/a[1]/em[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数21_链接文本", "desc": "", "relativeXPath": "/div[5]/strong[1]/a[1]", "allXPaths": ["/div[5]/strong[1]/a[1]", "//a[contains(., '10万+')]", "id(\"J_comment_100038089763\")"], "exampleValues": [{"num": 0, "value": "10万+"}, {"num": 1, "value": "2000+"}, {"num": 2, "value": "5万+"}, {"num": 3, "value": "1000+"}, {"num": 4, "value": "10万+"}, {"num": 5, "value": "2000+"}, {"num": 6, "value": "200+"}, {"num": 7, "value": "5000+"}, {"num": 8, "value": "10"}, {"num": 9, "value": "4"}, {"num": 10, "value": "13"}, {"num": 11, "value": "1000+"}, {"num": 12, "value": "52"}, {"num": 13, "value": "1000+"}, {"num": 14, "value": "1000+"}, {"num": 15, "value": "2000+"}, {"num": 16, "value": "20"}, {"num": 17, "value": "61"}, {"num": 18, "value": "28"}, {"num": 19, "value": "9"}, {"num": 20, "value": "23"}, {"num": 21, "value": "1"}, {"num": 22, "value": "100+"}, {"num": 23, "value": "21"}, {"num": 24, "value": "20"}, {"num": 25, "value": "0"}, {"num": 26, "value": "1"}, {"num": 27, "value": "0"}, {"num": 28, "value": "0"}, {"num": 29, "value": "1万+"}], "unique_index": "/div[5]/strong[1]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}