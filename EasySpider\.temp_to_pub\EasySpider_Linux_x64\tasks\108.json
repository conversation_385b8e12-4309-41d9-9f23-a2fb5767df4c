{"id": 108, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "7/2/2023, 2:21:07 AM", "version": "0.3.3", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "/手机/数码"}, {"id": 1, "name": "参数2_文本", "desc": "", "type": "string", "exampleValue": "\n        \n        \n          \n          \n            \n            \n              \n            \n            \n          \n        \n        \n          \n             ;0\n            我的购物车\n          \n          \n        \n        平板電腦爆款耳機手機數據線年貨節\n        \n        领券中心今日推荐\n      "}, {"id": 2, "name": "参数3_链接文本", "desc": "", "type": "string", "exampleValue": "平板電腦"}, {"id": 3, "name": "参数4_链接地址", "desc": "", "type": "string", "exampleValue": "https://search.jd.com/Search?keyword=%E5%B9%B3%E6%9D%BF%E7%94%B5%E8%84%91&enc=utf-8&wq=%E5%B9%B3%E6%9D%BF%E7%94%B5%E8%84%91&pvid=84c62205dccd43dfad1b6eb5fdf5077b"}, {"id": 4, "name": "参数5_文本", "desc": "", "type": "string", "exampleValue": "全球特讯更多"}, {"id": 5, "name": "参数6_链接文本", "desc": "", "type": "string", "exampleValue": "手机"}, {"id": 6, "name": "参数7_链接地址", "desc": "", "type": "string", "exampleValue": "https://shouji.jd.com/"}, {"id": 7, "name": "参数8_图片地址", "desc": "", "type": "string", "exampleValue": "//m.360buyimg.com/babel/jfs/t1/108842/7/32026/99263/649e2fc3Fa370bf37/2534e25fd2f41f81.jpg!q70.dpg"}, {"id": 8, "name": "参数9_文本", "desc": "", "type": "string", "exampleValue": "康佳（KONKA）电风扇七叶柔和轻音大风量落地扇家用风扇节能低噪电扇广角摇头KF-L17D(升级款）"}, {"id": 9, "name": "参数10_文本", "desc": "", "type": "string", "exampleValue": "109."}, {"id": 10, "name": "参数11_文本", "desc": "", "type": "string", "exampleValue": "￥"}, {"id": 11, "name": "参数12_文本", "desc": "", "type": "string", "exampleValue": "00"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 3, 4, 6], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": false, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a[1]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '手机')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "手机"}], "unique_index": "t8z5t3bqmsljka5t7c", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": false, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a[1]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '手机')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "https://shouji.jd.com/"}], "unique_index": "t8z5t3bqmsljka5t7c", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数3_文本", "desc": "", "relativeXPath": "/html/body/div[4]/div[1]/div[4]", "allXPaths": ["/html/body/div[4]/div[1]/div[4]", "//div[contains(., '平板電腦爆款耳機手機')]", "id(\"hotwords\")", "/html/body/div[last()-6]/div/div"], "exampleValues": [{"num": 0, "value": "平板電腦爆款耳機手機數據線年貨節"}], "unique_index": "y1ix419463ljka5t7c", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数4_文本", "desc": "", "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[4]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[4]", "//div[contains(., '/家纺/家居/厨具')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG LeftSide_menu_hover__OCHiO']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-9]"], "exampleValues": [{"num": 0, "value": "/家纺/家居/厨具"}], "unique_index": "xg27dfns7pqljka5t7c", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 2, "index": 3, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '/手机/数码')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG LeftSide_menu_hover__OCHiO']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]"], "exampleValues": [{"num": 0, "value": "/手机/数码"}], "unique_index": "zzphd7wvdcrljkaecd3", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数2_文本", "desc": "", "relativeXPath": "/html/body/div[4]/div[1]", "allXPaths": ["/html/body/div[4]/div[1]", "//div[contains(., '')]", "//DIV[@class='w']", "/html/body/div[last()-6]/div"], "exampleValues": [{"num": 0, "value": "\n        \n        \n          \n          \n            \n            \n              \n            \n            \n          \n        \n        \n          \n             ;0\n            我的购物车\n          \n          \n        \n        平板電腦爆款耳機手機數據線年貨節\n        \n        领券中心今日推荐\n      "}], "unique_index": "p9fl7hfr8yhljkaecd3", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": false, "name": "参数3_链接文本", "desc": "", "relativeXPath": "/html/body/div[4]/div[1]/div[4]/a[1]", "allXPaths": ["/html/body/div[4]/div[1]/div[4]/a[1]", "//a[contains(., '平板電腦')]", "/html/body/div[last()-6]/div/div/a[last()-4]"], "exampleValues": [{"num": 0, "value": "平板電腦"}], "unique_index": "23n80vwyeayljkaecd3", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": false, "name": "参数4_链接地址", "desc": "", "relativeXPath": "/html/body/div[4]/div[1]/div[4]/a[1]", "allXPaths": ["/html/body/div[4]/div[1]/div[4]/a[1]", "//a[contains(., '平板電腦')]", "/html/body/div[last()-6]/div/div/a[last()-4]"], "exampleValues": [{"num": 0, "value": "https://search.jd.com/Search?keyword=%E5%B9%B3%E6%9D%BF%E7%94%B5%E8%84%91&enc=utf-8&wq=%E5%B9%B3%E6%9D%BF%E7%94%B5%E8%84%91&pvid=84c62205dccd43dfad1b6eb5fdf5077b"}], "unique_index": "23n80vwyeayljkaecd3", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数5_文本", "desc": "", "relativeXPath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[4]/div[1]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[3]/div[1]/div[4]/div[1]", "//div[contains(., '全球特讯更多')]", "//DIV[@class='information_bt clr']", "/html/body/div[last()-5]/div/div[last()-4]/div/div/div/div[last()-3]/div[last()-1]"], "exampleValues": [{"num": 0, "value": "全球特讯更多"}], "unique_index": "b0y6f782im8ljkaecd3", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 3, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": false, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div/a", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '手机')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/a[last()-1]"]}}, {"id": 5, "index": 5, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数6_链接文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "手机"}, {"num": 1, "value": "数码"}, {"num": 2, "value": "家用电器"}, {"num": 3, "value": "电脑"}, {"num": 4, "value": "办公"}, {"num": 5, "value": "家纺"}, {"num": 6, "value": "家居"}, {"num": 7, "value": "厨具"}, {"num": 8, "value": "家具"}, {"num": 9, "value": "家装"}, {"num": 10, "value": "灯具"}, {"num": 11, "value": "工业品"}, {"num": 12, "value": "内衣"}, {"num": 13, "value": "男装"}, {"num": 14, "value": "女装"}, {"num": 15, "value": "童装"}, {"num": 16, "value": "箱包"}, {"num": 17, "value": "钟表"}, {"num": 18, "value": "珠宝"}, {"num": 19, "value": "女鞋"}, {"num": 20, "value": "运动"}, {"num": 21, "value": "户外"}, {"num": 22, "value": "男鞋"}, {"num": 23, "value": "汽车用品"}, {"num": 24, "value": "车载电器"}, {"num": 25, "value": "母婴"}, {"num": 26, "value": "洗护喂养"}, {"num": 27, "value": "玩具乐器"}, {"num": 28, "value": "宠物生活"}, {"num": 29, "value": "家庭清洁"}, {"num": 30, "value": "个人护理"}, {"num": 31, "value": "计生情趣"}, {"num": 32, "value": "图书"}, {"num": 33, "value": "童书"}, {"num": 34, "value": "文学"}], "unique_index": "h7kytnmx0zuljkaeh6q", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数7_链接地址", "desc": "", "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "https://shouji.jd.com/"}, {"num": 1, "value": "https://shuma.jd.com/"}, {"num": 2, "value": "https://search.jd.com/Search?keyword=%E5%B0%8F%E5%AE%B6%E7%94%B5&enc=utf-8&wq=%E5%B0%8F%E5%AE%B6%E7%94%B5&pvid=261a350161304c979fa0e7ce95c05671"}, {"num": 3, "value": "https://diannao.jd.com/"}, {"num": 4, "value": "https://bg.jd.com/"}, {"num": 5, "value": "https://channel.jd.com/jf.html"}, {"num": 6, "value": "https://channel.jd.com/home.html"}, {"num": 7, "value": "https://channel.jd.com/kitchenware.html"}, {"num": 8, "value": "https://channel.jd.com/furniture.html"}, {"num": 9, "value": "https://jzjc.jd.com/"}, {"num": 10, "value": "https://channel.jd.com/9855-9856.html"}, {"num": 11, "value": "https://pro.jd.com/mall/active/2u2DR1dUiK34csAE3DqmcG8aXvUK/index.html"}, {"num": 12, "value": "https://channel.jd.com/underwear.html"}, {"num": 13, "value": "https://channel.jd.com/1315-1342.html"}, {"num": 14, "value": "https://channel.jd.com/women.html"}, {"num": 15, "value": "https://list.jd.com/list.html?cat=1319,11842"}, {"num": 16, "value": "https://channel.jd.com/bag.html"}, {"num": 17, "value": "https://channel.jd.com/watch.html"}, {"num": 18, "value": "https://channel.jd.com/jewellery.html"}, {"num": 19, "value": "https://channel.jd.com/womensshoes.html"}, {"num": 20, "value": "https://phat.jd.com/10-109.html"}, {"num": 21, "value": "https://channel.jd.com/outdoor.html"}, {"num": 22, "value": "https://channel.jd.com/mensshoes.html"}, {"num": 23, "value": "https://che.jd.com/"}, {"num": 24, "value": "https://list.jd.com/list.html?cat=6728,6740&page=1&delivery_glb=1&stock=1&sort=sort_totalsales15_desc&trans=1&JL=4_7_0#J_main"}, {"num": 25, "value": "https://search.jd.com/Search?keyword=%E6%AF%8D%E5%A9%B4&enc=utf-8&qrst=1&rt=1&stop=1&vt=2&wq=%E6%AF%8D%E5%A9%B4&stock=1&gp=2&click=1"}, {"num": 26, "value": "https://channel.jd.com/feed.html"}, {"num": 27, "value": "https://toy.jd.com/"}, {"num": 28, "value": "https://channel.jd.com/pet.html"}, {"num": 29, "value": "https://channel.jd.com/beauty.html"}, {"num": 30, "value": "https://lady.jd.com/"}, {"num": 31, "value": "https://channel.jd.com/9192-9196.html"}, {"num": 32, "value": "https://book.jd.com/"}, {"num": 33, "value": "https://book.jd.com/children.html"}, {"num": 34, "value": "https://channel.jd.com/p_wenxuezongheguan.html"}], "unique_index": "h7kytnmx0zuljkaeh6q", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 4, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [7], "isInLoop": false, "position": 3, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[5]/div[1]/div[1]/div[1]/div[1]/div", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '康佳（KONKA）电')]", "//DIV[@class='item']", "/html/body/div[last()-5]/div/div/div/div/div/div/div[last()-30]"]}}, {"id": 6, "index": 7, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 4, "contentType": 1, "relative": true, "name": "参数8_图片地址", "desc": "", "relativeXPath": "/img[1]", "allXPaths": ["/img[1]", "//img[contains(., '')]", "/html/body/div[last()-5]/div/div/div/div/div/div/div[last()-30]/img"], "exampleValues": [{"num": 0, "value": "//m.360buyimg.com/babel/jfs/t1/108842/7/32026/99263/649e2fc3Fa370bf37/2534e25fd2f41f81.jpg!q70.dpg"}, {"num": 1, "value": "//m.360buyimg.com/babel/jfs/t1/180391/26/12310/175561/60def301Ee8602e90/e07c5d6fd5519cab.jpg!q70.dpg"}, {"num": 2, "value": "//m.360buyimg.com/babel/jfs/t1/150311/36/35137/180091/649ea1a8Fd888633a/92e350263aac224d.jpg!q70.dpg"}, {"num": 3, "value": "//m.360buyimg.com/babel/jfs/t1/114148/3/36481/106567/64926d79F79db17fa/f0135bff60dfdacb.jpg!q70.dpg"}, {"num": 4, "value": "//m.360buyimg.com/babel/jfs/t1/138436/27/28979/71117/62ff284dE653b7301/183b5d0959e7f915.jpg!q70.dpg"}, {"num": 5, "value": "//m.360buyimg.com/babel/jfs/t1/94009/17/37627/58513/649724b7Fa92fc5b6/99dee0f0c730c028.jpg!q70.dpg"}, {"num": 6, "value": "//m.360buyimg.com/babel/jfs/t1/199606/38/37458/120699/64912465F28152156/772a3ebbc6f9c68d.jpg!q70.dpg"}, {"num": 7, "value": "//m.360buyimg.com/babel/jfs/t1/59995/1/24616/232386/63f5833dFdc4bf70d/d826b9c6f9de47d8.jpg!q70.dpg"}, {"num": 8, "value": "//m.360buyimg.com/babel/jfs/t1/6368/17/16904/100601/63ed996eF2a3b04e4/ca7df0064048b4fa.jpg!q70.dpg"}, {"num": 9, "value": "//m.360buyimg.com/babel/jfs/t1/85091/40/39586/99610/64916613Ff1629ae5/d5499d7374358ac4.jpg!q70.dpg"}, {"num": 10, "value": "//m.360buyimg.com/babel/jfs/t1/202939/11/38613/77213/649e9530F7ba53117/edd9742cfedd9d09.jpg!q70.dpg"}, {"num": 11, "value": "//m.360buyimg.com/babel/jfs/t1/223445/27/26270/91168/649f00d4F7f2ff1cf/b44f52b66573c975.jpg!q70.dpg"}, {"num": 12, "value": "//m.360buyimg.com/babel/jfs/t1/192332/32/27826/55351/630f2a1aE6bde9ad5/1426ea7e4cb01831.jpg!q70.dpg"}, {"num": 13, "value": "//m.360buyimg.com/babel/jfs/t1/120379/6/36109/23628/645b6db6F00f56e66/f0d772495093cda2.jpg!q70.dpg"}, {"num": 14, "value": "//m.360buyimg.com/babel/jfs/t1/170639/39/36983/105245/6479a3acFff1c1ec0/7c556854b036a608.jpg!q70.dpg"}, {"num": 15, "value": "//m.360buyimg.com/babel/jfs/t1/99165/15/31109/124777/64978adfF8f6c4244/b35074f0503c9367.jpg!q70.dpg"}, {"num": 16, "value": "//m.360buyimg.com/babel/jfs/t1/127746/18/35679/55382/649f082bF3b1e8c78/100ed188d2ca35ed.jpg!q70.dpg"}, {"num": 17, "value": "//m.360buyimg.com/babel/jfs/t4483/17/1214563064/103693/36c2def4/58da2eeaN77aa598c.jpg!q70.dpg"}, {"num": 18, "value": "//m.360buyimg.com/babel/jfs/t1/97463/33/28572/109661/64902566Fea216a3d/d13dbfd40a729275.jpg!q70.dpg"}, {"num": 19, "value": "//m.360buyimg.com/babel/jfs/t1/109966/20/42061/156531/648bd744Fbd202c8a/9b1ed9d22df428be.jpg!q70.dpg"}, {"num": 20, "value": "//m.360buyimg.com/babel/jfs/t1/121334/39/37052/78149/64813485Ff500aa58/729709bcff133d57.jpg!q70.dpg"}, {"num": 21, "value": "//m.360buyimg.com/babel/jfs/t1/222858/15/30100/81994/649eb576F5240d6a0/b36193fd62c79ded.jpg!q70.dpg"}, {"num": 22, "value": "//m.360buyimg.com/babel/jfs/t1/91618/36/24441/77564/6336bb6aE68e46b39/affbe18b71aa5455.jpg!q70.dpg"}, {"num": 23, "value": "//m.360buyimg.com/babel/jfs/t1/138173/35/37813/160959/6492b0b9F43340aae/a01265470a900fd6.jpg!q70.dpg"}, {"num": 24, "value": "//m.360buyimg.com/babel/jfs/t1/137442/16/37388/145081/649e88a6F0fb5c773/e733e3f5dbee1af6.jpg!q70.dpg"}, {"num": 25, "value": "//m.360buyimg.com/babel/jfs/t1/3343/27/17890/105234/63ed9902F62d0e8c7/1a39423731196c55.jpg!q70.dpg"}, {"num": 26, "value": "//m.360buyimg.com/babel/jfs/t1/63799/14/21916/94063/634272c2Efd5abb31/7a3b12cfa33860d1.jpg!q70.dpg"}, {"num": 27, "value": "//m.360buyimg.com/babel/jfs/t1/123629/32/20476/116662/625385eeE86726f1f/6ebc3b4b47e03726.jpg!q70.dpg"}, {"num": 28, "value": "//m.360buyimg.com/babel/jfs/t1/85816/35/37410/170702/649e6f44Fdcffdb84/e4767cedebe1a2cc.jpg!q70.dpg"}, {"num": 29, "value": "//m.360buyimg.com/babel/jfs/t1/180670/34/24323/86578/6285e273E907a00a0/9de930fc0d8a6105.jpg!q70.dpg"}, {"num": 30, "value": "//m.360buyimg.com/babel/jfs/t1/77338/37/21808/214928/63885d81Eca316311/3d1fbda3a472ad0a.jpg!q70.dpg"}], "unique_index": "/img[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/div[1]", "allXPaths": ["/div[1]", "//div[contains(., '康佳（KONKA）电')]", "//DIV[@class='item_title']", "/html/body/div[last()-5]/div/div/div/div/div/div/div[last()-30]/div[last()-1]"], "exampleValues": [{"num": 0, "value": "康佳（KONKA）电风扇七叶柔和轻音大风量落地扇家用风扇节能低噪电扇广角摇头KF-L17D(升级款）"}, {"num": 1, "value": "小米（MI）wifi放大器prowifi信号增强器300M无线速率无线信号增强器强电版非路由器需配合路由器使用"}, {"num": 2, "value": "伊利优酸乳原味250ml*24盒/箱乳饮料早餐伴侣礼盒装时代少年团同款"}, {"num": 3, "value": "美的（Midea）智能电饭煲电饭锅迷你小容量3L匠铜聚能釜内胆一键快速饭24H预约蒸米锅FB30simple101（2-6人）"}, {"num": 4, "value": "亿力洗车机延长管出水管配件家用商用洗车机配件高压出水管钢鞭管4系10米延长管"}, {"num": 5, "value": "三星（SAMSUNG）256GBUSB3.1U盘FIT升级版+电脑车载迷你优盘高速便携学生办公读速400MB/s（Gen1）"}, {"num": 6, "value": "美菱（MeiL<PERSON>）电风扇/落地扇六叶四档加强大风量风扇家用摇头定时节能省电办公室落地式FS-40A（29）"}, {"num": 7, "value": "绿田（LUTIAN）洗车配件高压喷壶泡沫壶PA壶高泡壶发泡台州壶雪炮（适用家用机）"}, {"num": 8, "value": "农夫山泉东方树叶绿茶500ml*15瓶0糖0脂0卡无糖饮料茶饮料整箱装"}, {"num": 9, "value": "潘婷氨基酸无硅油洗发水微米净透排浊赋能300g清爽控油去油强韧蓬松"}, {"num": 10, "value": "松下（Panasonic）电吹风机大功率高速大风力低噪音速干送老婆折叠便携纳诺怡护发不伤发电家用吹风筒EH-WNA3B"}, {"num": 11, "value": "小熊（Bear）加湿器卧室婴儿家用办公室迷你空气加湿器大雾量低噪桌面高温除菌4升JSQ-C40N3"}, {"num": 12, "value": "巴布豆(BOBDOG)小波浪薄柔亲肤学步裤XL20片(12-17KG)柔软透气不闷热"}, {"num": 13, "value": "OPPO耳机oppo有线耳机通用华为小米手机Type-C接口适用于FindN/FindX3/Reno7MH135耳机"}, {"num": 14, "value": "唯他可可（VitaCoco）天然椰子水椰汁饮料富含电解质含维生素CNFC果汁330ml*12瓶"}, {"num": 15, "value": "奥克斯（AUX）工业冷风机空调扇移动商用空调扇单冷制冷器水冷空调家用冷风扇（58000风量35L大水箱）838A机械"}, {"num": 16, "value": "片仔癀皇后牌珍珠霜25g补水保湿滋润擦脸霜男女面霜护肤品"}, {"num": 17, "value": "统一100%番茄汁精选新疆番茄（不添加白砂糖、食用盐）335ml*24罐"}, {"num": 18, "value": "南极人（NanJiren）夏凉被空调被150x200cm夏天薄被子可水洗夏被芯单人宿舍儿童被"}, {"num": 19, "value": "徐福记香酥全蛋味沙琪玛营养早餐休闲零食下午茶点心饼干蛋糕469g/袋"}, {"num": 20, "value": "猫人MiiOW男士内裤男冰丝石墨烯抑菌无痕平角内裤柔软透气中腰短裤男士平角裤新品礼盒装（石墨烯抑菌）黑色+深灰+深蓝XL（120斤-140斤）"}, {"num": 21, "value": "美的（Midea）电热水瓶热水壶电水壶304不锈钢净甜水壶热水瓶多段控温保温恒温开水壶电水壶烧水壶Colour201"}, {"num": 22, "value": "绿田（LUTIAN）洗车机配件原装高压管出水管加厚耐用钢编延长管（10米）"}, {"num": 23, "value": "南极人棉被子被芯春秋四季被200*230cm6斤双人学生空调被褥垫被铺盖"}, {"num": 24, "value": "美的（Midea）【冰爽凉风】家用空调扇净化加湿冷风扇水冷降温塔扇节能遥控制冷小型无叶风扇落地冷风机AAC12AR"}, {"num": 25, "value": "农夫山泉东方树叶乌龙茶500ml*15瓶0糖0脂0卡无糖饮料茶饮料整箱装"}, {"num": 26, "value": "南极人床褥加厚榻榻米针织床褥子床垫1.8x2米双人可折叠软垫"}, {"num": 27, "value": "玉有情朱砂手串男女款护身符925梵文生肖本命佛守护神文殊菩萨手链礼物生肖牛虎-虚空藏菩萨【10ＭＭ】"}, {"num": 28, "value": "威露士健康抑菌消毒99.9%洗手液(健康呵护)袋装525ml补充装抑菌99.9%"}, {"num": 29, "value": "奥迪双钻（AULDEY）超级飞侠儿童玩具迷你变形机器人-乐迪男孩女孩生日礼物710010"}, {"num": 30, "value": "皇冠（danisa）丹麦曲奇饼干礼盒681g休闲零食早餐饼干蛋糕送礼团购印尼进口"}], "unique_index": "/div[1]", "default": "", "beforeJS": "arguments[0].innerText += \" 商品名\"", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/div[2]", "allXPaths": ["/div[2]", "//div[contains(., '￥109.00')]", "//DIV[@class='price']", "/html/body/div[last()-5]/div/div/div/div/div/div/div[last()-30]/div"], "exampleValues": [{"num": 0, "value": "109."}, {"num": 1, "value": "69."}, {"num": 2, "value": "48."}, {"num": 3, "value": "199."}, {"num": 4, "value": "99."}, {"num": 5, "value": "379."}, {"num": 6, "value": "119."}, {"num": 7, "value": "169."}, {"num": 8, "value": "69."}, {"num": 9, "value": "57."}, {"num": 10, "value": "399."}, {"num": 11, "value": "359."}, {"num": 12, "value": "32."}, {"num": 13, "value": "69."}, {"num": 14, "value": "128."}, {"num": 15, "value": "379."}, {"num": 16, "value": "20."}, {"num": 17, "value": "148."}, {"num": 18, "value": "89."}, {"num": 19, "value": "21."}, {"num": 20, "value": "99."}, {"num": 21, "value": "199."}, {"num": 22, "value": "189."}, {"num": 23, "value": "179."}, {"num": 24, "value": "799."}, {"num": 25, "value": "70."}, {"num": 26, "value": "189."}, {"num": 27, "value": "288."}, {"num": 28, "value": "12."}, {"num": 29, "value": "29."}, {"num": 30, "value": "108."}], "unique_index": "/div[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数11_文本", "desc": "", "relativeXPath": "/div[2]/span[1]", "allXPaths": ["/div[2]/span[1]", "//span[contains(., '￥')]", "//SPAN[@class='symbol']", "/html/body/div[last()-5]/div/div/div/div/div/div/div[last()-30]/div/span[last()-1]"], "exampleValues": [{"num": 0, "value": "￥"}, {"num": 1, "value": "￥"}, {"num": 2, "value": "￥"}, {"num": 3, "value": "￥"}, {"num": 4, "value": "￥"}, {"num": 5, "value": "￥"}, {"num": 6, "value": "￥"}, {"num": 7, "value": "￥"}, {"num": 8, "value": "￥"}, {"num": 9, "value": "￥"}, {"num": 10, "value": "￥"}, {"num": 11, "value": "￥"}, {"num": 12, "value": "￥"}, {"num": 13, "value": "￥"}, {"num": 14, "value": "￥"}, {"num": 15, "value": "￥"}, {"num": 16, "value": "￥"}, {"num": 17, "value": "￥"}, {"num": 18, "value": "￥"}, {"num": 19, "value": "￥"}, {"num": 20, "value": "￥"}, {"num": 21, "value": "￥"}, {"num": 22, "value": "￥"}, {"num": 23, "value": "￥"}, {"num": 24, "value": "￥"}, {"num": 25, "value": "￥"}, {"num": 26, "value": "￥"}, {"num": 27, "value": "￥"}, {"num": 28, "value": "￥"}, {"num": 29, "value": "￥"}, {"num": 30, "value": "￥"}], "unique_index": "/div[2]/span[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数12_文本", "desc": "", "relativeXPath": "/div[2]/span[2]", "allXPaths": ["/div[2]/span[2]", "//span[contains(., '00')]", "//SPAN[@class='price_remaining']", "/html/body/div[last()-5]/div/div/div/div/div/div/div[last()-30]/div/span"], "exampleValues": [{"num": 0, "value": "00"}, {"num": 1, "value": "00"}, {"num": 2, "value": "00"}, {"num": 3, "value": "00"}, {"num": 4, "value": "00"}, {"num": 5, "value": "00"}, {"num": 6, "value": "00"}, {"num": 7, "value": "00"}, {"num": 8, "value": "90"}, {"num": 9, "value": "30"}, {"num": 10, "value": "00"}, {"num": 11, "value": "00"}, {"num": 12, "value": "00"}, {"num": 13, "value": "00"}, {"num": 14, "value": "00"}, {"num": 15, "value": "00"}, {"num": 16, "value": "00"}, {"num": 17, "value": "72"}, {"num": 18, "value": "00"}, {"num": 19, "value": "90"}, {"num": 20, "value": "00"}, {"num": 21, "value": "00"}, {"num": 22, "value": "00"}, {"num": 23, "value": "00"}, {"num": 24, "value": "00"}, {"num": 25, "value": "00"}, {"num": 26, "value": "00"}, {"num": 27, "value": "00"}, {"num": 28, "value": "90"}, {"num": 29, "value": "00"}, {"num": 30, "value": "00"}], "unique_index": "/div[2]/span[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}