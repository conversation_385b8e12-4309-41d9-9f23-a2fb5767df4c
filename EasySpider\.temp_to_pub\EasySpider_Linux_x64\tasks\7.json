{"id": 7, "name": "DownloadPic: Electronics, Cars, Fashion, Collectibles & More | eBay", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "create_time": "5/25/2023, 9:16:47 PM", "version": "0.3.1", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "Open Page", "value": "https://www.ebay.com", "desc": "List of URLs to be collected, separated by \\n for multiple lines", "type": "string", "exampleValue": "https://www.ebay.com"}], "outputParameters": [{"id": 0, "name": "para1_imageaddress", "desc": "", "type": "string", "exampleValue": "https://ir.ebaystatic.com/rs/v/fxxj3ttftm5ltcqnto1o4baovyl.png"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 5], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 4, "contentType": 0, "relative": false, "name": "para1_imageaddress", "desc": "", "extractType": 0, "relativeXPath": "/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[1]/h1[1]/a[1]/img[1]", "allXPaths": ["/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[1]/h1[1]/a[1]/img[1]", "//img[contains(., '')]", "id(\"gh-logo\")", "//IMG[@alt='eBay Logo']"], "exampleValues": [{"num": 0, "value": "https://ir.ebaystatic.com/rs/v/fxxj3ttftm5ltcqnto1o4baovyl.png"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 1}]}}, {"id": -1, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "Loop", "sequence": [4], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[6]/div[5]/ul[1]/li/a[1]/div[1]/div[1]/div[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[6]/div[5]/ul[1]/li[1]/a[1]/div[1]/div[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='hl-image__background']"]}}, {"id": -1, "index": 4, "parentId": 3, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "para2_text", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": ""}, {"num": 1, "value": ""}, {"num": 2, "value": ""}, {"num": 3, "value": ""}, {"num": 4, "value": ""}, {"num": 5, "value": ""}, {"num": 6, "value": ""}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 3, "index": 5, "parentId": 0, "type": 1, "option": 8, "title": "Loop", "sequence": [6], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "/html/body/div/div[8]/div[2]/div[1]/div[1]/div[1]/div[1]/ul[1]/li/a[1]/div[1]/img[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[6]/div[8]/div[2]/div[1]/div[1]/div[1]/div[1]/ul[1]/li[1]/a[1]/div[1]/img[1]", "//img[contains(., '')]", "//IMG[@alt='Apple iPhone 12 Pro Max, A2342, 128GB, G<PERSON><PERSON>e, Unlocked, Very Good Condition']"]}}, {"id": 4, "index": 6, "parentId": 3, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 4, "contentType": 0, "relative": true, "name": "para1_imageaddress", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "https://i.ebayimg.com/images/g/4zUAAOSwWCJkbVk9/s-l225.webp"}, {"num": 1, "value": "https://i.ebayimg.com/images/g/7zcAAOSwGqpka4eY/s-l225.webp"}, {"num": 2, "value": "https://i.ebayimg.com/images/g/gloAAOSwXGdkbVli/s-l225.webp"}, {"num": 3, "value": "https://i.ebayimg.com/images/g/h50AAOSwHjlkaCjy/s-l225.webp"}, {"num": 4, "value": "https://i.ebayimg.com/images/g/~scAAOSwTh5kbplj/s-l225.webp"}, {"num": 5, "value": "https://i.ebayimg.com/images/g/ZMQAAOSw9zhkbmiq/s-l225.webp"}, {"num": 6, "value": "https://i.ebayimg.com/images/g/J~sAAOSwzvFka4s6/s-l225.webp"}, {"num": 7, "value": "https://i.ebayimg.com/images/g/7QAAAOSwFtVkNlBZ/s-l225.webp"}, {"num": 8, "value": "https://i.ebayimg.com/images/g/8SMAAOSw6N9kbowF/s-l225.webp"}, {"num": 9, "value": "https://i.ebayimg.com/images/g/km0AAOSwnm5kbmjZ/s-l225.webp"}, {"num": 10, "value": "https://i.ebayimg.com/images/g/c-oAAOSwkidka5AJ/s-l225.webp"}, {"num": 11, "value": "https://i.ebayimg.com/images/g/IgQAAOSwaltkVTWg/s-l225.webp"}, {"num": 12, "value": "https://i.ebayimg.com/images/g/~~YAAOSwFC9kbpmV/s-l225.webp"}, {"num": 13, "value": "https://i.ebayimg.com/images/g/mk8AAOSwPgFka5GS/s-l225.webp"}, {"num": 14, "value": "https://i.ebayimg.com/images/g/ITsAAOSw~lpkbmm-/s-l225.webp"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 1}]}}]}