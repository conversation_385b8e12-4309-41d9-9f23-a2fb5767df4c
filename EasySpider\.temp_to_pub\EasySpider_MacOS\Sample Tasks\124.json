{"id": 124, "name": "Dynamic Iframe", "url": "https://www.easyspider.cn/iframe.html", "links": "https://www.easyspider.cn/iframe.html", "create_time": "7/4/2023, 8:25:10 PM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "https://www.easyspider.cn/iframe.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.easyspider.cn/iframe.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.easyspider.cn/iframe.html"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "IFRAME TEST"}, {"id": 1, "name": "参数2_链接地址", "desc": "", "type": "string", "exampleValue": "https://github.com/NaiboWang/EasySpider/wiki"}, {"id": 2, "name": "参数3_文本", "desc": "", "type": "string", "exampleValue": "Row 1, Cell 1"}, {"id": 3, "name": "参数4_文本", "desc": "", "type": "string", "exampleValue": "EasySpider: Visual Web Crawler"}, {"id": 4, "name": "参数5_文本", "desc": "", "type": "string", "exampleValue": "Row1,Cell1"}, {"id": 5, "name": "参数6_文本", "desc": "", "type": "string", "exampleValue": "Row1,Cell2"}, {"id": 6, "name": "参数7_文本", "desc": "", "type": "string", "exampleValue": "Header1"}, {"id": 7, "name": "参数8_文本", "desc": "", "type": "string", "exampleValue": "Header2"}, {"id": 8, "name": "参数10_文本", "desc": "", "type": "string", "exampleValue": "IFRAME TEST"}, {"id": 9, "name": "参数11_文本", "desc": "", "type": "string", "exampleValue": "Designandexecutewebscrapingtasksvisually,justlikeusingExcel,regardlessofcodingexperience."}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 8, 9, 11], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.easyspider.cn/iframe.html", "links": "https://www.easyspider.cn/iframe.html", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h1[1]", "allXPaths": ["/html/body/h1[1]", "//h1[contains(., 'IFRAME TES')]", "/html/body/h1"], "exampleValues": [{"num": 0, "value": "IFRAME TEST"}], "unique_index": "c5t4uk4z7juljo8jmu7", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/table[1]/tbody[1]/tr", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/table[1]/tbody[1]/tr[1]", "//tr[contains(., '')]", "/html/body/table/tbody/tr[last()-2]"]}}, {"id": -1, "index": 4, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/td[1]", "allXPaths": ["/td[1]", "//td[contains(., 'Row 1, Cel')]", "/html/body/table/tbody/tr[last()-1]/td[last()-1]"], "exampleValues": [{"num": 0, "value": "Row1,Cell1"}], "unique_index": "/td[1]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/td[2]", "allXPaths": ["/td[2]", "//td[contains(., 'Row 1, Cel')]", "/html/body/table/tbody/tr[last()-1]/td"], "exampleValues": [{"num": 0, "value": "Row1,Cell2"}], "unique_index": "/td[2]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数3_文本", "desc": "", "relativeXPath": "/th[1]", "allXPaths": ["/th[1]", "//th[contains(., 'Header 1')]", "/html/body/table/tbody/tr[last()-2]/th[last()-1]"], "exampleValues": [{"num": 1, "value": "Header1"}], "unique_index": "/th[1]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/th[2]", "allXPaths": ["/th[2]", "//th[contains(., '<PERSON>er 2')]", "/html/body/table/tbody/tr[last()-2]/th"], "exampleValues": [{"num": 1, "value": "Header2"}], "unique_index": "/th[2]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": -1, "index": 5, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[1]/ul[1]/li[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[1]/ul[1]/li[1]", "//li[contains(., 'Home')]", "//LI[@class='nav-item']", "/html/body/div[last()-7]/div/div/ul/li[last()-5]"], "exampleValues": [{"num": 0, "value": "Home"}], "unique_index": "gvkh1kd9fd8ljo8k8qg", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [7], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[3]/div[1]/div", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[3]/div[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='func-item']", "/html/body/div[last()-6]/div/div[last()-2]"]}}, {"id": -1, "index": 7, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/div[2]", "allXPaths": ["/div[2]", "//div[contains(., 'Suitable f')]", "//DIV[@class='func-title']", "/html/body/div[last()-6]/div/div[last()-2]/div[last()-1]"], "exampleValues": [{"num": 0, "value": "SuitableforAnyoneintheWorkplace"}], "unique_index": "/div[2]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/div[3]", "allXPaths": ["/div[3]", "//div[contains(., 'Design and')]", "//DIV[@class='func-desc']", "/html/body/div[last()-6]/div/div[last()-2]/div"], "exampleValues": [{"num": 0, "value": "Designandexecutewebscrapingtasksvisually,justlikeusingExcel,regardlessofcodingexperience."}], "unique_index": "/div[3]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 2, "index": 8, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h1[1]", "allXPaths": ["/html/body/h1[1]", "//h1[contains(., 'IFRAME TES')]", "/html/body/h1"], "exampleValues": [{"num": 0, "value": "IFRAME TEST"}], "unique_index": "jamaji3jfhljo94msg", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": false, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/html/body/div[2]/div[1]/div[1]/ul[1]/li[2]/a[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[1]/ul[1]/li[2]/a[1]", "//a[contains(., 'Documentat')]", "/html/body/div[last()-7]/div/div/ul/li[last()-4]/a"], "exampleValues": [{"num": 0, "value": "https://github.com/NaiboWang/EasySpider/wiki"}], "unique_index": "bud5cdkjta7ljo94wf8", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数3_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/table[1]/tbody[1]/tr[2]/td[1]", "allXPaths": ["/html/body/table[1]/tbody[1]/tr[2]/td[1]", "//td[contains(., 'Row 1, Cel')]", "/html/body/table/tbody/tr[last()-1]/td[last()-1]"], "exampleValues": [{"num": 0, "value": "Row 1, Cell 1"}], "unique_index": "wvcxdwkrvutljo954sk", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数4_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[1]/div[1]/div[1]/div[1]", "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[1]", "//div[contains(., 'EasySpider')]", "//DIV[@class='move-title']", "/html/body/div[last()-8]/div/div[last()-2]/div[last()-4]"], "exampleValues": [{"num": 0, "value": "EasySpider: Visual Web Crawler"}], "unique_index": "a2i77k15454ljo958tg", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 3, "index": 9, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [10], "isInLoop": false, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/table[1]/tbody[1]/tr", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/table[1]/tbody[1]/tr[1]", "//tr[contains(., '')]", "/html/body/table/tbody/tr[last()-2]"]}}, {"id": 5, "index": 10, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/td[1]", "allXPaths": ["/td[1]", "//td[contains(., 'Row 1, Cel')]", "/html/body/table/tbody/tr[last()-1]/td[last()-1]"], "exampleValues": [{"num": 0, "value": "Row1,Cell1"}], "unique_index": "/td[1]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数6_文本", "desc": "", "relativeXPath": "/td[2]", "allXPaths": ["/td[2]", "//td[contains(., 'Row 1, Cel')]", "/html/body/table/tbody/tr[last()-1]/td"], "exampleValues": [{"num": 0, "value": "Row1,Cell2"}], "unique_index": "/td[2]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数7_文本", "desc": "", "relativeXPath": "/th[1]", "allXPaths": ["/th[1]", "//th[contains(., 'Header 1')]", "/html/body/table/tbody/tr[last()-2]/th[last()-1]"], "exampleValues": [{"num": 1, "value": "Header1"}], "unique_index": "/th[1]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数8_文本", "desc": "", "relativeXPath": "/th[2]", "allXPaths": ["/th[2]", "//th[contains(., '<PERSON>er 2')]", "/html/body/table/tbody/tr[last()-2]/th"], "exampleValues": [{"num": 1, "value": "Header2"}], "unique_index": "/th[2]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数10_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/h1[1]", "allXPaths": ["/html/body/h1[1]", "//h1[contains(., 'IFRAME TES')]", "/html/body/h1"], "exampleValues": [{"num": 0, "value": "IFRAME TEST"}], "unique_index": "hm0p4o1gussljo973rk", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 4, "index": 11, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [12], "isInLoop": false, "position": 3, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[3]/div[1]/div", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[3]/div[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='func-item']", "/html/body/div[last()-6]/div/div[last()-2]"]}}, {"id": 6, "index": 12, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/div[2]", "allXPaths": ["/div[2]", "//div[contains(., 'Suitable f')]", "//DIV[@class='func-title']", "/html/body/div[last()-6]/div/div[last()-2]/div[last()-1]"], "exampleValues": [{"num": 0, "value": "SuitableforAnyoneintheWorkplace"}], "unique_index": "/div[2]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数11_文本", "desc": "", "relativeXPath": "/div[3]", "allXPaths": ["/div[3]", "//div[contains(., 'Design and')]", "//DIV[@class='func-desc']", "/html/body/div[last()-6]/div/div[last()-2]/div"], "exampleValues": [{"num": 0, "value": "Designandexecutewebscrapingtasksvisually,justlikeusingExcel,regardlessofcodingexperience."}], "unique_index": "/div[3]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}