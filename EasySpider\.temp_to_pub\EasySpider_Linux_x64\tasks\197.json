{"id": 197, "name": "新web采集任务", "url": "https://space.bilibili.com/291929894/favlist", "links": "https://space.bilibili.com/291929894/favlist", "create_time": "", "update_time": "7/23/2023, 8:08:05 PM", "version": "0.5.0", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "https://space.bilibili.com/291929894/favlist", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://space.bilibili.com/291929894/favlist", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://space.bilibili.com/291929894/favlist"}], "outputParameters": [{"id": 0, "name": "参数1_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "11:48:18播放：2.4万收藏：1188UP主：开源之家投稿：2020-8-4视频已失效已被UP主删除"}, {"id": 1, "name": "参数2_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 2, "name": "参数3_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//i0.hdslb.com/bfs/archive/be27fd62c99036dce67efface486fb0a88ffed06.jpg@320w_200h_1c_!web-space-favlist-video.webp"}, {"id": 3, "name": "参数4_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "播放：2.4万"}, {"id": 4, "name": "参数5_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "收藏：1188"}, {"id": 5, "name": "参数6_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "UP主：开源之家"}, {"id": 6, "name": "参数7_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "投稿：2020-8-4"}, {"id": 7, "name": "参数8_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "视频已失效"}, {"id": 8, "name": "参数9_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "已被UP主删除"}, {"id": 9, "name": "参数10_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "已失效视频"}, {"id": 10, "name": "参数11_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 11, "name": "参数12_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "收藏于：2021-12-27"}, {"id": 12, "name": "参数13_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "取消收藏"}, {"id": 13, "name": "参数14_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "移动到"}, {"id": 14, "name": "参数15_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "复制到"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 3, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://space.bilibili.com/291929894/favlist", "links": "https://space.bilibili.com/291929894/favlist", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3, 4], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[4]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/ul[1]/li", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "//a", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 1}}, {"id": 4, "index": 4, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": true, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[4]/div[1]/div[1]/div[2]/div[3]/ul[1]/li", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[2]/div[3]/ul[1]/li[1]", "//li[contains(., '11:48:18播放')]", "//LI[@class='small-item disabled']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]"]}}, {"id": 5, "index": 5, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '11:48:18播放')]", "//A[@class='cover cover-normal disabled']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "11:48:18播放：2.4万收藏：1188UP主：开源之家投稿：2020-8-4视频已失效已被UP主删除"}], "unique_index": "/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '11:48:18播放')]", "//A[@class='cover cover-normal disabled']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数3_图片地址", "desc": "", "relativeXPath": "/a[1]/div[1]/picture[1]/img[1]", "allXPaths": ["/a[1]/div[1]/picture[1]/img[1]", "//img[contains(., '')]", "//IMG[@alt='已失效视频']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/a[last()-1]/div[last()-2]/picture/img"], "exampleValues": [{"num": 0, "value": "//i0.hdslb.com/bfs/archive/be27fd62c99036dce67efface486fb0a88ffed06.jpg@320w_200h_1c_!web-space-favlist-video.webp"}], "unique_index": "/a[1]/div[1]/picture[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 1}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/a[1]/div[2]/div[1]/p[1]", "allXPaths": ["/a[1]/div[2]/div[1]/p[1]", "//p[contains(., '播放：2.4万')]", "//P[@class='view']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/a[last()-1]/div[last()-1]/div/p[last()-3]"], "exampleValues": [{"num": 0, "value": "播放：2.4万"}], "unique_index": "/a[1]/div[2]/div[1]/p[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/a[1]/div[2]/div[1]/p[2]", "allXPaths": ["/a[1]/div[2]/div[1]/p[2]", "//p[contains(., '收藏：1188')]", "//P[@class='favorite']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/a[last()-1]/div[last()-1]/div/p[last()-2]"], "exampleValues": [{"num": 0, "value": "收藏：1188"}], "unique_index": "/a[1]/div[2]/div[1]/p[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数6_文本", "desc": "", "relativeXPath": "/a[1]/div[2]/div[1]/p[3]", "allXPaths": ["/a[1]/div[2]/div[1]/p[3]", "//p[contains(., 'UP主：开源之家')]", "//P[@class='author']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/a[last()-1]/div[last()-1]/div/p[last()-1]"], "exampleValues": [{"num": 0, "value": "UP主：开源之家"}], "unique_index": "/a[1]/div[2]/div[1]/p[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数7_文本", "desc": "", "relativeXPath": "/a[1]/div[2]/div[1]/p[4]", "allXPaths": ["/a[1]/div[2]/div[1]/p[4]", "//p[contains(., '投稿：2020-8-')]", "//P[@class='pubdate']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/a[last()-1]/div[last()-1]/div/p"], "exampleValues": [{"num": 0, "value": "投稿：2020-8-4"}], "unique_index": "/a[1]/div[2]/div[1]/p[4]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数8_文本", "desc": "", "relativeXPath": "/a[1]/div[3]/p[1]", "allXPaths": ["/a[1]/div[3]/p[1]", "//p[contains(., '视频已失效')]", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/a[last()-1]/div/p"], "exampleValues": [{"num": 0, "value": "视频已失效"}], "unique_index": "/a[1]/div[3]/p[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/a[1]/div[3]/div[2]", "allXPaths": ["/a[1]/div[3]/div[2]", "//div[contains(., '已被UP主删除')]", "//DIV[@class='delete-from']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/a[last()-1]/div/div"], "exampleValues": [{"num": 0, "value": "已被UP主删除"}], "unique_index": "/a[1]/div[3]/div[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数10_链接文本", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '已失效视频')]", "//A[@class='title']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/a"], "exampleValues": [{"num": 0, "value": "已失效视频"}], "unique_index": "/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数11_链接地址", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '已失效视频')]", "//A[@class='title']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/a"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数12_文本", "desc": "", "relativeXPath": "/div[1]", "allXPaths": ["/div[1]", "//div[contains(., '')]", "//DIV[@class='meta pubdate']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/div[last()-2]"], "exampleValues": [{"num": 0, "value": "收藏于：2021-12-27"}], "unique_index": "/div[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数13_文本", "desc": "", "relativeXPath": "/div[2]/ul[1]/li[1]", "allXPaths": ["/div[2]/ul[1]/li[1]", "//li[contains(., '')]", "//LI[@class='be-dropdown-item be-dropdown-item-delimiter']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/div[last()-1]/ul/li[last()-2]"], "exampleValues": [{"num": 0, "value": "取消收藏"}], "unique_index": "/div[2]/ul[1]/li[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数14_文本", "desc": "", "relativeXPath": "/div[2]/ul[1]/li[2]", "allXPaths": ["/div[2]/ul[1]/li[2]", "//li[contains(., '')]", "//LI[@class='be-dropdown-item']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/div[last()-1]/ul/li[last()-1]"], "exampleValues": [{"num": 0, "value": "移动到"}], "unique_index": "/div[2]/ul[1]/li[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数15_文本", "desc": "", "relativeXPath": "/div[2]/ul[1]/li[3]", "allXPaths": ["/div[2]/ul[1]/li[3]", "//li[contains(., '')]", "//LI[@class='be-dropdown-item']", "/html/body/div[last()-5]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-8]/div[last()-1]/ul/li"], "exampleValues": [{"num": 0, "value": "复制到"}], "unique_index": "/div[2]/ul[1]/li[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}