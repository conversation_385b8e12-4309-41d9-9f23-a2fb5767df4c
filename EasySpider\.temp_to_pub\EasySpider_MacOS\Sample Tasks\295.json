{"id": 295, "name": "【软科排名】-中国最好学科排名|最权威的大学学科|高校学科排名", "url": "https://www.shanghairanking.cn/rankings/bcsr/2023", "links": "https://www.shanghairanking.cn/rankings/bcsr/2023", "create_time": "12/15/2023, 2:19:03 PM", "update_time": "12/17/2023, 12:43:20 PM", "version": "0.6.0", "saveThreshold": 1000, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "TTTT", "dataWriteMode": 1, "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "browser": "chrome", "desc": "https://www.shanghairanking.cn/rankings/bcsr/2023", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.shanghairanking.cn/rankings/bcsr/2023", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.shanghairanking.cn/rankings/bcsr/2023"}, {"id": 1, "name": "loopTimes_1", "nodeId": 6, "nodeName": "循环点击下一页", "desc": "循环循环点击下一页执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1"}, {"id": 1, "name": "参数2_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1"}, {"id": 2, "name": "参数3_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "前3%"}, {"id": 3, "name": "参数4_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://www.shanghairanking.cn/_uni/logo/28312850.png"}, {"id": 4, "name": "参数5_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "复旦大学\n            "}, {"id": 5, "name": "参数6_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/institution/fudan-university"}, {"id": 6, "name": "参数7_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/_nuxt/img/uncollection.388fe56.svg"}, {"id": 7, "name": "参数8_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n            "}, {"id": 8, "name": "参数9_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/institution/fudan-university"}, {"id": 9, "name": "参数10_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1044"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.shanghairanking.cn/rankings/bcsr/2023", "links": "https://www.shanghairanking.cn/rankings/bcsr/2023", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环 - 不固定元素列表", "sequence": [3, 6], "isInLoop": false, "position": 1, "parameters": {"history": 7, "tabIndex": -1, "useLoop": false, "xpath": "//a[1]/span[2]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "//*[@id=\"01\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"02\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"04\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"04\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"04\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"05\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"05\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"05\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"06\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"06\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"06\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[10]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[11]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[12]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[13]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[14]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[10]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[11]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[12]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[13]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[14]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[15]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[16]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[17]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[18]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[19]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[20]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[21]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[22]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[23]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[24]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[25]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[26]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[27]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[28]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[29]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[30]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[31]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[32]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[33]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[34]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[35]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[36]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[37]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[38]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[10]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[5]/a[1]/span[2]", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 7, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ""}}, {"id": 5, "index": 4, "parentId": 4, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[2]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[2]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[1]", "//tr[contains(., '')]", "/html/body/div[last()-5]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]"]}}, {"id": 7, "index": 5, "parentId": 5, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/td[1]/div[1]", "allXPaths": ["/td[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='ranking']", "/html/body/div[last()-5]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-4]/div"], "exampleValues": [{"num": 0, "value": "1"}], "unique_index": "/td[1]/div[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/td[2]/span[1]", "allXPaths": ["/td[2]/span[1]", "//span[contains(., '1')]", "/html/body/div[last()-5]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-3]/span"], "exampleValues": [{"num": 0, "value": "1"}], "unique_index": "/td[2]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数3_文本", "desc": "", "relativeXPath": "/td[3]", "allXPaths": ["/td[3]", "//td[contains(., '')]", "/html/body/div[last()-5]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-2]"], "exampleValues": [{"num": 0, "value": "前3%"}], "unique_index": "/td[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数4_图片地址", "desc": "", "relativeXPath": "/td[4]/div[1]/div[1]/img[1]", "allXPaths": ["/td[4]/div[1]/div[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='univ-logo']", "//IMG[@alt='复旦大学']", "/html/body/div[last()-5]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div[last()-1]/img"], "exampleValues": [{"num": 0, "value": "https://www.shanghairanking.cn/_uni/logo/28312850.png"}], "unique_index": "/td[4]/div[1]/div[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数5_链接文本", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '复旦大学')]", "//A[@class='name-cn']", "/html/body/div[last()-5]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div/div[last()-1]/div/div/a"], "exampleValues": [{"num": 0, "value": "复旦大学\n            "}], "unique_index": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数6_链接地址", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '复旦大学')]", "//A[@class='name-cn']", "/html/body/div[last()-5]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div/div[last()-1]/div/div/a"], "exampleValues": [{"num": 0, "value": "/institution/fudan-university"}], "unique_index": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数7_图片地址", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/img[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-5]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div/div[last()-1]/div/div/div/img"], "exampleValues": [{"num": 0, "value": "/_nuxt/img/uncollection.388fe56.svg"}], "unique_index": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数8_链接文本", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "//a[contains(., '')]", "//A[@class='name-en']", "/html/body/div[last()-5]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div/div/div/div/a"], "exampleValues": [{"num": 0, "value": "\n            "}], "unique_index": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数9_链接地址", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "//a[contains(., '')]", "//A[@class='name-en']", "/html/body/div[last()-5]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div/div/div/div/a"], "exampleValues": [{"num": 0, "value": "/institution/fudan-university"}], "unique_index": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/td[5]", "allXPaths": ["/td[5]", "//td[contains(., '')]", "/html/body/div[last()-5]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td"], "exampleValues": [{"num": 0, "value": "1044"}], "unique_index": "/td[5]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 4, "index": 6, "parentId": 2, "type": 1, "option": 8, "title": "循环点击下一页", "sequence": [4, 7], "isInLoop": true, "position": 1, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"ant-pagination-next\")]/a[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[2]/div[1]/div[1]/ul[1]/li[5]/a[1]", "//a[contains(., '')]", "//A[@class='ant-pagination-item-link']", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/ul/li[last()-1]/a"]}}, {"id": 6, "index": 7, "parentId": 4, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 8, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[2]/div[1]/div[1]/ul[1]/li[5]/a[1]", "//a[contains(., '')]", "//A[@class='ant-pagination-item-link']", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/ul/li[last()-1]/a"]}}, {"id": -1, "index": 8, "parentId": 2, "type": 2, "option": 9, "title": "判断条件 - 从左往右依次判断", "sequence": [11, 14, 19], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": -1, "parentId": 3, "index": 9, "type": 3, "option": 10, "title": "无条件", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "parentId": 3, "index": 10, "type": 3, "option": 10, "title": "无条件", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"index": 11, "id": -1, "parentId": 3, "type": 3, "option": 10, "title": "针对当前循环项的JavaScript命令返回值", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 7, "value": "", "code": "return arguments[0].innerText.length < 3", "waitTime": 0}, "position": 0}, {"id": -1, "index": 12, "parentId": 0, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [13], "isInLoop": false, "position": 1, "parameters": {"history": 9, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div/div[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]", "//div[contains(., '01 哲学')]", "//DIV[@class='subject-category']", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div[last()-1]/div[last()-11]/div[last()-1]"]}}, {"id": -1, "index": 13, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 9, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数11_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "01 哲学"}], "unique_index": "y4bvlameqalq665424", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"index": 14, "id": -1, "parentId": 3, "type": 3, "option": 10, "title": "无条件", "sequence": [16, 15], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 15, "parentId": 7, "type": 0, "option": 5, "title": "跳过当前循环", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 4, "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"id": -1, "index": 16, "parentId": 7, "type": 0, "option": 5, "title": "执行Python代码", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 5, "code": "print(\"skip\")", "waitTime": 0, "recordASField": 0, "paraType": "text", "emailConfig": {"host": "", "port": 465, "username": "", "password": "", "from": "", "to": "", "subject": "", "content": ""}}}, {"id": -1, "index": 17, "parentId": 2, "type": 1, "option": 8, "title": "循环点击每个元素", "sequence": [18], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 2, "pathList": "//*[@id=\"01\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"02\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"03\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"04\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"04\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"04\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"05\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"05\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"05\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"06\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"06\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"06\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[10]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[11]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[12]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[13]/a[1]/span[2]\n//*[@id=\"07\"]/div[2]/div[14]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[10]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[11]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[12]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[13]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[14]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[15]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[16]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[17]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[18]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[19]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[20]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[21]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[22]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[23]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[24]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[25]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[26]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[27]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[28]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[29]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[30]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[31]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[32]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[33]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[34]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[35]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[36]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[37]/a[1]/span[2]\n//*[@id=\"08\"]/div[2]/div[38]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"09\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[6]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[7]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[8]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[9]/a[1]/span[2]\n//*[@id=\"10\"]/div[2]/div[10]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"12\"]/div[2]/div[5]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[1]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[2]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[3]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[4]/a[1]/span[2]\n//*[@id=\"13\"]/div[2]/div[5]/a[1]/span[2]", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 18, "parentId": 3, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "newTab": 1, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ""}}, {"index": 19, "id": -1, "parentId": 3, "type": 3, "option": 10, "title": "无条件", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 2}]}