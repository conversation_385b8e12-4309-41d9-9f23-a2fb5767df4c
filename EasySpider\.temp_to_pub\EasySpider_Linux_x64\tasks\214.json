{"id": 214, "name": "弹窗示例", "url": "http://localhost:63342/EasySpider_portal/test_pages/alert_test.html?_ijt=bhcd4kjgmci10pi60o10up8aip&_ij_reload=RELOAD_ON_SAVE", "links": "https://easyspider.cn/test_pages/alert_test.html", "create_time": "12/5/2023, 1:20:36 AM", "update_time": "12/5/2023, 1:48:25 AM", "version": "0.6.0", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "http://localhost:63342/EasySpider_portal/test_pages/alert_test.html?_ijt=bhcd4kjgmci10pi60o10up8aip&_ij_reload=RELOAD_ON_SAVE", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://easyspider.cn/test_pages/alert_test.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://easyspider.cn/test_pages/alert_test.html"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n    TEST CONTENT\n"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "http://localhost:63342/EasySpider_portal/test_pages/alert_test.html?_ijt=bhcd4kjgmci10pi60o10up8aip&_ij_reload=RELOAD_ON_SAVE", "links": "https://easyspider.cn/test_pages/alert_test.html", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击点击我", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/button[1]", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/button[1]", "//button[contains(., '点击我')]", "/html/body/button"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/p[1]", "allXPaths": ["/html/body/p[1]", "//p[contains(., 'TEST')]", "/html/body/p"], "exampleValues": [{"num": 0, "value": "\n    TEST CONTENT\n"}], "unique_index": "bamas1sffoslpr6gdvm", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 4, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": "8", "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text", "alertHandleType": 1}}]}