{"id": 144, "name": "长安的荔枝 (豆瓣)", "url": "https://book.douban.com/subject/36104107/", "links": "https://book.douban.com/subject/36104107/", "create_time": "7/6/2023, 5:02:52 PM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "csv", "containJudge": false, "desc": "https://book.douban.com/subject/36104107/", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://book.douban.com/subject/36104107/", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://book.douban.com/subject/36104107/"}], "outputParameters": [{"id": 0, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "string", "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 5], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://book.douban.com/subject/36104107/", "links": "https://book.douban.com/subject/36104107/", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 9, "relative": false, "name": "自定义参数_0", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 3, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "return $x('//*[@id=\"info\"]/text()[13]')[0]", "waitTime": 0, "recordASField": "1"}}, {"id": -1, "index": 4, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "自定义参数_0", "desc": "", "extractType": 0, "relativeXPath": "//*[@id=\"info\"]/text()[13]", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 2, "index": 5, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "function getISBN(){\nlet infoElement = document.getElementById('info');\nlet infoText = infoElement.innerText;\n\nlet parts = infoText.split('ISBN:');\nif(parts.length > 1) {\n    let bindingType = parts[1].split('\\n')[0].trim();\n    return bindingType;  // \"平装\"\n} else {\n    console.log('没有找到 \"装帧：\" 后面的字符串');\n}}\nreturn getISBN()", "waitTime": 0, "recordASField": "1"}}]}