{"id": 301, "name": "教育学 - 中国科学院文献情报中心期刊分区表升级版", "url": "https://advanced.fenqubiao.com/Macro/Journal?name=%E6%95%99%E8%82%B2%E5%AD%A6&year=2022", "links": "https://advanced.fenqubiao.com/Macro/Journal?name=%E6%95%99%E8%82%B2%E5%AD%A6&year=2022", "create_time": "12/20/2023, 5:28:09 PM", "update_time": "12/20/2023, 5:46:10 PM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "dataWriteMode": 1, "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "browser": "chrome", "removeDuplicate": 0, "desc": "https://advanced.fenqubiao.com/Macro/Journal?name=%E6%95%99%E8%82%B2%E5%AD%A6&year=2022", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://advanced.fenqubiao.com/Macro/Journal?name=%E6%95%99%E8%82%B2%E5%AD%A6&year=2022", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://advanced.fenqubiao.com/Macro/Journal?name=%E6%95%99%E8%82%B2%E5%AD%A6&year=2022"}], "outputParameters": [{"id": 0, "name": "参数2_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1"}, {"id": 1, "name": "参数3_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "REVIEW OF EDUCATIONAL RESEARCH"}, {"id": 2, "name": "参数4_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/Journal/Detail/247367"}, {"id": 3, "name": "参数5_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "review"}, {"id": 4, "name": "参数6_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "0034-6543"}, {"id": 5, "name": "参数7_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "区"}, {"id": 6, "name": "区号", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://advanced.fenqubiao.com/Macro/Journal?name=%E6%95%99%E8%82%B2%E5%AD%A6&year=2022", "links": "https://advanced.fenqubiao.com/Macro/Journal?name=%E6%95%99%E8%82%B2%E5%AD%A6&year=2022", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[1]/section[2]/div[1]/div[1]/div[1]/div[2]/div[2]/table[1]/tbody[1]/tr[1]/td", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/section[2]/div[1]/div[1]/div[1]/div[2]/div[2]/table[1]/tbody[1]/tr[1]/td[1]", "//td[contains(., '1')]", "//TD[@class=' ']", "/html/body/div[last()-3]/div/div/section/div/div/div/div/div/table/tbody/tr[last()-19]/td[last()-3]"]}}, {"id": -1, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "区"}], "unique_index": "i0yk4xzxzlqdkmkqr", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}]}}, {"id": 2, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [5], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[1]/section[2]/div[1]/div[1]/div[1]/div[2]/div[2]/table[1]/tbody[1]/tr", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "skipCount": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/section[2]/div[1]/div[1]/div[1]/div[2]/div[2]/table[1]/tbody[1]/tr[1]", "//tr[contains(., '1REVIEW OF')]", "//TR[@class='odd']", "/html/body/div[last()-3]/div/div/section/div/div/div/div/div/table/tbody/tr[last()-19]"]}}, {"id": 3, "index": 5, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/td[1]", "allXPaths": ["/td[1]", "//td[contains(., '1')]", "//TD[@class=' ']", "/html/body/div[last()-3]/div/div/section/div/div/div/div/div/table/tbody/tr[last()-19]/td[last()-3]"], "exampleValues": [{"num": 0, "value": "1"}], "unique_index": "/td[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数3_链接文本", "desc": "", "relativeXPath": "/td[2]/a[1]", "allXPaths": ["/td[2]/a[1]", "//a[contains(., 'REVIEW OF')]", "/html/body/div[last()-3]/div/div/section/div/div/div/div/div/table/tbody/tr[last()-19]/td[last()-2]/a"], "exampleValues": [{"num": 0, "value": "REVIEW OF EDUCATIONAL RESEARCH"}], "unique_index": "/td[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数4_链接地址", "desc": "", "relativeXPath": "/td[2]/a[1]", "allXPaths": ["/td[2]/a[1]", "//a[contains(., 'REVIEW OF')]", "/html/body/div[last()-3]/div/div/section/div/div/div/div/div/table/tbody/tr[last()-19]/td[last()-2]/a"], "exampleValues": [{"num": 0, "value": "/Journal/Detail/247367"}], "unique_index": "/td[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/td[2]/span[1]", "allXPaths": ["/td[2]/span[1]", "//span[contains(., 'review')]", "//SPAN[@class='label label-primary box-con']", "/html/body/div[last()-3]/div/div/section/div/div/div/div/div/table/tbody/tr[last()-19]/td[last()-2]/span"], "exampleValues": [{"num": 0, "value": "review"}], "unique_index": "/td[2]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数6_文本", "desc": "", "relativeXPath": "/td[3]", "allXPaths": ["/td[3]", "//td[contains(., '0034-6543')]", "//TD[@class=' ']", "/html/body/div[last()-3]/div/div/section/div/div/div/div/div/table/tbody/tr[last()-19]/td[last()-1]"], "exampleValues": [{"num": 0, "value": "0034-6543"}], "unique_index": "/td[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数7_文本", "desc": "", "relativeXPath": "/td[4]/span[1]", "allXPaths": ["/td[4]/span[1]", "//span[contains(., '区')]", "id(\"c247376\")", "//SPAN[@class='class']", "/html/body/div[last()-3]/div/div/section/div/div/div/div/div/table/tbody/tr[last()-19]/td/span"], "exampleValues": [{"num": 0, "value": "区"}], "unique_index": "/td[4]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "splitLine": 0}, {"nodeType": 0, "contentType": 14, "relative": true, "name": "区号", "desc": "", "iframe": false, "extractType": 0, "relativeXPath": "/td[4]/span[1]", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "data-attr", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}]}}]}