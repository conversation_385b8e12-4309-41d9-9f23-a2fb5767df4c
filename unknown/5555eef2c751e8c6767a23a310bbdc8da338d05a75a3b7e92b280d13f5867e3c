{"id": 44, "name": "ebay自定义", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.ebay.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.ebay.com"}], "outputParameters": [{"id": 0, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "string", "exampleValue": ""}, {"id": 1, "name": "自定义操作2", "desc": "自定义操作返回的数据", "type": "string", "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "scrollType": 0, "scrollCount": 0}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "codeMode": 0, "code": "return document.querySelectorAll(\"#mainContent > div.hl-cat-nav > ul > li.hl-cat-nav__active\")[0].tagName", "waitTime": 0, "recordASField": true}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作2", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "codeMode": "1", "code": "python D:/tes123t.py --test 123", "waitTime": 0, "recordASField": true}}]}