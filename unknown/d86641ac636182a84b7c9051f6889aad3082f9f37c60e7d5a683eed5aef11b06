{"id": 112, "name": "中国地震台网——历史查询 - 子元素", "url": "http://www.ceic.ac.cn/history", "links": "http://www.ceic.ac.cn/history", "create_time": "7/6/2023, 5:46:25 PM", "update_time": "2024-01-03 16:13:28", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "json", "saveName": "TTTTT", "dataWriteMode": 1, "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": true, "browser": "chrome", "removeDuplicate": 0, "desc": "http://www.ceic.ac.cn/history", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://www.ceic.ac.cn/history", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "http://www.ceic.ac.cn/history"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "12", "value": "12"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "exampleValue": "3.4"}, {"id": 1, "name": "参数2_文本", "desc": "", "exampleValue": "2023-07-0603:25:04"}, {"id": 2, "name": "参数3_文本", "desc": "", "exampleValue": "40.13"}, {"id": 3, "name": "参数4_文本", "desc": "", "exampleValue": "77.12"}, {"id": 4, "name": "参数5_文本", "desc": "", "exampleValue": "10"}, {"id": 5, "name": "参数6_链接文本", "desc": "", "exampleValue": "新疆克孜勒苏州阿图什市"}, {"id": 6, "name": "参数7_链接地址", "desc": "", "exampleValue": "https://news.ceic.ac.cn/CD20230706032505.html"}, {"id": 7, "name": "参数8_文本", "desc": "", "exampleValue": "震级(M)"}, {"id": 8, "name": "参数9_文本", "desc": "", "exampleValue": "发震时刻(UTC+8)"}, {"id": 9, "name": "参数10_文本", "desc": "", "exampleValue": "纬度(°)"}, {"id": 10, "name": "参数11_文本", "desc": "", "exampleValue": "经度(°)"}, {"id": 11, "name": "参数12_文本", "desc": "", "exampleValue": "深度(千米)"}, {"id": 12, "name": "参数13_文本", "desc": "", "exampleValue": "参考位置"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 11], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": "0"}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "http://www.ceic.ac.cn/history", "links": "http://www.ceic.ac.cn/history", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"weidu1\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "12", "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[2]/input[1]", "//input[contains(., '')]", "id(\"weidu1\")", "//INPUT[@class='span1']", "//INPUT[@name='weidu1']", "/html/body/div[last()-3]/div[last()-1]/div/div/div[last()-1]/form/div[last()-3]/input[last()-1]"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search\"]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[5]/a[1]", "//a[contains(., '查询')]", "id(\"search\")", "//A[@class='check']", "/html/body/div[last()-3]/div[last()-1]/div/div/div[last()-1]/form/div/a"]}}, {"id": -1, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [6, 5], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"pagination\")]/ul[1]/li[last()-1]/a[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 10, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '»')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div/div/div/ul/li[last()-1]/a"], "waitType": "1"}}, {"id": -1, "index": 5, "parentId": 4, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "//*[contains(@class, \"pagination\")]/ul[1]/li[10]/a[1]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '»')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div/div/div/ul/li[last()-1]/a"], "loopType": 0}}, {"id": -1, "index": 6, "parentId": 4, "type": 1, "option": 8, "title": "循环", "sequence": [7], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr[1]", "//tr[contains(., '震级(M)发震时刻(')]", "//TR[@class='speed-tr-h1']", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]"]}}, {"id": -1, "index": 7, "parentId": 5, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/td[1]", "allXPaths": ["/td[1]", "//td[contains(., '3.8')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr/td[last()-5]"], "exampleValues": [{"num": 0, "value": "3.8"}, {"num": 2, "value": "3.8"}, {"num": 3, "value": "4.4"}, {"num": 4, "value": "5.6"}, {"num": 5, "value": "5.3"}, {"num": 6, "value": "3.1"}, {"num": 7, "value": "3.2"}, {"num": 8, "value": "3.7"}, {"num": 9, "value": "3.6"}, {"num": 10, "value": "6.2"}, {"num": 11, "value": "2.2"}, {"num": 12, "value": "3.0"}, {"num": 13, "value": "4.1"}, {"num": 14, "value": "5.2"}, {"num": 15, "value": "3.6"}, {"num": 16, "value": "3.7"}, {"num": 17, "value": "3.3"}, {"num": 18, "value": "4.3"}, {"num": 19, "value": "3.6"}, {"num": 20, "value": "6.2"}], "unique_index": "/td[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/td[2]", "allXPaths": ["/td[2]", "//td[contains(., '2023-06-11')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr/td[last()-4]"], "exampleValues": [{"num": 0, "value": "2023-06-1117:40:14"}, {"num": 2, "value": "2023-06-1801:08:11"}, {"num": 3, "value": "2023-06-1800:14:24"}, {"num": 4, "value": "2023-06-1719:35:59"}, {"num": 5, "value": "2023-06-1708:26:14"}, {"num": 6, "value": "2023-06-1708:05:51"}, {"num": 7, "value": "2023-06-1601:19:35"}, {"num": 8, "value": "2023-06-1521:58:09"}, {"num": 9, "value": "2023-06-1511:21:27"}, {"num": 10, "value": "2023-06-1510:19:24"}, {"num": 11, "value": "2023-06-1422:24:41"}, {"num": 12, "value": "2023-06-1413:39:40"}, {"num": 13, "value": "2023-06-1404:17:56"}, {"num": 14, "value": "2023-06-1316:03:43"}, {"num": 15, "value": "2023-06-1308:48:30"}, {"num": 16, "value": "2023-06-1305:52:59"}, {"num": 17, "value": "2023-06-1200:04:18"}, {"num": 18, "value": "2023-06-1120:25:38"}, {"num": 19, "value": "2023-06-1119:29:45"}, {"num": 20, "value": "2023-06-1117:54:45"}], "unique_index": "/td[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数3_文本", "desc": "", "relativeXPath": "/td[3]", "allXPaths": ["/td[3]", "//td[contains(., '40.79')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr/td[last()-3]"], "exampleValues": [{"num": 0, "value": "40.79"}, {"num": 2, "value": "35.80"}, {"num": 3, "value": "35.79"}, {"num": 4, "value": "47.75"}, {"num": 5, "value": "41.10"}, {"num": 6, "value": "39.58"}, {"num": 7, "value": "38.31"}, {"num": 8, "value": "40.14"}, {"num": 9, "value": "32.44"}, {"num": 10, "value": "13.80"}, {"num": 11, "value": "37.12"}, {"num": 12, "value": "38.16"}, {"num": 13, "value": "41.71"}, {"num": 14, "value": "33.10"}, {"num": 15, "value": "40.18"}, {"num": 16, "value": "33.10"}, {"num": 17, "value": "43.38"}, {"num": 18, "value": "24.26"}, {"num": 19, "value": "48.74"}, {"num": 20, "value": "42.50"}], "unique_index": "/td[3]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/td[4]", "allXPaths": ["/td[4]", "//td[contains(., '82.63')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr/td[last()-2]"], "exampleValues": [{"num": 0, "value": "82.63"}, {"num": 2, "value": "79.78"}, {"num": 3, "value": "79.83"}, {"num": 4, "value": "147.60"}, {"num": 5, "value": "142.80"}, {"num": 6, "value": "82.57"}, {"num": 7, "value": "89.40"}, {"num": 8, "value": "77.46"}, {"num": 9, "value": "94.24"}, {"num": 10, "value": "120.85"}, {"num": 11, "value": "114.78"}, {"num": 12, "value": "88.79"}, {"num": 13, "value": "80.81"}, {"num": 14, "value": "75.80"}, {"num": 15, "value": "83.80"}, {"num": 16, "value": "86.73"}, {"num": 17, "value": "88.98"}, {"num": 18, "value": "122.47"}, {"num": 19, "value": "129.79"}, {"num": 20, "value": "142.00"}], "unique_index": "/td[4]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/td[5]", "allXPaths": ["/td[5]", "//td[contains(., '20')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr/td[last()-1]"], "exampleValues": [{"num": 0, "value": "20"}, {"num": 2, "value": "10"}, {"num": 3, "value": "10"}, {"num": 4, "value": "430"}, {"num": 5, "value": "10"}, {"num": 6, "value": "20"}, {"num": 7, "value": "10"}, {"num": 8, "value": "23"}, {"num": 9, "value": "10"}, {"num": 10, "value": "100"}, {"num": 11, "value": "10"}, {"num": 12, "value": "9"}, {"num": 13, "value": "10"}, {"num": 14, "value": "20"}, {"num": 15, "value": "18"}, {"num": 16, "value": "10"}, {"num": 17, "value": "19"}, {"num": 18, "value": "27"}, {"num": 19, "value": "17"}, {"num": 20, "value": "130"}], "unique_index": "/td[5]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数6_链接文本", "desc": "", "relativeXPath": "/td[6]/a[1]", "allXPaths": ["/td[6]/a[1]", "//a[contains(., '新疆阿克苏地区沙雅县')]", "id(\"cid\")", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr/td/a"], "exampleValues": [{"num": 0, "value": "新疆阿克苏地区沙雅县"}, {"num": 2, "value": "新疆和田地区和田县"}, {"num": 3, "value": "新疆和田地区和田县"}, {"num": 4, "value": "千岛群岛西北"}, {"num": 5, "value": "日本本州东岸近海"}, {"num": 6, "value": "新疆阿克苏地区沙雅县"}, {"num": 7, "value": "新疆巴音郭楞州若羌县"}, {"num": 8, "value": "新疆克孜勒苏州阿图什市"}, {"num": 9, "value": "西藏那曲市巴青县"}, {"num": 10, "value": "菲律宾"}, {"num": 11, "value": "河北邢台市任泽区"}, {"num": 12, "value": "新疆巴音郭楞州若羌县"}, {"num": 13, "value": "新疆阿克苏地区温宿县"}, {"num": 14, "value": "克什米尔地区"}, {"num": 15, "value": "新疆阿克苏地区沙雅县"}, {"num": 16, "value": "西藏那曲市尼玛县"}, {"num": 17, "value": "新疆吐鲁番市高昌区"}, {"num": 18, "value": "台湾花莲县海域"}, {"num": 19, "value": "黑龙江伊春市嘉荫县"}, {"num": 20, "value": "日本北海道"}], "unique_index": "/td[6]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数7_链接地址", "desc": "", "relativeXPath": "/td[6]/a[1]", "allXPaths": ["/td[6]/a[1]", "//a[contains(., '新疆阿克苏地区沙雅县')]", "id(\"cid\")", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr/td/a"], "exampleValues": [{"num": 0, "value": "https://news.ceic.ac.cn/CD20230611174015.html"}, {"num": 2, "value": "https://news.ceic.ac.cn/CD20230618010812.html"}, {"num": 3, "value": "https://news.ceic.ac.cn/CD20230618001425.html"}, {"num": 4, "value": "https://news.ceic.ac.cn/CC20230617193560.html"}, {"num": 5, "value": "https://news.ceic.ac.cn/CC20230617082615.html"}, {"num": 6, "value": "https://news.ceic.ac.cn/CD20230617080552.html"}, {"num": 7, "value": "https://news.ceic.ac.cn/CD20230616011935.html"}, {"num": 8, "value": "https://news.ceic.ac.cn/CD20230615215810.html"}, {"num": 9, "value": "https://news.ceic.ac.cn/CD20230615112127.html"}, {"num": 10, "value": "https://news.ceic.ac.cn/CC20230615101924.html"}, {"num": 11, "value": "https://news.ceic.ac.cn/CD20230614222441.html"}, {"num": 12, "value": "https://news.ceic.ac.cn/CD20230614133941.html"}, {"num": 13, "value": "https://news.ceic.ac.cn/CD20230614041757.html"}, {"num": 14, "value": "https://news.ceic.ac.cn/CC20230613160344.html"}, {"num": 15, "value": "https://news.ceic.ac.cn/CD20230613084830.html"}, {"num": 16, "value": "https://news.ceic.ac.cn/CC20230613055259.html"}, {"num": 17, "value": "https://news.ceic.ac.cn/CD20230612000419.html"}, {"num": 18, "value": "https://news.ceic.ac.cn/CD20230611202539.html"}, {"num": 19, "value": "https://news.ceic.ac.cn/CD20230611192945.html"}, {"num": 20, "value": "https://news.ceic.ac.cn/CC20230611175446.html"}], "unique_index": "/td[6]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数8_文本", "desc": "", "relativeXPath": "/th[1]", "allXPaths": ["/th[1]", "//th[contains(., '震级(M)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-5]"], "exampleValues": [{"num": 1, "value": "震级(M)"}], "unique_index": "/th[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/th[2]", "allXPaths": ["/th[2]", "//th[contains(., '发震时刻(UTC+8')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-4]"], "exampleValues": [{"num": 1, "value": "发震时刻(UTC+8)"}], "unique_index": "/th[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/th[3]", "allXPaths": ["/th[3]", "//th[contains(., '纬度(°)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-3]"], "exampleValues": [{"num": 1, "value": "纬度(°)"}], "unique_index": "/th[3]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数11_文本", "desc": "", "relativeXPath": "/th[4]", "allXPaths": ["/th[4]", "//th[contains(., '经度(°)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-2]"], "exampleValues": [{"num": 1, "value": "经度(°)"}], "unique_index": "/th[4]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数12_文本", "desc": "", "relativeXPath": "/th[5]", "allXPaths": ["/th[5]", "//th[contains(., '深度(千米)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-1]"], "exampleValues": [{"num": 1, "value": "深度(千米)"}], "unique_index": "/th[5]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数13_文本", "desc": "", "relativeXPath": "/th[6]", "allXPaths": ["/th[6]", "//th[contains(., '参考位置')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th"], "exampleValues": [{"num": 1, "value": "参考位置"}], "unique_index": "/th[6]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": -1, "index": 8, "parentId": 5, "type": 2, "option": 9, "title": "判断条件", "sequence": [9], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 7, "index": 9, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "3", "value": "新疆", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 7, "index": 10, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 4, "index": 11, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [13, 12], "isInLoop": false, "position": 3, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 2, "pathList": "//*[contains(@class, \"pagination\")]/ul[1]/li[1]/a[1]\n//*[contains(@class, \"pagination\")]/ul[1]/li[2]/a[1]\n//*[contains(@class, \"pagination\")]/ul[1]/li[3]/a[1]\n//*[contains(@class, \"pagination\")]/ul[1]/li[4]/a[1]\n//*[contains(@class, \"pagination\")]/ul[1]/li[5]/a[1]\n//*[contains(@class, \"pagination\")]/ul[1]/li[6]/a[1]\n//*[contains(@class, \"pagination\")]/ul[1]/li[7]/a[1]\n//*[contains(@class, \"pagination\")]/ul[1]/li[8]/a[1]\n//*[contains(@class, \"pagination\")]/ul[1]/li[9]/a[1]", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 6, "index": 12, "parentId": 4, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 2}}, {"id": 5, "index": 13, "parentId": 4, "type": 1, "option": 8, "title": "循环", "sequence": [15], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr[1]", "//tr[contains(., '震级(M)发震时刻(')]", "//TR[@class='speed-tr-h1']", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]"]}}, {"id": 10, "index": 14, "parentId": 8, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/td[1]", "allXPaths": ["/td[1]", "//td[contains(., '3.4')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td[last()-5]"], "exampleValues": [{"num": 0, "value": "3.4"}], "unique_index": "/td[1]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/td[2]", "allXPaths": ["/td[2]", "//td[contains(., '2023-07-06')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td[last()-4]"], "exampleValues": [{"num": 0, "value": "2023-07-0603:25:04"}], "unique_index": "/td[2]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数3_文本", "desc": "", "relativeXPath": "/td[3]", "allXPaths": ["/td[3]", "//td[contains(., '40.13')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td[last()-3]"], "exampleValues": [{"num": 0, "value": "40.13"}], "unique_index": "/td[3]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/td[4]", "allXPaths": ["/td[4]", "//td[contains(., '77.12')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td[last()-2]"], "exampleValues": [{"num": 0, "value": "77.12"}], "unique_index": "/td[4]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/td[5]", "allXPaths": ["/td[5]", "//td[contains(., '10')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td[last()-1]"], "exampleValues": [{"num": 0, "value": "10"}], "unique_index": "/td[5]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数6_链接文本", "desc": "", "relativeXPath": "/td[6]/a[1]", "allXPaths": ["/td[6]/a[1]", "//a[contains(., '新疆克孜勒苏州阿图什')]", "id(\"cid\")", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td/a"], "exampleValues": [{"num": 0, "value": "新疆克孜勒苏州阿图什市"}], "unique_index": "/td[6]/a[1]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数7_链接地址", "desc": "", "relativeXPath": "/td[6]/a[1]", "allXPaths": ["/td[6]/a[1]", "//a[contains(., '新疆克孜勒苏州阿图什')]", "id(\"cid\")", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-19]/td/a"], "exampleValues": [{"num": 0, "value": "https://news.ceic.ac.cn/CD20230706032505.html"}], "unique_index": "/td[6]/a[1]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数8_文本", "desc": "", "relativeXPath": "/th[1]", "allXPaths": ["/th[1]", "//th[contains(., '震级(M)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-5]"], "exampleValues": [{"num": 1, "value": "震级(M)"}], "unique_index": "/th[1]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/th[2]", "allXPaths": ["/th[2]", "//th[contains(., '发震时刻(UTC+8')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-4]"], "exampleValues": [{"num": 1, "value": "发震时刻(UTC+8)"}], "unique_index": "/th[2]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/th[3]", "allXPaths": ["/th[3]", "//th[contains(., '纬度(°)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-3]"], "exampleValues": [{"num": 1, "value": "纬度(°)"}], "unique_index": "/th[3]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数11_文本", "desc": "", "relativeXPath": "/th[4]", "allXPaths": ["/th[4]", "//th[contains(., '经度(°)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-2]"], "exampleValues": [{"num": 1, "value": "经度(°)"}], "unique_index": "/th[4]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数12_文本", "desc": "", "relativeXPath": "/th[5]", "allXPaths": ["/th[5]", "//th[contains(., '深度(千米)')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th[last()-1]"], "exampleValues": [{"num": 1, "value": "深度(千米)"}], "unique_index": "/th[5]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数13_文本", "desc": "", "relativeXPath": "/th[6]", "allXPaths": ["/th[6]", "//th[contains(., '参考位置')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]/th"], "exampleValues": [{"num": 1, "value": "参考位置"}], "unique_index": "/th[6]", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 7, "index": 15, "parentId": 5, "type": 2, "option": 9, "title": "判断条件 - 从左往右依次判断", "sequence": [16, 17], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": 8, "parentId": 7, "index": 16, "type": 3, "option": 10, "title": "针对当前循环项的JavaScript命令返回值", "sequence": [14], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 7, "value": "", "code": "let keywords = [\"30\", \"14\", \"16\", \"17\", \"22\"];\nlet result = false;\n\nfor (let keyword of keywords) {\n  let xpathQuery = \"./td[5]\"; //指定为第5个td元素\n  let nodes = document.evaluate(xpathQuery, arguments[0], null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n  if (nodes.singleNodeValue.innerText.includes(keyword)) {\n    result = true;\n    break;\n  }\n}\nreturn result", "waitTime": 0}, "position": 0}, {"id": 9, "parentId": 7, "index": 17, "type": 3, "option": 10, "title": "无条件", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}]}