{"id": 118, "name": "iP地址查询 502", "url": "https://www.ip138.com", "links": "https://www.ip138.com", "create_time": "7/4/2023, 7:41:22 PM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "https://www.ip138.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.ip138.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.ip138.com"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "502 Bad Gateway"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.ip138.com", "links": "https://www.ip138.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": -1, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/p", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/p[1]", "//p[contains(., '您的iP地址是：[')]", "/html/body/p[last()-2]"]}}, {"id": -1, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "", "allXPaths": ["", "//p[contains(., '您的iP地址是：[')]", "/html/body/p[last()-2]"], "exampleValues": [{"num": 0, "value": "您的iP地址是：[]来自：新加坡Singtel"}], "unique_index": "", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数2_链接文本", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '220.255.29')]", "/html/body/p[last()-2]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "**************"}], "unique_index": "/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数3_链接地址", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '220.255.29')]", "/html/body/p[last()-2]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "https://www.ip138.com/iplookup.php?ip=**************&action=2"}], "unique_index": "/a[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数4_链接文本", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '')]", "/html/body/p[last()-2]/a"], "exampleValues": [{"num": 0, "value": ""}], "unique_index": "/a[2]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数5_链接地址", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '')]", "/html/body/p[last()-2]/a"], "exampleValues": [{"num": 0, "value": "https://www.ipshudi.com/"}], "unique_index": "/a[2]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数6_图片地址", "desc": "", "relativeXPath": "/a[2]/img[1]", "allXPaths": ["/a[2]/img[1]", "//img[contains(., '')]", "/html/body/p[last()-2]/a/img"], "exampleValues": [{"num": 0, "value": "https://6.ipchaxun.net/**************.gif"}], "unique_index": "/a[2]/img[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数7_文本", "desc": "", "relativeXPath": "/a[1]/font[1]", "allXPaths": ["/a[1]/font[1]", "//font[contains(., 'ip查询api接口')]", "/html/body/p[last()-1]/a/font"], "exampleValues": [{"num": 1, "value": "ip查询api接口"}], "unique_index": "/a[1]/font[1]", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 2, "index": 4, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/center[1]/h1[1]", "allXPaths": ["/html/body/center[1]/h1[1]", "//h1[contains(., '502 Bad Ga')]", "/html/body/center[last()-1]/h1"], "exampleValues": [{"num": 0, "value": "502 Bad Gateway"}], "unique_index": "l37gwwpsg29ljnkgn7r", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}