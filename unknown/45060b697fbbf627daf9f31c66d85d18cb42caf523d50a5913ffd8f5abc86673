{"id": 184, "name": "JD IFRAME", "url": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=https://search.jd.com/Search?keyword=123&enc=utf-8&wq=123&pvid=b0985cab50aa4f6ebd24fc0208bb718e", "links": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=https://search.jd.com/Search?keyword=123&enc=utf-8&wq=123&pvid=b0985cab50aa4f6ebd24fc0208bb718e", "create_time": "7/16/2023, 12:06:18 PM", "update_time": "7/16/2023, 12:09:15 PM", "version": "0.3.6", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=https://search.jd.com/Search?keyword=123&enc=utf-8&wq=123&pvid=b0985cab50aa4f6ebd24fc0208bb718e", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=https://search.jd.com/Search?keyword=123&enc=utf-8&wq=123&pvid=b0985cab50aa4f6ebd24fc0208bb718e", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=https://search.jd.com/Search?keyword=123&enc=utf-8&wq=123&pvid=b0985cab50aa4f6ebd24fc0208bb718e"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "text", "exampleValue": "12", "value": "12"}], "outputParameters": [{"id": 0, "name": "参数1_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t\n\t\t\t\t        \t"}, {"id": 1, "name": "参数2_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//item.jd.com/10080265235775.html"}, {"id": 2, "name": "参数3_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//img10.360buyimg.com/n7/jfs/t1/211397/11/37712/151327/649ec23aF55dc129a/3af42aec4dda905b.jpg"}, {"id": 3, "name": "参数4_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "￥"}, {"id": 4, "name": "参数5_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1699.00"}, {"id": 5, "name": "参数6_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n\t\t\t\t\t\t\t\t美的（Midea）滚筒式洗衣机变频家用 内衣羽绒全自动巴氏除菌洗衣服机 十公斤大容量一级能效节能 【可洗羽绒羊毛】柔拍净洗不结团MG133 123\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t"}, {"id": 6, "name": "参数7_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//item.jd.com/10080265235775.html"}, {"id": 7, "name": "参数8_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "美的（Midea）滚筒式洗衣机变频家用内衣羽绒全自动巴氏除菌洗衣服机十公斤大容量一级能效节能【可洗羽绒羊毛】柔拍净洗不结团MG133"}, {"id": 8, "name": "参数9_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "123"}, {"id": 9, "name": "参数10_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "条评价"}, {"id": 10, "name": "参数11_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "0"}, {"id": 11, "name": "参数12_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//item.jd.com/10080265235775.html#comment"}, {"id": 12, "name": "参数13_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "美的全屋智慧甄品旗舰店"}, {"id": 13, "name": "参数14_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//mall.jd.com/index-12721035.html?from=pc"}, {"id": 14, "name": "参数15_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "对比"}, {"id": 15, "name": "参数16_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 16, "name": "参数17_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "关注"}, {"id": 17, "name": "参数18_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "javascript:;"}, {"id": 18, "name": "参数19_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "加入购物车"}, {"id": 19, "name": "参数20_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//cart.jd.com/gate.action?pid=10080265235775&pcount=1&ptype=1"}, {"id": 20, "name": "参数21_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "广告"}, {"id": 21, "name": "参数22_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 22, "name": "参数23_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//m.360buyimg.com/cc/jfs/t1/113659/27/28361/2962/62ecb1f0E6c5fc50c/b914680e87a2c8e9.png"}, {"id": 23, "name": "参数24_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "99元5件幼儿启蒙团购电话4006186622"}, {"id": 24, "name": "参数25_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自营"}, {"id": 25, "name": "参数26_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "3件8.0折"}, {"id": 26, "name": "参数27_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "7.7-7.31"}, {"id": 27, "name": "参数28_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//m.360buyimg.com/cc/jfs/t1/197706/29/27826/4384/63314bc6E21b4c3e9/8b4d5fc44efc5b3f.png"}, {"id": 28, "name": "参数29_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "每满200-20"}, {"id": 29, "name": "参数30_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "..."}, {"id": 30, "name": "参数31_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "满赠"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=https://search.jd.com/Search?keyword=123&enc=utf-8&wq=123&pvid=b0985cab50aa4f6ebd24fc0208bb718e", "links": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=https://search.jd.com/Search?keyword=123&enc=utf-8&wq=123&pvid=b0985cab50aa4f6ebd24fc0208bb718e", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "value": "12", "index": 0, "allXPaths": ["/html/body/div[3]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-13]/div[last()-2]/div/input"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"J_crumbsBar\"]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": "3", "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[5]/div[1]", "//div[contains(., '')]", "id(\"J_crumbsBar\")", "//DIV[@class='crumbs-bar']", "/html/body/div[last()-11]/div[last()-1]"]}}, {"id": 4, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li/div[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='gl-i-wrap']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div"]}}, {"id": 5, "index": 5, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/div[1]/a[1]", "allXPaths": ["/div[1]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t\n\t\t\t\t        \t"}], "unique_index": "/div[1]/a[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/div[1]/a[1]", "allXPaths": ["/div[1]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10080265235775.html"}], "unique_index": "/div[1]/a[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数3_图片地址", "desc": "", "relativeXPath": "/div[1]/a[1]/img[1]", "allXPaths": ["/div[1]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/a/img"], "exampleValues": [{"num": 0, "value": "//img10.360buyimg.com/n7/jfs/t1/211397/11/37712/151327/649ec23aF55dc129a/3af42aec4dda905b.jpg"}], "unique_index": "/div[1]/a[1]/img[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/div[2]/strong[1]/em[1]", "allXPaths": ["/div[2]/strong[1]/em[1]", "//em[contains(., '￥')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-6]/strong/em"], "exampleValues": [{"num": 0, "value": "￥"}], "unique_index": "/div[2]/strong[1]/em[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/div[2]/strong[1]/i[1]", "allXPaths": ["/div[2]/strong[1]/i[1]", "//i[contains(., '1699.00')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-6]/strong/i"], "exampleValues": [{"num": 0, "value": "1699.00"}], "unique_index": "/div[2]/strong[1]/i[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数6_链接文本", "desc": "", "relativeXPath": "/div[3]/a[1]", "allXPaths": ["/div[3]/a[1]", "//a[contains(., '美')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t美的（Midea）滚筒式洗衣机变频家用 内衣羽绒全自动巴氏除菌洗衣服机 十公斤大容量一级能效节能 【可洗羽绒羊毛】柔拍净洗不结团MG133 123\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t"}], "unique_index": "/div[3]/a[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数7_链接地址", "desc": "", "relativeXPath": "/div[3]/a[1]", "allXPaths": ["/div[3]/a[1]", "//a[contains(., '美')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10080265235775.html"}], "unique_index": "/div[3]/a[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数8_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]", "allXPaths": ["/div[3]/a[1]/em[1]", "//em[contains(., '美的（Midea）滚')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a/em"], "exampleValues": [{"num": 0, "value": "美的（Midea）滚筒式洗衣机变频家用内衣羽绒全自动巴氏除菌洗衣服机十公斤大容量一级能效节能【可洗羽绒羊毛】柔拍净洗不结团MG133"}], "unique_index": "/div[3]/a[1]/em[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/font[1]", "allXPaths": ["/div[3]/a[1]/em[1]/font[1]", "//font[contains(., '123')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a/em/font"], "exampleValues": [{"num": 0, "value": "123"}], "unique_index": "/div[3]/a[1]/em[1]/font[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/div[4]/strong[1]", "allXPaths": ["/div[4]/strong[1]", "//strong[contains(., '0条评价')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong"], "exampleValues": [{"num": 0, "value": "条评价"}], "unique_index": "/div[4]/strong[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数11_链接文本", "desc": "", "relativeXPath": "/div[4]/strong[1]/a[1]", "allXPaths": ["/div[4]/strong[1]/a[1]", "//a[contains(., '0')]", "id(\"J_comment_10080265235775\")", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong/a"], "exampleValues": [{"num": 0, "value": "0"}], "unique_index": "/div[4]/strong[1]/a[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数12_链接地址", "desc": "", "relativeXPath": "/div[4]/strong[1]/a[1]", "allXPaths": ["/div[4]/strong[1]/a[1]", "//a[contains(., '0')]", "id(\"J_comment_10080265235775\")", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/10080265235775.html#comment"}], "unique_index": "/div[4]/strong[1]/a[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数13_链接文本", "desc": "", "relativeXPath": "/div[5]/span[1]/a[1]", "allXPaths": ["/div[5]/span[1]/a[1]", "//a[contains(., '美的全屋智慧甄品旗舰')]", "//A[@class='curr-shop hd-shopname']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-3]/span/a"], "exampleValues": [{"num": 0, "value": "美的全屋智慧甄品旗舰店"}], "unique_index": "/div[5]/span[1]/a[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数14_链接地址", "desc": "", "relativeXPath": "/div[5]/span[1]/a[1]", "allXPaths": ["/div[5]/span[1]/a[1]", "//a[contains(., '美的全屋智慧甄品旗舰')]", "//A[@class='curr-shop hd-shopname']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-3]/span/a"], "exampleValues": [{"num": 0, "value": "//mall.jd.com/index-12721035.html?from=pc"}], "unique_index": "/div[5]/span[1]/a[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数15_链接文本", "desc": "", "relativeXPath": "/div[7]/a[1]", "allXPaths": ["/div[7]/a[1]", "//a[contains(., '对比')]", "//A[@class='p-o-btn contrast J_contrast contrast']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-2]"], "exampleValues": [{"num": 0, "value": "对比"}], "unique_index": "/div[7]/a[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数16_链接地址", "desc": "", "relativeXPath": "/div[7]/a[1]", "allXPaths": ["/div[7]/a[1]", "//a[contains(., '对比')]", "//A[@class='p-o-btn contrast J_contrast contrast']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-2]"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[7]/a[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数17_链接文本", "desc": "", "relativeXPath": "/div[7]/a[2]", "allXPaths": ["/div[7]/a[2]", "//a[contains(., '关注')]", "//A[@class='p-o-btn focus  J_focus']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "关注"}], "unique_index": "/div[7]/a[2]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数18_链接地址", "desc": "", "relativeXPath": "/div[7]/a[2]", "allXPaths": ["/div[7]/a[2]", "//a[contains(., '关注')]", "//A[@class='p-o-btn focus  J_focus']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[7]/a[2]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数19_链接文本", "desc": "", "relativeXPath": "/div[7]/a[3]", "allXPaths": ["/div[7]/a[3]", "//a[contains(., '加入购物车')]", "//A[@class='p-o-btn addcart']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a"], "exampleValues": [{"num": 0, "value": "加入购物车"}], "unique_index": "/div[7]/a[3]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数20_链接地址", "desc": "", "relativeXPath": "/div[7]/a[3]", "allXPaths": ["/div[7]/a[3]", "//a[contains(., '加入购物车')]", "//A[@class='p-o-btn addcart']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/a"], "exampleValues": [{"num": 0, "value": "//cart.jd.com/gate.action?pid=10080265235775&pcount=1&ptype=1"}], "unique_index": "/div[7]/a[3]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数21_文本", "desc": "", "relativeXPath": "/span[1]", "allXPaths": ["/span[1]", "//span[contains(., '广告')]", "//SPAN[@class='p-promo-flag']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/span"], "exampleValues": [{"num": 0, "value": "广告"}], "unique_index": "/span[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数22_图片地址", "desc": "", "relativeXPath": "/img[1]", "allXPaths": ["/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/img"], "exampleValues": [{"num": 0, "value": ""}], "unique_index": "/img[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数23_图片地址", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/img[1]", "allXPaths": ["/div[3]/a[1]/em[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='p-tag3']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div[last()-5]/a/em/img"], "exampleValues": [{"num": 1, "value": "//m.360buyimg.com/cc/jfs/t1/113659/27/28361/2962/62ecb1f0E6c5fc50c/b914680e87a2c8e9.png"}], "unique_index": "/div[3]/a[1]/em[1]/img[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数24_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/i[1]", "allXPaths": ["/div[3]/a[1]/i[1]", "//i[contains(., '99元5件 幼儿启蒙')]", "id(\"J_AD_12830944\")", "//I[@class='promo-words']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div[last()-5]/a/i"], "exampleValues": [{"num": 1, "value": "99元5件幼儿启蒙团购电话4006186622"}], "unique_index": "/div[3]/a[1]/i[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数25_文本", "desc": "", "relativeXPath": "/div[6]/i[1]", "allXPaths": ["/div[6]/i[1]", "//i[contains(., '自营')]", "//I[@class='goods-icons J-picon-tips J-picon-fix']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div[last()-2]/i"], "exampleValues": [{"num": 1, "value": "自营"}], "unique_index": "/div[6]/i[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数26_文本", "desc": "", "relativeXPath": "/div[1]/a[1]/div[1]/div[1]", "allXPaths": ["/div[1]/a[1]/div[1]/div[1]", "//div[contains(., '3件8.0折')]", "//DIV[@class='sign-title ac']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-27]/div/div[last()-7]/a/div/div"], "exampleValues": [{"num": 2, "value": "3件8.0折"}], "unique_index": "/div[1]/a[1]/div[1]/div[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数27_文本", "desc": "", "relativeXPath": "/div[1]/a[1]/div[1]/div[1]/span[1]", "allXPaths": ["/div[1]/a[1]/div[1]/div[1]/span[1]", "//span[contains(., '7.7-7.31')]", "//SPAN[@class='sign-date']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-27]/div/div[last()-7]/a/div/div/span"], "exampleValues": [{"num": 2, "value": "7.7-7.31"}], "unique_index": "/div[1]/a[1]/div[1]/div[1]/span[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数28_图片地址", "desc": "", "relativeXPath": "/div[5]/img[1]", "allXPaths": ["/div[5]/img[1]", "//img[contains(., '')]", "//IMG[@class='shop-tag fl']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-3]/img"], "exampleValues": [{"num": 3, "value": "//m.360buyimg.com/cc/jfs/t1/197706/29/27826/4384/63314bc6E21b4c3e9/8b4d5fc44efc5b3f.png"}], "unique_index": "/div[5]/img[1]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数29_文本", "desc": "", "relativeXPath": "/div[6]/i[2]", "allXPaths": ["/div[6]/i[2]", "//i[contains(., '每满200-20')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-2]/i"], "exampleValues": [{"num": 3, "value": "每满200-20"}], "unique_index": "/div[6]/i[2]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数30_文本", "desc": "", "relativeXPath": "/div[3]/a[1]/em[1]/font[2]", "allXPaths": ["/div[3]/a[1]/em[1]/font[2]", "//font[contains(., '...')]", "//FONT[@class='dot']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-10]/div/div[last()-5]/a/em/font"], "exampleValues": [{"num": 19, "value": "..."}], "unique_index": "/div[3]/a[1]/em[1]/font[2]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数31_文本", "desc": "", "relativeXPath": "/div[6]/i[3]", "allXPaths": ["/div[6]/i[3]", "//i[contains(., '满赠')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-9]/div/div[last()-2]/i"], "exampleValues": [{"num": 20, "value": "满赠"}], "unique_index": "/div[6]/i[3]", "iframe": true, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}