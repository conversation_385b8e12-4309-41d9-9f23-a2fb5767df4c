{"id": 58, "name": "新web采集任务", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "5/20/2023, 8:35:56 PM", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "参数1_图片地址", "desc": "", "type": "string", "exampleValue": "//m.360buyimg.com/babel/jfs/t1/81488/28/23346/102165/63b41485F7ecc4f22/be5cee8cf04d7e16.png"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 0}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[3]/div[1]/div[1]/div[2]/ul[1]/li/a[1]/img[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 0, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "allXPaths": ["/html/body/div[5]/div[1]/div[3]/div[1]/div[1]/div[2]/ul[1]/li[1]/a[1]/img[1]", "//img[contains(., '')]"]}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 4, "contentType": 0, "relative": true, "name": "参数1_图片地址", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "//m.360buyimg.com/babel/jfs/t1/81488/28/23346/102165/63b41485F7ecc4f22/be5cee8cf04d7e16.png"}, {"num": 1, "value": "//m.360buyimg.com/babel/jfs/t1/93200/23/34752/53589/63b4148cF5150739b/5d0dc855fe43ca85.png"}, {"num": 2, "value": "//m.360buyimg.com/babel/jfs/t1/54690/10/22629/29568/63b41496Fad92ac75/605f4fe1c473192c.png"}, {"num": 3, "value": "//m.360buyimg.com/babel/jfs/t1/53202/29/23281/117684/63b4149dF2beb8956/ac86841f42a75cd0.png"}, {"num": 4, "value": "//m.360buyimg.com/babel/jfs/t1/90039/40/25105/65438/63b414a5Fb09a6926/740fb4daeef82c57.png"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 1}], "loopType": 1}}]}