{"id": 50, "name": "中国地震台网——历史查询", "url": "http://www.ceic.ac.cn/history", "links": "http://www.ceic.ac.cn/history", "create_time": "7/3/2023, 4:41:12 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "http://www.ceic.ac.cn/history", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://www.ceic.ac.cn/history", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "http://www.ceic.ac.cn/history"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "string", "exampleValue": "12", "value": "12"}, {"id": 2, "name": "loopTimes_循环_2", "nodeId": 4, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "3.82023-06-11 17:40:1440.7982.6320新疆阿克苏地区沙雅县"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "http://www.ceic.ac.cn/history", "links": "http://www.ceic.ac.cn/history", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"weidu1\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "12", "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[2]/input[1]", "//input[contains(., '')]", "id(\"weidu1\")", "//INPUT[@class='span1']", "//INPUT[@name='weidu1']", "/html/body/div[last()-3]/div[last()-1]/div/div/div[last()-1]/form/div[last()-3]/input[last()-1]"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search\"]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[5]/a[1]", "//a[contains(., '查询')]", "id(\"search\")", "//A[@class='check']", "/html/body/div[last()-3]/div[last()-1]/div/div/div[last()-1]/form/div/a"]}}, {"id": 4, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [6, 5], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"pagination\")]/ul[1]/li[last()-1]/a[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '»')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div/div/div/ul/li[last()-1]/a"]}}, {"id": 6, "index": 5, "parentId": 4, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "//*[contains(@class, \"pagination\")]/ul[1]/li[10]/a[1]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '»')]", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div/div/div/ul/li[last()-1]/a"], "loopType": 0, "waitType": "1"}}, {"id": 5, "index": 6, "parentId": 4, "type": 1, "option": 8, "title": "循环", "sequence": [7], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr[1]", "//tr[contains(., '震级(M)发震时刻(')]", "//TR[@class='speed-tr-h1']", "/html/body/div[last()-3]/div[last()-1]/div/div/div/div/div[last()-1]/table/tbody/tr[last()-20]"]}}, {"id": 7, "index": 7, "parentId": 5, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "3.82023-06-11 17:40:1440.7982.6320新疆阿克苏地区沙雅县"}, {"num": 1, "value": "震级(M)发震时刻(UTC+8)纬度(°)经度(°)深度(千米)参考位置"}, {"num": 2, "value": "3.82023-06-18 01:08:1135.8079.7810新疆和田地区和田县"}, {"num": 3, "value": "4.42023-06-18 00:14:2435.7979.8310新疆和田地区和田县"}, {"num": 4, "value": "5.62023-06-17 19:35:5947.75147.60430千岛群岛西北"}, {"num": 5, "value": "5.32023-06-17 08:26:1441.10142.8010日本本州东岸近海"}, {"num": 6, "value": "3.12023-06-17 08:05:5139.5882.5720新疆阿克苏地区沙雅县"}, {"num": 7, "value": "3.22023-06-16 01:19:3538.3189.4010新疆巴音郭楞州若羌县"}, {"num": 8, "value": "3.72023-06-15 21:58:0940.1477.4623新疆克孜勒苏州阿图什市"}, {"num": 9, "value": "3.62023-06-15 11:21:2732.4494.2410西藏那曲市巴青县"}, {"num": 10, "value": "6.22023-06-15 10:19:2413.80120.85100菲律宾"}, {"num": 11, "value": "2.22023-06-14 22:24:4137.12114.7810河北邢台市任泽区"}, {"num": 12, "value": "3.02023-06-14 13:39:4038.1688.799新疆巴音郭楞州若羌县"}, {"num": 13, "value": "4.12023-06-14 04:17:5641.7180.8110新疆阿克苏地区温宿县"}, {"num": 14, "value": "5.22023-06-13 16:03:4333.1075.8020克什米尔地区"}, {"num": 15, "value": "3.62023-06-13 08:48:3040.1883.8018新疆阿克苏地区沙雅县"}, {"num": 16, "value": "3.72023-06-13 05:52:5933.1086.7310西藏那曲市尼玛县"}, {"num": 17, "value": "3.32023-06-12 00:04:1843.3888.9819新疆吐鲁番市高昌区"}, {"num": 18, "value": "4.32023-06-11 20:25:3824.26122.4727台湾花莲县海域"}, {"num": 19, "value": "3.62023-06-11 19:29:4548.74129.7917黑龙江伊春市嘉荫县"}, {"num": 20, "value": "6.22023-06-11 17:54:4542.50142.00130日本北海道"}], "unique_index": "g7lxd23vm1oljjpbdw0", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}