{"id": 62, "name": "新web采集任务", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "5/21/2023, 5:16:00 PM", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.jd.com"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "/家用电器"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": "2", "scrollCount": 2}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 0, "loopType": 2, "pathList": "//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[4]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[5]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[6]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[7]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[8]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[9]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[10]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[11]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[12]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[13]", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": "1", "breakCode": "return window.innerHeight > 500", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "/家用电器"}, {"num": 1, "value": "/电脑/办公"}, {"num": 2, "value": "/家纺/家居/厨具"}, {"num": 3, "value": "/家具/家装/灯具/工业品"}, {"num": 4, "value": "/内衣/男装/女装/童装"}, {"num": 5, "value": "/箱包/钟表/珠宝/女鞋"}, {"num": 6, "value": "/运动/户外/男鞋"}, {"num": 7, "value": "/汽车用品/车载电器"}, {"num": 8, "value": "/母婴/洗护喂养"}, {"num": 9, "value": "/玩具乐器/宠物生活"}, {"num": 10, "value": "/家庭清洁/个人护理/计生情趣"}, {"num": 11, "value": "/图书/童书/文学"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 2}}]}