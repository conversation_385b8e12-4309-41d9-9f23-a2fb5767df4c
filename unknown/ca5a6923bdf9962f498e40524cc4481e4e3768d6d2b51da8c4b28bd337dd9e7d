{"id": 74, "name": "Electronics, Cars, Fashion, Collectibles & More | eBay", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "create_time": "5/26/2023, 12:07:26 AM", "version": "0.3.1", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "Open Page", "value": "https://www.ebay.com", "desc": "List of URLs to be collected, separated by \\n for multiple lines", "type": "string", "exampleValue": "https://www.ebay.com"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 7, "title": "Mouse Move", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"hl-ad-row__text-wrapper\")]/div[3]/div[2]", "wait": 7, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": ["/html/body/div[6]/div[7]/a[1]/div[2]/div[2]/div[3]/div[2]", "//div[contains(., 'Get your t')]", "//DIV[@class='hl-cta__default hl-cta__default-js hl-ad-row__cta-button hl-ad-row__cta']"]}}]}