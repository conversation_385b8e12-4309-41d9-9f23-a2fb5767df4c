{"id": 111, "name": "Youtube", "url": "https://www.youtube.com", "links": "https://www.youtube.com", "create_time": "7/4/2023, 2:04:15 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "https://www.youtube.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.youtube.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.youtube.com"}], "outputParameters": [{"id": 0, "name": "自定义参数_0", "desc": "", "type": "string", "exampleValue": "自定义字段"}, {"id": 1, "name": "自定义参数_1", "desc": "", "type": "string", "exampleValue": "自定义字段"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.youtube.com", "links": "https://www.youtube.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": -1, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/ytd-app[1]/div[1]/ytd-page-manager[1]/ytd-browse[1]/ytd-two-column-browse-results-renderer[1]/div[1]/ytd-rich-grid-renderer[1]/div[6]/ytd-rich-grid-row[1]/div[1]/ytd-rich-item-renderer[1]/div[1]/ytd-rich-grid-media[1]/div[1]/div[2]/div[1]/h3[1]/a[1]", "//a[contains(., 'I Want to')]", "id(\"video-title-link\")", "//A[@class='yt-simple-endpoint focus-on-expand style-scope ytd-rich-grid-media']", "/html/body/ytd-app/div[last()-1]/ytd-page-manager/ytd-browse/ytd-two-column-browse-results-renderer/div[last()-1]/ytd-rich-grid-renderer/div/ytd-rich-grid-row[last()-4]/div/ytd-rich-item-renderer[last()-3]/div/ytd-rich-grid-media/div[last()-1]/div[last()-1]/div[last()-1]/h3/a"]}}, {"id": -1, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "I Want to Apologise to People in Singapore"}], "unique_index": "esmaxcela6iljn5v1nj", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "/watch?v=mNOFk8jNx88"}], "unique_index": "esmaxcela6iljn5v1nj", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 2, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "//ytd-rich-item-renderer//ytd-rich-grid-media//a[@id='video-title-link']", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": "1", "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 3, "index": 5, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "自定义参数_0", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "自定义参数_1", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}