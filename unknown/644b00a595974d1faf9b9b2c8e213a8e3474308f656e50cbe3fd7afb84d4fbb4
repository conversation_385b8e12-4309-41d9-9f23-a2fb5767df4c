{"id": 11, "name": "Electronics, Cars, Fashion, Collectibles & More | eBay", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "create_time": "5/25/2023, 9:53:28 PM", "version": "0.3.1", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "Open Page", "value": "https://www.ebay.com", "desc": "List of URLs to be collected, separated by \\n for multiple lines", "type": "string", "exampleValue": "https://www.ebay.com"}, {"id": 1, "name": "inputText_1", "nodeName": "Input Text", "nodeId": 2, "desc": "The text to be entered, such as 'computer' at eBay search box", "type": "string", "exampleValue": "iphone", "value": "iphone"}], "outputParameters": [{"id": 0, "name": "para1_text", "desc": "", "type": "string", "exampleValue": "Apple iPhone SE 2nd Gen (2020) - A2275 (GSM + CDMA) Unlocked - Very Good -"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "Input Text", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"gh-ac\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "iphone", "allXPaths": ["/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[5]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/div[1]/input[1]", "//input[contains(., '')]", "id(\"gh-ac\")", "//INPUT[@class='gh-tb ui-autocomplete-input ui-autocomplete-loading']", "//INPUT[@name='_nkw']"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "<PERSON>lick Element", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"gh-btn\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[5]/form[1]/table[1]/tbody[1]/tr[1]/td[3]/input[1]", "//input[contains(., '')]", "id(\"gh-btn\")", "//INPUT[@class='btn btn-prim gh-spr']"]}}, {"id": 4, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "Loop", "sequence": [5], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[4]/div[2]/div[1]/div[2]/ul[1]/li/div[1]/div[2]/a[1]/div[1]/span[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[4]/div[2]/div[1]/div[2]/ul[1]/li[2]/div[1]/div[2]/a[1]/div[1]/span[1]", "//span[contains(., 'Apple iPho')]"]}}, {"id": 5, "index": 5, "parentId": 4, "type": 0, "option": 3, "title": "Collect Data", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "para1_text", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "Apple iPhone SE 2nd Gen (2020) - A2275 (GSM + CDMA) Unlocked - Very Good -"}, {"num": 1, "value": "Apple iphone 2G iPhone 1st generation  Very Rare IOS 1.1.4 8GB A1203"}, {"num": 2, "value": "Fully Working Apple iphone 2g 1st generation unlocked 4GB 8GB 16GB Rare IOS 1.0"}, {"num": 3, "value": "100% Very New Original Apple iPhone 4S  8/16/32/64GB  IOS 6 IOS 9  Unlocked 3G"}, {"num": 4, "value": "New ListingApple iPhone 12 - 64GB - Blue NOT WORKING"}, {"num": 5, "value": "Apple iPhone XR Unlocked Various Colors 64GB 128GB 256GB Smartphone Used"}, {"num": 6, "value": "Apple iPhone 1st Generation 8GB - Black (AT&T) A1203 (GSM)  Tested Full working"}, {"num": 7, "value": "Apple iPhone 14 Pro Max A16 128GB 256GB 512GB 1TB Unlocked New Sealed"}, {"num": 8, "value": "Apple iPhone XR - 256GB - White (Unlocked) A2105 Screen Cracked"}, {"num": 9, "value": "Apple iPhone 13 Pro Max - 128GB - Sierra Blue (Unlocked)"}, {"num": 10, "value": "Apple iPhone 12 Pro - 128GB - Gold (Unlocked)"}, {"num": 11, "value": "Apple iPhone 13  128gb ESN BAD Green (Chimaera)"}, {"num": 12, "value": "Apple iPhone X - 64GB - Space Gray (Unlocked) A1901 (GSM) (CA)"}, {"num": 13, "value": "Apple iPhone 8 Plus Unlocked Various Colors 64GB 128GB 256GB Smartphone Used"}, {"num": 14, "value": "Apple iPhone X (iPhone 10) 64GB 256GB Grey Silver - UNLOCKED - Excellent Grade A"}, {"num": 15, "value": "AT&T FACTORY UNLOCK SERVICE Factory ATT Unlock Service only Clean iPhone"}, {"num": 16, "value": "New ListingApple iPhone 13 mini - 128GB - Pink (Unlocked)"}, {"num": 17, "value": "Apple iPhone 12 Pro Max - 128GB - Pacific Blue Cracked Sold As It Is"}, {"num": 18, "value": "📱 Apple iPhone 5 16/32/64GB - Unlocked Black white Grade A Condition IOS10📱"}, {"num": 19, "value": "New ListingApple iPhone 14 Pro Max - 128GB - Space Black (Verizon)"}, {"num": 20, "value": "Lot of  41  Apple iPhone 4/4S - SEE DESCRIPTION"}, {"num": 21, "value": "Apple iPhone 11 Pro, Pro Max Unlocked 64GB 256GB 512GBSmartphone Used"}, {"num": 22, "value": "Working very well ,  Apple iPhone 3GS 8GB 16GB 32GB unlocked 3G Smart phone"}, {"num": 23, "value": "Full working Orignal Apple iphone 1st 2nd 3rd Gen 2G 3G 3GS 4/8/16/32 Unlocked"}, {"num": 24, "value": "Apple iPhone 3GS 16GB Black unlocked 3G networks  Apple iPhone 3rd generation"}, {"num": 25, "value": "New ListingApple iPhone 5s"}, {"num": 26, "value": "Apple iPhone XS ,XS Max Unlocked Various Colors 64GB 256GB 512GB Smartphone Used"}, {"num": 27, "value": "New ListingApple iPhone 12 Pro - 256GB - Pacific Blue (Unlocked) A2341 (Face ID error)"}, {"num": 28, "value": "Shockproof FRONT + BACK Case Cover For iPhone 14 11 12 13 PRO MAX X XR XS 7 8"}, {"num": 29, "value": "Apple iPhone X - 64GB - Motherboard only (Unlocked) A1865 (CDMA + GSM)"}, {"num": 30, "value": "Apple iPhone 11 64GB Smartphone Brand New Metro by T-Mobile"}, {"num": 31, "value": "Apple IPhone 1st Generation-8GB-Black Unlocked 2G A1203 (GSM) veryGOOD condition"}, {"num": 32, "value": "Apple iPhone 13 Pro - 256GB - All Colors - Unlocked - Very Good Condition "}, {"num": 33, "value": "Apple iPhone 7 A1778 32/128/256GB AT&T T-Mobile GSM Unlocked Good"}, {"num": 34, "value": "Apple iPhone XS - 256 GB - (Unlocked) Cracked Back"}, {"num": 35, "value": "Apple iPhone 4s - 16GB - White (Unlocked) A1387 (CDMA   GSM)"}, {"num": 36, "value": "Apple iPhone 7 Unlocked Various Colors 32GB 128GB 256GB Smartphone Used "}, {"num": 37, "value": "Apple iPhone 3GS(iPhone 3rd gen)-8GB 16GB 32GB-Black/White Unlocked A1303(GSM)"}, {"num": 38, "value": "Used Original Unlocked Apple iPhone SE 4G LTE 4.0' 2GB RAM 16/64GB ROM Dual-core"}, {"num": 39, "value": "Apple iPhone XS Max - 64 GB - Space Grey (Unlocked) A2101 (GSM) (AU Stock)"}, {"num": 40, "value": "Original Unlocked Apple iPhone 4S -8/16/32/64GB iOS 9 3G WIFI Smartphone"}, {"num": 41, "value": "Apple iPhone 1st Generation   - 4GB 8GB 16GB  - Black (Unlocked) A1203 (GSM)"}, {"num": 42, "value": "Apple iPhone 14 Pro Max - 128GB - Deep Purple (Verizon)"}, {"num": 43, "value": "iPhone 12 Pro Max"}, {"num": 44, "value": "iPhone 12 Pro 128gb Unlocked Used Excellent"}, {"num": 45, "value": "Apple iPhone 3rd generation 3GS - 8GB 16GB 32GB -Black  White (Unlocked) phone"}, {"num": 46, "value": "New ListingApple iPhone 8 A1863 (10,1) 64GB 4.7\" Smartphone | Verizon | Rose Gold"}, {"num": 47, "value": "Apple iphone 1st(2G)/3G(2nd)/3GS(3rd) generation Unlocked-Tested-Works well lot"}, {"num": 48, "value": "Apple iPhone 11 - 64GB - <PERSON> Unlock Sold As It Is"}, {"num": 49, "value": "Apple iPhone 5 GSM UNLOCKED - 16GB Good Condition Black"}, {"num": 50, "value": "original Apple iPhone 1st Generation 16GB unlocked 2G GSM work good IOS3"}, {"num": 51, "value": "📱 Apple iPhone 5s 16/32/64GB - Unlocked Black silver gold Grade A Condition 📱"}, {"num": 52, "value": "📱 Apple iPhone 4S 8/16/32GB - Unlocked Black white Grade A+ Condition 📱 IOS6"}, {"num": 53, "value": "Apple iPhone XS Max - 256GB - Gold (Unlocked) A1921 (CDMA + GSM)"}, {"num": 54, "value": "Apple iPhone 11 Pro Unlocked Smartphone, 64GB, 256GB, 512GB SYDNEY STOCK"}, {"num": 55, "value": "Apple iPhone 12 mini Unlocked Smartphone, 64GB, 128GB, 256GB - SYDNEY STOCK"}, {"num": 56, "value": "Apple iPhone 5 16 32 64GB Black/Slate White/Silver unlocked for all SIM"}, {"num": 57, "value": "Apple iPhone 4s 8/16/32/64 GB  A1387 (CDMA + GSM) IOS 6.3.1 IOS 9.3.6  UNLOCKED"}, {"num": 58, "value": "Case For iPhone  14 13 12 11 7 8 X XR  Bumper SHOCKPROOf  Cover"}, {"num": 59, "value": "Apple iPhone SE smartphone A1723 32GB Space Grey Aus stock Unlocked"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}