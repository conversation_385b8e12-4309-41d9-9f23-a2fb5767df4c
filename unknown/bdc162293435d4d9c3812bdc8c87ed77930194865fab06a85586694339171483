{"id": 177, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "7/14/2023, 6:44:54 AM", "update_time": "7/14/2023, 7:01:48 AM", "version": "0.3.6", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.jd.com"}, {"id": 1, "name": "loopTimes_循环_1", "nodeId": 2, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/手机/数码/家用电器/电脑/办公/家纺/家居/厨具/家具/家装/灯具/工业品/内衣/男装/女装/童装/箱包/钟表/珠宝/女鞋/运动/户外/男鞋/汽车用品/车载电器/母婴/洗护喂养/玩具乐器/宠物生活/家庭清洁/个人护理/计生情趣/图书/童书/文学"}, {"id": 1, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 2, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 3, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 4, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 3, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [5, 6, 3, 7], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 6, "index": 3, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "//a[Field[\"自定义操作\"]]", "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '/手机/数码/家用电')]", "//DIV[@class='LeftSide_menu_list__qXCeM']", "/html/body/div[last()-6]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]"], "exampleValues": [{"num": 0, "value": "/手机/数码/家用电器/电脑/办公/家纺/家居/厨具/家具/家装/灯具/工业品/内衣/男装/女装/童装/箱包/钟表/珠宝/女鞋/运动/户外/男鞋/汽车用品/车载电器/母婴/洗护喂养/玩具乐器/宠物生活/家庭清洁/个人护理/计生情趣/图书/童书/文学"}], "unique_index": "6bzoou5h7glk1qcdmv", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 2, "index": 4, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "5", "code": "self.myVar = 0", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 4, "index": 5, "parentId": 3, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "5", "code": "self.myVar += 1", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 5, "index": 6, "parentId": 3, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "6", "code": "self.myVar", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 7, "index": 7, "parentId": 3, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": true, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": true, "xpath": "//a[Field[\"自定义操作\"]]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}]}