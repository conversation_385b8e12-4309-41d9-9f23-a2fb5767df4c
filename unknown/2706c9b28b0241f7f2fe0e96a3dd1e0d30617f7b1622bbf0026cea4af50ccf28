{"id": 80, "name": "Electronics, Cars, Fashion, Collectibles & More | eBay", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "create_time": "5/26/2023, 3:07:35 AM", "version": "0.3.1", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.ebay.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.ebay.com"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "string", "exampleValue": "iPhone", "value": "iPhone"}, {"id": 2, "name": "loopTimes_循环_2", "nodeId": 6, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 3, "value": 3}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "New ListingApple iPhone 11 Pro Max - 256GB - Gold (sim locked) A2218 (CDMA + GSM)"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 6], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"gh-ac\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "iPhone", "allXPaths": ["/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[5]/form[1]/table[1]/tbody[1]/tr[1]/td[1]/div[1]/div[1]/input[1]", "//input[contains(., '')]", "id(\"gh-ac\")", "//INPUT[@class='gh-tb ui-autocomplete-input ui-autocomplete-loading']", "//INPUT[@name='_nkw']"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"gh-btn\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/header[1]/table[1]/tbody[1]/tr[1]/td[5]/form[1]/table[1]/tbody[1]/tr[1]/td[3]/input[1]", "//input[contains(., '')]", "id(\"gh-btn\")", "//INPUT[@class='btn btn-prim gh-spr']"]}}, {"id": 5, "index": 4, "parentId": 4, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div/div[4]/div[2]/div[1]/div[2]/ul[1]/li/div[1]/div[2]/a[1]/div[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[7]/div[4]/div[2]/div[1]/div[2]/ul[1]/li[2]/div[1]/div[2]/a[1]/div[1]", "//div[contains(., 'New Listin')]", "//DIV[@class='s-item__title']"]}}, {"id": 7, "index": 5, "parentId": 5, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "New ListingApple iPhone 11 Pro Max - 256GB - Gold (sim locked) A2218 (CDMA + GSM)"}, {"num": 1, "value": "Apple iphone 2G iPhone 1st generation  Very Rare IOS 1.1.4 8GB A1203"}, {"num": 2, "value": "Fully Working Apple iphone 2g 1st generation unlocked 4GB 8GB 16GB Rare IOS 1.0"}, {"num": 3, "value": "Apple iPhone 8 Plus Unlocked Various Colors 64GB 128GB 256GB Smartphone Used"}, {"num": 4, "value": "Apple iPhone 12 - 64GB - Blue NOT WORKING"}, {"num": 5, "value": "Apple iPhone X - 64GB - Space Gray (Unlocked) A1901 (GSM) (CA)"}, {"num": 6, "value": "Apple iPhone 13 Pro Max - 128GB - Sierra Blue (Unlocked)"}, {"num": 7, "value": "Apple iPhone 14 Pro Max A16 128GB 256GB 512GB 1TB Unlocked New Sealed"}, {"num": 8, "value": "Apple iPhone 12 Pro - 128GB - Gold (Unlocked)"}, {"num": 9, "value": "Apple iPhone XR Unlocked Various Colors 64GB 128GB 256GB Smartphone Used"}, {"num": 10, "value": "Apple iPhone XR - 256GB - White (Unlocked) A2105 Screen Cracked"}, {"num": 11, "value": "Apple iPhone 11 Pro, Pro Max Unlocked 64GB 256GB 512GBSmartphone Used"}, {"num": 12, "value": "Shockproof FRONT + BACK Case Cover For iPhone 14 11 12 13 PRO MAX X XR XS 7 8"}, {"num": 13, "value": "100% Very New Original Apple iPhone 4S  8/16/32/64GB  IOS 6 IOS 9  Unlocked 3G"}, {"num": 14, "value": "Apple iPhone X (iPhone 10) 64GB 256GB Grey Silver - UNLOCKED - Excellent Grade A"}, {"num": 15, "value": "AT&T FACTORY UNLOCK SERVICE Factory ATT Unlock Service only Clean iPhone"}, {"num": 16, "value": "Apple iPhone 1st Generation 8GB - Black (AT&T) A1203 (GSM)  Tested Full working"}, {"num": 17, "value": "Apple iPhone 13  128gb ESN BAD Green (Chimaera)"}, {"num": 18, "value": "Apple iPhone 12 Pro Max - 128GB - Pacific Blue Cracked Sold As It Is"}, {"num": 19, "value": "Apple iPhone 13 Pro - 256GB - All Colors - Unlocked - Very Good Condition "}, {"num": 20, "value": "Apple iPhone XS ,XS Max Unlocked Various Colors 64GB 256GB 512GB Smartphone Used"}, {"num": 21, "value": "Apple iPhone 7 A1778 32/128/256GB AT&T T-Mobile GSM Unlocked Good"}, {"num": 22, "value": "Apple iPhone 12 Pro - 256GB - Pacific Blue (Unlocked) A2341 (Face ID error)"}, {"num": 23, "value": "iPhone 12 Pro Max"}, {"num": 24, "value": "Apple iPhone 14 Pro Max - 128GB - Deep Purple (Verizon)"}, {"num": 25, "value": "Lot of  41  Apple iPhone 4/4S - SEE DESCRIPTION"}, {"num": 26, "value": "Apple iPhone XS - 256 GB - (Unlocked) Cracked Back"}, {"num": 27, "value": "New ListingApple iPhone XR - 64GB - Blue (Unlocked) A1984 (CDMA + GSM) Good condition"}, {"num": 28, "value": "Apple iPhone 11 64GB Smartphone Brand New Metro by T-Mobile"}, {"num": 29, "value": "Used Original Unlocked Apple iPhone SE 4G LTE 4.0' 2GB RAM 16/64GB ROM Dual-core"}, {"num": 30, "value": "Apple IPhone 1st Generation-8GB-Black Unlocked 2G A1203 (GSM) veryGOOD condition"}, {"num": 31, "value": "Case For iPhone  14 13 12 11 7 8 X XR  Bumper SHOCKPROOf  Cover"}, {"num": 32, "value": "New ListingApple iPhone 14 Pro Max - 128GB - Space Black (Verizon)"}, {"num": 33, "value": "New ListingApple iPhone 5s"}, {"num": 34, "value": "Full working Orignal Apple iphone 1st 2nd 3rd Gen 2G 3G 3GS 4/8/16/32 Unlocked"}, {"num": 35, "value": "Working very well ,  Apple iPhone 3GS 8GB 16GB 32GB unlocked 3G Smart phone"}, {"num": 36, "value": "Apple iPhone 7 Unlocked Various Colors 32GB 128GB 256GB Smartphone Used "}, {"num": 37, "value": "Apple iPhone 4s - 16GB - White (Unlocked) A1387 (CDMA   GSM)"}, {"num": 38, "value": "Apple iPhone XS Max - 64 GB - Space Grey (Unlocked) A2101 (GSM) (AU Stock)"}, {"num": 39, "value": "Apple iPhone X - 64GB - Motherboard only (Unlocked) A1865 (CDMA + GSM)"}, {"num": 40, "value": "Apple iPhone 1st Generation   - 4GB 8GB 16GB  - Black (Unlocked) A1203 (GSM)"}, {"num": 41, "value": "📱 Apple iPhone 5 16/32/64GB - Unlocked Black white Grade A Condition IOS10📱"}, {"num": 42, "value": "Apple iPhone 12, Airpods Gen 1/2, Airpods Pro w/ Instructions & Cord *BOXES ONLY"}, {"num": 43, "value": "Apple iPhone 8 Plus A1864 (10,2) 256GB Smartphone | Verizon | Space Gray | 5.5\""}, {"num": 44, "value": "Apple iPhone 5 - 32GB - Black White  (Unlocked) A1428 (GSM)  IOS Smartphone"}, {"num": 45, "value": "Apple iPhone 3GS(iPhone 3rd gen)-8GB 16GB 32GB-Black/White Unlocked A1303(GSM)"}, {"num": 46, "value": "Full Cover TEMPERED Glass For iPhone 11 12 13 PRO XS MAX XR 7 8 Screen Protector"}, {"num": 47, "value": "Apple iphone 1st(2G)/3G(2nd)/3GS(3rd) generation Unlocked-Tested-Works well lot"}, {"num": 48, "value": "Apple iPhone 11 - 64GB - <PERSON> Unlock Sold As It Is"}, {"num": 49, "value": "Apple iPhone 3GS - 8GB - Black (AT&T) A1303 (GSM) Fast Ship Excellent Used 1"}, {"num": 50, "value": "Apple iPhone 11 Pro A2215 256GB Factory Unlocked Single sim Very Good condition"}, {"num": 51, "value": "Apple iPhone 11 Pro Max Unlocked Smartphone, 64GB, 256GB, 512GB SYDNEY STOCK"}, {"num": 52, "value": "original Apple iPhone 1st Generation 16GB unlocked 2G GSM work good IOS3"}, {"num": 53, "value": "Original Unlocked Apple iPhone 4S -8/16/32/64GB iOS 9 3G WIFI Smartphone"}, {"num": 54, "value": "Apple iPhone 12 mini Unlocked Smartphone, 64GB, 128GB, 256GB - SYDNEY STOCK"}, {"num": 55, "value": "Apple iPhone 14 Pro Max A16 A2894* Factory Unlocked New Sealed Dual-Sim - FedEX"}, {"num": 56, "value": "5D Privacy Tempered Glass Screen Protector For iPhone 11 14 12 PRO MAX 13  Cover"}, {"num": 57, "value": "Shockproof Case For iPhone 14 11 12 13 PRO MAX X XR XS 7 8  360 cover"}, {"num": 58, "value": "Full Working Apple iPhone 1st Generation 2G - 8GB - Black (Unlocked) A1203 (GSM)"}, {"num": 59, "value": "Apple iPhone 13 Pro 128GB Unlocked Good Condition"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 4, "index": 6, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4, 7], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"pagination__next\")]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 3, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[7]/div[4]/div[2]/div[1]/div[2]/ul[1]/li[64]/div[2]/span[1]/span[1]/nav[1]/a[1]", "//a[contains(., '')]", "//A[@class='pagination__next icon-link']"]}}, {"id": 6, "index": 7, "parentId": 4, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": true, "xpath": "//*[contains(@class, \"pagination__next\")]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[7]/div[4]/div[2]/div[1]/div[2]/ul[1]/li[64]/div[2]/span[1]/span[1]/nav[1]/a[1]", "//a[contains(., '')]", "//A[@class='pagination__next icon-link']"], "loopType": 0}}]}