{"id": 113, "name": "京东全球版-专业的综合网上购物商城", "url": "https://www.jd.com", "links": "https://www.jd.com", "create_time": "7/4/2023, 2:12:31 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.jd.com"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "string", "exampleValue": "iPhone", "value": "iPhone"}], "outputParameters": [{"id": 0, "name": "参数1_链接文本", "desc": "", "type": "string", "exampleValue": "\n\t\t\t\t\t\t\t\t\n\t\t\t\t        \t"}, {"id": 1, "name": "参数2_链接地址", "desc": "", "type": "string", "exampleValue": "//item.jd.com/100038089781.html"}, {"id": 2, "name": "参数3_图片地址", "desc": "", "type": "string", "exampleValue": "//img11.360buyimg.com/n7/jfs/t1/191960/4/28218/17255/63196f11Ee943e61b/ec9d8feadce365fe.jpg"}, {"id": 3, "name": "参数4_文本", "desc": "", "type": "string", "exampleValue": "<"}, {"id": 4, "name": "参数5_文本", "desc": "", "type": "string", "exampleValue": ">"}, {"id": 5, "name": "参数6_链接文本", "desc": "", "type": "string", "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 6, "name": "参数7_链接地址", "desc": "", "type": "string", "exampleValue": "javascript:;"}, {"id": 7, "name": "参数8_图片地址", "desc": "", "type": "string", "exampleValue": "//img11.360buyimg.com/n7/jfs/t1/191960/4/28218/17255/63196f11Ee943e61b/ec9d8feadce365fe.jpg"}, {"id": 8, "name": "参数9_链接文本", "desc": "", "type": "string", "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 9, "name": "参数10_链接地址", "desc": "", "type": "string", "exampleValue": "javascript:;"}, {"id": 10, "name": "参数11_图片地址", "desc": "", "type": "string", "exampleValue": "//img13.360buyimg.com/n7/jfs/t1/58549/24/22211/18752/63196e79E30ec4e33/5f9ce38bdbacf96c.jpg"}, {"id": 11, "name": "参数12_链接文本", "desc": "", "type": "string", "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 12, "name": "参数13_链接地址", "desc": "", "type": "string", "exampleValue": "javascript:;"}, {"id": 13, "name": "参数14_图片地址", "desc": "", "type": "string", "exampleValue": "//img10.360buyimg.com/n7/jfs/t1/50423/12/21455/19477/6319674cE5de04487/71457158f73abe8f.jpg"}, {"id": 14, "name": "参数15_链接文本", "desc": "", "type": "string", "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 15, "name": "参数16_链接地址", "desc": "", "type": "string", "exampleValue": "javascript:;"}, {"id": 16, "name": "参数17_图片地址", "desc": "", "type": "string", "exampleValue": "//img11.360buyimg.com/n7/jfs/t1/145950/1/30473/21295/63196dcfEebbe2254/7b1578b98436f737.jpg"}, {"id": 17, "name": "参数18_文本", "desc": "", "type": "string", "exampleValue": "￥"}, {"id": 18, "name": "参数19_文本", "desc": "", "type": "string", "exampleValue": "8749.00"}, {"id": 19, "name": "参数20_链接文本", "desc": "", "type": "string", "exampleValue": "\n\t\t\t\t\t\t\t\tApple iPhone 14 Pro (A2892) 256GB 深空黑色 支持移动联通电信5G 双卡双待手机【大王卡】\n\t\t\t\t\t\t\t\t【好物限时购】Apple好物限量特价，限时优惠等你来购~快来抢购吧！\n\t\t\t\t\t\t\t"}, {"id": 20, "name": "参数21_链接地址", "desc": "", "type": "string", "exampleValue": "//item.jd.com/100038089781.html"}, {"id": 21, "name": "参数22_文本", "desc": "", "type": "string", "exampleValue": "Apple14Pro(A2892)256GB深空黑色支持移动联通电信5G双卡双待手机【大王卡】"}, {"id": 22, "name": "参数23_文本", "desc": "", "type": "string", "exampleValue": "iPhone"}, {"id": 23, "name": "参数24_文本", "desc": "", "type": "string", "exampleValue": "【好物限时购】Apple好物限量特价，限时优惠等你来购~快来抢购吧！"}, {"id": 24, "name": "参数25_文本", "desc": "", "type": "string", "exampleValue": "条评价"}, {"id": 25, "name": "参数26_链接文本", "desc": "", "type": "string", "exampleValue": "10万+"}, {"id": 26, "name": "参数27_链接地址", "desc": "", "type": "string", "exampleValue": "//item.jd.com/100038089781.html#comment"}, {"id": 27, "name": "参数28_链接文本", "desc": "", "type": "string", "exampleValue": "关注"}, {"id": 28, "name": "参数29_链接地址", "desc": "", "type": "string", "exampleValue": "javascript:;"}, {"id": 29, "name": "参数30_链接文本", "desc": "", "type": "string", "exampleValue": "中国联通京东自营旗舰店"}, {"id": 30, "name": "参数31_链接地址", "desc": "", "type": "string", "exampleValue": "//mall.jd.com/index-1000073123.html?from=pc"}, {"id": 31, "name": "参数32_文本", "desc": "", "type": "string", "exampleValue": "自营"}, {"id": 32, "name": "参数33_文本", "desc": "", "type": "string", "exampleValue": "券7000-1100"}, {"id": 33, "name": "参数34_文本", "desc": "", "type": "string", "exampleValue": "赠"}, {"id": 34, "name": "参数35_文本", "desc": "", "type": "string", "exampleValue": "海外预定"}, {"id": 35, "name": "参数36_链接文本", "desc": "", "type": "string", "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 36, "name": "参数37_链接地址", "desc": "", "type": "string", "exampleValue": "javascript:;"}, {"id": 37, "name": "参数38_图片地址", "desc": "", "type": "string", "exampleValue": "//img10.360buyimg.com/n7/jfs/t1/211889/13/25653/16750/63ecb0feF255ba96e/bdf29159bc58b8cb.jpg"}, {"id": 38, "name": "参数39_链接文本", "desc": "", "type": "string", "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 39, "name": "参数40_链接地址", "desc": "", "type": "string", "exampleValue": "javascript:;"}, {"id": 40, "name": "参数41_图片地址", "desc": "", "type": "string", "exampleValue": "//img12.360buyimg.com/n7/jfs/t1/195608/23/33272/24575/64074bbcF2996dcca/45e38aaf1c8b37b3.jpg"}, {"id": 41, "name": "参数42_文本", "desc": "", "type": "string", "exampleValue": "IPHONE"}, {"id": 42, "name": "参数43_文本", "desc": "", "type": "string", "exampleValue": "拍拍"}, {"id": 43, "name": "参数44_图片地址", "desc": "", "type": "string", "exampleValue": "//m.360buyimg.com/cc/jfs/t1/197706/29/27826/4384/63314bc6E21b4c3e9/8b4d5fc44efc5b3f.png"}, {"id": 44, "name": "参数45_文本", "desc": "", "type": "string", "exampleValue": "预约中"}, {"id": 45, "name": "参数46_文本", "desc": "", "type": "string", "exampleValue": "剩余17天21时47分"}, {"id": 46, "name": "参数47_链接文本", "desc": "", "type": "string", "exampleValue": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}, {"id": 47, "name": "参数48_链接地址", "desc": "", "type": "string", "exampleValue": "javascript:;"}, {"id": 48, "name": "参数49_图片地址", "desc": "", "type": "string", "exampleValue": ""}, {"id": 49, "name": "参数50_文本", "desc": "", "type": "string", "exampleValue": "iPhone"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"key\"]", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "iPhone", "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/input[1]", "//input[contains(., '')]", "id(\"key\")", "//INPUT[@class='text defcolor']", "/html/body/div[last()-6]/div/div[last()-2]/div/input"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search-btn\"]/i[1]", "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": "2", "scrollCount": 2, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[4]/div[1]/div[2]/div[1]/button[1]/i[1]", "//i[contains(., '')]", "/html/body/div[last()-6]/div/div[last()-2]/div/button/i"]}}, {"id": 4, "index": 4, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li/div[1]", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[2]/div[2]/div[1]/div[1]/div[2]/ul[1]/li[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='gl-i-wrap']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div"]}}, {"id": 5, "index": 5, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/div[1]/a[1]", "allXPaths": ["/div[1]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-8]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t\n\t\t\t\t        \t"}], "unique_index": "/div[1]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/div[1]/a[1]", "allXPaths": ["/div[1]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-8]/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/100038089781.html"}], "unique_index": "/div[1]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数3_图片地址", "desc": "", "relativeXPath": "/div[1]/a[1]/img[1]", "allXPaths": ["/div[1]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-8]/a/img"], "exampleValues": [{"num": 0, "value": "//img11.360buyimg.com/n7/jfs/t1/191960/4/28218/17255/63196f11Ee943e61b/ec9d8feadce365fe.jpg"}], "unique_index": "/div[1]/a[1]/img[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/div[2]/span[1]", "allXPaths": ["/div[2]/span[1]", "//span[contains(., '<')]", "//SPAN[@class='ps-prev']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/span[last()-1]"], "exampleValues": [{"num": 0, "value": "<"}], "unique_index": "/div[2]/span[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/div[2]/span[2]", "allXPaths": ["/div[2]/span[2]", "//span[contains(., '>')]", "//SPAN[@class='ps-next']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/span"], "exampleValues": [{"num": 0, "value": ">"}], "unique_index": "/div[2]/span[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数6_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[1]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[1]/a[1]", "//a[contains(., '')]", "//A[@class='curr']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li[last()-3]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[1]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数7_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[1]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[1]/a[1]", "//a[contains(., '')]", "//A[@class='curr']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li[last()-3]/a"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[1]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数8_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[1]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[1]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li[last()-3]/a/img"], "exampleValues": [{"num": 0, "value": "//img11.360buyimg.com/n7/jfs/t1/191960/4/28218/17255/63196f11Ee943e61b/ec9d8feadce365fe.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[1]/a[1]/img[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数9_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[2]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[2]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li[last()-2]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[2]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数10_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[2]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[2]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li[last()-2]/a"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[2]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数11_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[2]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[2]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li[last()-2]/a/img"], "exampleValues": [{"num": 0, "value": "//img13.360buyimg.com/n7/jfs/t1/58549/24/22211/18752/63196e79E30ec4e33/5f9ce38bdbacf96c.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[2]/a[1]/img[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数12_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[3]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[3]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li[last()-1]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[3]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数13_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[3]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[3]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li[last()-1]/a"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[3]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数14_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[3]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[3]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li[last()-1]/a/img"], "exampleValues": [{"num": 0, "value": "//img10.360buyimg.com/n7/jfs/t1/50423/12/21455/19477/6319674cE5de04487/71457158f73abe8f.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[3]/a[1]/img[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数15_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[4]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[4]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[4]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数16_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[4]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[4]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[4]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数17_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[4]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[4]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-7]/div/ul/li/a/img"], "exampleValues": [{"num": 0, "value": "//img11.360buyimg.com/n7/jfs/t1/145950/1/30473/21295/63196dcfEebbe2254/7b1578b98436f737.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[4]/a[1]/img[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数18_文本", "desc": "", "relativeXPath": "/div[3]/strong[1]/em[1]", "allXPaths": ["/div[3]/strong[1]/em[1]", "//em[contains(., '￥')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-6]/strong/em"], "exampleValues": [{"num": 0, "value": "￥"}], "unique_index": "/div[3]/strong[1]/em[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数19_文本", "desc": "", "relativeXPath": "/div[3]/strong[1]/i[1]", "allXPaths": ["/div[3]/strong[1]/i[1]", "//i[contains(., '8749.00')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-6]/strong/i"], "exampleValues": [{"num": 0, "value": "8749.00"}], "unique_index": "/div[3]/strong[1]/i[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数20_链接文本", "desc": "", "relativeXPath": "/div[4]/a[1]", "allXPaths": ["/div[4]/a[1]", "//a[contains(., 'A')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a"], "exampleValues": [{"num": 0, "value": "\n\t\t\t\t\t\t\t\tApple iPhone 14 Pro (A2892) 256GB 深空黑色 支持移动联通电信5G 双卡双待手机【大王卡】\n\t\t\t\t\t\t\t\t【好物限时购】Apple好物限量特价，限时优惠等你来购~快来抢购吧！\n\t\t\t\t\t\t\t"}], "unique_index": "/div[4]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数21_链接地址", "desc": "", "relativeXPath": "/div[4]/a[1]", "allXPaths": ["/div[4]/a[1]", "//a[contains(., 'A')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/100038089781.html"}], "unique_index": "/div[4]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数22_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/em[1]", "allXPaths": ["/div[4]/a[1]/em[1]", "//em[contains(., 'Apple iPho')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a/em"], "exampleValues": [{"num": 0, "value": "Apple14Pro(A2892)256GB深空黑色支持移动联通电信5G双卡双待手机【大王卡】"}], "unique_index": "/div[4]/a[1]/em[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数23_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/em[1]/font[1]", "allXPaths": ["/div[4]/a[1]/em[1]/font[1]", "//font[contains(., 'iPhone')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a/em/font"], "exampleValues": [{"num": 0, "value": "iPhone"}], "unique_index": "/div[4]/a[1]/em[1]/font[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数24_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/i[1]", "allXPaths": ["/div[4]/a[1]/i[1]", "//i[contains(., '【好物限时购】App')]", "id(\"J_AD_100038089781\")", "//I[@class='promo-words']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-5]/a/i"], "exampleValues": [{"num": 0, "value": "【好物限时购】Apple好物限量特价，限时优惠等你来购~快来抢购吧！"}], "unique_index": "/div[4]/a[1]/i[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数25_文本", "desc": "", "relativeXPath": "/div[5]/strong[1]", "allXPaths": ["/div[5]/strong[1]", "//strong[contains(., '10万+条评价')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong"], "exampleValues": [{"num": 0, "value": "条评价"}], "unique_index": "/div[5]/strong[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数26_链接文本", "desc": "", "relativeXPath": "/div[5]/strong[1]/a[1]", "allXPaths": ["/div[5]/strong[1]/a[1]", "//a[contains(., '10万+')]", "id(\"J_comment_100038089781\")", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong/a"], "exampleValues": [{"num": 0, "value": "10万+"}], "unique_index": "/div[5]/strong[1]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数27_链接地址", "desc": "", "relativeXPath": "/div[5]/strong[1]/a[1]", "allXPaths": ["/div[5]/strong[1]/a[1]", "//a[contains(., '10万+')]", "id(\"J_comment_100038089781\")", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-4]/strong/a"], "exampleValues": [{"num": 0, "value": "//item.jd.com/100038089781.html#comment"}], "unique_index": "/div[5]/strong[1]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数28_链接文本", "desc": "", "relativeXPath": "/div[6]/a[1]", "allXPaths": ["/div[6]/a[1]", "//a[contains(., '关注')]", "//A[@class='J_focus']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-3]/a"], "exampleValues": [{"num": 0, "value": "关注"}], "unique_index": "/div[6]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数29_链接地址", "desc": "", "relativeXPath": "/div[6]/a[1]", "allXPaths": ["/div[6]/a[1]", "//a[contains(., '关注')]", "//A[@class='J_focus']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-3]/a"], "exampleValues": [{"num": 0, "value": "javascript:;"}], "unique_index": "/div[6]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数30_链接文本", "desc": "", "relativeXPath": "/div[7]/span[1]/a[1]", "allXPaths": ["/div[7]/span[1]/a[1]", "//a[contains(., '中国联通京东自营旗舰')]", "//A[@class='curr-shop hd-shopname']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-2]/span/a"], "exampleValues": [{"num": 0, "value": "中国联通京东自营旗舰店"}], "unique_index": "/div[7]/span[1]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数31_链接地址", "desc": "", "relativeXPath": "/div[7]/span[1]/a[1]", "allXPaths": ["/div[7]/span[1]/a[1]", "//a[contains(., '中国联通京东自营旗舰')]", "//A[@class='curr-shop hd-shopname']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-2]/span/a"], "exampleValues": [{"num": 0, "value": "//mall.jd.com/index-1000073123.html?from=pc"}], "unique_index": "/div[7]/span[1]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数32_文本", "desc": "", "relativeXPath": "/div[8]/i[1]", "allXPaths": ["/div[8]/i[1]", "//i[contains(., '自营')]", "//I[@class='goods-icons J-picon-tips J-picon-fix']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/i[last()-2]"], "exampleValues": [{"num": 0, "value": "自营"}], "unique_index": "/div[8]/i[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数33_文本", "desc": "", "relativeXPath": "/div[8]/i[2]", "allXPaths": ["/div[8]/i[2]", "//i[contains(., '券7000-1100')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/i[last()-1]"], "exampleValues": [{"num": 0, "value": "券7000-1100"}], "unique_index": "/div[8]/i[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数34_文本", "desc": "", "relativeXPath": "/div[8]/i[3]", "allXPaths": ["/div[8]/i[3]", "//i[contains(., '赠')]", "//I[@class='goods-icons4 J-picon-tips']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-29]/div/div[last()-1]/i"], "exampleValues": [{"num": 0, "value": "赠"}], "unique_index": "/div[8]/i[3]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数35_文本", "desc": "", "relativeXPath": "/div[9]", "allXPaths": ["/div[9]", "//div[contains(., '海外预定')]", "//DIV[@class='p-stock']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-28]/div/div"], "exampleValues": [{"num": 1, "value": "海外预定"}], "unique_index": "/div[9]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数36_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[5]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[5]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 3, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[5]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数37_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[5]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[5]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 3, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[5]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数38_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[5]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[5]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-26]/div/div[last()-7]/div/ul/li/a/img"], "exampleValues": [{"num": 3, "value": "//img10.360buyimg.com/n7/jfs/t1/211889/13/25653/16750/63ecb0feF255ba96e/bdf29159bc58b8cb.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[5]/a[1]/img[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数39_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[6]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[6]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-25]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 4, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[6]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数40_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[6]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[6]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-25]/div/div[last()-7]/div/ul/li/a"], "exampleValues": [{"num": 4, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[6]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数41_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[6]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[6]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-25]/div/div[last()-7]/div/ul/li/a/img"], "exampleValues": [{"num": 4, "value": "//img12.360buyimg.com/n7/jfs/t1/195608/23/33272/24575/64074bbcF2996dcca/45e38aaf1c8b37b3.jpg"}], "unique_index": "/div[2]/div[1]/ul[1]/li[6]/a[1]/img[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数42_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/em[1]/font[2]", "allXPaths": ["/div[4]/a[1]/em[1]/font[2]", "//font[contains(., 'IPHONE')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-24]/div/div[last()-5]/a/em/font"], "exampleValues": [{"num": 5, "value": "IPHONE"}], "unique_index": "/div[4]/a[1]/em[1]/font[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数43_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/em[1]/span[1]", "allXPaths": ["/div[4]/a[1]/em[1]/span[1]", "//span[contains(., '拍拍')]", "//SPAN[@class='p-tag']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-23]/div/div[last()-5]/a/em/span"], "exampleValues": [{"num": 6, "value": "拍拍"}], "unique_index": "/div[4]/a[1]/em[1]/span[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数44_图片地址", "desc": "", "relativeXPath": "/div[7]/img[1]", "allXPaths": ["/div[7]/img[1]", "//img[contains(., '')]", "//IMG[@class='shop-tag fl']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-23]/div/div[last()-2]/img"], "exampleValues": [{"num": 6, "value": "//m.360buyimg.com/cc/jfs/t1/197706/29/27826/4384/63314bc6E21b4c3e9/8b4d5fc44efc5b3f.png"}], "unique_index": "/div[7]/img[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数45_文本", "desc": "", "relativeXPath": "/div[10]/span[1]", "allXPaths": ["/div[10]/span[1]", "//span[contains(., '预约中')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-18]/div/div/span"], "exampleValues": [{"num": 11, "value": "预约中"}], "unique_index": "/div[10]/span[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数46_文本", "desc": "", "relativeXPath": "/div[10]/em[1]", "allXPaths": ["/div[10]/em[1]", "//em[contains(., '剩余17天21时47')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-18]/div/div/em"], "exampleValues": [{"num": 11, "value": "剩余17天21时47分"}], "unique_index": "/div[10]/em[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数47_链接文本", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[7]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[7]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-11]/div/div[last()-8]/div/ul/li/a"], "exampleValues": [{"num": 18, "value": "\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t"}], "unique_index": "/div[2]/div[1]/ul[1]/li[7]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数48_链接地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[7]/a[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[7]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-11]/div/div[last()-8]/div/ul/li/a"], "exampleValues": [{"num": 18, "value": "javascript:;"}], "unique_index": "/div[2]/div[1]/ul[1]/li[7]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数49_图片地址", "desc": "", "relativeXPath": "/div[2]/div[1]/ul[1]/li[7]/a[1]/img[1]", "allXPaths": ["/div[2]/div[1]/ul[1]/li[7]/a[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='err-product']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-11]/div/div[last()-8]/div/ul/li/a/img"], "exampleValues": [{"num": 18, "value": ""}], "unique_index": "/div[2]/div[1]/ul[1]/li[7]/a[1]/img[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数50_文本", "desc": "", "relativeXPath": "/div[4]/a[1]/em[1]/font[3]", "allXPaths": ["/div[4]/a[1]/em[1]/font[3]", "//font[contains(., 'iPhone')]", "//FONT[@class='skcolor_ljg']", "/html/body/div[last()-11]/div/div/div[last()-1]/div/div[last()-2]/ul/li[last()-2]/div/div[last()-5]/a/em/font"], "exampleValues": [{"num": 27, "value": "iPhone"}], "unique_index": "/div[4]/a[1]/em[1]/font[3]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}