欢迎将软件宣传给更多需要的朋友和Star我们的Github仓库！

在此文件夹下打开Linux Terimal, 并输入以下命令运行软件：
./easy-spider.sh
注意软件运行过程中不要关闭terminal。

官方网址: https://www.easyspider.cn

支持Ubuntu 20.04, <PERSON><PERSON>, Deepin x64及以上版本。

软件开源代码Github库地址：https://github.com/NaiboWang/EasySpider

官方文档地址：https://github.com/NaiboWang/EasySpider/wiki

视频教程：https://www.bilibili.com/video/BV1th411A7ey/

可以从其他机器导入任务，只需要把其他机器的tasks文件夹里的.json文件放入此目录的tasks文件夹里即可。同理执行号文件可以通过复制execution_instances文件夹中的.json文件来导入。注意，两个文件夹里的.json文件只支持命名为大于0的数字。
