{"id": 101, "name": "收藏夹", "url": "https://space.bilibili.com/291929894/favlist", "links": "https://space.bilibili.com/291929894/favlist", "create_time": "7/23/2023, 7:42:28 PM", "update_time": "7/23/2023, 8:12:15 PM", "version": "0.5.0", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "https://space.bilibili.com/291929894/favlist", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://space.bilibili.com/291929894/favlist", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://space.bilibili.com/291929894/favlist"}, {"id": 1, "name": "loopTimes_循环_1", "nodeId": 12, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数1_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "14:19播放：428.6万两不疑 第二季 第9话 德妃复位投稿：2022-12-14音频已失效"}, {"id": 1, "name": "参数2_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://www.bilibili.com/bangumi/play/ep682866"}, {"id": 2, "name": "参数3_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//i0.hdslb.com/bfs/archive/87bdd82a819cee798d6a428f1b500752664b8b80.jpg@320w_200h_1c_!web-space-favlist-video.webp"}, {"id": 3, "name": "参数4_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "播放：428.6万"}, {"id": 4, "name": "参数5_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "两不疑第二季第9话德妃复位"}, {"id": 5, "name": "参数6_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "投稿：2022-12-14"}, {"id": 6, "name": "参数7_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "音频已失效"}, {"id": 7, "name": "参数8_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "两不疑 第二季 第9话 德妃复位"}, {"id": 8, "name": "参数9_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://www.bilibili.com/bangumi/play/ep682866"}, {"id": 9, "name": "参数10_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "收藏于：2022-12-14"}, {"id": 10, "name": "参数11_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "取消收藏"}, {"id": 11, "name": "参数12_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "移动到"}, {"id": 12, "name": "参数13_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "复制到"}, {"id": 13, "name": "参数14_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "投稿：2-19"}, {"id": 14, "name": "参数15_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "已被UP主删除"}, {"id": 15, "name": "自定义参数_3", "desc": "", "type": "text", "recordASField": 0, "exampleValue": "自定义值"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 12], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://space.bilibili.com/291929894/favlist", "links": "https://space.bilibili.com/291929894/favlist", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": []}}, {"id": -1, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": -1, "index": 4, "parentId": 0, "type": 2, "option": 9, "title": "判断条件", "sequence": [5, 6], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": -1, "parentId": 3, "index": 5, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 3, "index": 6, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 7, "parentId": 0, "type": 2, "option": 9, "title": "判断条件", "sequence": [11], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}}, {"id": -1, "parentId": 3, "index": 8, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 3, "index": 9, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"index": 10, "id": -1, "parentId": 3, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 0}, {"index": 11, "id": -1, "parentId": 3, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": 2, "index": 12, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [18, 14, 13], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"be-pager-next\")]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[2]/div[3]/ul[2]/li[7]", "//li[contains(., '下一页')]", "//LI[@class='be-pager-next']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul/li"]}}, {"id": 5, "index": 13, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 4, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[2]/div[3]/ul[2]/li[7]", "//li[contains(., '下一页')]", "//LI[@class='be-pager-next']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul/li"]}}, {"id": 4, "index": 14, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [15], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[4]/div[1]/div[1]/div[2]/div[3]/ul[1]/li", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[4]/div[1]/div[1]/div[2]/div[3]/ul[1]/li[1]", "//li[contains(., '20:29播放：44')]", "//LI[@class='small-item']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-19]"]}}, {"id": 6, "index": 15, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '14:19播放：42')]", "//A[@class='cover cover-normal']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "14:19播放：428.6万两不疑 第二季 第9话 德妃复位投稿：2022-12-14音频已失效"}], "unique_index": "/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '14:19播放：42')]", "//A[@class='cover cover-normal']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "https://www.bilibili.com/bangumi/play/ep682866"}], "unique_index": "/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数3_图片地址", "desc": "", "relativeXPath": "/a[1]/div[1]/picture[1]/img[1]", "allXPaths": ["/a[1]/div[1]/picture[1]/img[1]", "//img[contains(., '')]", "//IMG[@alt='两不疑 第二季']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/a[last()-1]/div[last()-2]/picture/img"], "exampleValues": [{"num": 0, "value": "//i0.hdslb.com/bfs/archive/87bdd82a819cee798d6a428f1b500752664b8b80.jpg@320w_200h_1c_!web-space-favlist-video.webp"}], "unique_index": "/a[1]/div[1]/picture[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 1}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/a[1]/div[2]/div[1]/p[1]", "allXPaths": ["/a[1]/div[2]/div[1]/p[1]", "//p[contains(., '播放：428.6万')]", "//P[@class='view']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/a[last()-1]/div[last()-1]/div/p[last()-2]"], "exampleValues": [{"num": 0, "value": "播放：428.6万"}], "unique_index": "/a[1]/div[2]/div[1]/p[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/a[1]/div[2]/div[1]/p[2]", "allXPaths": ["/a[1]/div[2]/div[1]/p[2]", "//p[contains(., '两不疑 第二季 第9')]", "//P[@class='ep_title']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/a[last()-1]/div[last()-1]/div/p[last()-1]"], "exampleValues": [{"num": 0, "value": "两不疑第二季第9话德妃复位"}], "unique_index": "/a[1]/div[2]/div[1]/p[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数6_文本", "desc": "", "relativeXPath": "/a[1]/div[2]/div[1]/p[3]", "allXPaths": ["/a[1]/div[2]/div[1]/p[3]", "//p[contains(., '投稿：2022-12')]", "//P[@class='pubdate']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/a[last()-1]/div[last()-1]/div/p"], "exampleValues": [{"num": 0, "value": "投稿：2022-12-14"}], "unique_index": "/a[1]/div[2]/div[1]/p[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数7_文本", "desc": "", "relativeXPath": "/a[1]/div[3]/p[1]", "allXPaths": ["/a[1]/div[3]/p[1]", "//p[contains(., '音频已失效')]", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/a[last()-1]/div/p"], "exampleValues": [{"num": 0, "value": "音频已失效"}], "unique_index": "/a[1]/div[3]/p[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数8_链接文本", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '两不疑 第二季 第9')]", "//A[@class='title']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/a"], "exampleValues": [{"num": 0, "value": "两不疑 第二季 第9话 德妃复位"}], "unique_index": "/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数9_链接地址", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., '两不疑 第二季 第9')]", "//A[@class='title']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/a"], "exampleValues": [{"num": 0, "value": "https://www.bilibili.com/bangumi/play/ep682866"}], "unique_index": "/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/div[1]", "allXPaths": ["/div[1]", "//div[contains(., '')]", "//DIV[@class='meta pubdate']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/div[last()-2]"], "exampleValues": [{"num": 0, "value": "收藏于：2022-12-14"}], "unique_index": "/div[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数11_文本", "desc": "", "relativeXPath": "/div[2]/ul[1]/li[1]", "allXPaths": ["/div[2]/ul[1]/li[1]", "//li[contains(., '')]", "//LI[@class='be-dropdown-item be-dropdown-item-delimiter']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/div[last()-1]/ul/li[last()-2]"], "exampleValues": [{"num": 0, "value": "取消收藏"}], "unique_index": "/div[2]/ul[1]/li[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数12_文本", "desc": "", "relativeXPath": "/div[2]/ul[1]/li[2]", "allXPaths": ["/div[2]/ul[1]/li[2]", "//li[contains(., '')]", "//LI[@class='be-dropdown-item']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/div[last()-1]/ul/li[last()-1]"], "exampleValues": [{"num": 0, "value": "移动到"}], "unique_index": "/div[2]/ul[1]/li[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数13_文本", "desc": "", "relativeXPath": "/div[2]/ul[1]/li[3]", "allXPaths": ["/div[2]/ul[1]/li[3]", "//li[contains(., '')]", "//LI[@class='be-dropdown-item']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-4]/div[last()-1]/ul/li"], "exampleValues": [{"num": 0, "value": "复制到"}], "unique_index": "/div[2]/ul[1]/li[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数14_文本", "desc": "", "relativeXPath": "/a[1]/div[2]/div[1]/p[4]", "allXPaths": ["/a[1]/div[2]/div[1]/p[4]", "//p[contains(., '投稿：2-19')]", "//P[@class='pubdate']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li[last()-19]/a[last()-1]/div[last()-1]/div/p"], "exampleValues": [{"num": 1, "value": "投稿：2-19"}], "unique_index": "/a[1]/div[2]/div[1]/p[4]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数15_文本", "desc": "", "relativeXPath": "/a[1]/div[3]/div[2]", "allXPaths": ["/a[1]/div[3]/div[2]", "//div[contains(., '已被UP主删除')]", "//DIV[@class='delete-from']", "/html/body/div[last()-6]/div[last()-1]/div/div[last()-2]/div/div/ul[last()-1]/li/a[last()-1]/div/div"], "exampleValues": [{"num": 19, "value": "已被UP主删除"}], "unique_index": "/a[1]/div[3]/div[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": -1, "index": 16, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": []}}, {"id": -1, "index": 17, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": []}}, {"id": 3, "index": 18, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": [{"nodeType": 0, "contentType": 7, "relative": false, "name": "自定义参数_3", "desc": "", "extractType": 0, "relativeXPath": "//body", "recordASField": 0, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}]}}, {"id": -1, "index": 19, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "codeMode": 0, "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}]}