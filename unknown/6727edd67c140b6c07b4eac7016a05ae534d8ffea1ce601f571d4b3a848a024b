{"id": 195, "name": "Title", "url": "http://localhost:8074/taskGrid/test_pages/new_window.html", "links": "http://localhost:8074/taskGrid/test_pages/new_window.html", "create_time": "7/22/2023, 12:59:43 AM", "update_time": "7/22/2023, 12:59:43 AM", "version": "0.5.0", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "http://localhost:8074/taskGrid/test_pages/new_window.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://localhost:8074/taskGrid/test_pages/new_window.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "http://localhost:8074/taskGrid/test_pages/new_window.html"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n    \n        \n        \n            \n                \n                EasySpider: Visual Web Crawler\n                An open-source, free, and ad-free software for designing a web scraper without writing any code in minutes.\n                Click below to star this open-source project on Github~\n                \n                    \n                        Github Repository\n                        10K+ Stars on Github\n                    \n                \n                \n                    \n                        Download Now\n                        Download Now\n                    \n                \n                \n            \n            \n\n            \n            \n            \n\n            \n        \n    \n\n    \n        \n\n            \n                \n                    Home\n                    \n                    Documentation\n                    Videos\n                    Q&A\n                    Paper\n                    Patent\n                \n\n            \n        \n       \n    \n    \n        \n            \n                \n                Suitable for Anyone in the Workplace\n                Design and execute web scraping tasks visually, just like using Excel, regardless of coding experience.\n            \n            \n                \n                Supports Configuring Cloud Servers\n                Can configure a cloud server address for unified task information storage and management.\n            \n            \n                \n                Define Any Complex Task\n                Supports infinite loop nesting and conditional judgments, can also execute custom JavaScript instructions and system instructions. You can insert the exit loop Break statement into any position of the flowchart.\n            \n        \n    \n\n\n\n    \n        \n            Software Features\n            \n                \n                Open Source, Free and Ad-Free\n                The code is open source, all software features are free (excluding commercial use), with no pop-ups or external ads.\n            \n            \n                \n                Cross-Platform\n                The software can run on Windows, MacOS, and Linux.\n            \n            \n                \n                Quick and Easy\n                Graphical setup and execution of crawler tasks, typically a crawler task can be completed in design within 2-5 minutes.\n            \n            \n                \n                Safe\n                No registration required, all tasks and data are saved locally, no third-party servers involved.\n            \n            \n                \n                Flexible\n                Can add any browser plugins, and execute any JavaScript commands.\n            \n            \n                \n                Running Parallel Tasks\n                Can launch any number of execution programs, achieve large-scale data parallel acquisition.\n            \n            \n                \n                Captcha Recognition\n                Supports multiple captcha recognition schemes, such as graphic captcha, Google reCAPTCHA, etc.\n            \n            \n                \n                Element Screenshot and OCR Recognition\n                Supports element screenshots and OCR recognition, as well as image downloads.\n            \n            \n                \n                Proxy IP\n                Supports tunnel IP switching, private IP, etc.\n            \n            \n                \n                LAN Access\n                Can be used within a LAN without internet.\n            \n            \n                \n                External Program Invocation\n                Can call any external systems to meet any complex requirements.\n            \n            \n                \n                API Invocation\n                Tasks can be executed via API invocation, achieving advanced automation collection.\n            \n            \n                \n                Scheduled Execution\n                Supports scheduled task execution, becomes a handy life assistant.\n            \n            \n                \n                Pause Anytime\n                Can pause task execution at any time for manual debugging and captcha input.\n            \n            \n                \n                Define Variables\n                Can define variables and integrate these into any code statement.\n            \n            \n                \n                Mobile Device Simulation\n                Supports mobile device simulation, enabling mobile web page collection.\n            \n            \n                \n                Flexible Import\n                Supports reading Excel files to import a large number of input parameters.\n            \n            \n                \n                Free Export\n                Can export to Excel/CSV/TXT files, and supports writing into MySQL databases.\n            \n            \n                \n                Task Migration\n                Tasks can be copied and moved to other machines, regardless of the operating system environment.\n            \n            \n                \n                Bypass Cloudflare Verification\n                Supports the collection of websites protected by Cloudflare captcha.\n            \n            \n                \n                Page Scroll\n                Can set page scrolling to access content that needs scroll loading.\n            \n            \n                \n                IFRAME Support\n                Supports the collection of data within iframe tags.\n            \n            \n                \n                Cookies Modification\n                Can access and modify page cookies.\n            \n            \n                \n                Command Line Execution\n                Tasks can be executed directly via command line, seamlessly integrated into other programs.\n            \n            \n                \n                Headless Mode\n                Supports headless mode, allowing it to run in the background without opening a browser window.\n            \n            \n                \n                Regular Expressions\n                Supports regular expressions, allowing the usage of regex anywhere.\n            \n            \n                \n                Modify Web Content\n                Capable of modifying web content, enabling customized scraping for more precise requirements.\n            \n            \n                \n                Code Debugging\n                The software comes with an embedded code execution feature, allowing direct running and debugging of Python code.\n            \n            \n                \n                JSON Support\n                Supports JSON data collection.\n            \n            \n                \n                File Download\n                Supports downloading files, such as images, PDFs, and compressed files.\n            \n\n        \n    \n    \n    \n        Support Author\n        \n            EasySpider is a completely free and ad-free open-source software. The development and maintenance of the software rely entirely on the author's voluntary contributions. Therefore, you can choose to support the author and enable them to have more passion and energy to maintain this software. If you have benefited from this software and made a profit, you are also welcome to support the author through the following methods:\n            1. Alipay account: <EMAIL>. You can also scan the QR code below.\n            2. WeChat Pay: Scan the QR code below.\n            3. PayPal account: naibowang. You can also scan the QR code below.\n            \n        \n    \n\n    \n    .more-item {\n        height:320px;\n    }\n    .video-pc{\n        background-image: url(./img/animation_en.gif);\n    }\n    .video-mob{\n        background-image: url(./img/mcompmen.png);\n    }\n\n    \n        Copyright © 2020-document.write(new Date().getFullYear())2023 浙江大学 Zhejiang University. All Rights Reserved.  版权所有\n    \n    \n    \n    \n    \n        window.tcssReport = function(hottag, domain) { /*点击流上报*/\n            if (typeof pgvSendClick == 'function') {\n                pgvSendClick({\n                    virtualDomain: domain || \"tim.qq.com\",\n                    hottag: hottag\n                });\n            }\n        };\n        tcssReport('tim.index.pv')\n\n        // var zaixianwendang = $('#zaixianwendang').offset().top\n        // var dateManage = $('#date-manage').offset().top\n        // var msg = $('#msg').offset().top\n        // var yun = $('#yun').offset().top\n        // var pdocument = $('#document').offset().top\n        var windowHeight = $(window).height()\n            // 页面滚动触发播放事件\n        function pageScroll() {\n            var documentScrollTop = $(document).scrollTop()\n\n            if (documentScrollTop > 10) {\n                $('.header').addClass('scrollTop')\n            } else {\n                $('.header').removeClass('scrollTop')\n            }\n            // if (documentScrollTop > pdocument - windowHeight / 2 - 300) {\n            //     $('#document').addClass('moveUp')\n            // }\n            // if (documentScrollTop > dateManage - windowHeight / 2 - 300) {\n            //     $('#date-manage').addClass('moveUp')\n            // }\n            // if (documentScrollTop > msg - windowHeight / 2 - 300) {\n            //     $('#msg').addClass('moveUp')\n            // }\n            // if (documentScrollTop > yun - windowHeight / 2 - 300) {\n            //     $('#yun').addClass('moveUp')\n            // }\n        }\n        $(document).scroll(pageScroll)\n        initVideoPosition()\n\n        function initVideoPosition() {\n            var w = 2560,\n                h = 997,\n                ml = 1298,\n                mb = 126,\n                mw = 180,\n                mh = 319,\n                pl = 1519,\n                pb = 264,\n                pw = 626,\n                ph = 501,\n                nw = (document.body.clientWidth).toFixed(9),\n                nh = (nw * h / w).toFixed(9),\n                mlp = (ml / w).toFixed(9),\n                mbp = (mb / h).toFixed(9),\n                mwp = (mw / w).toFixed(9),\n                mhp = (mh / h).toFixed(9),\n                mlnpx = nw * mlp - 130,\n                mbnpx = nh * mbp - 178,\n                mwnpx = nw * mwp * 2,\n                mhnpx = nh * mhp * 2,\n\n\n                plp = (pl / w).toFixed(9),\n                pbp = (pb / h).toFixed(9),\n                pwp = (pw / w).toFixed(9),\n                php = (ph / h).toFixed(9),\n                plnpx = nw * plp - 160,\n                pbnpx = nh * pbp - 200,\n                pwnpx = nw * pwp * 1.7,\n                phnpx = nh * php * 1.7\n            $('.video-mob').css({\n                bottom: mbnpx + 'px',\n                left: mlnpx + 'px',\n                width: mwnpx + 'px',\n                height: mhnpx + 'px'\n            })\n            $('.video-pc').css({\n                bottom: pbnpx + 'px',\n                left: plnpx + 'px',\n                width: pwnpx + 'px',\n                height: phnpx + 'px'\n            })\n\n        }\n        setTimeout(function() {\n            document.getElementById('mob').play()\n            document.getElementById('pc').play()\n        }, 2000)\n        $('.wording').addClass('show')\n        window.onresize = function() {\n            initVideoPosition()\n        }\n        document.getElementById('mob').onended = function() {\n            var _this = this;\n            var ender = setTimeout(function() {\n                _this.play()\n                clearTimeout(ender)\n            }, 2000)\n        }\n        document.getElementById('pc').onended = function() {\n            var _this = this;\n            var ender = setTimeout(function() {\n                _this.play()\n                clearTimeout(ender)\n            }, 2000)\n        }\n        document.onmouseenter = function() {\n            return false;\n        }\n        document.onmousemove = function() {\n            return false;\n        }\n        document.oncontextmenu = function() {\n            return false;\n        }\n    \n16,941 PageviewsJun. 21st - Jul. 21st.jvectormap-container {    width: 100%;    height: 100%;    position: absolute;    overflow: hidden;}.jvectormap-tip {    position: absolute;    display: none;    border: solid 1px #CDCDCD;    border-radius: 3px;    background: #292929;    color: white;    font-family: Arial, Helvetica, sans-serif;    padding: 3px;    z-index: 9999;    font-size: 11px;    line-height: 13px;}.jvectormap-zoomin, .jvectormap-zoomout, .jvectormap-goback {    background: #ffffff none repeat scroll 0 0;    border: 1px solid #bebebe;    border-radius: 2px;    box-sizing: content-box;    color: #838383;    cursor: pointer;    font-weight: bold;    left: 10px;    padding: 3px;    position: absolute;    text-align: center;    z-index: 1;}.jvectormap-zoomin, .jvectormap-zoomout {    padding: 2px 10px;}.jvectormap-zoomin {    top: 10px;}.jvectormap-zoomout {    top: 37px;}.jvectormap-goback {    bottom: 10px;    z-index: 1000;    padding: 6px;}.jvectormap-spinner {    position: absolute;    left: 0;    top: 0;    right: 0;    bottom: 0;    background: center no-repeat url(data:image/gif;base64,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);}.jvectormap-legend-title {    font-weight: bold;    font-size: 14px;    text-align: center;}.jvectormap-legend-cnt {    position: absolute;}.jvectormap-legend-cnt-h {    bottom: 0;    right: 0;}.jvectormap-legend-cnt-v {    top: 0;    right: 0;}.jvectormap-legend {    background: black;    color: white;    border-radius: 3px;}.jvectormap-legend-cnt-h .jvectormap-legend {    float: left;    margin: 0 10px 10px 0;    padding: 3px 3px 1px 3px;}.jvectormap-legend-cnt-h .jvectormap-legend .jvectormap-legend-tick {    float: left;}.jvectormap-legend-cnt-v .jvectormap-legend {    margin: 10px 10px 0 0;    padding: 3px;}.jvectormap-legend-cnt-h .jvectormap-legend-tick {    width: 40px;}.jvectormap-legend-cnt-h .jvectormap-legend-tick-sample {    height: 15px;}.jvectormap-legend-cnt-v .jvectormap-legend-tick-sample {    height: 20px;    width: 20px;    display: inline-block;    vertical-align: middle;}.jvectormap-legend-tick-text {    font-size: 12px;}.jvectormap-legend-cnt-h .jvectormap-legend-tick-text {    text-align: center;}.jvectormap-legend-cnt-v .jvectormap-legend-tick-text {    display: inline-block;    vertical-align: middle;    line-height: 20px;    padding-left: 3px;}a#clustrmaps-widget-v2, #clustrmaps-widget-v2 {    display: block;    font-size: 11px;    line-height: 13px;    margin: 0 auto;    padding: 0;    position: relative;    text-align: center;    color: transparent;    min-height: 139px;    text-decoration: none;    /* text-shadow: 1px 1px 0 #01324f; */    border: 0 none;}.clustrmaps-map {    position: relative;}#clustrmaps-widget-v2 > .clustrmaps-map-container {    background-position: 0 0;    background-repeat: no-repeat;    position: relative;}#clustrmaps-widget-v2 > .clustrmaps-map-container > .clustrmaps-date {    text-align: center;    width: 100%;    z-index: 10;}#clustrmaps-widget-v2 > .clustrmaps-map-container > .clustrmaps-logo {    position: absolute;    background-image: url(\"//clustrmaps.com/assets/clustrmaps/img/logo4-small.png\");    bottom: 0px;    background-repeat: no-repeat;    background-position: center center;    z-index: 1;    width: 100px;    height: 31px;    left: 0px;}#clustrmaps-widget-v2 > .clustrmaps-map-container > .clustrmaps-connection {    background-position: center center;    background-repeat: no-repeat;    bottom: 0;    color: rgba(255, 255, 255, 0.5);    padding: 4px 4px;    position: absolute;    right: 0;    z-index: 1;    font-variant: small-caps;}#clustrmaps-widget-v2 > .clustrmaps-bottom-text {    letter-spacing: 0px;    background: #FFFFFF;    color: #000000;    text-shadow: none;}#clustrmaps-widget-v2 > .clustrmaps-cursor-click {    background-position: center center;    background-repeat: no-repeat;    display: block;    height: 29px;    position: absolute;    right: 0;    top: 56px;    width: 30px;}#clustrmaps-widget-v2 > .clustrmaps-map-container > .clustrmaps-connection.clustrmaps-failed {    color: rgba(255, 0, 0, 0.8);}/*#clustrmaps-widget-v2 > .clustrmaps-map-container {    background-image: url(\"//clustrmaps.com/images/map_v2_loading.png\");}*/#clustrmaps-widget-v2 > .clustrmaps-cursor-click {    background-image: url(\"//clustrmaps.com/assets/clustrmaps/img/cursor_click.png\");}#clustrmaps-widget-v2 > .clustrmaps-bottom-text.variation {    display: none;}/* CONTROL *//*#clustrmaps-widget-v2.clustrmaps-map-control > .clustrmaps-map-container {    background-image: url(\"//clustrmaps.com/images/map_v2-control.png\");}*/#clustrmaps-widget-v2.clustrmaps-map-control > .clustrmaps-bottom-text {    -webkit-border-radius: 3px;    -moz-border-radius: 3px;    border-radius: 3px;    border: 1px solid #999;    background: #F24D58;    background: -moz-linear-gradient(top, #FF636D 50%, #DD2929 50%);    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%,#f83737), color-stop(50%,#f83737));    background: linear-gradient(to bottom, #FF636D 50%,#DD2929 51%);    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\"#FF636D\", endColorstr=\"#f83737\",GradientType=0 );    display: block;    margin: 2px auto 0;    padding: 3px 14px;    color: #FFFFFF;    text-shadow: 1px 1px 0px #5B0000;    font-weight: 600;}#clustrmaps-widget-v2.clustrmaps-map-control  > .clustrmaps-bottom-text:hover {    border: 1px solid #888;    background: #a3f5a2;    background: -moz-linear-gradient(top,  #ed8b92 50%, #D76666 50%);    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%,#ed8b92), color-stop(50%,#D76666));    background: linear-gradient(to bottom, #ed8b92 50%,#D76666 51%);    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\"#ed8b92\", endColorstr=\"#D76666\",GradientType=0 );}#clustrmaps-widget-v2.clustrmaps-map-control > .clustrmaps-bottom-text.variation {    display: none;}.clstm {    text-transform: capitalize !important;    position: relative;}#clustrmaps-widget-v2.clustrmaps-map-variation > .clustrmaps-bottom-text {    -webkit-border-radius: 3px;    -moz-border-radius: 3px;    border-radius: 3px;    border: 1px solid #999;    background: #F24D58;    background: -moz-linear-gradient(top, #FF636D 50%, #DD2929 50%);    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%,#f83737), color-stop(50%,#f83737));    background: linear-gradient(to bottom, #FF636D 50%,#DD2929 51%);    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\"#FF636D\", endColorstr=\"#f83737\",GradientType=0 );    display: block;    margin: 2px auto 0;    padding: 3px 14px;    color: #FFFFFF;    text-shadow: 1px 1px 0px #5B0000;    font-weight: 600;}#clustrmaps-widget-v2.clustrmaps-map-variation > .clustrmaps-bottom-text {    display: none;}#clustrmaps-widget-v2.clustrmaps-map-variation > .clustrmaps-bottom-text.variation {    display: block;}#clustrmaps-widget-v2.clustrmaps-map-variation  > .clustrmaps-bottom-text:hover {    border: 1px solid #888;    background: #a3f5a2;    background: -moz-linear-gradient(top,  #ed8b92 50%, #D76666 50%);    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%,#ed8b92), color-stop(50%,#D76666));    background: linear-gradient(to bottom, #ed8b92 50%,#D76666 51%);    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\"#ed8b92\", endColorstr=\"#D76666\",GradientType=0 );}.clustrmaps-visitors, .clustrmaps-date, .clustrmaps-bottom-text {    font-family: Arial, Helvetica, sans-serif;    text-align: center;    font-weight: normal;}.clustrmaps-bottom-text {    font-size: 12px;}\n\n\n.copyrights{text-indent:-9999px;height:0;line-height:0;font-size:0;overflow:hidden;}\n.clustrmaps-map-control{\n    display: none!important;\n}\n\n\n\n\n\n     ✖ ✍操作台（点此拖动，左上角调整大小）  \n              ● 已选中1个元素，您可以:\n                  确认采集    取消选择  Path: /html/body  \n"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "http://localhost:8074/taskGrid/test_pages/new_window.html", "links": "http://localhost:8074/taskGrid/test_pages/new_window.html", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3, 4], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/a", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 1}}, {"id": 4, "index": 4, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": ["/html/body", "//body[contains(., '')]", "/html/body"], "exampleValues": [{"num": 0, "value": "\n    \n        \n        \n            \n                \n                EasySpider: Visual Web Crawler\n                An open-source, free, and ad-free software for designing a web scraper without writing any code in minutes.\n                Click below to star this open-source project on Github~\n                \n                    \n                        Github Repository\n                        10K+ Stars on Github\n                    \n                \n                \n                    \n                        Download Now\n                        Download Now\n                    \n                \n                \n            \n            \n\n            \n            \n            \n\n            \n        \n    \n\n    \n        \n\n            \n                \n                    Home\n                    \n                    Documentation\n                    Videos\n                    Q&A\n                    Paper\n                    Patent\n                \n\n            \n        \n       \n    \n    \n        \n            \n                \n                Suitable for Anyone in the Workplace\n                Design and execute web scraping tasks visually, just like using Excel, regardless of coding experience.\n            \n            \n                \n                Supports Configuring Cloud Servers\n                Can configure a cloud server address for unified task information storage and management.\n            \n            \n                \n                Define Any Complex Task\n                Supports infinite loop nesting and conditional judgments, can also execute custom JavaScript instructions and system instructions. You can insert the exit loop Break statement into any position of the flowchart.\n            \n        \n    \n\n\n\n    \n        \n            Software Features\n            \n                \n                Open Source, Free and Ad-Free\n                The code is open source, all software features are free (excluding commercial use), with no pop-ups or external ads.\n            \n            \n                \n                Cross-Platform\n                The software can run on Windows, MacOS, and Linux.\n            \n            \n                \n                Quick and Easy\n                Graphical setup and execution of crawler tasks, typically a crawler task can be completed in design within 2-5 minutes.\n            \n            \n                \n                Safe\n                No registration required, all tasks and data are saved locally, no third-party servers involved.\n            \n            \n                \n                Flexible\n                Can add any browser plugins, and execute any JavaScript commands.\n            \n            \n                \n                Running Parallel Tasks\n                Can launch any number of execution programs, achieve large-scale data parallel acquisition.\n            \n            \n                \n                Captcha Recognition\n                Supports multiple captcha recognition schemes, such as graphic captcha, Google reCAPTCHA, etc.\n            \n            \n                \n                Element Screenshot and OCR Recognition\n                Supports element screenshots and OCR recognition, as well as image downloads.\n            \n            \n                \n                Proxy IP\n                Supports tunnel IP switching, private IP, etc.\n            \n            \n                \n                LAN Access\n                Can be used within a LAN without internet.\n            \n            \n                \n                External Program Invocation\n                Can call any external systems to meet any complex requirements.\n            \n            \n                \n                API Invocation\n                Tasks can be executed via API invocation, achieving advanced automation collection.\n            \n            \n                \n                Scheduled Execution\n                Supports scheduled task execution, becomes a handy life assistant.\n            \n            \n                \n                Pause Anytime\n                Can pause task execution at any time for manual debugging and captcha input.\n            \n            \n                \n                Define Variables\n                Can define variables and integrate these into any code statement.\n            \n            \n                \n                Mobile Device Simulation\n                Supports mobile device simulation, enabling mobile web page collection.\n            \n            \n                \n                Flexible Import\n                Supports reading Excel files to import a large number of input parameters.\n            \n            \n                \n                Free Export\n                Can export to Excel/CSV/TXT files, and supports writing into MySQL databases.\n            \n            \n                \n                Task Migration\n                Tasks can be copied and moved to other machines, regardless of the operating system environment.\n            \n            \n                \n                Bypass Cloudflare Verification\n                Supports the collection of websites protected by Cloudflare captcha.\n            \n            \n                \n                Page Scroll\n                Can set page scrolling to access content that needs scroll loading.\n            \n            \n                \n                IFRAME Support\n                Supports the collection of data within iframe tags.\n            \n            \n                \n                Cookies Modification\n                Can access and modify page cookies.\n            \n            \n                \n                Command Line Execution\n                Tasks can be executed directly via command line, seamlessly integrated into other programs.\n            \n            \n                \n                Headless Mode\n                Supports headless mode, allowing it to run in the background without opening a browser window.\n            \n            \n                \n                Regular Expressions\n                Supports regular expressions, allowing the usage of regex anywhere.\n            \n            \n                \n                Modify Web Content\n                Capable of modifying web content, enabling customized scraping for more precise requirements.\n            \n            \n                \n                Code Debugging\n                The software comes with an embedded code execution feature, allowing direct running and debugging of Python code.\n            \n            \n                \n                JSON Support\n                Supports JSON data collection.\n            \n            \n                \n                File Download\n                Supports downloading files, such as images, PDFs, and compressed files.\n            \n\n        \n    \n    \n    \n        Support Author\n        \n            EasySpider is a completely free and ad-free open-source software. The development and maintenance of the software rely entirely on the author's voluntary contributions. Therefore, you can choose to support the author and enable them to have more passion and energy to maintain this software. If you have benefited from this software and made a profit, you are also welcome to support the author through the following methods:\n            1. Alipay account: <EMAIL>. You can also scan the QR code below.\n            2. WeChat Pay: Scan the QR code below.\n            3. PayPal account: naibowang. You can also scan the QR code below.\n            \n        \n    \n\n    \n    .more-item {\n        height:320px;\n    }\n    .video-pc{\n        background-image: url(./img/animation_en.gif);\n    }\n    .video-mob{\n        background-image: url(./img/mcompmen.png);\n    }\n\n    \n        Copyright © 2020-document.write(new Date().getFullYear())2023 浙江大学 Zhejiang University. All Rights Reserved.  版权所有\n    \n    \n    \n    \n    \n        window.tcssReport = function(hottag, domain) { /*点击流上报*/\n            if (typeof pgvSendClick == 'function') {\n                pgvSendClick({\n                    virtualDomain: domain || \"tim.qq.com\",\n                    hottag: hottag\n                });\n            }\n        };\n        tcssReport('tim.index.pv')\n\n        // var zaixianwendang = $('#zaixianwendang').offset().top\n        // var dateManage = $('#date-manage').offset().top\n        // var msg = $('#msg').offset().top\n        // var yun = $('#yun').offset().top\n        // var pdocument = $('#document').offset().top\n        var windowHeight = $(window).height()\n            // 页面滚动触发播放事件\n        function pageScroll() {\n            var documentScrollTop = $(document).scrollTop()\n\n            if (documentScrollTop > 10) {\n                $('.header').addClass('scrollTop')\n            } else {\n                $('.header').removeClass('scrollTop')\n            }\n            // if (documentScrollTop > pdocument - windowHeight / 2 - 300) {\n            //     $('#document').addClass('moveUp')\n            // }\n            // if (documentScrollTop > dateManage - windowHeight / 2 - 300) {\n            //     $('#date-manage').addClass('moveUp')\n            // }\n            // if (documentScrollTop > msg - windowHeight / 2 - 300) {\n            //     $('#msg').addClass('moveUp')\n            // }\n            // if (documentScrollTop > yun - windowHeight / 2 - 300) {\n            //     $('#yun').addClass('moveUp')\n            // }\n        }\n        $(document).scroll(pageScroll)\n        initVideoPosition()\n\n        function initVideoPosition() {\n            var w = 2560,\n                h = 997,\n                ml = 1298,\n                mb = 126,\n                mw = 180,\n                mh = 319,\n                pl = 1519,\n                pb = 264,\n                pw = 626,\n                ph = 501,\n                nw = (document.body.clientWidth).toFixed(9),\n                nh = (nw * h / w).toFixed(9),\n                mlp = (ml / w).toFixed(9),\n                mbp = (mb / h).toFixed(9),\n                mwp = (mw / w).toFixed(9),\n                mhp = (mh / h).toFixed(9),\n                mlnpx = nw * mlp - 130,\n                mbnpx = nh * mbp - 178,\n                mwnpx = nw * mwp * 2,\n                mhnpx = nh * mhp * 2,\n\n\n                plp = (pl / w).toFixed(9),\n                pbp = (pb / h).toFixed(9),\n                pwp = (pw / w).toFixed(9),\n                php = (ph / h).toFixed(9),\n                plnpx = nw * plp - 160,\n                pbnpx = nh * pbp - 200,\n                pwnpx = nw * pwp * 1.7,\n                phnpx = nh * php * 1.7\n            $('.video-mob').css({\n                bottom: mbnpx + 'px',\n                left: mlnpx + 'px',\n                width: mwnpx + 'px',\n                height: mhnpx + 'px'\n            })\n            $('.video-pc').css({\n                bottom: pbnpx + 'px',\n                left: plnpx + 'px',\n                width: pwnpx + 'px',\n                height: phnpx + 'px'\n            })\n\n        }\n        setTimeout(function() {\n            document.getElementById('mob').play()\n            document.getElementById('pc').play()\n        }, 2000)\n        $('.wording').addClass('show')\n        window.onresize = function() {\n            initVideoPosition()\n        }\n        document.getElementById('mob').onended = function() {\n            var _this = this;\n            var ender = setTimeout(function() {\n                _this.play()\n                clearTimeout(ender)\n            }, 2000)\n        }\n        document.getElementById('pc').onended = function() {\n            var _this = this;\n            var ender = setTimeout(function() {\n                _this.play()\n                clearTimeout(ender)\n            }, 2000)\n        }\n        document.onmouseenter = function() {\n            return false;\n        }\n        document.onmousemove = function() {\n            return false;\n        }\n        document.oncontextmenu = function() {\n            return false;\n        }\n    \n16,941 PageviewsJun. 21st - Jul. 21st.jvectormap-container {    width: 100%;    height: 100%;    position: absolute;    overflow: hidden;}.jvectormap-tip {    position: absolute;    display: none;    border: solid 1px #CDCDCD;    border-radius: 3px;    background: #292929;    color: white;    font-family: Arial, Helvetica, sans-serif;    padding: 3px;    z-index: 9999;    font-size: 11px;    line-height: 13px;}.jvectormap-zoomin, .jvectormap-zoomout, .jvectormap-goback {    background: #ffffff none repeat scroll 0 0;    border: 1px solid #bebebe;    border-radius: 2px;    box-sizing: content-box;    color: #838383;    cursor: pointer;    font-weight: bold;    left: 10px;    padding: 3px;    position: absolute;    text-align: center;    z-index: 1;}.jvectormap-zoomin, .jvectormap-zoomout {    padding: 2px 10px;}.jvectormap-zoomin {    top: 10px;}.jvectormap-zoomout {    top: 37px;}.jvectormap-goback {    bottom: 10px;    z-index: 1000;    padding: 6px;}.jvectormap-spinner {    position: absolute;    left: 0;    top: 0;    right: 0;    bottom: 0;    background: center no-repeat url(data:image/gif;base64,R0lGODlhIAAgAPMAAP///wAAAMbGxoSEhLa2tpqamjY2NlZWVtjY2OTk5Ly8vB4eHgQEBAAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAIAAgAAAE5xDISWlhperN52JLhSSdRgwVo1ICQZRUsiwHpTJT4iowNS8vyW2icCF6k8HMMBkCEDskxTBDAZwuAkkqIfxIQyhBQBFvAQSDITM5VDW6XNE4KagNh6Bgwe60smQUB3d4Rz1ZBApnFASDd0hihh12BkE9kjAJVlycXIg7CQIFA6SlnJ87paqbSKiKoqusnbMdmDC2tXQlkUhziYtyWTxIfy6BE8WJt5YJvpJivxNaGmLHT0VnOgSYf0dZXS7APdpB309RnHOG5gDqXGLDaC457D1zZ/V/nmOM82XiHRLYKhKP1oZmADdEAAAh+QQJCgAAACwAAAAAIAAgAAAE6hDISWlZpOrNp1lGNRSdRpDUolIGw5RUYhhHukqFu8DsrEyqnWThGvAmhVlteBvojpTDDBUEIFwMFBRAmBkSgOrBFZogCASwBDEY/CZSg7GSE0gSCjQBMVG023xWBhklAnoEdhQEfyNqMIcKjhRsjEdnezB+A4k8gTwJhFuiW4dokXiloUepBAp5qaKpp6+Ho7aWW54wl7obvEe0kRuoplCGepwSx2jJvqHEmGt6whJpGpfJCHmOoNHKaHx61WiSR92E4lbFoq+B6QDtuetcaBPnW6+O7wDHpIiK9SaVK5GgV543tzjgGcghAgAh+QQJCgAAACwAAAAAIAAgAAAE7hDISSkxpOrN5zFHNWRdhSiVoVLHspRUMoyUakyEe8PTPCATW9A14E0UvuAKMNAZKYUZCiBMuBakSQKG8G2FzUWox2AUtAQFcBKlVQoLgQReZhQlCIJesQXI5B0CBnUMOxMCenoCfTCEWBsJColTMANldx15BGs8B5wlCZ9Po6OJkwmRpnqkqnuSrayqfKmqpLajoiW5HJq7FL1Gr2mMMcKUMIiJgIemy7xZtJsTmsM4xHiKv5KMCXqfyUCJEonXPN2rAOIAmsfB3uPoAK++G+w48edZPK+M6hLJpQg484enXIdQFSS1u6UhksENEQAAIfkECQoAAAAsAAAAACAAIAAABOcQyEmpGKLqzWcZRVUQnZYg1aBSh2GUVEIQ2aQOE+G+cD4ntpWkZQj1JIiZIogDFFyHI0UxQwFugMSOFIPJftfVAEoZLBbcLEFhlQiqGp1Vd140AUklUN3eCA51C1EWMzMCezCBBmkxVIVHBWd3HHl9JQOIJSdSnJ0TDKChCwUJjoWMPaGqDKannasMo6WnM562R5YluZRwur0wpgqZE7NKUm+FNRPIhjBJxKZteWuIBMN4zRMIVIhffcgojwCF117i4nlLnY5ztRLsnOk+aV+oJY7V7m76PdkS4trKcdg0Zc0tTcKkRAAAIfkECQoAAAAsAAAAACAAIAAABO4QyEkpKqjqzScpRaVkXZWQEximw1BSCUEIlDohrft6cpKCk5xid5MNJTaAIkekKGQkWyKHkvhKsR7ARmitkAYDYRIbUQRQjWBwJRzChi9CRlBcY1UN4g0/VNB0AlcvcAYHRyZPdEQFYV8ccwR5HWxEJ02YmRMLnJ1xCYp0Y5idpQuhopmmC2KgojKasUQDk5BNAwwMOh2RtRq5uQuPZKGIJQIGwAwGf6I0JXMpC8C7kXWDBINFMxS4DKMAWVWAGYsAdNqW5uaRxkSKJOZKaU3tPOBZ4DuK2LATgJhkPJMgTwKCdFjyPHEnKxFCDhEAACH5BAkKAAAALAAAAAAgACAAAATzEMhJaVKp6s2nIkolIJ2WkBShpkVRWqqQrhLSEu9MZJKK9y1ZrqYK9WiClmvoUaF8gIQSNeF1Er4MNFn4SRSDARWroAIETg1iVwuHjYB1kYc1mwruwXKC9gmsJXliGxc+XiUCby9ydh1sOSdMkpMTBpaXBzsfhoc5l58Gm5yToAaZhaOUqjkDgCWNHAULCwOLaTmzswadEqggQwgHuQsHIoZCHQMMQgQGubVEcxOPFAcMDAYUA85eWARmfSRQCdcMe0zeP1AAygwLlJtPNAAL19DARdPzBOWSm1brJBi45soRAWQAAkrQIykShQ9wVhHCwCQCACH5BAkKAAAALAAAAAAgACAAAATrEMhJaVKp6s2nIkqFZF2VIBWhUsJaTokqUCoBq+E71SRQeyqUToLA7VxF0JDyIQh/MVVPMt1ECZlfcjZJ9mIKoaTl1MRIl5o4CUKXOwmyrCInCKqcWtvadL2SYhyASyNDJ0uIiRMDjI0Fd30/iI2UA5GSS5UDj2l6NoqgOgN4gksEBgYFf0FDqKgHnyZ9OX8HrgYHdHpcHQULXAS2qKpENRg7eAMLC7kTBaixUYFkKAzWAAnLC7FLVxLWDBLKCwaKTULgEwbLA4hJtOkSBNqITT3xEgfLpBtzE/jiuL04RGEBgwWhShRgQExHBAAh+QQJCgAAACwAAAAAIAAgAAAE7xDISWlSqerNpyJKhWRdlSAVoVLCWk6JKlAqAavhO9UkUHsqlE6CwO1cRdCQ8iEIfzFVTzLdRAmZX3I2SfZiCqGk5dTESJeaOAlClzsJsqwiJwiqnFrb2nS9kmIcgEsjQydLiIlHehhpejaIjzh9eomSjZR+ipslWIRLAgMDOR2DOqKogTB9pCUJBagDBXR6XB0EBkIIsaRsGGMMAxoDBgYHTKJiUYEGDAzHC9EACcUGkIgFzgwZ0QsSBcXHiQvOwgDdEwfFs0sDzt4S6BK4xYjkDOzn0unFeBzOBijIm1Dgmg5YFQwsCMjp1oJ8LyIAACH5BAkKAAAALAAAAAAgACAAAATwEMhJaVKp6s2nIkqFZF2VIBWhUsJaTokqUCoBq+E71SRQeyqUToLA7VxF0JDyIQh/MVVPMt1ECZlfcjZJ9mIKoaTl1MRIl5o4CUKXOwmyrCInCKqcWtvadL2SYhyASyNDJ0uIiUd6GGl6NoiPOH16iZKNlH6KmyWFOggHhEEvAwwMA0N9GBsEC6amhnVcEwavDAazGwIDaH1ipaYLBUTCGgQDA8NdHz0FpqgTBwsLqAbWAAnIA4FWKdMLGdYGEgraigbT0OITBcg5QwPT4xLrROZL6AuQAPUS7bxLpoWidY0JtxLHKhwwMJBTHgPKdEQAACH5BAkKAAAALAAAAAAgACAAAATrEMhJaVKp6s2nIkqFZF2VIBWhUsJaTokqUCoBq+E71SRQeyqUToLA7VxF0JDyIQh/MVVPMt1ECZlfcjZJ9mIKoaTl1MRIl5o4CUKXOwmyrCInCKqcWtvadL2SYhyASyNDJ0uIiUd6GAULDJCRiXo1CpGXDJOUjY+Yip9DhToJA4RBLwMLCwVDfRgbBAaqqoZ1XBMHswsHtxtFaH1iqaoGNgAIxRpbFAgfPQSqpbgGBqUD1wBXeCYp1AYZ19JJOYgH1KwA4UBvQwXUBxPqVD9L3sbp2BNk2xvvFPJd+MFCN6HAAIKgNggY0KtEBAAh+QQJCgAAACwAAAAAIAAgAAAE6BDISWlSqerNpyJKhWRdlSAVoVLCWk6JKlAqAavhO9UkUHsqlE6CwO1cRdCQ8iEIfzFVTzLdRAmZX3I2SfYIDMaAFdTESJeaEDAIMxYFqrOUaNW4E4ObYcCXaiBVEgULe0NJaxxtYksjh2NLkZISgDgJhHthkpU4mW6blRiYmZOlh4JWkDqILwUGBnE6TYEbCgevr0N1gH4At7gHiRpFaLNrrq8HNgAJA70AWxQIH1+vsYMDAzZQPC9VCNkDWUhGkuE5PxJNwiUK4UfLzOlD4WvzAHaoG9nxPi5d+jYUqfAhhykOFwJWiAAAIfkECQoAAAAsAAAAACAAIAAABPAQyElpUqnqzaciSoVkXVUMFaFSwlpOCcMYlErAavhOMnNLNo8KsZsMZItJEIDIFSkLGQoQTNhIsFehRww2CQLKF0tYGKYSg+ygsZIuNqJksKgbfgIGepNo2cIUB3V1B3IvNiBYNQaDSTtfhhx0CwVPI0UJe0+bm4g5VgcGoqOcnjmjqDSdnhgEoamcsZuXO1aWQy8KAwOAuTYYGwi7w5h+Kr0SJ8MFihpNbx+4Erq7BYBuzsdiH1jCAzoSfl0rVirNbRXlBBlLX+BP0XJLAPGzTkAuAOqb0WT5AH7OcdCm5B8TgRwSRKIHQtaLCwg1RAAAOwAAAAAAAAAAAA==);}.jvectormap-legend-title {    font-weight: bold;    font-size: 14px;    text-align: center;}.jvectormap-legend-cnt {    position: absolute;}.jvectormap-legend-cnt-h {    bottom: 0;    right: 0;}.jvectormap-legend-cnt-v {    top: 0;    right: 0;}.jvectormap-legend {    background: black;    color: white;    border-radius: 3px;}.jvectormap-legend-cnt-h .jvectormap-legend {    float: left;    margin: 0 10px 10px 0;    padding: 3px 3px 1px 3px;}.jvectormap-legend-cnt-h .jvectormap-legend .jvectormap-legend-tick {    float: left;}.jvectormap-legend-cnt-v .jvectormap-legend {    margin: 10px 10px 0 0;    padding: 3px;}.jvectormap-legend-cnt-h .jvectormap-legend-tick {    width: 40px;}.jvectormap-legend-cnt-h .jvectormap-legend-tick-sample {    height: 15px;}.jvectormap-legend-cnt-v .jvectormap-legend-tick-sample {    height: 20px;    width: 20px;    display: inline-block;    vertical-align: middle;}.jvectormap-legend-tick-text {    font-size: 12px;}.jvectormap-legend-cnt-h .jvectormap-legend-tick-text {    text-align: center;}.jvectormap-legend-cnt-v .jvectormap-legend-tick-text {    display: inline-block;    vertical-align: middle;    line-height: 20px;    padding-left: 3px;}a#clustrmaps-widget-v2, #clustrmaps-widget-v2 {    display: block;    font-size: 11px;    line-height: 13px;    margin: 0 auto;    padding: 0;    position: relative;    text-align: center;    color: transparent;    min-height: 139px;    text-decoration: none;    /* text-shadow: 1px 1px 0 #01324f; */    border: 0 none;}.clustrmaps-map {    position: relative;}#clustrmaps-widget-v2 > .clustrmaps-map-container {    background-position: 0 0;    background-repeat: no-repeat;    position: relative;}#clustrmaps-widget-v2 > .clustrmaps-map-container > .clustrmaps-date {    text-align: center;    width: 100%;    z-index: 10;}#clustrmaps-widget-v2 > .clustrmaps-map-container > .clustrmaps-logo {    position: absolute;    background-image: url(\"//clustrmaps.com/assets/clustrmaps/img/logo4-small.png\");    bottom: 0px;    background-repeat: no-repeat;    background-position: center center;    z-index: 1;    width: 100px;    height: 31px;    left: 0px;}#clustrmaps-widget-v2 > .clustrmaps-map-container > .clustrmaps-connection {    background-position: center center;    background-repeat: no-repeat;    bottom: 0;    color: rgba(255, 255, 255, 0.5);    padding: 4px 4px;    position: absolute;    right: 0;    z-index: 1;    font-variant: small-caps;}#clustrmaps-widget-v2 > .clustrmaps-bottom-text {    letter-spacing: 0px;    background: #FFFFFF;    color: #000000;    text-shadow: none;}#clustrmaps-widget-v2 > .clustrmaps-cursor-click {    background-position: center center;    background-repeat: no-repeat;    display: block;    height: 29px;    position: absolute;    right: 0;    top: 56px;    width: 30px;}#clustrmaps-widget-v2 > .clustrmaps-map-container > .clustrmaps-connection.clustrmaps-failed {    color: rgba(255, 0, 0, 0.8);}/*#clustrmaps-widget-v2 > .clustrmaps-map-container {    background-image: url(\"//clustrmaps.com/images/map_v2_loading.png\");}*/#clustrmaps-widget-v2 > .clustrmaps-cursor-click {    background-image: url(\"//clustrmaps.com/assets/clustrmaps/img/cursor_click.png\");}#clustrmaps-widget-v2 > .clustrmaps-bottom-text.variation {    display: none;}/* CONTROL *//*#clustrmaps-widget-v2.clustrmaps-map-control > .clustrmaps-map-container {    background-image: url(\"//clustrmaps.com/images/map_v2-control.png\");}*/#clustrmaps-widget-v2.clustrmaps-map-control > .clustrmaps-bottom-text {    -webkit-border-radius: 3px;    -moz-border-radius: 3px;    border-radius: 3px;    border: 1px solid #999;    background: #F24D58;    background: -moz-linear-gradient(top, #FF636D 50%, #DD2929 50%);    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%,#f83737), color-stop(50%,#f83737));    background: linear-gradient(to bottom, #FF636D 50%,#DD2929 51%);    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\"#FF636D\", endColorstr=\"#f83737\",GradientType=0 );    display: block;    margin: 2px auto 0;    padding: 3px 14px;    color: #FFFFFF;    text-shadow: 1px 1px 0px #5B0000;    font-weight: 600;}#clustrmaps-widget-v2.clustrmaps-map-control  > .clustrmaps-bottom-text:hover {    border: 1px solid #888;    background: #a3f5a2;    background: -moz-linear-gradient(top,  #ed8b92 50%, #D76666 50%);    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%,#ed8b92), color-stop(50%,#D76666));    background: linear-gradient(to bottom, #ed8b92 50%,#D76666 51%);    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\"#ed8b92\", endColorstr=\"#D76666\",GradientType=0 );}#clustrmaps-widget-v2.clustrmaps-map-control > .clustrmaps-bottom-text.variation {    display: none;}.clstm {    text-transform: capitalize !important;    position: relative;}#clustrmaps-widget-v2.clustrmaps-map-variation > .clustrmaps-bottom-text {    -webkit-border-radius: 3px;    -moz-border-radius: 3px;    border-radius: 3px;    border: 1px solid #999;    background: #F24D58;    background: -moz-linear-gradient(top, #FF636D 50%, #DD2929 50%);    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%,#f83737), color-stop(50%,#f83737));    background: linear-gradient(to bottom, #FF636D 50%,#DD2929 51%);    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\"#FF636D\", endColorstr=\"#f83737\",GradientType=0 );    display: block;    margin: 2px auto 0;    padding: 3px 14px;    color: #FFFFFF;    text-shadow: 1px 1px 0px #5B0000;    font-weight: 600;}#clustrmaps-widget-v2.clustrmaps-map-variation > .clustrmaps-bottom-text {    display: none;}#clustrmaps-widget-v2.clustrmaps-map-variation > .clustrmaps-bottom-text.variation {    display: block;}#clustrmaps-widget-v2.clustrmaps-map-variation  > .clustrmaps-bottom-text:hover {    border: 1px solid #888;    background: #a3f5a2;    background: -moz-linear-gradient(top,  #ed8b92 50%, #D76666 50%);    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%,#ed8b92), color-stop(50%,#D76666));    background: linear-gradient(to bottom, #ed8b92 50%,#D76666 51%);    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\"#ed8b92\", endColorstr=\"#D76666\",GradientType=0 );}.clustrmaps-visitors, .clustrmaps-date, .clustrmaps-bottom-text {    font-family: Arial, Helvetica, sans-serif;    text-align: center;    font-weight: normal;}.clustrmaps-bottom-text {    font-size: 12px;}\n\n\n.copyrights{text-indent:-9999px;height:0;line-height:0;font-size:0;overflow:hidden;}\n.clustrmaps-map-control{\n    display: none!important;\n}\n\n\n\n\n\n     ✖ ✍操作台（点此拖动，左上角调整大小）  \n              ● 已选中1个元素，您可以:\n                  确认采集    取消选择  Path: /html/body  \n"}], "unique_index": "vwlzigwycslkcttnsw", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}