{"id": 29, "name": "eBay Detail Page Info Collection", "url": "https://www.ebay.com", "links": "https://www.ebay.com", "containJudge": false, "desc": "https://www.ebay.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "Open Page", "value": "https://www.ebay.com", "desc": "要采集的网址列表,多行以\\n分开", "type": "string", "exampleValue": "https://www.ebay.com"}, {"id": 1, "name": "inputText_1", "nodeName": "Input Text", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "string", "exampleValue": "iPhone", "value": "iPhone"}, {"id": 2, "name": "loopTimes_Loop Click Next Page_2", "nodeId": 7, "nodeName": "Loop Click Next Page", "desc": "循环Loop Click Next Page执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "para1_text", "desc": "", "type": "string", "exampleValue": " Apple iPhone 11 - 128GB 64GB - GSM+CDMA Factory Unlocked \"Excellent' "}, {"id": 1, "name": "para2_text", "desc": "", "type": "string", "exampleValue": "US $224.00"}, {"id": 2, "name": "para3_text", "desc": "", "type": "string", "exampleValue": "1,605 sold"}, {"id": 3, "name": "para4_text", "desc": "", "type": "string", "exampleValue": "3,874 watchers"}, {"id": 4, "name": "para5_text", "desc": "", "type": "string", "exampleValue": "Returns accepted"}, {"id": 5, "name": "para6_text", "desc": "", "type": "string", "exampleValue": "US $17.25 (approx HKD 135.25) eBay International ShippingShop worldwide with confidence including detailed tracking and hassle-free returns. Learn more | See detailsfor shipping"}, {"id": 6, "name": "para7_text", "desc": "", "type": "string", "exampleValue": "Phones are in Excellent cosmetic condition. Devices are ready to activate. Clean ESN. PhoneCheck "}, {"id": 7, "name": "para8_text", "desc": "", "type": "string", "exampleValue": "Read moreabout condition"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 3, 7], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "Open Page", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "url": "https://www.ebay.com", "links": "https://www.ebay.com", "scrollType": 0, "scrollCount": 0}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "Input Text", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"gh-ac\"]", "wait": 0, "value": "iPhone"}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "<PERSON>lick Element", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"gh-btn\"]", "wait": 0, "scrollType": 0, "scrollCount": 0, "params": []}}, {"id": 5, "index": 4, "parentId": 4, "type": 1, "option": 8, "title": "Loop", "sequence": [5, 6], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[8]/div[4]/div[2]/div[1]/div[2]/ul[1]/li/div[1]/div[2]/a[1]", "wait": 0, "scrollType": 0, "scrollCount": 0, "loopType": 1, "pathList": "", "textList": "", "exitCount": 0, "historyWait": 2}}, {"id": 7, "index": 5, "parentId": 5, "type": 0, "option": 2, "title": "Click Page Link", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[8]/div[4]/div[2]/div[1]/div[2]/ul[1]/li/div[1]/div[2]/a[1]", "wait": 0, "scrollType": 0, "scrollCount": 0, "params": [], "loopType": 1}}, {"id": 8, "index": 6, "parentId": 5, "type": 0, "option": 3, "title": "Extract Data", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 2, "tabIndex": 1, "useLoop": false, "xpath": "", "wait": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "para1_text", "desc": "", "relativeXPath": "/html/body/div[5]/div[3]/div[1]/div[1]/div[2]/div[3]/div[1]/div[1]/div[1]", "exampleValues": [{"num": 0, "value": " Apple iPhone 11 - 128GB 64GB - GSM+CDMA Factory Unlocked \"Excellent' "}], "default": ""}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para2_text", "desc": "", "relativeXPath": "/html/body/div[5]/div[3]/div[1]/div[1]/div[2]/div[3]/div[2]/form[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/span[1]/span[1]", "exampleValues": [{"num": 0, "value": "US $224.00"}], "default": ""}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para3_text", "desc": "", "relativeXPath": "/html/body/div[5]/div[3]/div[1]/div[1]/div[2]/div[3]/div[2]/form[1]/div[4]/ul[1]/li[1]", "exampleValues": [{"num": 0, "value": "1,605 sold"}], "default": ""}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para4_text", "desc": "", "relativeXPath": "/html/body/div[5]/div[3]/div[1]/div[1]/div[2]/div[3]/div[2]/form[1]/div[4]/ul[1]/li[2]", "exampleValues": [{"num": 0, "value": "3,874 watchers"}], "default": ""}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para5_text", "desc": "", "relativeXPath": "/html/body/div[5]/div[3]/div[1]/div[1]/div[2]/div[3]/div[2]/form[1]/div[4]/ul[1]/li[3]", "exampleValues": [{"num": 0, "value": "Returns accepted"}], "default": ""}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para6_text", "desc": "", "relativeXPath": "/html/body/div[5]/div[3]/div[1]/div[1]/div[2]/div[3]/div[2]/form[1]/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]", "exampleValues": [{"num": 0, "value": "US $17.25 (approx HKD 135.25) eBay International ShippingShop worldwide with confidence including detailed tracking and hassle-free returns. Learn more | See detailsfor shipping"}], "default": ""}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para7_text", "desc": "", "relativeXPath": "/html/body/div[5]/div[3]/div[1]/div[1]/div[2]/div[3]/div[2]/form[1]/div[1]/div[1]/div[1]/div[2]/div[2]/span[2]", "exampleValues": [{"num": 0, "value": "Phones are in Excellent cosmetic condition. Devices are ready to activate. Clean ESN. PhoneCheck "}], "default": ""}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "para8_text", "desc": "", "relativeXPath": "/html/body/div[5]/div[3]/div[1]/div[1]/div[2]/div[3]/div[2]/form[1]/div[1]/div[1]/div[1]/div[2]/div[2]/a[1]/span[1]", "exampleValues": [{"num": 0, "value": "Read moreabout condition"}], "default": ""}]}}, {"id": 4, "index": 7, "parentId": 0, "type": 1, "option": 8, "title": "Loop Click Next Page", "sequence": [4, 8], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": 0, "useLoop": false, "xpath": "//*[contains(@class, \"pagination__next\")]", "wait": 0, "scrollType": 0, "scrollCount": 0, "loopType": 0, "pathList": "", "textList": "", "exitCount": 0, "historyWait": 2}}, {"id": 6, "index": 8, "parentId": 4, "type": 0, "option": 2, "title": "Click Next", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 5, "tabIndex": 0, "useLoop": true, "xpath": "//*[contains(@class, \"pagination__next\")]", "wait": 0, "scrollType": 0, "scrollCount": 0, "params": [], "loopType": 0}}]}