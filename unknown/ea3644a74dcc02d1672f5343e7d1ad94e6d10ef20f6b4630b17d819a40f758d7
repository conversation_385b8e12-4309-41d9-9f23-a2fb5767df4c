{"id": 45, "name": "定义JS操作", "url": "https://www.jd.com", "links": "https://www.jd.com", "containJudge": false, "desc": "https://www.jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.jd.com"}, {"id": 1, "name": "loopTimes_循环_1", "nodeId": 3, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}, {"id": 2, "name": "inputText_2", "nodeName": "输入文字", "nodeId": 4, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "string", "exampleValue": "", "value": ""}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 3], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.jd.com", "links": "https://www.jd.com", "scrollType": 0, "scrollCount": 0}}, {"id": 4, "index": 2, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": 0, "useLoop": true, "xpath": "//*[contains(@ceM\")]/div[1]", "wait": 0, "beforeJS": "arguments[0].click()", "beforeJSWaitTime": 4, "afterJS": "arguments[0].innerText = \"test\"", "afterJSWaitTime": 5, "scrollType": 0, "scrollCount": 0, "params": [], "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]", "//div[contains(., '/手机/数码')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG ']"]}}, {"id": 2, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4, 2], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 0, "loopType": 0, "pathList": "", "textList": "", "exitCount": 0, "historyWait": 2}}, {"id": 3, "index": 4, "parentId": 2, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "adsf", "beforeJSWaitTime": 1, "afterJS": "qwe", "afterJSWaitTime": 2, "value": ""}}]}