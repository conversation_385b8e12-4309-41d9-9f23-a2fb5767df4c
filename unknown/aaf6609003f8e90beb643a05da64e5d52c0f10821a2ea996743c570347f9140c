{"id": 135, "name": "HTML Select example", "url": "http://localhost:8074/taskGrid/test_pages/select.html", "links": "http://localhost:8074/taskGrid/test_pages/select.html", "create_time": "7/5/2023, 6:52:44 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "http://localhost:8074/taskGrid/test_pages/select.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://localhost:8074/taskGrid/test_pages/select.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "http://localhost:8074/taskGrid/test_pages/select.html"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "http://localhost:8074/taskGrid/test_pages/select.html", "links": "http://localhost:8074/taskGrid/test_pages/select.html", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"cars\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": ["/html/body/form[1]/select[1]", "//select[contains(., '')]", "id(\"cars\")", "/html/body/form/select"], "optionMode": 0, "optionValue": "<PERSON><PERSON>"}}]}