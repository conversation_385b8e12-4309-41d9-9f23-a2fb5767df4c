{"id": 103, "name": "", "url": "https://www.maersk.com.cn/instantPrice/", "links": "https://www.maersk.com.cn/instantPrice/", "create_time": "6/27/2023, 11:54:03 PM", "version": "0.3.3", "containJudge": false, "desc": "https://www.maersk.com.cn/instantPrice/", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.maersk.com.cn/instantPrice/", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.maersk.com.cn/instantPrice/"}, {"id": 1, "name": "inputText_1", "nodeName": "输入文字", "nodeId": 2, "desc": "要输入的文本，如京东搜索框输入：电脑", "type": "string", "exampleValue": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2, 4], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.maersk.com.cn/instantPrice/", "links": "https://www.maersk.com.cn/instantPrice/", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"searchFormOrigin\")]/div[1]/div[1]/div[1]/div[1]/div[1]/input[1]", "wait": 7, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "<PERSON><PERSON>", "allXPaths": ["/html/body/div[2]/main[1]/div[1]/article[1]/section[1]/section[1]/section[1]/div[1]/form[1]/fieldset[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/input[1]", "//input[contains(., '')]", "//INPUT[@class='form-input__field']", "/html/body/div[last()-4]/main/div/article/section/section/section/div/form/fieldset[last()-3]/div/div/div[last()-1]/div[last()-2]/div/div/div/div/div/input"]}}, {"id": -1, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 8, "tabIndex": -1, "useLoop": false, "xpath": "//li[contains(., '<PERSON>g <PERSON> (')]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 100, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/main[1]/div[1]/article[1]/section[1]/section[1]/section[1]/div[1]/form[1]/fieldset[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/ul[1]/li[2]", "//li[contains(., '<PERSON>g <PERSON> (')]", "//LI[@class='typeahead__suggestions__link typeahead__suggestions__link--history']", "/html/body/div[last()-4]/main/div/article/section/section/section/div/form/fieldset[last()-3]/div/div/div[last()-1]/div[last()-2]/div/div/div/div/div/div/ul/li[last()-14]"]}}, {"id": 3, "index": 4, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"typeahead__suggestions__wrapper\")]/ul[1]/li[3]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/main[1]/div[1]/article[1]/section[1]/section[1]/section[1]/div[1]/form[1]/fieldset[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/ul[1]/li[3]", "//li[contains(., 'Shanghai (')]", "//LI[@class='typeahead__suggestions__link typeahead__suggestions__link--history']", "/html/body/div[last()-4]/main/div/article/section/section/section/div/form/fieldset[last()-3]/div/div/div[last()-1]/div[last()-2]/div/div/div/div/div/div/ul/li[last()-13]"]}}]}