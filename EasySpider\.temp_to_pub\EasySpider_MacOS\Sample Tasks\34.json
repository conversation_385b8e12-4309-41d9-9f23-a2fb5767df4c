{"id": 34, "name": "中国地震台网——历史查询", "url": "http://www.ceic.ac.cn/history", "links": "http://www.ceic.ac.cn/history", "create_time": "7/8/2023, 4:14:50 AM", "update_time": "7/14/2023, 8:53:16 AM", "version": "0.3.6", "saveThreshold": 7, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "DIZHEN", "inputExcel": "test.xlsx", "startFromExit": 1, "containJudge": false, "desc": "http://www.ceic.ac.cn/history", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://www.ceic.ac.cn/history", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "http://www.ceic.ac.cn/history"}, {"id": 1, "name": "loopTimes_循环_1", "nodeId": 4, "nodeName": "循环", "desc": "循环循环执行的次数（0代表无限循环）", "type": "int", "exampleValue": 5, "value": 5}, {"id": 2, "name": "loopText_2", "nodeId": 12, "nodeName": "循环", "desc": "要输入的文本/网址,多行以\\n分开", "type": "text", "exampleValue": "15~30\n45~90", "value": "15~30\n45~90"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "exampleValue": "4.0"}, {"id": 1, "name": "参数2_文本", "desc": "", "exampleValue": "2023-06-2900:43:34"}, {"id": 2, "name": "参数3_文本", "desc": "", "exampleValue": "34.35"}, {"id": 3, "name": "参数4_文本", "desc": "", "exampleValue": "89.02"}, {"id": 4, "name": "参数5_文本", "desc": "", "exampleValue": "8"}, {"id": 5, "name": "参数6_链接文本", "desc": "", "exampleValue": "西藏那曲市安多县"}, {"id": 6, "name": "参数7_链接地址", "desc": "", "exampleValue": "https://news.ceic.ac.cn/CC20230629004335.html"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 12], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "http://www.ceic.ac.cn/history", "links": "http://www.ceic.ac.cn/history", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"weidu1\"]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "1", "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[2]/input[1]", "//input[contains(., '')]", "id(\"weidu1\")", "//INPUT[@class='span1']", "//INPUT[@name='weidu1']"]}}, {"id": -1, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search\"]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[5]/a[1]", "//a[contains(., '查询')]", "id(\"search\")", "//A[@class='check']"]}}, {"id": 6, "index": 4, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [6, 5], "isInLoop": true, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"pagination\")]/ul[1]/li[last()-1]/a[1]", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 5, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '»')]"]}}, {"id": 8, "index": 5, "parentId": 6, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/ul[1]/li[10]/a[1]", "//a[contains(., '»')]"], "loopType": 0}}, {"id": 7, "index": 6, "parentId": 6, "type": 1, "option": 8, "title": "循环", "sequence": [7], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 2, "pathList": "//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[2]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[3]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[4]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[5]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[6]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[7]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[8]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[9]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[10]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[11]\n//*[contains(@class, \"tr-red\")]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[13]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[14]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[15]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[16]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[17]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[18]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[19]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[20]\n//*[contains(@class, \"speed-table1\")]/tbody[1]/tr[21]", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 9, "index": 7, "parentId": 7, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/td[1]", "allXPaths": ["/td[1]", "//td[contains(., '4.0')]"], "exampleValues": [{"num": 0, "value": "4.0"}, {"num": 1, "value": "5.9"}, {"num": 2, "value": "3.3"}, {"num": 3, "value": "3.0"}, {"num": 4, "value": "5.0"}, {"num": 5, "value": "5.6"}, {"num": 6, "value": "3.0"}, {"num": 7, "value": "3.3"}, {"num": 8, "value": "3.7"}, {"num": 9, "value": "5.5"}, {"num": 10, "value": "6.4"}, {"num": 11, "value": "3.0"}, {"num": 12, "value": "3.9"}, {"num": 13, "value": "3.3"}, {"num": 14, "value": "3.7"}, {"num": 15, "value": "3.8"}, {"num": 16, "value": "4.4"}, {"num": 17, "value": "5.6"}, {"num": 18, "value": "5.3"}, {"num": 19, "value": "3.1"}], "unique_index": "/td[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/td[2]", "allXPaths": ["/td[2]", "//td[contains(., '2023-06-29')]"], "exampleValues": [{"num": 0, "value": "2023-06-2900:43:34"}, {"num": 1, "value": "2023-06-2807:38:20"}, {"num": 2, "value": "2023-06-2509:16:14"}, {"num": 3, "value": "2023-06-2419:55:31"}, {"num": 4, "value": "2023-06-2403:07:36"}, {"num": 5, "value": "2023-06-2401:39:13"}, {"num": 6, "value": "2023-06-2120:25:40"}, {"num": 7, "value": "2023-06-2109:26:04"}, {"num": 8, "value": "2023-06-1913:24:49"}, {"num": 9, "value": "2023-06-1909:40:23"}, {"num": 10, "value": "2023-06-1904:30:23"}, {"num": 11, "value": "2023-06-1814:18:16"}, {"num": 12, "value": "2023-06-1810:58:23"}, {"num": 13, "value": "2023-06-1809:23:56"}, {"num": 14, "value": "2023-06-1804:46:46"}, {"num": 15, "value": "2023-06-1801:08:11"}, {"num": 16, "value": "2023-06-1800:14:24"}, {"num": 17, "value": "2023-06-1719:35:59"}, {"num": 18, "value": "2023-06-1708:26:14"}, {"num": 19, "value": "2023-06-1708:05:51"}], "unique_index": "/td[2]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数3_文本", "desc": "", "relativeXPath": "/td[3]", "allXPaths": ["/td[3]", "//td[contains(., '34.35')]"], "exampleValues": [{"num": 0, "value": "34.35"}, {"num": 1, "value": "42.10"}, {"num": 2, "value": "37.56"}, {"num": 3, "value": "40.11"}, {"num": 4, "value": "20.72"}, {"num": 5, "value": "45.75"}, {"num": 6, "value": "39.94"}, {"num": 7, "value": "24.46"}, {"num": 8, "value": "40.49"}, {"num": 9, "value": "15.20"}, {"num": 10, "value": "23.30"}, {"num": 11, "value": "37.76"}, {"num": 12, "value": "35.94"}, {"num": 13, "value": "36.32"}, {"num": 14, "value": "35.81"}, {"num": 15, "value": "35.80"}, {"num": 16, "value": "35.79"}, {"num": 17, "value": "47.75"}, {"num": 18, "value": "41.10"}, {"num": 19, "value": "39.58"}], "unique_index": "/td[3]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数4_文本", "desc": "", "relativeXPath": "/td[4]", "allXPaths": ["/td[4]", "//td[contains(., '89.02')]"], "exampleValues": [{"num": 0, "value": "89.02"}, {"num": 1, "value": "134.20"}, {"num": 2, "value": "77.91"}, {"num": 3, "value": "75.68"}, {"num": 4, "value": "109.07"}, {"num": 5, "value": "143.25"}, {"num": 6, "value": "82.98"}, {"num": 7, "value": "102.03"}, {"num": 8, "value": "77.59"}, {"num": 9, "value": "96.10"}, {"num": 10, "value": "-108.45"}, {"num": 11, "value": "101.29"}, {"num": 12, "value": "79.86"}, {"num": 13, "value": "111.74"}, {"num": 14, "value": "79.80"}, {"num": 15, "value": "79.78"}, {"num": 16, "value": "79.83"}, {"num": 17, "value": "147.60"}, {"num": 18, "value": "142.80"}, {"num": 19, "value": "82.57"}], "unique_index": "/td[4]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数5_文本", "desc": "", "relativeXPath": "/td[5]", "allXPaths": ["/td[5]", "//td[contains(., '8')]"], "exampleValues": [{"num": 0, "value": "8"}, {"num": 1, "value": "450"}, {"num": 2, "value": "20"}, {"num": 3, "value": "20"}, {"num": 4, "value": "20"}, {"num": 5, "value": "320"}, {"num": 6, "value": "10"}, {"num": 7, "value": "10"}, {"num": 8, "value": "25"}, {"num": 9, "value": "10"}, {"num": 10, "value": "10"}, {"num": 11, "value": "9"}, {"num": 12, "value": "10"}, {"num": 13, "value": "26"}, {"num": 14, "value": "10"}, {"num": 15, "value": "10"}, {"num": 16, "value": "10"}, {"num": 17, "value": "430"}, {"num": 18, "value": "10"}, {"num": 19, "value": "20"}], "unique_index": "/td[5]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数6_链接文本", "desc": "", "relativeXPath": "/td[6]/a[1]", "allXPaths": ["/td[6]/a[1]", "//a[contains(., '西藏那曲市安多县')]", "id(\"cid\")"], "exampleValues": [{"num": 0, "value": "西藏那曲市安多县"}, {"num": 1, "value": "俄罗斯东南沿岸近海"}, {"num": 2, "value": "新疆和田地区皮山县"}, {"num": 3, "value": "新疆克孜勒苏州乌恰县"}, {"num": 4, "value": "北部湾"}, {"num": 5, "value": "日本北海道地区"}, {"num": 6, "value": "新疆阿克苏地区沙雅县"}, {"num": 7, "value": "云南玉溪市峨山县"}, {"num": 8, "value": "新疆克孜勒苏州阿图什市"}, {"num": 9, "value": "缅甸南岸近海"}, {"num": 10, "value": "加利福尼亚湾"}, {"num": 11, "value": "青海海北州门源县"}, {"num": 12, "value": "新疆和田地区和田县"}, {"num": 13, "value": "山西临汾市洪洞县"}, {"num": 14, "value": "新疆和田地区和田县"}, {"num": 15, "value": "新疆和田地区和田县"}, {"num": 16, "value": "新疆和田地区和田县"}, {"num": 17, "value": "千岛群岛西北"}, {"num": 18, "value": "日本本州东岸近海"}, {"num": 19, "value": "新疆阿克苏地区沙雅县"}], "unique_index": "/td[6]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数7_链接地址", "desc": "", "relativeXPath": "/td[6]/a[1]", "allXPaths": ["/td[6]/a[1]", "//a[contains(., '西藏那曲市安多县')]", "id(\"cid\")"], "exampleValues": [{"num": 0, "value": "https://news.ceic.ac.cn/CC20230629004335.html"}, {"num": 1, "value": "https://news.ceic.ac.cn/CC20230628073821.html"}, {"num": 2, "value": "https://news.ceic.ac.cn/CD20230625091615.html"}, {"num": 3, "value": "https://news.ceic.ac.cn/CD20230624195532.html"}, {"num": 4, "value": "https://news.ceic.ac.cn/CC20230624030737.html"}, {"num": 5, "value": "https://news.ceic.ac.cn/CC20230624013914.html"}, {"num": 6, "value": "https://news.ceic.ac.cn/CD20230621202541.html"}, {"num": 7, "value": "https://news.ceic.ac.cn/CD20230621092604.html"}, {"num": 8, "value": "https://news.ceic.ac.cn/CD20230619132450.html"}, {"num": 9, "value": "https://news.ceic.ac.cn/CC20230619094024.html"}, {"num": 10, "value": "https://news.ceic.ac.cn/CC20230619043023.html"}, {"num": 11, "value": "https://news.ceic.ac.cn/CD20230618141817.html"}, {"num": 12, "value": "https://news.ceic.ac.cn/CC20230618105823.html"}, {"num": 13, "value": "https://news.ceic.ac.cn/CD20230618092356.html"}, {"num": 14, "value": "https://news.ceic.ac.cn/CD20230618044647.html"}, {"num": 15, "value": "https://news.ceic.ac.cn/CD20230618010812.html"}, {"num": 16, "value": "https://news.ceic.ac.cn/CD20230618001425.html"}, {"num": 17, "value": "https://news.ceic.ac.cn/CC20230617193560.html"}, {"num": 18, "value": "https://news.ceic.ac.cn/CC20230617082615.html"}, {"num": 19, "value": "https://news.ceic.ac.cn/CD20230617080552.html"}], "unique_index": "/td[6]/a[1]", "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 2}}, {"id": -1, "index": 8, "parentId": 5, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": "3", "code": "123", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": -1, "index": 9, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": "0", "code": "1123", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": -1, "index": 10, "parentId": 2, "type": 0, "option": 5, "title": "退出循环", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": "3", "code": "123", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": -1, "index": 11, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": "1", "code": "456", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 2, "index": 12, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [13, 14, 15, 4], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": "3", "pathList": "", "textList": "15~30\n45~90", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 3, "index": 13, "parentId": 2, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"weidu1\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "", "index": 1, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[2]/input[1]", "//input[contains(., '')]", "id(\"weidu1\")", "//INPUT[@class='span1']", "//INPUT[@name='weidu1']", "/html/body/div[last()-3]/div[last()-1]/div/div/div[last()-1]/form/div[last()-3]/input[last()-1]"]}}, {"id": 4, "index": 14, "parentId": 2, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"weidu2\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "value": "", "index": 2, "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[2]/input[2]", "//input[contains(., '')]", "id(\"weidu2\")", "//INPUT[@class='span1']", "//INPUT[@name='weidu2']", "/html/body/div[last()-3]/div[last()-1]/div/div/div[last()-1]/form/div[last()-3]/input"]}}, {"id": 5, "index": 15, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"search\"]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[3]/div[1]/div[1]/div[1]/form[1]/div[5]/a[1]", "//a[contains(., '查询')]", "id(\"search\")", "//A[@class='check']", "/html/body/div[last()-3]/div[last()-1]/div/div/div[last()-1]/form/div/a"]}}]}