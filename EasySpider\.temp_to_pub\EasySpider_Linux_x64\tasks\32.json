{"id": 32, "name": "知乎_登录后采集", "url": "https://www.zhihu.com", "links": "https://www.zhihu.com", "containJudge": false, "desc": "https://www.zhihu.com\n使用带用户配置的浏览器模式来先手工登录后保存信息，再接着执行。", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.zhihu.com", "desc": "要采集的网址列表,多行以\\n分开", "type": "string", "exampleValue": "https://www.zhihu.com"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "历史上有哪些通过“正当手段”干出不正当事的人物？"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "url": "https://www.zhihu.com", "links": "https://www.zhihu.com", "scrollType": 0, "scrollCount": 0}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "scrollType": 0, "scrollCount": 0, "loopType": 2, "pathList": "//*[contains(@class, \"css-0\")]/div[2]/div[1]/div[1]/div[1]/h2[1]/div[1]\n//*[contains(@class, \"css-0\")]/div[3]/div[1]/div[1]/div[1]/h2[1]/div[1]\n//*[contains(@class, \"css-0\")]/div[4]/div[1]/div[1]/div[1]/h2[1]/div[1]\n//*[contains(@class, \"css-0\")]/div[5]/div[1]/div[1]/div[1]/h2[1]/div[1]\n//*[contains(@class, \"css-0\")]/div[6]/div[1]/div[1]/div[1]/h2[1]/div[1]\n//*[contains(@class, \"css-0\")]/div[7]/div[1]/div[1]/div[1]/h2[1]/div[1]\n//*[contains(@class, \"css-0\")]/div[8]/div[1]/div[1]/div[1]/h2[1]/div[1]\n//*[contains(@class, \"css-0\")]/div[9]/div[1]/div[1]/div[1]/h2[1]/div[1]\n//*[contains(@class, \"css-0\")]/div[10]/div[1]/div[1]/div[1]/h2[1]/div[1]\n//*[contains(@class, \"css-0\")]/div[11]/div[1]/div[1]/div[1]/h2[1]/div[1]\n//*[contains(@class, \"css-0\")]/div[12]/div[1]/div[1]/div[1]/h2[1]/div[1]\n//*[contains(@class, \"css-0\")]/div[13]/div[1]/div[1]/div[1]/h2[1]/div[1]", "textList": "", "exitCount": 0, "historyWait": 2}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "wait": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "", "exampleValues": [{"num": 0, "value": "历史上有哪些通过“正当手段”干出不正当事的人物？"}, {"num": 1, "value": "新加坡有哪些不好的地方？"}, {"num": 2, "value": "孙悟空可以秒杀山村老尸那样的厉鬼吗？"}, {"num": 3, "value": "为什么渐渐厌倦玩《原神》了?"}, {"num": 4, "value": "历史上有哪些著名的考古乌龙事件?"}, {"num": 5, "value": "苹果公司为什么能把用户调教得这么好？"}, {"num": 6, "value": "哪个瞬间让你发现了世界的bug？"}, {"num": 7, "value": "假如中国的院士，想为亲属谋体制内的工作，难度大吗？为什么？"}, {"num": 8, "value": "你一直珍藏的视频是哪个？"}, {"num": 9, "value": "如何评价《原神》角色艾莉丝？"}, {"num": 10, "value": "索罗斯如何做空的英镑、泰铢？为什么做空香港失败了？"}, {"num": 11, "value": "如何在婚前认清并杜绝王力宏这种男人？"}], "default": ""}], "loopType": 2}}]}