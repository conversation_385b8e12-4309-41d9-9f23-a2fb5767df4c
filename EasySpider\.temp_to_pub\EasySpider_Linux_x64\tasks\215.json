{"id": 215, "name": "弹窗示例", "url": "https://easyspider.cn/test_pages/alert_test.html", "links": "https://easyspider.cn/test_pages/alert_test.html", "create_time": "12/5/2023, 1:53:45 AM", "update_time": "12/13/2023, 3:50:21 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://easyspider.cn/test_pages/alert_test.html", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://easyspider.cn/test_pages/alert_test.html", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://easyspider.cn/test_pages/alert_test.html"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n    弹窗示例\n    \n    \n        function showAlert() {\n            alert('你已经点击了按钮!');\n        }\n    \n\n\n\n#wrapperToolkitIframe{\n  position: absolute;\n  top:0;\n}\n.toolcannotdrag{\n  background-color: navy;\n  width: 100%;\n  text-align: center;\n  font-size: 13px;\n  height: 26px !important;\n  padding-top: 8px !important;\n  color: white;\n}\n\n\n点击我\n\n\n     ✖ ✍操作台（点此拖动，左上角调整大小）  \n              ● 已选中1个元素，您可以:\n                  确认采集    取消选择  Path: //html  \n"}, {"id": 1, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 2, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 4, 3, 5], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://easyspider.cn/test_pages/alert_test.html", "links": "https://easyspider.cn/test_pages/alert_test.html", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击点击我", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/button[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 2, "allXPaths": ["/html/body/button[1]", "//button[contains(., '点击我')]", "/html/body/button"]}}, {"id": 3, "index": 3, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body[1]/p[eval(\"self.a+1\")]", "allXPaths": ["//html[1]", "//html[contains(., '弹窗示例')]", "/html"], "exampleValues": [{"num": 0, "value": "\n    弹窗示例\n    \n    \n        function showAlert() {\n            alert('你已经点击了按钮!');\n        }\n    \n\n\n\n#wrapperToolkitIframe{\n  position: absolute;\n  top:0;\n}\n.toolcannotdrag{\n  background-color: navy;\n  width: 100%;\n  text-align: center;\n  font-size: 13px;\n  height: 26px !important;\n  padding-top: 8px !important;\n  color: white;\n}\n\n\n点击我\n\n\n     ✖ ✍操作台（点此拖动，左上角调整大小）  \n              ● 已选中1个元素，您可以:\n                  确认采集    取消选择  Path: //html  \n"}], "unique_index": "d8of3rzoypelpr7n0qm", "iframe": false, "default": "12345", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "alert(eval(\"self.a+2+int('1')\"))", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 2, "index": 4, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": "5", "code": "self.a = 1\nself.b = \"45\"", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 4, "index": 5, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 3, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "codeMode": 0, "code": "console.log(\"Field[\"参数1_文本\"]Field[\"参数1_文本\"]\")", "waitTime": 0, "recordASField": 0, "paraType": "text"}}]}