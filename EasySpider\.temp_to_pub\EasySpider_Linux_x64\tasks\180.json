{"id": 180, "name": "Switch Select - APK Downloader - Download APK & OBB (Latest Version)", "url": "https://apkcombo.com/downloader/", "links": "https://apkcombo.com/downloader/", "create_time": "", "update_time": "7/15/2023, 5:23:55 AM", "version": "0.3.6", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "https://apkcombo.com/downloader/", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://apkcombo.com/downloader/", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://apkcombo.com/downloader/"}, {"id": 1, "name": "loopText_1", "nodeId": 2, "nodeName": "循环", "desc": "要输入的文本/网址,多行以\\n分开", "type": "text", "exampleValue": "1~3~3", "value": "1~3~3"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 4, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://apkcombo.com/downloader/", "links": "https://apkcombo.com/downloader/", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 3, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": "3", "pathList": "", "textList": "1~3~3", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 4, "index": 3, "parentId": 3, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "//*[@id=\"device\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "optionMode": 0, "optionValue": "Phone", "index": 2, "allXPaths": ["/html/body/section[1]/div[1]/div[1]/main[1]/form[1]/fieldset[1]/div[1]/div[1]/div[1]/select[1]", "//select[contains(., 'Default\nP')]", "id(\"device\")", "//SELECT[@name='device']", "/html/body/section/div[last()-1]/div/main/form/fieldset/div[last()-1]/div[last()-2]/div/select"]}}, {"id": 2, "index": 4, "parentId": 0, "type": 0, "option": 6, "title": "切换下拉选项", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "//*[@id=\"device\"]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "optionMode": 0, "optionValue": "", "index": 0}}]}