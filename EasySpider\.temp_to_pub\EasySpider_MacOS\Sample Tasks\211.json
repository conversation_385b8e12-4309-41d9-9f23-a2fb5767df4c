{"id": 211, "name": "[2310.04498] UV-continuum $β$ slopes of individual $z \\sim 2-6$ clumps and their evolution", "url": "https://arxiv.org/abs/2310.04498", "links": "https://arxiv.org/abs/2310.04498", "create_time": "10/10/2023, 10:45:15 PM", "update_time": "10/10/2023, 10:45:15 PM", "version": "0.5.0", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "https://arxiv.org/abs/2310.04498", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://arxiv.org/abs/2310.04498", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://arxiv.org/abs/2310.04498"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://arxiv.org/abs/2310.04498", "links": "https://arxiv.org/abs/2310.04498", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击Download PDF", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"download-pdf\")]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[3]/main[1]/div[1]/div[1]/div[2]/div[1]/ul[1]/li[1]/a[1]", "//a[contains(., 'Download P')]", "//A[@class='abs-button download-pdf']", "/html/body/div[last()-4]/main/div/div/div[last()-2]/div[last()-5]/ul/li[last()-2]/a"]}}]}