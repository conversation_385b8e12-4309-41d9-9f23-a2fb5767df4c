{"id": 235, "name": "【软科排名】-中国最好学科排名|哲学|哲学", "url": "https://www.shanghairanking.cn/rankings/bcsr/2023/0101", "links": "https://www.shanghairanking.cn/rankings/bcsr/2023/0101", "create_time": "12/8/2023, 10:05:28 PM", "update_time": "12/9/2023, 3:13:12 AM", "version": "0.6.0", "saveThreshold": 10, "quitWaitTime": 60, "environment": 0, "maximizeWindow": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "pauseKey": "p", "containJudge": false, "desc": "https://www.shanghairanking.cn/rankings/bcsr/2023/0101", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.shanghairanking.cn/rankings/bcsr/2023/0101", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://www.shanghairanking.cn/rankings/bcsr/2023/0101"}, {"id": 1, "name": "loopTimes_循环点击单个元素_1", "nodeId": 2, "nodeName": "循环点击单个元素", "desc": "循环循环点击单个元素执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数2_文本1", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "2023"}, {"id": 1, "name": "参数1_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1"}, {"id": 2, "name": "参数2_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1"}, {"id": 3, "name": "参数3_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "前3%"}, {"id": 4, "name": "参数4_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://www.shanghairanking.cn/_uni/logo/28312850.png"}, {"id": 5, "name": "参数5_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "复旦大学\n            "}, {"id": 6, "name": "参数6_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/institution/fudan-university"}, {"id": 7, "name": "参数7_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/_nuxt/img/uncollection.388fe56.svg"}, {"id": 8, "name": "参数8_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n            "}, {"id": 9, "name": "参数9_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/institution/fudan-university"}, {"id": 10, "name": "参数10_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1044"}, {"id": 11, "name": "参数12_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ".\n\n        软科中国最好学科排名源自服务于高校学科建设管理部门的学科发展水平动态监测数据系统，2017年开始计算学科综合排名并对外公开发布。软科中国最好学科排名的指标体系包括人才培养、平台项目、成果获奖、学术论文、高端人才五个指标类别，使用70余项学科建设管理中密切关注的量化指标，强调通过客观数据反映学科点对本学科稀缺资源和标志性成果的占有和贡献。软科中国最好学科排名采用的学科口径是教育部最新《学位授予和人才培养学科目录》中的一级学科。在每个一级学科，排名的对象是在该一级学科设有学术型研究生学位授权点的所有高校，发布的是在该学科排名前50%的高校。软科中国最好学科排名最新发布的榜单包括98个一级学科，涉及近500所高校的上万个学科点（查看排名方法）。\n    "}, {"id": 12, "name": "自定义参数_1", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 6, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://www.shanghairanking.cn/rankings/bcsr/2023/0101", "links": "https://www.shanghairanking.cn/rankings/bcsr/2023/0101", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 3, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环点击单个元素", "sequence": [7, 3], "isInLoop": false, "position": 2, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"ant-pagination-next\")]/a[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[2]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[2]/div[1]/div[1]/ul[1]/li[5]/a[1]", "//a[contains(., '')]", "//A[@class='ant-pagination-item-link']", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/ul/li[last()-1]/a"]}}, {"id": 5, "index": 3, "parentId": 3, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "alertHandleType": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[2]/div[1]/div[1]/ul[1]/li[5]/a[1]", "//a[contains(., '')]", "//A[@class='ant-pagination-item-link']", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/ul/li[last()-1]/a"], "loopType": 0}}, {"id": -1, "index": 4, "parentId": 3, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[2]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[2]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[1]", "//tr[contains(., '')]", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]"]}}, {"id": -1, "index": 5, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": false, "newLine": true, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/td[1]/div[1]", "allXPaths": ["/td[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='ranking']", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr/td[last()-4]/div"], "exampleValues": [{"num": 0, "value": "60"}], "unique_index": "/td[1]/div[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/td[3]", "allXPaths": ["/td[3]", "//td[contains(., '')]", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr/td[last()-2]"], "exampleValues": [{"num": 0, "value": "前50%"}], "unique_index": "/td[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数4_链接文本", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '青海民族大学')]", "//A[@class='name-cn']", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr/td[last()-1]/div/div/div[last()-1]/div/div/a"], "exampleValues": [{"num": 0, "value": "青海民族大学\n            "}], "unique_index": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数5_链接地址", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '青海民族大学')]", "//A[@class='name-cn']", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr/td[last()-1]/div/div/div[last()-1]/div/div/a"], "exampleValues": [{"num": 0, "value": "/institution/qinghai-nationalities-university"}], "unique_index": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数7_链接文本", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "//a[contains(., '')]", "//A[@class='name-en']", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr/td[last()-1]/div/div/div/div/div/a"], "exampleValues": [{"num": 0, "value": "\n            "}], "unique_index": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数8_链接地址", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "//a[contains(., '')]", "//A[@class='name-en']", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr/td[last()-1]/div/div/div/div/div/a"], "exampleValues": [{"num": 0, "value": "/institution/qinghai-nationalities-university"}], "unique_index": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/td[5]", "allXPaths": ["/td[5]", "//td[contains(., '')]", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr/td"], "exampleValues": [{"num": 0, "value": "64"}], "unique_index": "/td[5]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/td[2]/span[1]", "allXPaths": ["/td[2]/span[1]", "//span[contains(., '29')]", "/html/body/div[last()-3]/div/div/div[last()-2]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-3]/span"], "exampleValues": [{"num": 1, "value": "29"}], "unique_index": "/td[2]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "自定义参数_1", "desc": "", "iframe": false, "extractType": 0, "relativeXPath": "/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/h1[1]", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}], "loopType": 1}}, {"id": 2, "index": 6, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 3, "contentType": 0, "relative": false, "name": "参数2_文本1", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/input[1]", "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/input[1]", "//input[contains(., '')]", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div[last()-2]/div/div/div/div[last()-1]/div/div[last()-1]/input"], "exampleValues": [{"num": 0, "value": "2023"}], "unique_index": "w26kj5fq52clpwxyig8", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": 4, "index": 7, "parentId": 3, "type": 1, "option": 8, "title": "循环采集数据", "sequence": [8], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[2]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "exitElement": "//body", "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[3]/div[2]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[1]", "//tr[contains(., '')]", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]"]}}, {"id": 6, "index": 8, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "newLine": 1, "params": [{"nodeType": 0, "contentType": 1, "relative": true, "name": "参数1_文本", "desc": "", "relativeXPath": "/td[1]/div[1]", "allXPaths": ["/td[1]/div[1]", "//div[contains(., '')]", "//DIV[@class='ranking']", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-4]/div"], "exampleValues": [{"num": 0, "value": "1"}], "unique_index": "/td[1]/div[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数2_文本", "desc": "", "relativeXPath": "/td[2]/span[1]", "allXPaths": ["/td[2]/span[1]", "//span[contains(., '1')]", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-3]/span"], "exampleValues": [{"num": 0, "value": "1"}], "unique_index": "/td[2]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数3_文本", "desc": "", "relativeXPath": "/td[3]", "allXPaths": ["/td[3]", "//td[contains(., '')]", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-2]"], "exampleValues": [{"num": 0, "value": "前3%"}], "unique_index": "/td[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数4_图片地址", "desc": "", "relativeXPath": "/td[4]/div[1]/div[1]/img[1]", "allXPaths": ["/td[4]/div[1]/div[1]/img[1]", "//img[contains(., '')]", "//IMG[@class='univ-logo']", "//IMG[@alt='复旦大学']", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div[last()-1]/img"], "exampleValues": [{"num": 0, "value": "https://www.shanghairanking.cn/_uni/logo/28312850.png"}], "unique_index": "/td[4]/div[1]/div[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数5_链接文本", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '复旦大学')]", "//A[@class='name-cn']", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div/div[last()-1]/div/div/a"], "exampleValues": [{"num": 0, "value": "复旦大学\n            "}], "unique_index": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数6_链接地址", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '复旦大学')]", "//A[@class='name-cn']", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div/div[last()-1]/div/div/a"], "exampleValues": [{"num": 0, "value": "/institution/fudan-university"}], "unique_index": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数7_图片地址", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/img[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div/div[last()-1]/div/div/div/img"], "exampleValues": [{"num": 0, "value": "/_nuxt/img/uncollection.388fe56.svg"}], "unique_index": "/td[4]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数8_链接文本", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "//a[contains(., '')]", "//A[@class='name-en']", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div/div/div/div/a"], "exampleValues": [{"num": 0, "value": "\n            "}], "unique_index": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数9_链接地址", "desc": "", "relativeXPath": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "allXPaths": ["/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "//a[contains(., '')]", "//A[@class='name-en']", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td[last()-1]/div/div/div/div/div/a"], "exampleValues": [{"num": 0, "value": "/institution/fudan-university"}], "unique_index": "/td[4]/div[1]/div[2]/div[2]/div[1]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数10_文本", "desc": "", "relativeXPath": "/td[5]", "allXPaths": ["/td[5]", "//td[contains(., '')]", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div/div/div[last()-1]/div/div[last()-1]/table/tbody/tr[last()-29]/td"], "exampleValues": [{"num": 0, "value": "1044"}], "unique_index": "/td[5]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "参数12_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[2]/div[1]/div[2]/div[1]/div[1]", "allXPaths": ["/html/body/div[1]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[2]/div[1]/div[2]/div[1]/div[1]", "//div[contains(., '.')]", "//DIV[@class='expansion-content']", "/html/body/div[last()-3]/div/div/div[last()-1]/div/div[last()-2]/div/div/div/div/div/div[last()-2]"], "exampleValues": [{"num": 0, "value": ".\n\n        软科中国最好学科排名源自服务于高校学科建设管理部门的学科发展水平动态监测数据系统，2017年开始计算学科综合排名并对外公开发布。软科中国最好学科排名的指标体系包括人才培养、平台项目、成果获奖、学术论文、高端人才五个指标类别，使用70余项学科建设管理中密切关注的量化指标，强调通过客观数据反映学科点对本学科稀缺资源和标志性成果的占有和贡献。软科中国最好学科排名采用的学科口径是教育部最新《学位授予和人才培养学科目录》中的一级学科。在每个一级学科，排名的对象是在该一级学科设有学术型研究生学位授权点的所有高校，发布的是在该学科排名前50%的高校。软科中国最好学科排名最新发布的榜单包括98个一级学科，涉及近500所高校的上万个学科点（查看排名方法）。\n    "}], "unique_index": "vl5lhhlhiwlpwznr15", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "自定义参数_1", "desc": "", "iframe": false, "extractType": 0, "relativeXPath": "//body", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}]}}]}