{"id": 19, "name": "文章详情获取与下载", "url": "https://kns.cnki.net/kcms2/article/abstract?v=6rsav1Ief6C0F2w4VPinV5EzWC1S0JNZzH7ZLJKh2fN5JYdnKZQ1UIOO_JWhOonGoVyrlaR_YbyZhEoGHVd7vql-S6xyHmwvOSpFk0Ih6MW3k7LA7Cg6Lw==&uniplatform=NZKPT", "links": "https://navi.cnki.net/knavi/journals/GGYY/detail?uniplatform=NZKPT", "create_time": "2023/7/13 10:58:10", "update_time": "7/14/2023, 3:36:11 AM", "version": "0.3.6", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "csv", "saveName": "论文详情", "inputExcel": "", "containJudge": true, "desc": "https://kns.cnki.net/kcms2/article/abstract?v=6rsav1Ief6C0F2w4VPinV5EzWC1S0JNZzH7ZLJKh2fN5JYdnKZQ1UIOO_JWhOonGoVyrlaR_YbyZhEoGHVd7vql-S6xyHmwvOSpFk0Ih6MW3k7LA7Cg6Lw==&uniplatform=NZKPT", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://navi.cnki.net/knavi/journals/GGYY/detail?uniplatform=NZKPT", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://navi.cnki.net/knavi/journals/GGYY/detail?uniplatform=NZKPT"}], "outputParameters": [{"id": 0, "name": "期刊", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "中国工业经济"}, {"id": 1, "name": "发刊期数", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "2023(05)"}, {"id": 2, "name": "核心", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}, {"id": 3, "name": "标题", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "共同富裕与重塑中国经济循环——政治经济学的理论逻辑与经验证据"}, {"id": 4, "name": "作者", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n乔晓楠1,2<PERSON><PERSON><PERSON>_qia<PERSON>@163.com李欣1蒲佩芝1\n"}, {"id": 5, "name": "机构", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n1. 南开大学经济学院2. 南开大学中国特色社会主义经济建设协同创新中心\n"}, {"id": 6, "name": "摘要", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "构建新发展格局的本质是以实现高水平的自立自强为目标，重塑国内国际双循环的关系。经济循环包括生产、分配、交换、消费等环节，本文尝试基于政治经济学原理研究收入分配对经济循环的影响，进而将共同富裕与新发展格局联系起来统一考察。研究发现：劳动报酬占比是收入分配领域的核心问题。在供给侧，收入分配通过工资变化影响技术选择与生产效率。在需求侧，收入分配通过直接与间接两个渠道影响需求规模与产能利用水平，其中，工资变化导致劳动者自身再生产消费规模的变化表现为直接渠道，而工资变化引致利润率变化进而对积累的影响表现为间接渠道。两个渠道同时发生作用但方向相反，因此，可以根据工资变化对产能利用水平的不同影响将经济区分为“利润主导型”与“工资主导型”。按照上述理论逻辑，本文对2000年以来中国的收入分配、工资、全劳动生产率、资本有机构成、利润率、积累率、产能利用水平以及国内国际双循环的关系进行全面分析，发现中国经济以2010年作为转折点，由“利润主导型”转向“工资主导型”。这意味着推进共同富裕促进新发展格局的构建兼具必要性与可行性。此外，本文还从所有制、分配、政府与市场关系以及空间结构等方面提出促进共同富裕的若干..."}, {"id": 7, "name": "关键词", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n共同富裕;\n                      分配;\n                      经济循环;\n                      积累方式;\n                      \n"}, {"id": 8, "name": "资助基金", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}, {"id": 9, "name": "DOI", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "10.19581/j.cnki.ciejournal.2023.05.001"}, {"id": 10, "name": "专辑", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "经济与管理科学"}, {"id": 11, "name": "专题", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "经济体制改革"}, {"id": 12, "name": "分类号", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "F124"}, {"id": 13, "name": "下载", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n                  下载：1093"}, {"id": 14, "name": "页码", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n                  页码：5-23"}, {"id": 15, "name": "页数", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n                  页数：19"}, {"id": 16, "name": "大小", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "\n                  大小：1507K"}, {"id": 17, "name": "目录", "desc": "", "type": "text", "recordASField": 1, "exampleValue": " 一、问题提出 二、文献述评 三、理论逻辑     1. 社会再生产过程中的经济循环     2. 经济循环中的分配环节     3. 分配从供给侧影响经济循环的机制     4. 分配从需求侧影响经济循环的机制     5. 在分配环节调整工资对劳动报酬占比以及收入差距的影响     6. 中国特色社会主义再生产与重塑经济循环的逻辑 四、经验分析(1)     1. 分配环节：工资时薪、劳动报酬占比与基尼系数     2. 供给侧：工业积累过程中的资本有机构成变化     3. 需求侧：国内国际双循环下的产能利用率变化 五、结论与政策启示"}, {"id": 18, "name": "被引", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}, {"id": 19, "name": "self.ta = 0", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 20, "name": "print(\"CONTINUE\")", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 21, "name": "BREAK", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 22, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}, {"id": 23, "name": "自定义操作", "desc": "自定义操作返回的数据", "type": "text", "recordASField": 0, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 18], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://kns.cnki.net/kcms2/article/abstract?v=6rsav1Ief6C0F2w4VPinV5EzWC1S0JNZzH7ZLJKh2fN5JYdnKZQ1UIOO_JWhOonGoVyrlaR_YbyZhEoGHVd7vql-S6xyHmwvOSpFk0Ih6MW3k7LA7Cg6Lw==&uniplatform=NZKPT", "links": "https://navi.cnki.net/knavi/journals/GGYY/detail?uniplatform=NZKPT", "maxWaitTime": 10, "scrollType": "1", "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 15, "index": 2, "parentId": 10, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": false, "name": "期刊", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/span[1]/a[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/span[1]/a[1]", "//a[contains(., '中国工业经济')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-11]/div[last()-1]/span/a[last()-1]"], "exampleValues": [{"num": 0, "value": "中国工业经济"}], "unique_index": "n9wuh4em8filk0ig1ni", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": false, "name": "发刊期数", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/span[1]/a[2]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[1]/div[1]/span[1]/a[2]", "//a[contains(., '2023(05)')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-11]/div[last()-1]/span/a"], "exampleValues": [{"num": 0, "value": "2023(05)"}], "unique_index": "nibp3t8tvhblk0ig929", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "核心", "desc": "", "extractType": 0, "relativeXPath": "//*[@class='type']", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}, {"nodeType": 0, "contentType": 1, "relative": false, "name": "标题", "desc": "", "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h1[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h1[1]", "//h1[contains(., '共同富裕与重塑中国经')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-9]/div/h1"], "exampleValues": [{"num": 0, "value": "共同富裕与重塑中国经济循环——政治经济学的理论逻辑与经验证据"}], "unique_index": "", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "作者", "desc": "", "extractType": 0, "relativeXPath": "//*[@id='authorpart']", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[1]", "//h3[contains(., '乔晓楠1,2xia')]", "id(\"authorpart\")", "//H3[@class='author']", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-9]/div/h3[last()-1]"], "exampleValues": [{"num": 0, "value": "\n乔晓楠1,2<PERSON><PERSON><PERSON>_qia<PERSON>@163.com李欣1蒲佩芝1\n"}], "unique_index": "d2ah0rw7uallk0j0e9v", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "机构", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[2]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[2]", "//h3[contains(., '1. 南开大学经济')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-9]/div/h3"], "exampleValues": [{"num": 0, "value": "\n1. 南开大学经济学院2. 南开大学中国特色社会主义经济建设协同创新中心\n"}], "unique_index": "29a79cqiks3lk0j1962", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "摘要", "desc": "", "extractType": 0, "relativeXPath": "//*[@class='abstract-text']", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[4]/span[2]", "//span[contains(., '构建新发展格局的本质')]", "id(\"ChDivSummary\")", "//SPAN[@class='abstract-text']", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-8]/span[last()-1]"], "exampleValues": [{"num": 0, "value": "构建新发展格局的本质是以实现高水平的自立自强为目标，重塑国内国际双循环的关系。经济循环包括生产、分配、交换、消费等环节，本文尝试基于政治经济学原理研究收入分配对经济循环的影响，进而将共同富裕与新发展格局联系起来统一考察。研究发现：劳动报酬占比是收入分配领域的核心问题。在供给侧，收入分配通过工资变化影响技术选择与生产效率。在需求侧，收入分配通过直接与间接两个渠道影响需求规模与产能利用水平，其中，工资变化导致劳动者自身再生产消费规模的变化表现为直接渠道，而工资变化引致利润率变化进而对积累的影响表现为间接渠道。两个渠道同时发生作用但方向相反，因此，可以根据工资变化对产能利用水平的不同影响将经济区分为“利润主导型”与“工资主导型”。按照上述理论逻辑，本文对2000年以来中国的收入分配、工资、全劳动生产率、资本有机构成、利润率、积累率、产能利用水平以及国内国际双循环的关系进行全面分析，发现中国经济以2010年作为转折点，由“利润主导型”转向“工资主导型”。这意味着推进共同富裕促进新发展格局的构建兼具必要性与可行性。此外，本文还从所有制、分配、政府与市场关系以及空间结构等方面提出促进共同富裕的若干..."}], "unique_index": "p5dnf0j8icplk0jgcwd", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "关键词", "desc": "", "extractType": 0, "relativeXPath": "//*[@class='keywords']", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[5]/p[1]", "//p[contains(., '共同富裕;')]", "//P[@class='keywords']", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-7]/p"], "exampleValues": [{"num": 0, "value": "\n共同富裕;\n                      分配;\n                      经济循环;\n                      积累方式;\n                      \n"}], "unique_index": "12eyhcw3sigolk0ji1fu", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "资助基金", "desc": "", "extractType": 0, "relativeXPath": "//*[@class='funds']", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "DOI", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[7]/ul[1]/li[1]/p[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[7]/ul[1]/li[1]/p[1]", "//p[contains(., '10.19581/j')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-5]/ul/li[last()-3]/p"], "exampleValues": [{"num": 0, "value": "10.19581/j.cnki.ciejournal.2023.05.001"}], "unique_index": "vpufqfj0k3lk0jldx6", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "专辑", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[7]/ul[1]/li[2]/p[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[7]/ul[1]/li[2]/p[1]", "//p[contains(., '经济与管理科学')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-5]/ul/li[last()-2]/p"], "exampleValues": [{"num": 0, "value": "经济与管理科学"}], "unique_index": "l81pn81t1nlk0jmrsr", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "专题", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[7]/ul[1]/li[3]/p[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[7]/ul[1]/li[3]/p[1]", "//p[contains(., '经济体制改革')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-5]/ul/li[last()-1]/p"], "exampleValues": [{"num": 0, "value": "经济体制改革"}], "unique_index": "yf99uq4acplk0jmwb2", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "分类号", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[7]/ul[1]/li[4]/p[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[7]/ul[1]/li[4]/p[1]", "//p[contains(., 'F124')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-5]/ul/li/p"], "exampleValues": [{"num": 0, "value": "F124"}], "unique_index": "c5rhxku1zeflk0jn0bt", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "下载", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[9]/div[2]/div[1]/div[1]/p[1]/span[1]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[9]/div[2]/div[1]/div[1]/p[1]/span[1]", "//span[contains(., '')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-3]/div[last()-2]/div/div/p/span[last()-3]"], "exampleValues": [{"num": 0, "value": "\n                  下载：1093"}], "unique_index": "zooabinkaoflk0jqpqr", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "页码", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[9]/div[2]/div[1]/div[1]/p[1]/span[2]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[9]/div[2]/div[1]/div[1]/p[1]/span[2]", "//span[contains(., '')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-3]/div[last()-2]/div/div/p/span[last()-2]"], "exampleValues": [{"num": 0, "value": "\n                  页码：5-23"}], "unique_index": "4hsemrrrv3qlk0jr8ht", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "页数", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[9]/div[2]/div[1]/div[1]/p[1]/span[3]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[9]/div[2]/div[1]/div[1]/p[1]/span[3]", "//span[contains(., '')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-3]/div[last()-2]/div/div/p/span[last()-1]"], "exampleValues": [{"num": 0, "value": "\n                  页数：19"}], "unique_index": "m9ec4ggyfxilk0jrd4v", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "大小", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[9]/div[2]/div[1]/div[1]/p[1]/span[4]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[9]/div[2]/div[1]/div[1]/p[1]/span[4]", "//span[contains(., '')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-3]/div[last()-2]/div/div/p/span"], "exampleValues": [{"num": 0, "value": "\n                  大小：1507K"}], "unique_index": "2no9d48bi42lk0jrh1f", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 0, "relative": false, "name": "目录", "desc": "", "extractType": 0, "relativeXPath": "//*[@class='catalog-list']", "allXPaths": ["/html/body/div[2]/div[1]/div[1]/div[1]/ul[1]", "//ul[contains(., '一、问题提出 二、')]", "//UL[@class='catalog-list']", "/html/body/div[last()-6]/div[last()-1]/div[last()-3]/div/ul"], "exampleValues": [{"num": 0, "value": " 一、问题提出 二、文献述评 三、理论逻辑     1. 社会再生产过程中的经济循环     2. 经济循环中的分配环节     3. 分配从供给侧影响经济循环的机制     4. 分配从需求侧影响经济循环的机制     5. 在分配环节调整工资对劳动报酬占比以及收入差距的影响     6. 中国特色社会主义再生产与重塑经济循环的逻辑 四、经验分析(1)     1. 分配环节：工资时薪、劳动报酬占比与基尼系数     2. 供给侧：工业积累过程中的资本有机构成变化     3. 需求侧：国内国际双循环下的产能利用率变化 五、结论与政策启示"}], "unique_index": "xbnn1pj2rmmlk0jtf84", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 9, "relative": false, "name": "被引", "desc": "", "extractType": 0, "relativeXPath": "//*[id='paramcitingtimes']", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "return arguments[0].value", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}]}}, {"id": -1, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4], "isInLoop": false, "position": 2, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[1]/span/a[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[1]/span[1]/a[1]", "//a[contains(., '乔晓楠1,2')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-9]/div/h3[last()-1]/span[last()-2]/a"]}}, {"id": -1, "index": 4, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数5_链接文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "乔晓楠1,2"}], "unique_index": "llb9mkev30jlk0issdn", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": -1, "index": 5, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [6], "isInLoop": false, "position": 3, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[1]/span/a[1]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[1]/span[1]/a[1]", "//a[contains(., '乔晓楠1,2')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-9]/div/h3[last()-1]/span[last()-2]/a"]}}, {"id": -1, "index": 6, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "作者", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "乔晓楠1,2"}], "unique_index": "zwwvo9bgwtlk0iuz1o", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 7, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "参数8_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[2]", "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[3]/div[1]/h3[2]", "//h3[contains(., '1. 南开大学经济')]", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-9]/div/h3"], "exampleValues": [{"num": 0, "value": "\n1. 南开大学经济学院2. 南开大学中国特色社会主义经济建设协同创新中心\n"}], "unique_index": "goy4x0b5loulk0izojl", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}, {"id": -1, "index": 8, "parentId": 0, "type": 2, "option": 9, "title": "判断条件", "sequence": [9, 10], "isInLoop": false, "position": 4, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 5, "index": 9, "type": 3, "option": 10, "title": "条件分支", "sequence": [17], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "2", "value": "/*[@id='authorpart']", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 5, "index": 10, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"index": 11, "id": -1, "parentId": 2, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": false, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 0}, {"id": -1, "index": 12, "parentId": 4, "type": 0, "option": 12, "title": "条件分支", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "index": 13, "parentId": 5, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "作者", "desc": "", "extractType": 0, "relativeXPath": "/*[@id='authorpart']", "recordASField": 0, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}]}}, {"id": -1, "index": 14, "parentId": 4, "type": 1, "option": 8, "title": "循环", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": -1, "index": 15, "parentId": 5, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 14, "index": 16, "parentId": 10, "type": 0, "option": 2, "title": "点击更多-查看完整摘要", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "//*[@id=\"ChDivSummaryMore\"]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": []}}, {"id": -1, "index": 17, "parentId": 8, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"pdfDown\"]", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[9]/div[2]/ul[1]/li[4]/a[1]", "//a[contains(., 'PDF下载')]", "id(\"pdfDown\")", "//A[@name='pdfDown']", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-3]/div[last()-2]/ul/li[last()-1]/a"]}}, {"id": 2, "index": 18, "parentId": 0, "type": 1, "option": 8, "title": "循环-点击每期", "sequence": [19, 29, 20], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "/html/body/div[2]/div[2]/div[3]/div[3]/div[1]/div[1]/div[1]/div[1]/dl/dd[1]/a", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0}}, {"id": 3, "index": 19, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": true, "xpath": "", "iframe": false, "wait": 5, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 1, "maxWaitTime": 10, "params": []}}, {"id": 5, "index": 20, "parentId": 2, "type": 1, "option": 8, "title": "循环-每篇文章", "sequence": [22, 44, 40], "isInLoop": true, "position": 2, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"CataLogContent\"]/div[1]/div/dd/span/a", "iframe": false, "wait": 5, "waitType": "1", "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": "1", "pathList": "/*[@id=\"CataLogContent\"]/div[1]/div/dd", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 21, "parentId": 6, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": "", "loopType": 2}}, {"id": 6, "index": 22, "parentId": 5, "type": 2, "option": 9, "title": "判断条件", "sequence": [38, 23], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": 10, "parentId": 6, "index": 23, "type": 3, "option": 10, "title": "条件分支-存在作者", "sequence": [25, 16, 2, 26], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "0", "value": "", "code": "return arguments[0].querySelector('.author').textContent !== ''", "waitTime": 0}, "position": 1}, {"id": -1, "parentId": 10, "index": 24, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 13, "index": 25, "parentId": 10, "type": 0, "option": 2, "title": "点击-论文链接", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": true, "xpath": "//*[@id=\"CataLogContent\"]/div[1]/div/dd//a", "iframe": false, "wait": 10, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": []}}, {"id": 16, "index": 26, "parentId": 10, "type": 0, "option": 2, "title": "点击-下载pdf", "sequence": [], "isInLoop": true, "position": 3, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"pdfDown\"]", "iframe": false, "wait": 10, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/div[1]/div[3]/div[1]/div[1]/div[9]/div[2]/ul[1]/li[4]/a[1]", "//a[contains(., 'PDF下载')]", "id(\"pdfDown\")", "//A[@name='pdfDown']", "/html/body/div[last()-6]/div[last()-1]/div[last()-1]/div/div/div[last()-3]/div[last()-2]/ul/li[last()-1]/a"]}}, {"id": -1, "index": 27, "parentId": 0, "type": 0, "option": 5, "title": "v_a=0", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "v_a = 0", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": -1, "index": 28, "parentId": 3, "type": 0, "option": 5, "title": "v_a += 1", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "v_a += 1", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 4, "index": 29, "parentId": 2, "type": 0, "option": 5, "title": "self.ta = 0", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "5", "code": "self.ta = 0", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": -1, "index": 30, "parentId": 8, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": 0, "code": "a += 1", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": -1, "index": 31, "parentId": 5, "type": 0, "option": 5, "title": "hhh", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": 0, "code": "return a", "waitTime": 0, "recordASField": 1, "paraType": "text"}}, {"id": -1, "index": 32, "parentId": 5, "type": 2, "option": 9, "title": "判断条件", "sequence": [33], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 6, "index": 33, "type": 3, "option": 10, "title": "条件分支", "sequence": [30, 36, 35], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "5", "value": "", "code": "return a<5", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 6, "index": 34, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": -1, "index": 35, "parentId": 8, "type": 0, "option": 5, "title": "跳过当前循环", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "4", "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": -1, "index": 36, "parentId": 8, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": 0, "code": "return \"a: \" + a.toString()", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 12, "index": 37, "parentId": 9, "type": 0, "option": 5, "title": "print(\"CONTINUE\")", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "5", "code": "print(\"CONTINUE\")", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"index": 38, "id": 9, "parentId": 6, "type": 3, "option": 10, "title": "self.ta < 3", "sequence": [37], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "8", "value": "", "code": "self.ta < 3", "waitTime": 0}, "position": 0}, {"id": -1, "index": 39, "parentId": 7, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "6", "code": "self.ta ", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 8, "index": 40, "parentId": 5, "type": 2, "option": 9, "title": "判断条件", "sequence": [41], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": 11, "parentId": 8, "index": 41, "type": 3, "option": 10, "title": "self.ta > 7", "sequence": [43, 45], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "8", "value": "", "code": "self.ta > 7", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 7, "index": 42, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 17, "index": 43, "parentId": 11, "type": 0, "option": 5, "title": "BREAK", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "3", "code": "", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 7, "index": 44, "parentId": 5, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "5", "code": "self.ta += 1\nprint(\"self.ta: \", self.ta)", "waitTime": 0, "recordASField": 0, "paraType": "text"}}, {"id": 18, "index": 45, "parentId": 11, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": 0, "codeMode": "5", "code": "print(\"BREAK\")", "waitTime": 0, "recordASField": 0, "paraType": "text"}}]}