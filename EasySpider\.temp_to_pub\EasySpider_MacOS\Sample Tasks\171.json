{"id": 171, "name": "知网节超时验证", "url": "https://kns.cnki.net/kcms2/article/abstract?v=6rsav1Ief6Bjq7ZDCNfX2Bdpm3yJoLnDLpOU7UmNrkWbeNAIZPlI_XRTFvJODp5EEgokN7PhT16k4BbhW0lglVR8nXyZixk8tT5DImVq8amWBeinYu-FTgIt48OpVnz7&uniplatform=NZKPT", "links": "https://kns.cnki.net/kcms2/article/abstract?v=6rsav1Ief6Bjq7ZDCNfX2Bdpm3yJoLnDLpOU7UmNrkWbeNAIZPlI_XRTFvJODp5EEgokN7PhT16k4BbhW0lglVR8nXyZixk8tT5DImVq8amWBeinYu-FTgIt48OpVnz7&uniplatform=NZKPT", "create_time": "7/13/2023, 6:57:52 PM", "update_time": "7/13/2023, 6:57:52 PM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "containJudge": false, "desc": "https://kns.cnki.net/kcms2/article/abstract?v=6rsav1Ief6Bjq7ZDCNfX2Bdpm3yJoLnDLpOU7UmNrkWbeNAIZPlI_XRTFvJODp5EEgokN7PhT16k4BbhW0lglVR8nXyZixk8tT5DImVq8amWBeinYu-FTgIt48OpVnz7&uniplatform=NZKPT", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://kns.cnki.net/kcms2/article/abstract?v=6rsav1Ief6Bjq7ZDCNfX2Bdpm3yJoLnDLpOU7UmNrkWbeNAIZPlI_XRTFvJODp5EEgokN7PhT16k4BbhW0lglVR8nXyZixk8tT5DImVq8amWBeinYu-FTgIt48OpVnz7&uniplatform=NZKPT", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://kns.cnki.net/kcms2/article/abstract?v=6rsav1Ief6Bjq7ZDCNfX2Bdpm3yJoLnDLpOU7UmNrkWbeNAIZPlI_XRTFvJODp5EEgokN7PhT16k4BbhW0lglVR8nXyZixk8tT5DImVq8amWBeinYu-FTgIt48OpVnz7&uniplatform=NZKPT"}], "outputParameters": [{"id": 0, "name": "自定义参数_1", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}, {"id": 1, "name": "自定义参数_2", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "自定义值"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0, "waitType": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 4, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://kns.cnki.net/kcms2/article/abstract?v=6rsav1Ief6Bjq7ZDCNfX2Bdpm3yJoLnDLpOU7UmNrkWbeNAIZPlI_XRTFvJODp5EEgokN7PhT16k4BbhW0lglVR8nXyZixk8tT5DImVq8amWBeinYu-FTgIt48OpVnz7&uniplatform=NZKPT", "links": "https://kns.cnki.net/kcms2/article/abstract?v=6rsav1Ief6Bjq7ZDCNfX2Bdpm3yJoLnDLpOU7UmNrkWbeNAIZPlI_XRTFvJODp5EEgokN7PhT16k4BbhW0lglVR8nXyZixk8tT5DImVq8amWBeinYu-FTgIt48OpVnz7&uniplatform=NZKPT", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "clear": false, "params": [{"nodeType": 3, "contentType": 0, "relative": false, "name": "自定义参数_1", "desc": "", "extractType": 0, "relativeXPath": "id('paramcitingtimes')", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}, {"nodeType": 3, "contentType": 0, "relative": false, "name": "自定义参数_2", "desc": "", "extractType": 0, "relativeXPath": "//*[id='paramcitingtimes']", "recordASField": 1, "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义值"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "paraType": "text"}]}}]}