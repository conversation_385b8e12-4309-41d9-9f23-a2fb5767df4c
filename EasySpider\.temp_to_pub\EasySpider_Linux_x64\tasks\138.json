{"id": 138, "name": "Dynamic Iframe1", "url": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://jd.com", "links": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=https://global.jd.com", "create_time": "7/5/2023, 8:40:16 AM", "update_time": "7/17/2023, 2:07:45 AM", "version": "0.3.6", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://jd.com", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=https://global.jd.com", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=https://global.jd.com"}], "outputParameters": [{"id": 0, "name": "参数1_链接文本", "desc": "", "exampleValue": "手机"}, {"id": 1, "name": "参数2_链接地址", "desc": "", "exampleValue": "https://shouji.jd.com/"}, {"id": 2, "name": "自定义操作", "desc": "自定义操作返回的数据", "recordASField": "1", "exampleValue": ""}, {"id": 3, "name": "自定义操作2", "desc": "自定义操作返回的数据", "recordASField": "1", "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 19, 17, 14], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 3, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=http://jd.com", "links": "http://localhost:8074/taskGrid/test_pages/iframe.html?address=https://global.jd.com", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[2]", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]", "//div[contains(., '/家用电器')]", "//DIV[@class='LeftSide_menu_item__SBMWC LeftSide_text_space__2UhbG ']", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-11]"]}}, {"id": -1, "index": 3, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [4], "isInLoop": false, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 4, "parentId": 2, "type": 0, "option": 7, "title": "移动到元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "allXPaths": "", "loopType": 1}}, {"id": -1, "index": 5, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [8, 6, 7], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div/a", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 6, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div/a", "iframe": true, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 1}}, {"id": -1, "index": 7, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": false, "name": "自定义参数_0", "desc": "", "extractType": 0, "relativeXPath": "/html/body", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "iframe": true}]}}, {"id": -1, "index": 8, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 0, "contentType": 0, "relative": true, "name": "自定义参数_1", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": [], "exampleValues": [{"num": 0, "value": "自定义字段"}], "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0, "iframe": true}]}}, {"id": -1, "index": 9, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [11], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": "1", "pathList": "//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[1]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[2]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[3]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[3]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[4]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[4]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[4]/a[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[5]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[5]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[5]/a[4]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[6]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[6]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[6]/a[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[6]/a[4]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[7]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[7]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[7]/a[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[7]/a[4]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[8]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[8]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[8]/a[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[9]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[9]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[10]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[10]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[11]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[11]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[12]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[12]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[12]/a[3]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[13]/a[1]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[13]/a[2]\n//*[contains(@class, \"LeftSide_menu_list__qXCeM\")]/div[13]/a[3]", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": -1, "index": 10, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "数码"}], "unique_index": "7ozwfteuj7nljoxx7ne", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "https://shuma.jd.com/"}], "unique_index": "7ozwfteuj7nljoxx7ne", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 2}}, {"id": -1, "index": 11, "parentId": 2, "type": 2, "option": 9, "title": "判断条件", "sequence": [12, 13], "isInLoop": true, "position": 0, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0}}, {"id": -1, "parentId": 3, "index": 12, "type": 3, "option": 10, "title": "条件分支", "sequence": [10], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": "1", "value": "京东", "code": "return arguments[0].innerText.length < 3", "waitTime": 0}, "position": 0}, {"id": -1, "parentId": 3, "index": 13, "type": 3, "option": 10, "title": "条件分支", "sequence": [], "isInLoop": true, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "class": 0, "value": "", "code": "", "waitTime": 0}, "position": 1}, {"id": 4, "index": 14, "parentId": 0, "type": 1, "option": 8, "title": "循环123", "sequence": [15, 16], "isInLoop": false, "position": 3, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div/a", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[5]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/a[1]", "//a[contains(., '手机')]", "/html/body/div[last()-5]/div/div[last()-4]/div/div[last()-2]/div/div/div/div[last()-1]/div[last()-12]/a[last()-1]"], "waitElement": "//*[@id=\"service-2017\"]/div[1]/ol/li[1]", "waitElementTime": 10, "waitElementInIframe": 1, "waitElementIframeIndex": 0}}, {"id": 5, "index": 15, "parentId": 4, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "extractType": 0, "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "手机"}], "unique_index": "8rrms8dqpmjljozhosi", "iframe": true, "default": "", "beforeJS": "arguments[0].innerText = arguments[0].innerText.replace(\"家\", \"Home\")", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "", "allXPaths": "", "exampleValues": [{"num": 0, "value": "https://shouji.jd.com/"}], "unique_index": "8rrms8dqpmjljozhosi", "iframe": true, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}, {"id": 6, "index": 16, "parentId": 4, "type": 0, "option": 5, "title": "自定义操作", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": "2", "code": "return arguments[0].innerText.length", "waitTime": 0, "recordASField": "1"}}, {"id": 3, "index": 17, "parentId": 0, "type": 0, "option": 5, "title": "自定义操作2", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "codeMode": 0, "code": "return \"Field[\"参数1_链接文本\"] + Field[\"参数2_链接地址\"] + Field[\"参数1_链接文本\"]\"", "waitTime": 0, "recordASField": "1"}}, {"id": -1, "index": 18, "parentId": 0, "type": 0, "option": 4, "title": "输入文字", "sequence": [], "isInLoop": false, "position": 2, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "value": "", "index": 0}}, {"id": 2, "index": 19, "parentId": 0, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": []}}]}