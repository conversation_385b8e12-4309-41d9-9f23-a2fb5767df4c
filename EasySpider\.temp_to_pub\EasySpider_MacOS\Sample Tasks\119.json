{"id": 119, "name": "", "url": "https://lihkg.com/", "links": "https://lihkg.com/thread/3433502/page/1", "create_time": "7/4/2023, 5:09:13 PM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "https://lihkg.com/", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://lihkg.com/thread/3433502/page/1", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://lihkg.com/thread/3433502/page/1"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 6], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://lihkg.com/", "links": "https://lihkg.com/thread/3433502/page/1", "maxWaitTime": 10, "scrollType": "2", "scrollCount": 3, "scrollWaitTime": 1}}, {"id": -1, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"P3e8vKaXmUeXC9dJgjnsu\")]/div[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[2]/div[2]/div[1]/div[1]", "//div[contains(., 'LIHKG 討論區使')]", "/html/body/div[last()-4]/div[last()-2]/div/div/div"]}}, {"id": -1, "index": 3, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"P3e8vKaXmUeXC9dJgjnsu\")]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[2]/div[2]/div[1]", "//div[contains(., 'LIHKG 討論區使')]", "//DIV[@class='P3e8vKaXmUeXC9dJgjnsu']", "/html/body/div[last()-4]/div[last()-2]/div/div"]}}, {"id": -1, "index": 4, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"_1PdImYJBCsN8lH0MB4tnqV\")]/a[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": "2", "scrollCount": 5, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[2]/div[1]/div[1]/ul[1]/li[1]/a[1]", "//a[contains(., '最新')]", "/html/body/div[last()-4]/div[last()-2]/div[last()-1]/div[last()-1]/ul/li[last()-1]/a"]}}, {"id": -1, "index": 5, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 3, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"P3e8vKaXmUeXC9dJgjnsu\")]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[2]/div[2]/div[1]", "//div[contains(., 'LIHKG 討論區使')]", "//DIV[@class='P3e8vKaXmUeXC9dJgjnsu']", "/html/body/div[last()-4]/div[last()-2]/div/div"]}}, {"id": 2, "index": 6, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "//*[@id=\"1\"]/div[1]/small[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": "2", "scrollCount": 4, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[2]/div[2]/div[1]/div[2]/div[2]/div[2]/div[1]/div[1]/small[1]", "//small[contains(., '#1李芯悅•1 小時')]", "//SMALL[@class='_1VcuFUmnOEK51TsshmrnJM']", "/html/body/div[last()-5]/div[last()-2]/div/div/div[last()-2]/div/div[last()-24]/div[last()-1]/div/small"]}}]}