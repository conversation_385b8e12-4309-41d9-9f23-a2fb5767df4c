{"id": 96, "name": "[2305.07067] SigRec: Automatic Recovery of Function Signatures in Smart Contracts", "url": "https://arxiv.org/abs/2305.07067", "links": "https://arxiv.org/pdf/2008.03554.pdf", "create_time": "6/2/2023, 1:00:27 AM", "version": "0.3.2", "containJudge": false, "desc": "https://arxiv.org/abs/2305.07067", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://arxiv.org/pdf/2008.03554.pdf", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://arxiv.org/pdf/2008.03554.pdf"}], "outputParameters": [], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://arxiv.org/abs/2305.07067", "links": "https://arxiv.org/pdf/2008.03554.pdf", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"download-pdf\")]", "wait": 2, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[2]/main[1]/div[1]/div[1]/div[2]/div[1]/ul[1]/li[1]/a[1]", "//a[contains(., 'PDF')]", "//A[@class='abs-button download-pdf']"]}}]}