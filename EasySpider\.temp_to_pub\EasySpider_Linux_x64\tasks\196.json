{"id": 196, "name": "Bilibili评论爬取", "url": "https://member.bilibili.com/platform/comment/article", "links": "https://member.bilibili.com/platform/comment/article", "create_time": "7/23/2023, 6:12:02 PM", "update_time": "7/23/2023, 6:15:15 PM", "version": "0.5.0", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "maxViewLength": 15, "recordLog": 1, "outputFormat": "xlsx", "saveName": "current_time", "inputExcel": "", "startFromExit": 0, "containJudge": false, "desc": "https://member.bilibili.com/platform/comment/article", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://member.bilibili.com/platform/comment/article", "desc": "要采集的网址列表，多行以\\n分开", "type": "text", "exampleValue": "https://member.bilibili.com/platform/comment/article"}, {"id": 1, "name": "loopTimes_循环点击下一页_1", "nodeId": 2, "nodeName": "循环点击下一页", "desc": "循环循环点击下一页执行的次数（0代表无限循环）", "type": "int", "exampleValue": 0, "value": 0}], "outputParameters": [{"id": 0, "name": "参数1_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 1, "name": "参数2_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//space.bilibili.com/291929894"}, {"id": 2, "name": "参数3_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://i2.hdslb.com/bfs/face/2e6fb6974ca4b14f3e4f47a9d5f100c653f95066.jpg"}, {"id": 3, "name": "参数4_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 4, "name": "参数5_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//www.bilibili.com/video/BV1Fk4y1L7xX"}, {"id": 5, "name": "参数6_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "http://i0.hdslb.com/bfs/archive/261754d722ebf2c8dd3af455b44285927fbbe757.jpg"}, {"id": 6, "name": "参数7_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "可视化爬虫易采集EasySpider：一个无需写代码，可视化的几分钟设计一个爬虫的开源免费软件该视频全部评论"}, {"id": 7, "name": "参数8_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "/platform/comment/article/BV1Fk4y1L7xX/%25E5%258F%25AF%25E8%25A7%2586%25E5%258C%2596%25E7%2588%25AC%25E8%2599%25AB%25E6%2598%2593%25E9%2587%2587%25E9%259B%2586EasySpider%25EF%25BC%259A%25E4%25B8%2580%25E4%25B8%25AA%25E6%2597%25A0%25E9%259C%2580%25E5%2586%2599%25E4%25BB%25A3%25E7%25A0%2581%25EF%25BC%258C%25E5%258F%25AF%25E8%25A7%2586%25E5%258C%2596%25E7%259A%2584%25E5%2587%25A0%25E5%2588%2586%25E9%2592%259F%25E8%25AE%25BE%25E8%25AE%25A1%25E4%25B8%2580%25E4%25B8%25AA%25E7%2588%25AC%25E8%2599%25AB%25E7%259A%2584%25E5%25BC%2580%25E6%25BA%2590%25E5%2585%258D%25E8%25B4%25B9%25E8%25BD%25AF%25E4%25BB%25B6?type=1"}, {"id": 8, "name": "参数9_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "已充电"}, {"id": 9, "name": "参数10_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "天际青年"}, {"id": 10, "name": "参数11_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//space.bilibili.com/291929894"}, {"id": 11, "name": "参数12_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "回复"}, {"id": 12, "name": "参数13_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "@豪He鑫"}, {"id": 13, "name": "参数14_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//space.bilibili.com/1209416740"}, {"id": 14, "name": "参数15_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "的评论"}, {"id": 15, "name": "参数16_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "查看评论"}, {"id": 16, "name": "参数17_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "收起评论"}, {"id": 17, "name": "参数18_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 18, "name": "参数19_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//space.bilibili.com/1209416740"}, {"id": 19, "name": "参数20_图片地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "https://i1.hdslb.com/bfs/face/149f643807d3777efe7fc7bb7f24db0683beda5e.jpg@48w_48h"}, {"id": 20, "name": "参数21_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "豪He鑫"}, {"id": 21, "name": "参数22_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//space.bilibili.com/1209416740"}, {"id": 22, "name": "参数23_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "我点使用纯净版浏览器设计还有其他的都闪一下，然后没打开浏览器啊"}, {"id": 23, "name": "参数24_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "2023-07-1909:57:18"}, {"id": 24, "name": "参数25_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "Win7下载32位"}, {"id": 25, "name": "参数26_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "//www.bilibili.com/video/BV1Fk4y1L7xX"}, {"id": 26, "name": "参数27_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "Win7下载32位"}, {"id": 27, "name": "参数28_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "2023-07-2013:01:39"}, {"id": 28, "name": "参数29_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 29, "name": "参数30_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 30, "name": "参数31_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "回复"}, {"id": 31, "name": "参数32_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 32, "name": "参数33_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "举报"}, {"id": 33, "name": "参数34_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 34, "name": "参数35_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "删除"}, {"id": 35, "name": "参数36_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 36, "name": "参数37_文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "2023-07-1909:57:18"}, {"id": 37, "name": "参数38_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "1"}, {"id": 38, "name": "参数39_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 39, "name": "参数40_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "回复"}, {"id": 40, "name": "参数41_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 41, "name": "参数42_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "举报"}, {"id": 42, "name": "参数43_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}, {"id": 43, "name": "参数44_链接文本", "desc": "", "type": "text", "recordASField": 1, "exampleValue": "删除"}, {"id": 44, "name": "参数45_链接地址", "desc": "", "type": "text", "recordASField": 1, "exampleValue": ""}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 8, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "url": "https://member.bilibili.com/platform/comment/article", "links": "https://member.bilibili.com/platform/comment/article", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "cookies": ""}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环点击下一页", "sequence": [4, 3], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "//*[contains(@class, \"bcc-pagination-next\")]", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 0, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[4]/div[2]/div[2]/div[4]/ul[1]/li[6]", "//li[contains(., '下一页')]", "//LI[@class='bcc-pagination-item bcc-pagination-next']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/div/ul/li[last()-1]"]}}, {"id": 4, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "", "iframe": false, "wait": 8, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": ["/html/body/div[1]/div[3]/div[4]/div[2]/div[2]/div[4]/ul[1]/li[6]", "//li[contains(., '下一页')]", "//LI[@class='bcc-pagination-item bcc-pagination-next']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/div/ul/li[last()-1]"], "loopType": 0}}, {"id": 3, "index": 4, "parentId": 2, "type": 1, "option": 8, "title": "循环", "sequence": [5], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[3]/div[4]/div[2]/div[2]/section[1]/div[1]/div[1]/div", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ["/html/body/div[1]/div[3]/div[4]/div[2]/div[2]/section[1]/div[1]/div[1]/div[1]", "//div[contains(., '可视化爬虫易采集Ea')]", "//DIV[@class='comment-list-item']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]"]}}, {"id": 5, "index": 5, "parentId": 3, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 5, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "waitElement": "", "waitElementTime": 10, "waitElementIframeIndex": 0, "clear": 0, "params": [{"nodeType": 1, "contentType": 0, "relative": true, "name": "参数1_链接文本", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '')]", "//A[@class='user-avatar']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/a[last()-1]"], "exampleValues": [{"num": 0, "value": ""}], "unique_index": "/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数2_链接地址", "desc": "", "relativeXPath": "/a[1]", "allXPaths": ["/a[1]", "//a[contains(., '')]", "//A[@class='user-avatar']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "//space.bilibili.com/291929894"}], "unique_index": "/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数3_图片地址", "desc": "", "relativeXPath": "/a[1]/img[1]", "allXPaths": ["/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/a[last()-1]/img"], "exampleValues": [{"num": 0, "value": "https://i2.hdslb.com/bfs/face/2e6fb6974ca4b14f3e4f47a9d5f100c653f95066.jpg"}], "unique_index": "/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数4_链接文本", "desc": "", "relativeXPath": "/div[2]/a[1]", "allXPaths": ["/div[2]/a[1]", "//a[contains(., '')]", "//A[@class='pic']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-3]/a[last()-1]"], "exampleValues": [{"num": 0, "value": ""}], "unique_index": "/div[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数5_链接地址", "desc": "", "relativeXPath": "/div[2]/a[1]", "allXPaths": ["/div[2]/a[1]", "//a[contains(., '')]", "//A[@class='pic']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-3]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "//www.bilibili.com/video/BV1Fk4y1L7xX"}], "unique_index": "/div[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数6_图片地址", "desc": "", "relativeXPath": "/div[2]/a[1]/img[1]", "allXPaths": ["/div[2]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-3]/a[last()-1]/img"], "exampleValues": [{"num": 0, "value": "http://i0.hdslb.com/bfs/archive/261754d722ebf2c8dd3af455b44285927fbbe757.jpg"}], "unique_index": "/div[2]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数7_链接文本", "desc": "", "relativeXPath": "/div[2]/a[2]", "allXPaths": ["/div[2]/a[2]", "//a[contains(., '可视化爬虫易采集Ea')]", "//A[@class='title ellipsis']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-3]/a"], "exampleValues": [{"num": 0, "value": "可视化爬虫易采集EasySpider：一个无需写代码，可视化的几分钟设计一个爬虫的开源免费软件该视频全部评论"}], "unique_index": "/div[2]/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数8_链接地址", "desc": "", "relativeXPath": "/div[2]/a[2]", "allXPaths": ["/div[2]/a[2]", "//a[contains(., '可视化爬虫易采集Ea')]", "//A[@class='title ellipsis']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-3]/a"], "exampleValues": [{"num": 0, "value": "/platform/comment/article/BV1Fk4y1L7xX/%25E5%258F%25AF%25E8%25A7%2586%25E5%258C%2596%25E7%2588%25AC%25E8%2599%25AB%25E6%2598%2593%25E9%2587%2587%25E9%259B%2586EasySpider%25EF%25BC%259A%25E4%25B8%2580%25E4%25B8%25AA%25E6%2597%25A0%25E9%259C%2580%25E5%2586%2599%25E4%25BB%25A3%25E7%25A0%2581%25EF%25BC%258C%25E5%258F%25AF%25E8%25A7%2586%25E5%258C%2596%25E7%259A%2584%25E5%2587%25A0%25E5%2588%2586%25E9%2592%259F%25E8%25AE%25BE%25E8%25AE%25A1%25E4%25B8%2580%25E4%25B8%25AA%25E7%2588%25AC%25E8%2599%25AB%25E7%259A%2584%25E5%25BC%2580%25E6%25BA%2590%25E5%2585%258D%25E8%25B4%25B9%25E8%25BD%25AF%25E4%25BB%25B6?type=1"}], "unique_index": "/div[2]/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数9_文本", "desc": "", "relativeXPath": "/div[3]/span[1]", "allXPaths": ["/div[3]/span[1]", "//span[contains(., '已充电')]", "//SPAN[@class='relation-label']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-2]/span[last()-4]"], "exampleValues": [{"num": 0, "value": "已充电"}], "unique_index": "/div[3]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数10_链接文本", "desc": "", "relativeXPath": "/div[3]/a[1]", "allXPaths": ["/div[3]/a[1]", "//a[contains(., '天际青年')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-2]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "天际青年"}], "unique_index": "/div[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数11_链接地址", "desc": "", "relativeXPath": "/div[3]/a[1]", "allXPaths": ["/div[3]/a[1]", "//a[contains(., '天际青年')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-2]/a[last()-1]"], "exampleValues": [{"num": 0, "value": "//space.bilibili.com/291929894"}], "unique_index": "/div[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数12_文本", "desc": "", "relativeXPath": "/div[3]/span[3]", "allXPaths": ["/div[3]/span[3]", "//span[contains(., '回复')]", "//SPAN[@class='ci-title-split']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-2]/span[last()-2]"], "exampleValues": [{"num": 0, "value": "回复"}], "unique_index": "/div[3]/span[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数13_链接文本", "desc": "", "relativeXPath": "/div[3]/a[2]", "allXPaths": ["/div[3]/a[2]", "//a[contains(., '@豪He鑫')]", "//A[@class='parent-user']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-2]/a"], "exampleValues": [{"num": 0, "value": "@豪He鑫"}], "unique_index": "/div[3]/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数14_链接地址", "desc": "", "relativeXPath": "/div[3]/a[2]", "allXPaths": ["/div[3]/a[2]", "//a[contains(., '@豪He鑫')]", "//A[@class='parent-user']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-2]/a"], "exampleValues": [{"num": 0, "value": "//space.bilibili.com/1209416740"}], "unique_index": "/div[3]/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数15_文本", "desc": "", "relativeXPath": "/div[3]/span[4]", "allXPaths": ["/div[3]/span[4]", "//span[contains(., '的评论')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-2]/span[last()-1]"], "exampleValues": [{"num": 0, "value": "的评论"}], "unique_index": "/div[3]/span[4]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数16_文本", "desc": "", "relativeXPath": "/div[3]/span[5]/span[1]", "allXPaths": ["/div[3]/span[5]/span[1]", "//span[contains(., '查看评论')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-2]/span/span[last()-1]"], "exampleValues": [{"num": 0, "value": "查看评论"}], "unique_index": "/div[3]/span[5]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数17_文本", "desc": "", "relativeXPath": "/div[3]/span[5]/span[2]", "allXPaths": ["/div[3]/span[5]/span[2]", "//span[contains(., '收起评论')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-2]/span/span"], "exampleValues": [{"num": 0, "value": "收起评论"}], "unique_index": "/div[3]/span[5]/span[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数18_链接文本", "desc": "", "relativeXPath": "/div[4]/a[1]", "allXPaths": ["/div[4]/a[1]", "//a[contains(., '')]", "//A[@class='cipr-avatar']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-1]/a"], "exampleValues": [{"num": 0, "value": ""}], "unique_index": "/div[4]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数19_链接地址", "desc": "", "relativeXPath": "/div[4]/a[1]", "allXPaths": ["/div[4]/a[1]", "//a[contains(., '')]", "//A[@class='cipr-avatar']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-1]/a"], "exampleValues": [{"num": 0, "value": "//space.bilibili.com/1209416740"}], "unique_index": "/div[4]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 4, "contentType": 1, "relative": true, "name": "参数20_图片地址", "desc": "", "relativeXPath": "/div[4]/a[1]/img[1]", "allXPaths": ["/div[4]/a[1]/img[1]", "//img[contains(., '')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-1]/a/img"], "exampleValues": [{"num": 0, "value": "https://i1.hdslb.com/bfs/face/149f643807d3777efe7fc7bb7f24db0683beda5e.jpg@48w_48h"}], "unique_index": "/div[4]/a[1]/img[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数21_链接文本", "desc": "", "relativeXPath": "/div[4]/div[1]/a[1]", "allXPaths": ["/div[4]/div[1]/a[1]", "//a[contains(., '豪He鑫')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-1]/div[last()-2]/a"], "exampleValues": [{"num": 0, "value": "豪He鑫"}], "unique_index": "/div[4]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数22_链接地址", "desc": "", "relativeXPath": "/div[4]/div[1]/a[1]", "allXPaths": ["/div[4]/div[1]/a[1]", "//a[contains(., '豪He鑫')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-1]/div[last()-2]/a"], "exampleValues": [{"num": 0, "value": "//space.bilibili.com/1209416740"}], "unique_index": "/div[4]/div[1]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数23_文本", "desc": "", "relativeXPath": "/div[4]/div[2]", "allXPaths": ["/div[4]/div[2]", "//div[contains(., '我点  使用纯净版浏')]", "//DIV[@class='cipr-content']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-1]/div[last()-1]"], "exampleValues": [{"num": 0, "value": "我点使用纯净版浏览器设计还有其他的都闪一下，然后没打开浏览器啊"}], "unique_index": "/div[4]/div[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数24_文本", "desc": "", "relativeXPath": "/div[4]/div[3]", "allXPaths": ["/div[4]/div[3]", "//div[contains(., '2023-07-19')]", "//DIV[@class='cipr-footer']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div[last()-1]/div"], "exampleValues": [{"num": 0, "value": "2023-07-1909:57:18"}], "unique_index": "/div[4]/div[3]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数25_链接文本", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., 'Win7下载32位')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/a"], "exampleValues": [{"num": 0, "value": "Win7下载32位"}], "unique_index": "/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数26_链接地址", "desc": "", "relativeXPath": "/a[2]", "allXPaths": ["/a[2]", "//a[contains(., 'Win7下载32位')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/a"], "exampleValues": [{"num": 0, "value": "//www.bilibili.com/video/BV1Fk4y1L7xX"}], "unique_index": "/a[2]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数27_文本", "desc": "", "relativeXPath": "/a[2]/div[1]", "allXPaths": ["/a[2]/div[1]", "//div[contains(., 'Win7下载32位')]", "//DIV[@class='ci-content']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/a/div"], "exampleValues": [{"num": 0, "value": "Win7下载32位"}], "unique_index": "/a[2]/div[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数28_文本", "desc": "", "relativeXPath": "/div[5]/span[1]", "allXPaths": ["/div[5]/span[1]", "//span[contains(., '2023-07-20')]", "//SPAN[@class='date']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div/span[last()-4]"], "exampleValues": [{"num": 0, "value": "2023-07-2013:01:39"}], "unique_index": "/div[5]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数29_链接文本", "desc": "", "relativeXPath": "/div[5]/span[2]/a[1]", "allXPaths": ["/div[5]/span[2]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div/span[last()-3]/a"], "exampleValues": [{"num": 0, "value": ""}], "unique_index": "/div[5]/span[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数30_链接地址", "desc": "", "relativeXPath": "/div[5]/span[2]/a[1]", "allXPaths": ["/div[5]/span[2]/a[1]", "//a[contains(., '')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div/span[last()-3]/a"], "exampleValues": [{"num": 0, "value": ""}], "unique_index": "/div[5]/span[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数31_链接文本", "desc": "", "relativeXPath": "/div[5]/span[3]/a[1]", "allXPaths": ["/div[5]/span[3]/a[1]", "//a[contains(., '回复')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div/span[last()-2]/a"], "exampleValues": [{"num": 0, "value": "回复"}], "unique_index": "/div[5]/span[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数32_链接地址", "desc": "", "relativeXPath": "/div[5]/span[3]/a[1]", "allXPaths": ["/div[5]/span[3]/a[1]", "//a[contains(., '回复')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div/span[last()-2]/a"], "exampleValues": [{"num": 0, "value": ""}], "unique_index": "/div[5]/span[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数33_链接文本", "desc": "", "relativeXPath": "/div[5]/span[4]/a[1]", "allXPaths": ["/div[5]/span[4]/a[1]", "//a[contains(., '举报')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div/span[last()-1]/a"], "exampleValues": [{"num": 0, "value": "举报"}], "unique_index": "/div[5]/span[4]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数34_链接地址", "desc": "", "relativeXPath": "/div[5]/span[4]/a[1]", "allXPaths": ["/div[5]/span[4]/a[1]", "//a[contains(., '举报')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div/span[last()-1]/a"], "exampleValues": [{"num": 0, "value": ""}], "unique_index": "/div[5]/span[4]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数35_链接文本", "desc": "", "relativeXPath": "/div[5]/span[5]/a[1]", "allXPaths": ["/div[5]/span[5]/a[1]", "//a[contains(., '删除')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div/span/a"], "exampleValues": [{"num": 0, "value": "删除"}], "unique_index": "/div[5]/span[5]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数36_链接地址", "desc": "", "relativeXPath": "/div[5]/span[5]/a[1]", "allXPaths": ["/div[5]/span[5]/a[1]", "//a[contains(., '删除')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-9]/div/span/a"], "exampleValues": [{"num": 0, "value": ""}], "unique_index": "/div[5]/span[5]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 0, "contentType": 1, "relative": true, "name": "参数37_文本", "desc": "", "relativeXPath": "/div[4]/span[1]", "allXPaths": ["/div[4]/span[1]", "//span[contains(., '2023-07-19')]", "//SPAN[@class='date']", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-8]/div/span[last()-4]"], "exampleValues": [{"num": 1, "value": "2023-07-1909:57:18"}], "unique_index": "/div[4]/span[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数38_链接文本", "desc": "", "relativeXPath": "/div[4]/span[2]/a[1]", "allXPaths": ["/div[4]/span[2]/a[1]", "//a[contains(., '1')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-8]/div/span[last()-3]/a"], "exampleValues": [{"num": 1, "value": "1"}], "unique_index": "/div[4]/span[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数39_链接地址", "desc": "", "relativeXPath": "/div[4]/span[2]/a[1]", "allXPaths": ["/div[4]/span[2]/a[1]", "//a[contains(., '1')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-8]/div/span[last()-3]/a"], "exampleValues": [{"num": 1, "value": ""}], "unique_index": "/div[4]/span[2]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数40_链接文本", "desc": "", "relativeXPath": "/div[4]/span[3]/a[1]", "allXPaths": ["/div[4]/span[3]/a[1]", "//a[contains(., '回复')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-8]/div/span[last()-2]/a"], "exampleValues": [{"num": 1, "value": "回复"}], "unique_index": "/div[4]/span[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数41_链接地址", "desc": "", "relativeXPath": "/div[4]/span[3]/a[1]", "allXPaths": ["/div[4]/span[3]/a[1]", "//a[contains(., '回复')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-8]/div/span[last()-2]/a"], "exampleValues": [{"num": 1, "value": ""}], "unique_index": "/div[4]/span[3]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数42_链接文本", "desc": "", "relativeXPath": "/div[4]/span[4]/a[1]", "allXPaths": ["/div[4]/span[4]/a[1]", "//a[contains(., '举报')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-8]/div/span[last()-1]/a"], "exampleValues": [{"num": 1, "value": "举报"}], "unique_index": "/div[4]/span[4]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数43_链接地址", "desc": "", "relativeXPath": "/div[4]/span[4]/a[1]", "allXPaths": ["/div[4]/span[4]/a[1]", "//a[contains(., '举报')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-8]/div/span[last()-1]/a"], "exampleValues": [{"num": 1, "value": ""}], "unique_index": "/div[4]/span[4]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 1, "contentType": 0, "relative": true, "name": "参数44_链接文本", "desc": "", "relativeXPath": "/div[4]/span[5]/a[1]", "allXPaths": ["/div[4]/span[5]/a[1]", "//a[contains(., '删除')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-8]/div/span/a"], "exampleValues": [{"num": 1, "value": "删除"}], "unique_index": "/div[4]/span[5]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}, {"nodeType": 2, "contentType": 0, "relative": true, "name": "参数45_链接地址", "desc": "", "relativeXPath": "/div[4]/span[5]/a[1]", "allXPaths": ["/div[4]/span[5]/a[1]", "//a[contains(., '删除')]", "/html/body/div[last()-3]/div[last()-2]/div/div[last()-1]/div/section/div/div/div[last()-8]/div/span/a"], "exampleValues": [{"num": 1, "value": ""}], "unique_index": "/div[4]/span[5]/a[1]", "iframe": false, "default": "", "paraType": "text", "recordASField": 1, "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}], "loopType": 1}}]}