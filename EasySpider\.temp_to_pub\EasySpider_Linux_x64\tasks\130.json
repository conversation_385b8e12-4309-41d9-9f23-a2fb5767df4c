{"id": 130, "name": "百度一下，你就知道", "url": "https://www.baidu.com/", "links": "https://www.baidu.com/", "create_time": "7/5/2023, 5:15:46 AM", "version": "0.3.5", "saveThreshold": 10, "cloudflare": 0, "environment": 0, "containJudge": false, "desc": "https://www.baidu.com/", "inputParameters": [{"id": 0, "name": "urlList_0", "nodeId": 1, "nodeName": "打开网页", "value": "https://www.baidu.com/", "desc": "要采集的网址列表，多行以\\n分开", "type": "string", "exampleValue": "https://www.baidu.com/"}], "outputParameters": [{"id": 0, "name": "参数1_文本", "desc": "", "type": "string", "exampleValue": "把牢正确方向 增进团结互信"}], "graph": [{"index": 0, "id": 0, "parentId": 0, "type": -1, "option": 0, "title": "root", "sequence": [1, 2], "parameters": {"history": 1, "tabIndex": 0, "useLoop": false, "xpath": "", "wait": 0}, "isInLoop": false}, {"id": 1, "index": 1, "parentId": 0, "type": 0, "option": 1, "title": "打开网页", "sequence": [], "isInLoop": false, "position": 0, "parameters": {"useLoop": false, "xpath": "", "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "url": "https://www.baidu.com/", "links": "https://www.baidu.com/", "maxWaitTime": 10, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1}}, {"id": 2, "index": 2, "parentId": 0, "type": 1, "option": 8, "title": "循环", "sequence": [3, 4], "isInLoop": false, "position": 1, "parameters": {"history": 4, "tabIndex": -1, "useLoop": false, "xpath": "/html/body/div[1]/div[1]/div[5]/div[1]/div[1]/div[3]/ul[1]/li/a[1]", "iframe": true, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "loopType": 1, "pathList": "", "textList": "", "code": "", "waitTime": 0, "exitCount": 0, "historyWait": 2, "breakMode": 0, "breakCode": "", "breakCodeWaitTime": 0, "allXPaths": ""}}, {"id": 3, "index": 3, "parentId": 2, "type": 0, "option": 2, "title": "点击元素", "sequence": [], "isInLoop": true, "position": 0, "parameters": {"history": 4, "tabIndex": -1, "useLoop": true, "xpath": "/html/body/div[1]/div[1]/div[5]/div[1]/div[1]/div[3]/ul[1]/li/a[1]", "iframe": false, "wait": 2, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "scrollType": 0, "scrollCount": 1, "scrollWaitTime": 1, "clickWay": 0, "maxWaitTime": 10, "params": [], "allXPaths": "", "loopType": 1}}, {"id": 4, "index": 4, "parentId": 2, "type": 0, "option": 3, "title": "提取数据", "sequence": [], "isInLoop": true, "position": 1, "parameters": {"history": 1, "tabIndex": -1, "useLoop": false, "xpath": "", "iframe": false, "wait": 0, "waitType": 0, "beforeJS": "", "beforeJSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "params": [{"nodeType": 3, "contentType": 0, "relative": false, "name": "参数1_文本", "desc": "", "extractType": 0, "relativeXPath": "/html/body/div[3]/div[1]/div[1]/div[1]/div[1]/form[1]/span[1]/input[1]", "allXPaths": ["/html/body/div[3]/div[1]/div[1]/div[1]/div[1]/form[1]/span[1]/input[1]", "//input[contains(., '')]", "id(\"kw\")", "//INPUT[@class='s_ipt']", "//INPUT[@name='wd']", "/html/body/div[last()-6]/div[last()-3]/div/div[last()-3]/div/form/span[last()-2]/input"], "exampleValues": [{"num": 0, "value": "把牢正确方向 增进团结互信"}], "unique_index": "s89sfrujkzljosgb72", "iframe": false, "default": "", "beforeJS": "", "beforeJSWaitTime": 0, "JS": "", "JSWaitTime": 0, "afterJS": "", "afterJSWaitTime": 0, "downloadPic": 0}]}}]}